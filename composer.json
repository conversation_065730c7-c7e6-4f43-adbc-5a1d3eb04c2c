{"name": "scolavisa/class", "description": "Class course learning administration support system.", "keywords": ["class", "learning", "administration", "course", "student", "teacher", "planning", "schedule"], "license": "Commercial", "type": "project", "require": {"php": "^8.2", "ext-gd": "*", "ext-json": "*", "bacon/bacon-qr-code": "^2.0.8", "bugsnag/bugsnag-laravel": "^v2.26.0", "doctrine/dbal": "^3.3.8", "guzzlehttp/guzzle": "^7.5.1", "laravel/framework": "^v10.9.0", "laravel/passport": "^v11.8.0", "laravel/pulse": "^1.2", "laravel/sanctum": "^v3.2.5", "laravel/tinker": "^v2.8.1", "laravel/ui": "^v4.2.1", "laravelcollective/html": "^v6.4.1", "league/flysystem-sftp-v3": "^3.0", "maatwebsite/excel": "^3.1.48", "mailgun/mailgun-php": "^3.6", "nesbot/carbon": "^2.73.0", "pragmarx/google2fa-laravel": "^v2.1.1", "pragmarx/google2fa-qrcode": "^v3.0.0", "scolavisa/scolib": "1.3.1", "spatie/laravel-html": "3.2.1", "symfony/http-client": "^6.2", "symfony/mailgun-mailer": "^6.2", "theantichris/icalendar-creator": "^v1.0.1", "tombenevides/metavel": "^0.0.2"}, "require-dev": {"spatie/laravel-ignition": "2.1.1", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^v7.5.2", "phpunit/phpunit": "^10.1.2"}, "repositories": [{"type": "git", "url": "**************:Scolavisa/scolib.git"}], "autoload": {"classmap": ["database"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/SvHelpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"test": ["@php artisan test"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"], "post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "platform-check": false, "allow-plugins": {"composer/package-versions-deprecated": true, "pestphp/pest-plugin": true, "php-http/discovery": true}, "platform": {"php": "8.2"}}}