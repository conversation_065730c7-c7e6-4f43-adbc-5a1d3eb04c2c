{"version": 3, "file": "summernote.js", "sources": ["../src/js/base/renderer.js", "../src/js/bs3/ui.js", "../src/js/base/core/func.js", "../src/js/base/core/lists.js", "../src/js/base/core/env.js", "../src/js/base/core/dom.js", "../src/js/base/summernote-en-US.js", "../src/js/base/core/key.js", "../src/js/base/core/range.js", "../src/js/base/core/async.js", "../src/js/base/editing/History.js", "../src/js/base/editing/Style.js", "../src/js/base/editing/Bullet.js", "../src/js/base/editing/Typing.js", "../src/js/base/editing/Table.js", "../src/js/base/module/Editor.js", "../src/js/base/module/Clipboard.js", "../src/js/base/module/Dropzone.js", "../src/js/base/module/Codeview.js", "../src/js/base/module/Statusbar.js", "../src/js/base/module/Fullscreen.js", "../src/js/base/module/Handle.js", "../src/js/base/module/AutoLink.js", "../src/js/base/module/AutoSync.js", "../src/js/base/module/Placeholder.js", "../src/js/base/module/Buttons.js", "../src/js/base/module/Toolbar.js", "../src/js/base/module/LinkDialog.js", "../src/js/base/module/LinkPopover.js", "../src/js/base/module/ImageDialog.js", "../src/js/base/module/ImagePopover.js", "../src/js/base/module/TablePopover.js", "../src/js/base/module/VideoDialog.js", "../src/js/base/module/HelpDialog.js", "../src/js/base/module/AirPopover.js", "../src/js/base/module/HintPopover.js", "../src/js/base/Context.js", "../src/js/summernote.js", "../src/js/bs3/settings.js"], "sourcesContent": ["import $ from 'jquery';\n\nclass Renderer {\n  constructor(markup, children, options, callback) {\n    this.markup = markup;\n    this.children = children;\n    this.options = options;\n    this.callback = callback;\n  }\n\n  render($parent) {\n    const $node = $(this.markup);\n\n    if (this.options && this.options.contents) {\n      $node.html(this.options.contents);\n    }\n\n    if (this.options && this.options.className) {\n      $node.addClass(this.options.className);\n    }\n\n    if (this.options && this.options.data) {\n      $.each(this.options.data, (k, v) => {\n        $node.attr('data-' + k, v);\n      });\n    }\n\n    if (this.options && this.options.click) {\n      $node.on('click', this.options.click);\n    }\n\n    if (this.children) {\n      const $container = $node.find('.note-children-container');\n      this.children.forEach((child) => {\n        child.render($container.length ? $container : $node);\n      });\n    }\n\n    if (this.callback) {\n      this.callback($node, this.options);\n    }\n\n    if (this.options && this.options.callback) {\n      this.options.callback($node);\n    }\n\n    if ($parent) {\n      $parent.append($node);\n    }\n\n    return $node;\n  }\n}\n\nexport default {\n  create: (markup, callback) => {\n    return () => {\n      const options = typeof arguments[1] === 'object' ? arguments[1] : arguments[0];\n      let children = $.isArray(arguments[0]) ? arguments[0] : [];\n      if (options && options.children) {\n        children = options.children;\n      }\n      return new Renderer(markup, children, options, callback);\n    };\n  }\n};\n", "import $ from 'jquery';\nimport renderer from '../base/renderer';\n\nconst editor = renderer.create('<div class=\"note-editor note-frame panel\"/>');\nconst toolbar = renderer.create('<div class=\"note-toolbar-wrapper panel-default\"><div class=\"note-toolbar panel-heading\" role=\"toolbar\"></div></div>');\nconst editingArea = renderer.create('<div class=\"note-editing-area\"/>');\nconst codable = renderer.create('<textarea class=\"note-codable\" role=\"textbox\" aria-multiline=\"true\"/>');\nconst editable = renderer.create('<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"/>');\nconst statusbar = renderer.create([\n  '<output class=\"note-status-output\" aria-live=\"polite\"/>',\n  '<div class=\"note-statusbar\" role=\"status\">',\n  '  <div class=\"note-resizebar\" role=\"seperator\" aria-orientation=\"horizontal\" aria-label=\"Resize\">',\n  '    <div class=\"note-icon-bar\"/>',\n  '    <div class=\"note-icon-bar\"/>',\n  '    <div class=\"note-icon-bar\"/>',\n  '  </div>',\n  '</div>'\n].join(''));\n\nconst airEditor = renderer.create('<div class=\"note-editor\"/>');\nconst airEditable = renderer.create([\n  '  <output class=\"note-status-output\" aria-live=\"polite\"/>',\n  '<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"/>'\n].join(''));\n\nconst buttonGroup = renderer.create('<div class=\"note-btn-group btn-group\">');\n\nconst dropdown = renderer.create('<ul class=\"dropdown-menu\" role=\"list\">', function($node, options) {\n  const markup = $.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n    const option = (typeof item === 'object') ? item.option : undefined;\n\n    const dataValue = 'data-value=\"' + value + '\"';\n    const dataOption = (option !== undefined) ? ' data-option=\"' + option + '\"' : '';\n    return '<li role=\"listitem\" aria-label=\"' + item + '\"><a href=\"#\" ' + (dataValue + dataOption) + '>' + content + '</a></li>';\n  }).join('') : options.items;\n\n  $node.html(markup).attr({'aria-label': options.title});\n});\n\nconst dropdownButtonContents = function(contents, options) {\n  return contents + ' ' + icon(options.icons.caret, 'span');\n};\n\nconst dropdownCheck = renderer.create('<ul class=\"dropdown-menu note-check\" role=\"list\">', function($node, options) {\n  const markup = $.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n    return '<li role=\"listitem\" aria-label=\"' + item + '\"><a href=\"#\" data-value=\"' + value + '\">' + icon(options.checkClassName) + ' ' + content + '</a></li>';\n  }).join('') : options.items;\n  $node.html(markup).attr({'aria-label': options.title});\n});\n\nconst palette = renderer.create('<div class=\"note-color-palette\"/>', function($node, options) {\n  const contents = [];\n  for (let row = 0, rowSize = options.colors.length; row < rowSize; row++) {\n    const eventName = options.eventName;\n    const colors = options.colors[row];\n    const colorsName = options.colorsName[row];\n    const buttons = [];\n    for (let col = 0, colSize = colors.length; col < colSize; col++) {\n      const color = colors[col];\n      const colorName = colorsName[col];\n      buttons.push([\n        '<button type=\"button\" class=\"note-color-btn\"',\n        'style=\"background-color:', color, '\" ',\n        'data-event=\"', eventName, '\" ',\n        'data-value=\"', color, '\" ',\n        'title=\"', colorName, '\" ',\n        'aria-label=\"', colorName, '\" ',\n        'data-toggle=\"button\" tabindex=\"-1\"></button>'\n      ].join(''));\n    }\n    contents.push('<div class=\"note-color-row\">' + buttons.join('') + '</div>');\n  }\n  $node.html(contents.join(''));\n\n  if (options.tooltip) {\n    $node.find('.note-color-btn').tooltip({\n      container: options.container,\n      trigger: 'hover',\n      placement: 'bottom'\n    });\n  }\n});\n\nconst dialog = renderer.create('<div class=\"modal\" aria-hidden=\"false\" tabindex=\"-1\" role=\"dialog\"/>', function($node, options) {\n  if (options.fade) {\n    $node.addClass('fade');\n  }\n  $node.attr({\n    'aria-label': options.title\n  });\n  $node.html([\n    '<div class=\"modal-dialog\">',\n    '  <div class=\"modal-content\">',\n    (options.title\n      ? '    <div class=\"modal-header\">' +\n    '      <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\" aria-hidden=\"true\">&times;</button>' +\n    '      <h4 class=\"modal-title\">' + options.title + '</h4>' +\n    '    </div>' : ''\n    ),\n    '    <div class=\"modal-body\">' + options.body + '</div>',\n    (options.footer\n      ? '    <div class=\"modal-footer\">' + options.footer + '</div>' : ''\n    ),\n    '  </div>',\n    '</div>'\n  ].join(''));\n});\n\nconst popover = renderer.create([\n  '<div class=\"note-popover popover in\">',\n  '  <div class=\"arrow\"/>',\n  '  <div class=\"popover-content note-children-container\"/>',\n  '</div>'\n].join(''), function($node, options) {\n  const direction = typeof options.direction !== 'undefined' ? options.direction : 'bottom';\n\n  $node.addClass(direction);\n\n  if (options.hideArrow) {\n    $node.find('.arrow').hide();\n  }\n});\n\nconst checkbox = renderer.create('<div class=\"checkbox\"></div>', function($node, options) {\n  $node.html([\n    ' <label' + (options.id ? ' for=\"' + options.id + '\"' : '') + '>',\n    ' <input role=\"checkbox\" type=\"checkbox\"' + (options.id ? ' id=\"' + options.id + '\"' : ''),\n    (options.checked ? ' checked' : ''),\n    ' aria-checked=\"' + (options.checked ? 'true' : 'false') + '\"/>',\n    (options.text ? options.text : ''),\n    '</label>'\n  ].join(''));\n});\n\nconst icon = function(iconClassName, tagName) {\n  tagName = tagName || 'i';\n  return '<' + tagName + ' class=\"' + iconClassName + '\"/>';\n};\nconst ui = {\n  editor: editor,\n  toolbar: toolbar,\n  editingArea: editingArea,\n  codable: codable,\n  editable: editable,\n  statusbar: statusbar,\n  airEditor: airEditor,\n  airEditable: airEditable,\n  buttonGroup: buttonGroup,\n  dropdown: dropdown,\n  dropdownButtonContents: dropdownButtonContents,\n  dropdownCheck: dropdownCheck,\n  palette: palette,\n  dialog: dialog,\n  popover: popover,\n  checkbox: checkbox,\n  icon: icon,\n  options: {},\n\n  button: function($node, options) {\n    return renderer.create('<button type=\"button\" class=\"note-btn btn btn-default btn-sm\" role=\"button\" tabindex=\"-1\">', function($node, options) {\n      if (options && options.tooltip) {\n        $node.attr({\n          title: options.tooltip,\n          'aria-label': options.tooltip\n        }).tooltip({\n          container: options.container,\n          trigger: 'hover',\n          placement: 'bottom'\n        });\n      }\n    })($node, options);\n  },\n\n  toggleBtn: function($btn, isEnable) {\n    $btn.toggleClass('disabled', !isEnable);\n    $btn.attr('disabled', !isEnable);\n  },\n\n  toggleBtnActive: function($btn, isActive) {\n    $btn.toggleClass('active', isActive);\n  },\n\n  onDialogShown: function($dialog, handler) {\n    $dialog.one('shown.bs.modal', handler);\n  },\n\n  onDialogHidden: function($dialog, handler) {\n    $dialog.one('hidden.bs.modal', handler);\n  },\n\n  showDialog: function($dialog) {\n    $dialog.modal('show');\n  },\n\n  hideDialog: function($dialog) {\n    $dialog.modal('hide');\n  },\n\n  createLayout: function($note, options) {\n    const $editor = (options.airMode ? ui.airEditor([\n      ui.editingArea([\n        ui.airEditable()\n      ])\n    ]) : ui.editor([\n      ui.toolbar(),\n      ui.editingArea([\n        ui.codable(),\n        ui.editable()\n      ]),\n      ui.statusbar()\n    ])).render();\n\n    $editor.insertAfter($note);\n\n    return {\n      note: $note,\n      editor: $editor,\n      toolbar: $editor.find('.note-toolbar'),\n      editingArea: $editor.find('.note-editing-area'),\n      editable: $editor.find('.note-editable'),\n      codable: $editor.find('.note-codable'),\n      statusbar: $editor.find('.note-statusbar')\n    };\n  },\n\n  removeLayout: function($note, layoutInfo) {\n    $note.html(layoutInfo.editable.html());\n    layoutInfo.editor.remove();\n    $note.show();\n  }\n};\n\nexport default ui;\n", "/**\n * @class core.func\n *\n * func utils (for high-order func's arg)\n *\n * @singleton\n * @alternateClassName func\n */\nfunction eq(itemA) {\n  return function(itemB) {\n    return itemA === itemB;\n  };\n}\n\nfunction eq2(itemA, itemB) {\n  return itemA === itemB;\n}\n\nfunction peq2(propName) {\n  return function(itemA, itemB) {\n    return itemA[propName] === itemB[propName];\n  };\n}\n\nfunction ok() {\n  return true;\n}\n\nfunction fail() {\n  return false;\n}\n\nfunction not(f) {\n  return () => {\n    return !f.apply(f, arguments);\n  };\n}\n\nfunction and(fA, fB) {\n  return function(item) {\n    return fA(item) && fB(item);\n  };\n}\n\nfunction self(a) {\n  return a;\n}\n\nfunction invoke(obj, method) {\n  return () => {\n    return obj[method].apply(obj, arguments);\n  };\n}\n\nlet idCounter = 0;\n\n/**\n * generate a globally-unique id\n *\n * @param {String} [prefix]\n */\nfunction uniqueId(prefix) {\n  const id = ++idCounter + '';\n  return prefix ? prefix + id : id;\n}\n\n/**\n * returns bnd (bounds) from rect\n *\n * - IE Compatibility Issue: http://goo.gl/sRLOAo\n * - Scroll Issue: http://goo.gl/sNjUc\n *\n * @param {Rect} rect\n * @return {Object} bounds\n * @return {Number} bounds.top\n * @return {Number} bounds.left\n * @return {Number} bounds.width\n * @return {Number} bounds.height\n */\nfunction rect2bnd(rect) {\n  const $document = $(document);\n  return {\n    top: rect.top + $document.scrollTop(),\n    left: rect.left + $document.scrollLeft(),\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n}\n\n/**\n * returns a copy of the object where the keys have become the values and the values the keys.\n * @param {Object} obj\n * @return {Object}\n */\nfunction invertObject(obj) {\n  const inverted = {};\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      inverted[obj[key]] = key;\n    }\n  }\n  return inverted;\n}\n\n/**\n * @param {String} namespace\n * @param {String} [prefix]\n * @return {String}\n */\nfunction namespaceToCamel(namespace, prefix) {\n  prefix = prefix || '';\n  return prefix + namespace.split('.').map(function(name) {\n    return name.substring(0, 1).toUpperCase() + name.substring(1);\n  }).join('');\n}\n\n/**\n * Returns a function, that, as long as it continues to be invoked, will not\n * be triggered. The function will be called after it stops being called for\n * N milliseconds. If `immediate` is passed, trigger the function on the\n * leading edge, instead of the trailing.\n * @param {Function} func\n * @param {Number} wait\n * @param {Boolean} immediate\n * @return {Function}\n */\nfunction debounce(func, wait, immediate) {\n  let timeout;\n  return () => {\n    const context = this;\n    const args = arguments;\n    const later = () => {\n      timeout = null;\n      if (!immediate) {\n        func.apply(context, args);\n      }\n    };\n    const callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) {\n      func.apply(context, args);\n    }\n  };\n}\n\nexport default {\n  eq,\n  eq2,\n  peq2,\n  ok,\n  fail,\n  self,\n  not,\n  and,\n  invoke,\n  uniqueId,\n  rect2bnd,\n  invertObject,\n  namespaceToCamel,\n  debounce\n};\n", "import $ from 'jquery';\nimport func from './func';\n\n/**\n * returns the first item of an array.\n *\n * @param {Array} array\n */\nfunction head(array) {\n  return array[0];\n}\n\n/**\n * returns the last item of an array.\n *\n * @param {Array} array\n */\nfunction last(array) {\n  return array[array.length - 1];\n}\n\n/**\n * returns everything but the last entry of the array.\n *\n * @param {Array} array\n */\nfunction initial(array) {\n  return array.slice(0, array.length - 1);\n}\n\n/**\n * returns the rest of the items in an array.\n *\n * @param {Array} array\n */\nfunction tail(array) {\n  return array.slice(1);\n}\n\n/**\n * returns item of array\n */\nfunction find(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    const item = array[idx];\n    if (pred(item)) {\n      return item;\n    }\n  }\n}\n\n/**\n * returns true if all of the values in the array pass the predicate truth test.\n */\nfunction all(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!pred(array[idx])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * returns index of item\n */\nfunction indexOf(array, item) {\n  return $.inArray(item, array);\n}\n\n/**\n * returns true if the value is present in the list.\n */\nfunction contains(array, item) {\n  return indexOf(array, item) !== -1;\n}\n\n/**\n * get sum from a list\n *\n * @param {Array} array - array\n * @param {Function} fn - iterator\n */\nfunction sum(array, fn) {\n  fn = fn || func.self;\n  return array.reduce(function(memo, v) {\n    return memo + fn(v);\n  }, 0);\n}\n\n/**\n * returns a copy of the collection with array type.\n * @param {Collection} collection - collection eg) node.childNodes, ...\n */\nfunction from(collection) {\n  const result = [];\n  const length = collection.length;\n  let idx = -1;\n  while (++idx < length) {\n    result[idx] = collection[idx];\n  }\n  return result;\n}\n\n/**\n * returns whether list is empty or not\n */\nfunction isEmpty(array) {\n  return !array || !array.length;\n}\n\n/**\n * cluster elements by predicate function.\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n * @param {Array[]}\n */\nfunction clusterBy(array, fn) {\n  if (!array.length) { return []; }\n  const aTail = tail(array);\n  return aTail.reduce(function(memo, v) {\n    const aLast = last(memo);\n    if (fn(last(aLast), v)) {\n      aLast[aLast.length] = v;\n    } else {\n      memo[memo.length] = [v];\n    }\n    return memo;\n  }, [[head(array)]]);\n}\n\n/**\n * returns a copy of the array with all false values removed\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n */\nfunction compact(array) {\n  const aResult = [];\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (array[idx]) { aResult.push(array[idx]); }\n  }\n  return aResult;\n}\n\n/**\n * produces a duplicate-free version of the array\n *\n * @param {Array} array\n */\nfunction unique(array) {\n  const results = [];\n\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!contains(results, array[idx])) {\n      results.push(array[idx]);\n    }\n  }\n\n  return results;\n}\n\n/**\n * returns next item.\n * @param {Array} array\n */\nfunction next(array, item) {\n  const idx = indexOf(array, item);\n  if (idx === -1) { return null; }\n\n  return array[idx + 1];\n}\n\n/**\n * returns prev item.\n * @param {Array} array\n */\nfunction prev(array, item) {\n  const idx = indexOf(array, item);\n  if (idx === -1) { return null; }\n\n  return array[idx - 1];\n}\n\n/**\n * @class core.list\n *\n * list utils\n *\n * @singleton\n * @alternateClassName list\n */\nexport default {\n  head,\n  last,\n  initial,\n  tail,\n  prev,\n  next,\n  find,\n  contains,\n  all,\n  sum,\n  from,\n  isEmpty,\n  clusterBy,\n  compact,\n  unique\n};\n", "import $ from 'jquery';\nconst isSupportAmd = typeof define === 'function' && define.amd; // eslint-disable-line\n\n/**\n * returns whether font is installed or not.\n *\n * @param {String} fontName\n * @return {Boolean}\n */\nfunction isFontInstalled(fontName) {\n  const testFontName = fontName === 'Comic Sans MS' ? 'Courier New' : 'Comic Sans MS';\n  const $tester = $('<div>').css({\n    position: 'absolute',\n    left: '-9999px',\n    top: '-9999px',\n    fontSize: '200px'\n  }).text('mmmmmmmmmwwwwwww').appendTo(document.body);\n\n  const originalWidth = $tester.css('fontFamily', testFontName).width();\n  const width = $tester.css('fontFamily', fontName + ',' + testFontName).width();\n\n  $tester.remove();\n\n  return originalWidth !== width;\n}\n\nconst userAgent = navigator.userAgent;\nconst isMSIE = /MSIE|Trident/i.test(userAgent);\nlet browserVersion;\nif (isMSIE) {\n  let matches = /MSIE (\\d+[.]\\d+)/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n  matches = /Trident\\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n}\n\nconst isEdge = /Edge\\/\\d+/.test(userAgent);\n\nlet hasCodeMirror = !!window.CodeMirror;\nif (!hasCodeMirror && isSupportAmd) {\n  // Webpack\n  if (typeof __webpack_require__ === 'function') { // eslint-disable-line\n    try {\n      // If CodeMirror can't be resolved, `require.resolve` will throw an\n      // exception and `hasCodeMirror` won't be set to `true`.\n      require.resolve('codemirror');\n      hasCodeMirror = true;\n    } catch (e) {\n      // do nothing\n    }\n  } else if (typeof require !== 'undefined') {\n    // Browserify\n    if (typeof require.resolve !== 'undefined') {\n      try {\n        // If CodeMirror can't be resolved, `require.resolve` will throw an\n        // exception and `hasCodeMirror` won't be set to `true`.\n        require.resolve('codemirror');\n        hasCodeMirror = true;\n      } catch (e) {\n        // do nothing\n      }\n    // Almond/Require\n    } else if (typeof require.specified !== 'undefined') {\n      hasCodeMirror = require.specified('codemirror');\n    }\n  }\n}\n\nconst isSupportTouch =\n  (('ontouchstart' in window) ||\n   (navigator.MaxTouchPoints > 0) ||\n   (navigator.msMaxTouchPoints > 0));\n\n// [workaround] IE doesn't have input events for contentEditable\n// - see: https://goo.gl/4bfIvA\nconst inputEventName = (isMSIE || isEdge) ? 'DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted' : 'input';\n\n/**\n * @class core.env\n *\n * Object which check platform and agent\n *\n * @singleton\n * @alternateClassName env\n */\nexport default {\n  isMac: navigator.appVersion.indexOf('Mac') > -1,\n  isMSIE,\n  isEdge,\n  isFF: !isEdge && /firefox/i.test(userAgent),\n  isPhantom: /PhantomJS/i.test(userAgent),\n  isWebkit: !isEdge && /webkit/i.test(userAgent),\n  isChrome: !isEdge && /chrome/i.test(userAgent),\n  isSafari: !isEdge && /safari/i.test(userAgent),\n  browserVersion,\n  jqueryVersion: parseFloat($.fn.jquery),\n  isSupportAmd,\n  isSupportTouch,\n  hasCodeMirror,\n  isFontInstalled,\n  isW3CRangeSupport: !!document.createRange,\n  inputEventName\n};\n", "import $ from 'jquery';\nimport func from './func';\nimport lists from './lists';\nimport env from './env';\n\nconst NBSP_CHAR = String.fromCharCode(160);\nconst ZERO_WIDTH_NBSP_CHAR = '\\ufeff';\n\n/**\n * @method isEditable\n *\n * returns whether node is `note-editable` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEditable(node) {\n  return node && $(node).hasClass('note-editable');\n}\n\n/**\n * @method isControlSizing\n *\n * returns whether node is `note-control-sizing` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isControlSizing(node) {\n  return node && $(node).hasClass('note-control-sizing');\n}\n\n/**\n * @method makePredByNodeName\n *\n * returns predicate which judge whether nodeName is same\n *\n * @param {String} nodeName\n * @return {Function}\n */\nfunction makePredByNodeName(nodeName) {\n  nodeName = nodeName.toUpperCase();\n  return function(node) {\n    return node && node.nodeName.toUpperCase() === nodeName;\n  };\n}\n\n/**\n * @method isText\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is text(3)\n */\nfunction isText(node) {\n  return node && node.nodeType === 3;\n}\n\n/**\n * @method isElement\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is element(1)\n */\nfunction isElement(node) {\n  return node && node.nodeType === 1;\n}\n\n/**\n * ex) br, col, embed, hr, img, input, ...\n * @see http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements\n */\nfunction isVoid(node) {\n  return node && /^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT/.test(node.nodeName.toUpperCase());\n}\n\nfunction isPara(node) {\n  if (isEditable(node)) {\n    return false;\n  }\n\n  // Chrome(v31.0), FF(v25.0.1) use DIV for paragraph\n  return node && /^DIV|^P|^LI|^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nfunction isHeading(node) {\n  return node && /^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nconst isPre = makePredByNodeName('PRE');\n\nconst isLi = makePredByNodeName('LI');\n\nfunction isPurePara(node) {\n  return isPara(node) && !isLi(node);\n}\n\nconst isTable = makePredByNodeName('TABLE');\n\nconst isData = makePredByNodeName('DATA');\n\nfunction isInline(node) {\n  return !isBodyContainer(node) &&\n         !isList(node) &&\n         !isHr(node) &&\n         !isPara(node) &&\n         !isTable(node) &&\n         !isBlockquote(node) &&\n         !isData(node);\n}\n\nfunction isList(node) {\n  return node && /^UL|^OL/.test(node.nodeName.toUpperCase());\n}\n\nconst isHr = makePredByNodeName('HR');\n\nfunction isCell(node) {\n  return node && /^TD|^TH/.test(node.nodeName.toUpperCase());\n}\n\nconst isBlockquote = makePredByNodeName('BLOCKQUOTE');\n\nfunction isBodyContainer(node) {\n  return isCell(node) || isBlockquote(node) || isEditable(node);\n}\n\nconst isAnchor = makePredByNodeName('A');\n\nfunction isParaInline(node) {\n  return isInline(node) && !!ancestor(node, isPara);\n}\n\nfunction isBodyInline(node) {\n  return isInline(node) && !ancestor(node, isPara);\n}\n\nconst isBody = makePredByNodeName('BODY');\n\n/**\n * returns whether nodeB is closest sibling of nodeA\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n * @return {Boolean}\n */\nfunction isClosestSibling(nodeA, nodeB) {\n  return nodeA.nextSibling === nodeB ||\n         nodeA.previousSibling === nodeB;\n}\n\n/**\n * returns array of closest siblings with node\n *\n * @param {Node} node\n * @param {function} [pred] - predicate function\n * @return {Node[]}\n */\nfunction withClosestSiblings(node, pred) {\n  pred = pred || func.ok;\n\n  const siblings = [];\n  if (node.previousSibling && pred(node.previousSibling)) {\n    siblings.push(node.previousSibling);\n  }\n  siblings.push(node);\n  if (node.nextSibling && pred(node.nextSibling)) {\n    siblings.push(node.nextSibling);\n  }\n  return siblings;\n}\n\n/**\n * blank HTML for cursor position\n * - [workaround] old IE only works with &nbsp;\n * - [workaround] IE11 and other browser works with bogus br\n */\nconst blankHTML = env.isMSIE && env.browserVersion < 11 ? '&nbsp;' : '<br>';\n\n/**\n * @method nodeLength\n *\n * returns #text's text size or element's childNodes size\n *\n * @param {Node} node\n */\nfunction nodeLength(node) {\n  if (isText(node)) {\n    return node.nodeValue.length;\n  }\n\n  if (node) {\n    return node.childNodes.length;\n  }\n\n  return 0;\n}\n\n/**\n * returns whether node is empty or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEmpty(node) {\n  const len = nodeLength(node);\n\n  if (len === 0) {\n    return true;\n  } else if (!isText(node) && len === 1 && node.innerHTML === blankHTML) {\n    // ex) <p><br></p>, <span><br></span>\n    return true;\n  } else if (lists.all(node.childNodes, isText) && node.innerHTML === '') {\n    // ex) <p></p>, <span></span>\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * padding blankHTML if node is empty (for cursor position)\n */\nfunction paddingBlankHTML(node) {\n  if (!isVoid(node) && !nodeLength(node)) {\n    node.innerHTML = blankHTML;\n  }\n}\n\n/**\n * find nearest ancestor predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction ancestor(node, pred) {\n  while (node) {\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * find nearest ancestor only single child blood line and predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction singleChildAncestor(node, pred) {\n  node = node.parentNode;\n\n  while (node) {\n    if (nodeLength(node) !== 1) { break; }\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * returns new array of ancestor nodes (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listAncestor(node, pred) {\n  pred = pred || func.fail;\n\n  const ancestors = [];\n  ancestor(node, function(el) {\n    if (!isEditable(el)) {\n      ancestors.push(el);\n    }\n\n    return pred(el);\n  });\n  return ancestors;\n}\n\n/**\n * find farthest ancestor predicate hit\n */\nfunction lastAncestor(node, pred) {\n  const ancestors = listAncestor(node);\n  return lists.last(ancestors.filter(pred));\n}\n\n/**\n * returns common ancestor node between two nodes.\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n */\nfunction commonAncestor(nodeA, nodeB) {\n  const ancestors = listAncestor(nodeA);\n  for (let n = nodeB; n; n = n.parentNode) {\n    if ($.inArray(n, ancestors) > -1) { return n; }\n  }\n  return null; // difference document area\n}\n\n/**\n * listing all previous siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listPrev(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.previousSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing next siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listNext(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.nextSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing descendant nodes\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listDescendant(node, pred) {\n  const descendants = [];\n  pred = pred || func.ok;\n\n  // start DFS(depth first search) with node\n  (function fnWalk(current) {\n    if (node !== current && pred(current)) {\n      descendants.push(current);\n    }\n    for (let idx = 0, len = current.childNodes.length; idx < len; idx++) {\n      fnWalk(current.childNodes[idx]);\n    }\n  })(node);\n\n  return descendants;\n}\n\n/**\n * wrap node with new tag.\n *\n * @param {Node} node\n * @param {Node} tagName of wrapper\n * @return {Node} - wrapper\n */\nfunction wrap(node, wrapperName) {\n  const parent = node.parentNode;\n  const wrapper = $('<' + wrapperName + '>')[0];\n\n  parent.insertBefore(wrapper, node);\n  wrapper.appendChild(node);\n\n  return wrapper;\n}\n\n/**\n * insert node after preceding\n *\n * @param {Node} node\n * @param {Node} preceding - predicate function\n */\nfunction insertAfter(node, preceding) {\n  const next = preceding.nextSibling;\n  let parent = preceding.parentNode;\n  if (next) {\n    parent.insertBefore(node, next);\n  } else {\n    parent.appendChild(node);\n  }\n  return node;\n}\n\n/**\n * append elements.\n *\n * @param {Node} node\n * @param {Collection} aChild\n */\nfunction appendChildNodes(node, aChild) {\n  $.each(aChild, function(idx, child) {\n    node.appendChild(child);\n  });\n  return node;\n}\n\n/**\n * returns whether boundaryPoint is left edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isLeftEdgePoint(point) {\n  return point.offset === 0;\n}\n\n/**\n * returns whether boundaryPoint is right edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isRightEdgePoint(point) {\n  return point.offset === nodeLength(point.node);\n}\n\n/**\n * returns whether boundaryPoint is edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isEdgePoint(point) {\n  return isLeftEdgePoint(point) || isRightEdgePoint(point);\n}\n\n/**\n * returns whether node is left edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgeOf(node, ancestor) {\n  while (node && node !== ancestor) {\n    if (position(node) !== 0) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether node is right edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgeOf(node, ancestor) {\n  if (!ancestor) {\n    return false;\n  }\n  while (node && node !== ancestor) {\n    if (position(node) !== nodeLength(node.parentNode) - 1) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether point is left edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgePointOf(point, ancestor) {\n  return isLeftEdgePoint(point) && isLeftEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns whether point is right edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgePointOf(point, ancestor) {\n  return isRightEdgePoint(point) && isRightEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns offset from parent.\n *\n * @param {Node} node\n */\nfunction position(node) {\n  let offset = 0;\n  while ((node = node.previousSibling)) {\n    offset += 1;\n  }\n  return offset;\n}\n\nfunction hasChildren(node) {\n  return !!(node && node.childNodes && node.childNodes.length);\n}\n\n/**\n * returns previous boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction prevPoint(point, isSkipInnerOffset) {\n  let node;\n  let offset;\n\n  if (point.offset === 0) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    node = point.node.parentNode;\n    offset = position(point.node);\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset - 1];\n    offset = nodeLength(node);\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? 0 : point.offset - 1;\n  }\n\n  return {\n    node: node,\n    offset: offset\n  };\n}\n\n/**\n * returns next boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction nextPoint(point, isSkipInnerOffset) {\n  let node, offset;\n\n  if (nodeLength(point.node) === point.offset) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    node = point.node.parentNode;\n    offset = position(point.node) + 1;\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset];\n    offset = 0;\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? nodeLength(point.node) : point.offset + 1;\n  }\n\n  return {\n    node: node,\n    offset: offset\n  };\n}\n\n/**\n * returns whether pointA and pointB is same or not.\n *\n * @param {BoundaryPoint} pointA\n * @param {BoundaryPoint} pointB\n * @return {Boolean}\n */\nfunction isSamePoint(pointA, pointB) {\n  return pointA.node === pointB.node && pointA.offset === pointB.offset;\n}\n\n/**\n * returns whether point is visible (can set cursor) or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isVisiblePoint(point) {\n  if (isText(point.node) || !hasChildren(point.node) || isEmpty(point.node)) {\n    return true;\n  }\n\n  const leftNode = point.node.childNodes[point.offset - 1];\n  const rightNode = point.node.childNodes[point.offset];\n  if ((!leftNode || isVoid(leftNode)) && (!rightNode || isVoid(rightNode))) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * @method prevPointUtil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction prevPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = prevPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * @method nextPointUntil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction nextPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = nextPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * returns whether point has character or not.\n *\n * @param {Point} point\n * @return {Boolean}\n */\nfunction isCharPoint(point) {\n  if (!isText(point.node)) {\n    return false;\n  }\n\n  const ch = point.node.nodeValue.charAt(point.offset - 1);\n  return ch && (ch !== ' ' && ch !== NBSP_CHAR);\n}\n\n/**\n * @method walkPoint\n *\n * @param {BoundaryPoint} startPoint\n * @param {BoundaryPoint} endPoint\n * @param {Function} handler\n * @param {Boolean} isSkipInnerOffset\n */\nfunction walkPoint(startPoint, endPoint, handler, isSkipInnerOffset) {\n  let point = startPoint;\n\n  while (point) {\n    handler(point);\n\n    if (isSamePoint(point, endPoint)) {\n      break;\n    }\n\n    const isSkipOffset = isSkipInnerOffset &&\n                       startPoint.node !== point.node &&\n                       endPoint.node !== point.node;\n    point = nextPoint(point, isSkipOffset);\n  }\n}\n\n/**\n * @method makeOffsetPath\n *\n * return offsetPath(array of offset) from ancestor\n *\n * @param {Node} ancestor - ancestor node\n * @param {Node} node\n */\nfunction makeOffsetPath(ancestor, node) {\n  const ancestors = listAncestor(node, func.eq(ancestor));\n  return ancestors.map(position).reverse();\n}\n\n/**\n * @method fromOffsetPath\n *\n * return element from offsetPath(array of offset)\n *\n * @param {Node} ancestor - ancestor node\n * @param {array} offsets - offsetPath\n */\nfunction fromOffsetPath(ancestor, offsets) {\n  let current = ancestor;\n  for (let i = 0, len = offsets.length; i < len; i++) {\n    if (current.childNodes.length <= offsets[i]) {\n      current = current.childNodes[current.childNodes.length - 1];\n    } else {\n      current = current.childNodes[offsets[i]];\n    }\n  }\n  return current;\n}\n\n/**\n * @method splitNode\n *\n * split element or #text\n *\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitNode(point, options) {\n  const isSkipPaddingBlankHTML = options && options.isSkipPaddingBlankHTML;\n  const isNotSplitEdgePoint = options && options.isNotSplitEdgePoint;\n\n  // edge case\n  if (isEdgePoint(point) && (isText(point.node) || isNotSplitEdgePoint)) {\n    if (isLeftEdgePoint(point)) {\n      return point.node;\n    } else if (isRightEdgePoint(point)) {\n      return point.node.nextSibling;\n    }\n  }\n\n  // split #text\n  if (isText(point.node)) {\n    return point.node.splitText(point.offset);\n  } else {\n    const childNode = point.node.childNodes[point.offset];\n    const clone = insertAfter(point.node.cloneNode(false), point.node);\n    appendChildNodes(clone, listNext(childNode));\n\n    if (!isSkipPaddingBlankHTML) {\n      paddingBlankHTML(point.node);\n      paddingBlankHTML(clone);\n    }\n\n    return clone;\n  }\n}\n\n/**\n * @method splitTree\n *\n * split tree by point\n *\n * @param {Node} root - split root\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitTree(root, point, options) {\n  // ex) [#text, <span>, <p>]\n  const ancestors = listAncestor(point.node, func.eq(root));\n\n  if (!ancestors.length) {\n    return null;\n  } else if (ancestors.length === 1) {\n    return splitNode(point, options);\n  }\n\n  return ancestors.reduce(function(node, parent) {\n    if (node === point.node) {\n      node = splitNode(point, options);\n    }\n\n    return splitNode({\n      node: parent,\n      offset: node ? position(node) : nodeLength(parent)\n    }, options);\n  });\n}\n\n/**\n * split point\n *\n * @param {Point} point\n * @param {Boolean} isInline\n * @return {Object}\n */\nfunction splitPoint(point, isInline) {\n  // find splitRoot, container\n  //  - inline: splitRoot is a child of paragraph\n  //  - block: splitRoot is a child of bodyContainer\n  const pred = isInline ? isPara : isBodyContainer;\n  const ancestors = listAncestor(point.node, pred);\n  const topAncestor = lists.last(ancestors) || point.node;\n\n  let splitRoot, container;\n  if (pred(topAncestor)) {\n    splitRoot = ancestors[ancestors.length - 2];\n    container = topAncestor;\n  } else {\n    splitRoot = topAncestor;\n    container = splitRoot.parentNode;\n  }\n\n  // if splitRoot is exists, split with splitTree\n  let pivot = splitRoot && splitTree(splitRoot, point, {\n    isSkipPaddingBlankHTML: isInline,\n    isNotSplitEdgePoint: isInline\n  });\n\n  // if container is point.node, find pivot with point.offset\n  if (!pivot && container === point.node) {\n    pivot = point.node.childNodes[point.offset];\n  }\n\n  return {\n    rightNode: pivot,\n    container: container\n  };\n}\n\nfunction create(nodeName) {\n  return document.createElement(nodeName);\n}\n\nfunction createText(text) {\n  return document.createTextNode(text);\n}\n\n/**\n * @method remove\n *\n * remove node, (isRemoveChild: remove child or not)\n *\n * @param {Node} node\n * @param {Boolean} isRemoveChild\n */\nfunction remove(node, isRemoveChild) {\n  if (!node || !node.parentNode) { return; }\n  if (node.removeNode) { return node.removeNode(isRemoveChild); }\n\n  const parent = node.parentNode;\n  if (!isRemoveChild) {\n    const nodes = [];\n    for (let i = 0, len = node.childNodes.length; i < len; i++) {\n      nodes.push(node.childNodes[i]);\n    }\n\n    for (let i = 0, len = nodes.length; i < len; i++) {\n      parent.insertBefore(nodes[i], node);\n    }\n  }\n\n  parent.removeChild(node);\n}\n\n/**\n * @method removeWhile\n *\n * @param {Node} node\n * @param {Function} pred\n */\nfunction removeWhile(node, pred) {\n  while (node) {\n    if (isEditable(node) || !pred(node)) {\n      break;\n    }\n\n    const parent = node.parentNode;\n    remove(node);\n    node = parent;\n  }\n}\n\n/**\n * @method replace\n *\n * replace node with provided nodeName\n *\n * @param {Node} node\n * @param {String} nodeName\n * @return {Node} - new node\n */\nfunction replace(node, nodeName) {\n  if (node.nodeName.toUpperCase() === nodeName.toUpperCase()) {\n    return node;\n  }\n\n  const newNode = create(nodeName);\n\n  if (node.style.cssText) {\n    newNode.style.cssText = node.style.cssText;\n  }\n\n  appendChildNodes(newNode, lists.from(node.childNodes));\n  insertAfter(newNode, node);\n  remove(node);\n\n  return newNode;\n}\n\nconst isTextarea = makePredByNodeName('TEXTAREA');\n\n/**\n * @param {jQuery} $node\n * @param {Boolean} [stripLinebreaks] - default: false\n */\nfunction value($node, stripLinebreaks) {\n  const val = isTextarea($node[0]) ? $node.val() : $node.html();\n  if (stripLinebreaks) {\n    return val.replace(/[\\n\\r]/g, '');\n  }\n  return val;\n}\n\n/**\n * @method html\n *\n * get the HTML contents of node\n *\n * @param {jQuery} $node\n * @param {Boolean} [isNewlineOnBlock]\n */\nfunction html($node, isNewlineOnBlock) {\n  let markup = value($node);\n\n  if (isNewlineOnBlock) {\n    const regexTag = /<(\\/?)(\\b(?!!)[^>\\s]*)(.*?)(\\s*\\/?>)/g;\n    markup = markup.replace(regexTag, function(match, endSlash, name) {\n      name = name.toUpperCase();\n      const isEndOfInlineContainer = /^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(name) &&\n                                   !!endSlash;\n      const isBlockNode = /^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(name);\n\n      return match + ((isEndOfInlineContainer || isBlockNode) ? '\\n' : '');\n    });\n    markup = $.trim(markup);\n  }\n\n  return markup;\n}\n\nfunction posFromPlaceholder(placeholder) {\n  const $placeholder = $(placeholder);\n  const pos = $placeholder.offset();\n  const height = $placeholder.outerHeight(true); // include margin\n\n  return {\n    left: pos.left,\n    top: pos.top + height\n  };\n}\n\nfunction attachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.on(key, events[key]);\n  });\n}\n\nfunction detachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.off(key, events[key]);\n  });\n}\n\n/**\n * @method isCustomStyleTag\n *\n * assert if a node contains a \"note-styletag\" class,\n * which implies that's a custom-made style tag node\n *\n * @param {Node} an HTML DOM node\n */\nfunction isCustomStyleTag(node) {\n  return node && !isText(node) && lists.contains(node.classList, 'note-styletag');\n}\n\nexport default {\n  /** @property {String} NBSP_CHAR */\n  NBSP_CHAR,\n  /** @property {String} ZERO_WIDTH_NBSP_CHAR */\n  ZERO_WIDTH_NBSP_CHAR,\n  /** @property {String} blank */\n  blank: blankHTML,\n  /** @property {String} emptyPara */\n  emptyPara: `<p>${blankHTML}</p>`,\n  makePredByNodeName,\n  isEditable,\n  isControlSizing,\n  isText,\n  isElement,\n  isVoid,\n  isPara,\n  isPurePara,\n  isHeading,\n  isInline,\n  isBlock: func.not(isInline),\n  isBodyInline,\n  isBody,\n  isParaInline,\n  isPre,\n  isList,\n  isTable,\n  isData,\n  isCell,\n  isBlockquote,\n  isBodyContainer,\n  isAnchor,\n  isDiv: makePredByNodeName('DIV'),\n  isLi,\n  isBR: makePredByNodeName('BR'),\n  isSpan: makePredByNodeName('SPAN'),\n  isB: makePredByNodeName('B'),\n  isU: makePredByNodeName('U'),\n  isS: makePredByNodeName('S'),\n  isI: makePredByNodeName('I'),\n  isImg: makePredByNodeName('IMG'),\n  isTextarea,\n  isEmpty,\n  isEmptyAnchor: func.and(isAnchor, isEmpty),\n  isClosestSibling,\n  withClosestSiblings,\n  nodeLength,\n  isLeftEdgePoint,\n  isRightEdgePoint,\n  isEdgePoint,\n  isLeftEdgeOf,\n  isRightEdgeOf,\n  isLeftEdgePointOf,\n  isRightEdgePointOf,\n  prevPoint,\n  nextPoint,\n  isSamePoint,\n  isVisiblePoint,\n  prevPointUntil,\n  nextPointUntil,\n  isCharPoint,\n  walkPoint,\n  ancestor,\n  singleChildAncestor,\n  listAncestor,\n  lastAncestor,\n  listNext,\n  listPrev,\n  listDescendant,\n  commonAncestor,\n  wrap,\n  insertAfter,\n  appendChildNodes,\n  position,\n  hasChildren,\n  makeOffsetPath,\n  fromOffsetPath,\n  splitTree,\n  splitPoint,\n  create,\n  createText,\n  remove,\n  removeWhile,\n  replace,\n  html,\n  value,\n  posFromPlaceholder,\n  attachEvents,\n  detachEvents,\n  isCustomStyleTag\n};\n", "import $ from 'jquery';\n\n$.summernote = $.summernote || {\n  lang: {}\n};\n\n$.extend($.summernote.lang, {\n  'en-US': {\n    font: {\n      bold: 'Bold',\n      italic: 'Italic',\n      underline: 'Underline',\n      clear: 'Remove Font Style',\n      height: 'Line Height',\n      name: 'Font Family',\n      strikethrough: 'Strikethrough',\n      subscript: 'Subscript',\n      superscript: 'Superscript',\n      size: 'Font Size'\n    },\n    image: {\n      image: 'Picture',\n      insert: 'Insert Image',\n      resizeFull: 'Resize Full',\n      resizeHalf: 'Resize Half',\n      resizeQuarter: 'Resize Quarter',\n      floatLeft: 'Float Left',\n      floatRight: 'Float Right',\n      floatNone: 'Float None',\n      shapeRounded: 'Shape: Rounded',\n      shapeCircle: 'Shape: Circle',\n      shapeThumbnail: 'Shape: Thumbnail',\n      shapeNone: 'Shape: None',\n      dragImageHere: 'Drag image or text here',\n      dropImage: 'Drop image or Text',\n      selectFromFiles: 'Select from files',\n      maximumFileSize: 'Maximum file size',\n      maximumFileSizeError: 'Maximum file size exceeded.',\n      url: 'Image URL',\n      remove: 'Remove Image',\n      original: 'Original'\n    },\n    video: {\n      video: 'Video',\n      videoLink: 'Video Link',\n      insert: 'Insert Video',\n      url: 'Video URL',\n      providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)'\n    },\n    link: {\n      link: 'Link',\n      insert: 'Insert Link',\n      unlink: 'Unlink',\n      edit: 'Edit',\n      textToDisplay: 'Text to display',\n      url: 'To what URL should this link go?',\n      openInNewWindow: 'Open in new window'\n    },\n    table: {\n      table: 'Table',\n      addRowAbove: 'Add row above',\n      addRowBelow: 'Add row below',\n      addColLeft: 'Add column left',\n      addColRight: 'Add column right',\n      delRow: 'Delete row',\n      delCol: 'Delete column',\n      delTable: 'Delete table'\n    },\n    hr: {\n      insert: 'Insert Horizontal Rule'\n    },\n    style: {\n      style: 'Style',\n      p: 'Normal',\n      blockquote: 'Quote',\n      pre: 'Code',\n      h1: 'Header 1',\n      h2: 'Header 2',\n      h3: 'Header 3',\n      h4: 'Header 4',\n      h5: 'Header 5',\n      h6: 'Header 6'\n    },\n    lists: {\n      unordered: 'Unordered list',\n      ordered: 'Ordered list'\n    },\n    options: {\n      help: 'Help',\n      fullscreen: 'Full Screen',\n      codeview: 'Code View'\n    },\n    paragraph: {\n      paragraph: 'Paragraph',\n      outdent: 'Outdent',\n      indent: 'Indent',\n      left: 'Align left',\n      center: 'Align center',\n      right: 'Align right',\n      justify: 'Justify full'\n    },\n    color: {\n      recent: 'Recent Color',\n      more: 'More Color',\n      background: 'Background Color',\n      foreground: 'Foreground Color',\n      transparent: 'Transparent',\n      setTransparent: 'Set transparent',\n      reset: 'Reset',\n      resetToDefault: 'Reset to default'\n    },\n    shortcut: {\n      shortcuts: 'Keyboard shortcuts',\n      close: 'Close',\n      textFormatting: 'Text formatting',\n      action: 'Action',\n      paragraphFormatting: 'Paragraph formatting',\n      documentStyle: 'Document Style',\n      extraKeys: 'Extra keys'\n    },\n    help: {\n      'insertParagraph': 'Insert Paragraph',\n      'undo': 'Undoes the last command',\n      'redo': 'Redoes the last command',\n      'tab': 'Tab',\n      'untab': 'Untab',\n      'bold': 'Set a bold style',\n      'italic': 'Set a italic style',\n      'underline': 'Set a underline style',\n      'strikethrough': 'Set a strikethrough style',\n      'removeFormat': 'Clean a style',\n      'justifyLeft': 'Set left align',\n      'justifyCenter': 'Set center align',\n      'justifyRight': 'Set right align',\n      'justifyFull': 'Set full align',\n      'insertUnorderedList': 'Toggle unordered list',\n      'insertOrderedList': 'Toggle ordered list',\n      'outdent': 'Outdent on current paragraph',\n      'indent': 'Indent on current paragraph',\n      'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n      'formatH1': 'Change current block\\'s format as H1',\n      'formatH2': 'Change current block\\'s format as H2',\n      'formatH3': 'Change current block\\'s format as H3',\n      'formatH4': 'Change current block\\'s format as H4',\n      'formatH5': 'Change current block\\'s format as H5',\n      'formatH6': 'Change current block\\'s format as H6',\n      'insertHorizontalRule': 'Insert horizontal rule',\n      'linkDialog.show': 'Show Link Dialog'\n    },\n    history: {\n      undo: 'Undo',\n      redo: 'Redo'\n    },\n    specialChar: {\n      specialChar: 'SPECIAL CHARACTERS',\n      select: 'Select Special characters'\n    }\n  }\n});\n", "import lists from './lists';\nimport func from './func';\n\nconst KEY_MAP = {\n  'BACKSPACE': 8,\n  'TAB': 9,\n  'ENTER': 13,\n  'SPACE': 32,\n  'DELETE': 46,\n\n  // Arrow\n  'LEFT': 37,\n  'UP': 38,\n  'RIGHT': 39,\n  'DOWN': 40,\n\n  // Number: 0-9\n  'NUM0': 48,\n  'NUM1': 49,\n  'NUM2': 50,\n  'NUM3': 51,\n  'NUM4': 52,\n  'NUM5': 53,\n  'NUM6': 54,\n  'NUM7': 55,\n  'NUM8': 56,\n\n  // Alphabet: a-z\n  'B': 66,\n  'E': 69,\n  'I': 73,\n  'J': 74,\n  'K': 75,\n  'L': 76,\n  'R': 82,\n  'S': 83,\n  'U': 85,\n  'V': 86,\n  'Y': 89,\n  'Z': 90,\n\n  'SLASH': 191,\n  'LEFTBRACKET': 219,\n  'BACKSLASH': 220,\n  'RIGHTBRACKET': 221\n};\n\n/**\n * @class core.key\n *\n * Object for keycodes.\n *\n * @singleton\n * @alternateClassName key\n */\nexport default {\n  /**\n   * @method isEdit\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isEdit: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.BACKSPACE,\n      KEY_MAP.TAB,\n      KEY_MAP.ENTER,\n      KEY_MAP.SPACE,\n      KEY_MAP.DELETE\n    ], keyCode);\n  },\n  /**\n   * @method isMove\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isMove: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.LEFT,\n      KEY_MAP.UP,\n      KEY_MAP.RIGHT,\n      KEY_MAP.DOWN\n    ], keyCode);\n  },\n  /**\n   * @property {Object} nameFromCode\n   * @property {String} nameFromCode.8 \"BACKSPACE\"\n   */\n  nameFromCode: func.invertObject(KEY_MAP),\n  code: KEY_MAP\n};\n", "import $ from 'jquery';\nimport env from './env';\nimport func from './func';\nimport lists from './lists';\nimport dom from './dom';\n\n/**\n * return boundaryPoint from TextRange, inspired by <PERSON>'s HuskyRange.js\n *\n * @param {TextRange} textRange\n * @param {Boolean} isStart\n * @return {BoundaryPoint}\n *\n * @see http://msdn.microsoft.com/en-us/library/ie/ms535872(v=vs.85).aspx\n */\nfunction textRangeToPoint(textRange, isStart) {\n  let container = textRange.parentElement();\n  let offset;\n\n  const tester = document.body.createTextRange();\n  let prevContainer;\n  const childNodes = lists.from(container.childNodes);\n  for (offset = 0; offset < childNodes.length; offset++) {\n    if (dom.isText(childNodes[offset])) {\n      continue;\n    }\n    tester.moveToElementText(childNodes[offset]);\n    if (tester.compareEndPoints('StartToStart', textRange) >= 0) {\n      break;\n    }\n    prevContainer = childNodes[offset];\n  }\n\n  if (offset !== 0 && dom.isText(childNodes[offset - 1])) {\n    const textRangeStart = document.body.createTextRange();\n    let curTextNode = null;\n    textRangeStart.moveToElementText(prevContainer || container);\n    textRangeStart.collapse(!prevContainer);\n    curTextNode = prevContainer ? prevContainer.nextSibling : container.firstChild;\n\n    const pointTester = textRange.duplicate();\n    pointTester.setEndPoint('StartToStart', textRangeStart);\n    let textCount = pointTester.text.replace(/[\\r\\n]/g, '').length;\n\n    while (textCount > curTextNode.nodeValue.length && curTextNode.nextSibling) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    // [workaround] enforce IE to re-reference curTextNode, hack\n    const dummy = curTextNode.nodeValue; // eslint-disable-line\n\n    if (isStart && curTextNode.nextSibling && dom.isText(curTextNode.nextSibling) &&\n      textCount === curTextNode.nodeValue.length) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    container = curTextNode;\n    offset = textCount;\n  }\n\n  return {\n    cont: container,\n    offset: offset\n  };\n}\n\n/**\n * return TextRange from boundary point (inspired by google closure-library)\n * @param {BoundaryPoint} point\n * @return {TextRange}\n */\nfunction pointToTextRange(point) {\n  const textRangeInfo = function(container, offset) {\n    let node, isCollapseToStart;\n\n    if (dom.isText(container)) {\n      const prevTextNodes = dom.listPrev(container, func.not(dom.isText));\n      const prevContainer = lists.last(prevTextNodes).previousSibling;\n      node = prevContainer || container.parentNode;\n      offset += lists.sum(lists.tail(prevTextNodes), dom.nodeLength);\n      isCollapseToStart = !prevContainer;\n    } else {\n      node = container.childNodes[offset] || container;\n      if (dom.isText(node)) {\n        return textRangeInfo(node, 0);\n      }\n\n      offset = 0;\n      isCollapseToStart = false;\n    }\n\n    return {\n      node: node,\n      collapseToStart: isCollapseToStart,\n      offset: offset\n    };\n  };\n\n  const textRange = document.body.createTextRange();\n  const info = textRangeInfo(point.node, point.offset);\n\n  textRange.moveToElementText(info.node);\n  textRange.collapse(info.collapseToStart);\n  textRange.moveStart('character', info.offset);\n  return textRange;\n}\n\n/**\n   * Wrapped Range\n   *\n   * @constructor\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   */\nclass WrappedRange {\n  constructor(sc, so, ec, eo) {\n    this.sc = sc;\n    this.so = so;\n    this.ec = ec;\n    this.eo = eo;\n\n    // isOnEditable: judge whether range is on editable or not\n    this.isOnEditable = this.makeIsOn(dom.isEditable);\n    // isOnList: judge whether range is on list node or not\n    this.isOnList = this.makeIsOn(dom.isList);\n    // isOnAnchor: judge whether range is on anchor node or not\n    this.isOnAnchor = this.makeIsOn(dom.isAnchor);\n    // isOnCell: judge whether range is on cell node or not\n    this.isOnCell = this.makeIsOn(dom.isCell);\n    // isOnData: judge whether range is on data node or not\n    this.isOnData = this.makeIsOn(dom.isData);\n  }\n\n  // nativeRange: get nativeRange from sc, so, ec, eo\n  nativeRange() {\n    if (env.isW3CRangeSupport) {\n      const w3cRange = document.createRange();\n      w3cRange.setStart(this.sc, this.so);\n      w3cRange.setEnd(this.ec, this.eo);\n\n      return w3cRange;\n    } else {\n      const textRange = pointToTextRange({\n        node: this.sc,\n        offset: this.so\n      });\n\n      textRange.setEndPoint('EndToEnd', pointToTextRange({\n        node: this.ec,\n        offset: this.eo\n      }));\n\n      return textRange;\n    }\n  }\n\n  getPoints() {\n    return {\n      sc: this.sc,\n      so: this.so,\n      ec: this.ec,\n      eo: this.eo\n    };\n  }\n\n  getStartPoint() {\n    return {\n      node: this.sc,\n      offset: this.so\n    };\n  }\n\n  getEndPoint() {\n    return {\n      node: this.ec,\n      offset: this.eo\n    };\n  }\n\n  /**\n   * select update visible range\n   */\n  select() {\n    const nativeRng = this.nativeRange();\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (selection.rangeCount > 0) {\n        selection.removeAllRanges();\n      }\n      selection.addRange(nativeRng);\n    } else {\n      nativeRng.select();\n    }\n\n    return this;\n  }\n\n  /**\n   * Moves the scrollbar to start container(sc) of current range\n   *\n   * @return {WrappedRange}\n   */\n  scrollIntoView(container) {\n    const height = $(container).height();\n    if (container.scrollTop + height < this.sc.offsetTop) {\n      container.scrollTop += Math.abs(container.scrollTop + height - this.sc.offsetTop);\n    }\n\n    return this;\n  }\n\n  /**\n   * @return {WrappedRange}\n   */\n  normalize() {\n    /**\n     * @param {BoundaryPoint} point\n     * @param {Boolean} isLeftToRight\n     * @return {BoundaryPoint}\n     */\n    const getVisiblePoint = function(point, isLeftToRight) {\n      if ((dom.isVisiblePoint(point) && !dom.isEdgePoint(point)) ||\n          (dom.isVisiblePoint(point) && dom.isRightEdgePoint(point) && !isLeftToRight) ||\n          (dom.isVisiblePoint(point) && dom.isLeftEdgePoint(point) && isLeftToRight) ||\n          (dom.isVisiblePoint(point) && dom.isBlock(point.node) && dom.isEmpty(point.node))) {\n        return point;\n      }\n\n      // point on block's edge\n      const block = dom.ancestor(point.node, dom.isBlock);\n      if (((dom.isLeftEdgePointOf(point, block) || dom.isVoid(dom.prevPoint(point).node)) && !isLeftToRight) ||\n          ((dom.isRightEdgePointOf(point, block) || dom.isVoid(dom.nextPoint(point).node)) && isLeftToRight)) {\n        // returns point already on visible point\n        if (dom.isVisiblePoint(point)) {\n          return point;\n        }\n        // reverse direction\n        isLeftToRight = !isLeftToRight;\n      }\n\n      const nextPoint = isLeftToRight ? dom.nextPointUntil(dom.nextPoint(point), dom.isVisiblePoint)\n        : dom.prevPointUntil(dom.prevPoint(point), dom.isVisiblePoint);\n      return nextPoint || point;\n    };\n\n    const endPoint = getVisiblePoint(this.getEndPoint(), false);\n    const startPoint = this.isCollapsed() ? endPoint : getVisiblePoint(this.getStartPoint(), true);\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * returns matched nodes on range\n   *\n   * @param {Function} [pred] - predicate function\n   * @param {Object} [options]\n   * @param {Boolean} [options.includeAncestor]\n   * @param {Boolean} [options.fullyContains]\n   * @return {Node[]}\n   */\n  nodes(pred, options) {\n    pred = pred || func.ok;\n\n    const includeAncestor = options && options.includeAncestor;\n    const fullyContains = options && options.fullyContains;\n\n    // TODO compare points and sort\n    const startPoint = this.getStartPoint();\n    const endPoint = this.getEndPoint();\n\n    const nodes = [];\n    const leftEdgeNodes = [];\n\n    dom.walkPoint(startPoint, endPoint, function(point) {\n      if (dom.isEditable(point.node)) {\n        return;\n      }\n\n      let node;\n      if (fullyContains) {\n        if (dom.isLeftEdgePoint(point)) {\n          leftEdgeNodes.push(point.node);\n        }\n        if (dom.isRightEdgePoint(point) && lists.contains(leftEdgeNodes, point.node)) {\n          node = point.node;\n        }\n      } else if (includeAncestor) {\n        node = dom.ancestor(point.node, pred);\n      } else {\n        node = point.node;\n      }\n\n      if (node && pred(node)) {\n        nodes.push(node);\n      }\n    }, true);\n\n    return lists.unique(nodes);\n  }\n\n  /**\n   * returns commonAncestor of range\n   * @return {Element} - commonAncestor\n   */\n  commonAncestor() {\n    return dom.commonAncestor(this.sc, this.ec);\n  }\n\n  /**\n   * returns expanded range by pred\n   *\n   * @param {Function} pred - predicate function\n   * @return {WrappedRange}\n   */\n  expand(pred) {\n    const startAncestor = dom.ancestor(this.sc, pred);\n    const endAncestor = dom.ancestor(this.ec, pred);\n\n    if (!startAncestor && !endAncestor) {\n      return new WrappedRange(this.sc, this.so, this.ec, this.eo);\n    }\n\n    const boundaryPoints = this.getPoints();\n\n    if (startAncestor) {\n      boundaryPoints.sc = startAncestor;\n      boundaryPoints.so = 0;\n    }\n\n    if (endAncestor) {\n      boundaryPoints.ec = endAncestor;\n      boundaryPoints.eo = dom.nodeLength(endAncestor);\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * @param {Boolean} isCollapseToStart\n   * @return {WrappedRange}\n   */\n  collapse(isCollapseToStart) {\n    if (isCollapseToStart) {\n      return new WrappedRange(this.sc, this.so, this.sc, this.so);\n    } else {\n      return new WrappedRange(this.ec, this.eo, this.ec, this.eo);\n    }\n  }\n\n  /**\n   * splitText on range\n   */\n  splitText() {\n    const isSameContainer = this.sc === this.ec;\n    const boundaryPoints = this.getPoints();\n\n    if (dom.isText(this.ec) && !dom.isEdgePoint(this.getEndPoint())) {\n      this.ec.splitText(this.eo);\n    }\n\n    if (dom.isText(this.sc) && !dom.isEdgePoint(this.getStartPoint())) {\n      boundaryPoints.sc = this.sc.splitText(this.so);\n      boundaryPoints.so = 0;\n\n      if (isSameContainer) {\n        boundaryPoints.ec = boundaryPoints.sc;\n        boundaryPoints.eo = this.eo - this.so;\n      }\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * delete contents on range\n   * @return {WrappedRange}\n   */\n  deleteContents() {\n    if (this.isCollapsed()) {\n      return this;\n    }\n\n    const rng = this.splitText();\n    const nodes = rng.nodes(null, {\n      fullyContains: true\n    });\n\n    // find new cursor point\n    const point = dom.prevPointUntil(rng.getStartPoint(), function(point) {\n      return !lists.contains(nodes, point.node);\n    });\n\n    const emptyParents = [];\n    $.each(nodes, function(idx, node) {\n      // find empty parents\n      const parent = node.parentNode;\n      if (point.node !== parent && dom.nodeLength(parent) === 1) {\n        emptyParents.push(parent);\n      }\n      dom.remove(node, false);\n    });\n\n    // remove empty parents\n    $.each(emptyParents, function(idx, node) {\n      dom.remove(node, false);\n    });\n\n    return new WrappedRange(\n      point.node,\n      point.offset,\n      point.node,\n      point.offset\n    ).normalize();\n  }\n\n  /**\n   * makeIsOn: return isOn(pred) function\n   */\n  makeIsOn(pred) {\n    return function() {\n      const ancestor = dom.ancestor(this.sc, pred);\n      return !!ancestor && (ancestor === dom.ancestor(this.ec, pred));\n    };\n  }\n\n  /**\n   * @param {Function} pred\n   * @return {Boolean}\n   */\n  isLeftEdgeOf(pred) {\n    if (!dom.isLeftEdgePoint(this.getStartPoint())) {\n      return false;\n    }\n\n    const node = dom.ancestor(this.sc, pred);\n    return node && dom.isLeftEdgeOf(this.sc, node);\n  }\n\n  /**\n   * returns whether range was collapsed or not\n   */\n  isCollapsed() {\n    return this.sc === this.ec && this.so === this.eo;\n  }\n\n  /**\n   * wrap inline nodes which children of body with paragraph\n   *\n   * @return {WrappedRange}\n   */\n  wrapBodyInlineWithPara() {\n    if (dom.isBodyContainer(this.sc) && dom.isEmpty(this.sc)) {\n      this.sc.innerHTML = dom.emptyPara;\n      return new WrappedRange(this.sc.firstChild, 0, this.sc.firstChild, 0);\n    }\n\n    /**\n     * [workaround] firefox often create range on not visible point. so normalize here.\n     *  - firefox: |<p>text</p>|\n     *  - chrome: <p>|text|</p>\n     */\n    const rng = this.normalize();\n    if (dom.isParaInline(this.sc) || dom.isPara(this.sc)) {\n      return rng;\n    }\n\n    // find inline top ancestor\n    let topAncestor;\n    if (dom.isInline(rng.sc)) {\n      const ancestors = dom.listAncestor(rng.sc, func.not(dom.isInline));\n      topAncestor = lists.last(ancestors);\n      if (!dom.isInline(topAncestor)) {\n        topAncestor = ancestors[ancestors.length - 2] || rng.sc.childNodes[rng.so];\n      }\n    } else {\n      topAncestor = rng.sc.childNodes[rng.so > 0 ? rng.so - 1 : 0];\n    }\n\n    // siblings not in paragraph\n    let inlineSiblings = dom.listPrev(topAncestor, dom.isParaInline).reverse();\n    inlineSiblings = inlineSiblings.concat(dom.listNext(topAncestor.nextSibling, dom.isParaInline));\n\n    // wrap with paragraph\n    if (inlineSiblings.length) {\n      const para = dom.wrap(lists.head(inlineSiblings), 'p');\n      dom.appendChildNodes(para, lists.tail(inlineSiblings));\n    }\n\n    return this.normalize();\n  }\n\n  /**\n   * insert node at current cursor\n   *\n   * @param {Node} node\n   * @return {Node}\n   */\n  insertNode(node) {\n    const rng = this.wrapBodyInlineWithPara().deleteContents();\n    const info = dom.splitPoint(rng.getStartPoint(), dom.isInline(node));\n\n    if (info.rightNode) {\n      info.rightNode.parentNode.insertBefore(node, info.rightNode);\n    } else {\n      info.container.appendChild(node);\n    }\n\n    return node;\n  }\n\n  /**\n   * insert html at current cursor\n   */\n  pasteHTML(markup) {\n    const contentsContainer = $('<div></div>').html(markup)[0];\n    const childNodes = lists.from(contentsContainer.childNodes);\n\n    const rng = this.wrapBodyInlineWithPara().deleteContents();\n\n    return childNodes.reverse().map(function(childNode) {\n      return rng.insertNode(childNode);\n    }).reverse();\n  }\n\n  /**\n   * returns text in range\n   *\n   * @return {String}\n   */\n  toString() {\n    const nativeRng = this.nativeRange();\n    return env.isW3CRangeSupport ? nativeRng.toString() : nativeRng.text;\n  }\n\n  /**\n   * returns range for word before cursor\n   *\n   * @param {Boolean} [findAfter] - find after cursor, default: false\n   * @return {WrappedRange}\n   */\n  getWordRange(findAfter) {\n    let endPoint = this.getEndPoint();\n\n    if (!dom.isCharPoint(endPoint)) {\n      return this;\n    }\n\n    const startPoint = dom.prevPointUntil(endPoint, function(point) {\n      return !dom.isCharPoint(point);\n    });\n\n    if (findAfter) {\n      endPoint = dom.nextPointUntil(endPoint, function(point) {\n        return !dom.isCharPoint(point);\n      });\n    }\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * create offsetPath bookmark\n   *\n   * @param {Node} editable\n   */\n  bookmark(editable) {\n    return {\n      s: {\n        path: dom.makeOffsetPath(editable, this.sc),\n        offset: this.so\n      },\n      e: {\n        path: dom.makeOffsetPath(editable, this.ec),\n        offset: this.eo\n      }\n    };\n  }\n\n  /**\n   * create offsetPath bookmark base on paragraph\n   *\n   * @param {Node[]} paras\n   */\n  paraBookmark(paras) {\n    return {\n      s: {\n        path: lists.tail(dom.makeOffsetPath(lists.head(paras), this.sc)),\n        offset: this.so\n      },\n      e: {\n        path: lists.tail(dom.makeOffsetPath(lists.last(paras), this.ec)),\n        offset: this.eo\n      }\n    };\n  }\n\n  /**\n   * getClientRects\n   * @return {Rect[]}\n   */\n  getClientRects() {\n    const nativeRng = this.nativeRange();\n    return nativeRng.getClientRects();\n  }\n}\n\n/**\n * Data structure\n *  * BoundaryPoint: a point of dom tree\n *  * BoundaryPoints: two boundaryPoints corresponding to the start and the end of the Range\n *\n * See to http://www.w3.org/TR/DOM-Level-2-Traversal-Range/ranges.html#Level-2-Range-Position\n */\nexport default {\n  /**\n   * create Range Object From arguments or Browser Selection\n   *\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   * @return {WrappedRange}\n   */\n  create: function(sc, so, ec, eo) {\n    if (arguments.length === 4) {\n      return new WrappedRange(sc, so, ec, eo);\n    } else if (arguments.length === 2) { // collapsed\n      ec = sc;\n      eo = so;\n      return new WrappedRange(sc, so, ec, eo);\n    } else {\n      let wrappedRange = this.createFromSelection();\n      if (!wrappedRange && arguments.length === 1) {\n        wrappedRange = this.createFromNode(arguments[0]);\n        return wrappedRange.collapse(dom.emptyPara === arguments[0].innerHTML);\n      }\n      return wrappedRange;\n    }\n  },\n\n  createFromSelection: function() {\n    let sc, so, ec, eo;\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (!selection || selection.rangeCount === 0) {\n        return null;\n      } else if (dom.isBody(selection.anchorNode)) {\n        // Firefox: returns entire body as range on initialization.\n        // We won't never need it.\n        return null;\n      }\n\n      const nativeRng = selection.getRangeAt(0);\n      sc = nativeRng.startContainer;\n      so = nativeRng.startOffset;\n      ec = nativeRng.endContainer;\n      eo = nativeRng.endOffset;\n    } else { // IE8: TextRange\n      const textRange = document.selection.createRange();\n      const textRangeEnd = textRange.duplicate();\n      textRangeEnd.collapse(false);\n      const textRangeStart = textRange;\n      textRangeStart.collapse(true);\n\n      let startPoint = textRangeToPoint(textRangeStart, true);\n      let endPoint = textRangeToPoint(textRangeEnd, false);\n\n      // same visible point case: range was collapsed.\n      if (dom.isText(startPoint.node) && dom.isLeftEdgePoint(startPoint) &&\n          dom.isTextNode(endPoint.node) && dom.isRightEdgePoint(endPoint) &&\n          endPoint.node.nextSibling === startPoint.node) {\n        startPoint = endPoint;\n      }\n\n      sc = startPoint.cont;\n      so = startPoint.offset;\n      ec = endPoint.cont;\n      eo = endPoint.offset;\n    }\n\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from node\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNode: function(node) {\n    let sc = node;\n    let so = 0;\n    let ec = node;\n    let eo = dom.nodeLength(ec);\n\n    // browsers can't target a picture or void node\n    if (dom.isVoid(sc)) {\n      so = dom.listPrev(sc).length - 1;\n      sc = sc.parentNode;\n    }\n    if (dom.isBR(ec)) {\n      eo = dom.listPrev(ec).length - 1;\n      ec = ec.parentNode;\n    } else if (dom.isVoid(ec)) {\n      eo = dom.listPrev(ec).length;\n      ec = ec.parentNode;\n    }\n\n    return this.create(sc, so, ec, eo);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeBefore: function(node) {\n    return this.createFromNode(node).collapse(true);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeAfter: function(node) {\n    return this.createFromNode(node).collapse();\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from bookmark\n   *\n   * @param {Node} editable\n   * @param {Object} bookmark\n   * @return {WrappedRange}\n   */\n  createFromBookmark: function(editable, bookmark) {\n    const sc = dom.fromOffsetPath(editable, bookmark.s.path);\n    const so = bookmark.s.offset;\n    const ec = dom.fromOffsetPath(editable, bookmark.e.path);\n    const eo = bookmark.e.offset;\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from paraBookmark\n   *\n   * @param {Object} bookmark\n   * @param {Node[]} paras\n   * @return {WrappedRange}\n   */\n  createFromParaBookmark: function(bookmark, paras) {\n    const so = bookmark.s.offset;\n    const eo = bookmark.e.offset;\n    const sc = dom.fromOffsetPath(lists.head(paras), bookmark.s.path);\n    const ec = dom.fromOffsetPath(lists.last(paras), bookmark.e.path);\n\n    return new WrappedRange(sc, so, ec, eo);\n  }\n};\n", "import $ from 'jquery';\n\n/**\n * @method readFileAsDataURL\n *\n * read contents of file as representing URL\n *\n * @param {File} file\n * @return {Promise} - then: dataUrl\n */\nexport function readFileAsDataURL(file) {\n  return $.Deferred((deferred) => {\n    $.extend(new FileReader(), {\n      onload: (e) => {\n        const dataURL = e.target.result;\n        deferred.resolve(dataURL);\n      },\n      onerror: (err) => {\n        deferred.reject(err);\n      }\n    }).readAsDataURL(file);\n  }).promise();\n}\n\n/**\n * @method createImage\n *\n * create `<image>` from url string\n *\n * @param {String} url\n * @return {Promise} - then: $image\n */\nexport function createImage(url) {\n  return $.Deferred((deferred) => {\n    const $img = $('<img>');\n\n    $img.one('load', () => {\n      $img.off('error abort');\n      deferred.resolve($img);\n    }).one('error abort', () => {\n      $img.off('load').detach();\n      deferred.reject($img);\n    }).css({\n      display: 'none'\n    }).appendTo(document.body).attr('src', url);\n  }).promise();\n}\n", "import range from '../core/range';\n\nexport default class History {\n  constructor($editable) {\n    this.stack = [];\n    this.stackOffset = -1;\n    this.$editable = $editable;\n    this.editable = $editable[0];\n  }\n\n  makeSnapshot() {\n    const rng = range.create(this.editable);\n    const emptyBookmark = {s: {path: [], offset: 0}, e: {path: [], offset: 0}};\n\n    return {\n      contents: this.$editable.html(),\n      bookmark: (rng ? rng.bookmark(this.editable) : emptyBookmark)\n    };\n  }\n\n  applySnapshot(snapshot) {\n    if (snapshot.contents !== null) {\n      this.$editable.html(snapshot.contents);\n    }\n    if (snapshot.bookmark !== null) {\n      range.createFromBookmark(this.editable, snapshot.bookmark).select();\n    }\n  }\n\n  /**\n  * @method rewind\n  * Rewinds the history stack back to the first snapshot taken.\n  * Leaves the stack intact, so that \"Redo\" can still be used.\n  */\n  rewind() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    // Return to the first available snapshot.\n    this.stackOffset = 0;\n\n    // Apply that snapshot.\n    this.applySnapshot(this.stack[this.stackOffset]);\n  }\n\n  /**\n  * @method reset\n  * Resets the history stack completely; reverting to an empty editor.\n  */\n  reset() {\n    // Clear the stack.\n    this.stack = [];\n\n    // Restore stackOffset to its original value.\n    this.stackOffset = -1;\n\n    // Clear the editable area.\n    this.$editable.html('');\n\n    // Record our first snapshot (of nothing).\n    this.recordUndo();\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    if (this.stackOffset > 0) {\n      this.stackOffset--;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    if (this.stack.length - 1 > this.stackOffset) {\n      this.stackOffset++;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * recorded undo\n   */\n  recordUndo() {\n    this.stackOffset++;\n\n    // Wash out stack after stackOffset\n    if (this.stack.length > this.stackOffset) {\n      this.stack = this.stack.slice(0, this.stackOffset);\n    }\n\n    // Create new snapshot and push it to the end\n    this.stack.push(this.makeSnapshot());\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class Style {\n  /**\n   * @method jQueryCSS\n   *\n   * [workaround] for old jQuery\n   * passing an array of style properties to .css()\n   * will result in an object of property-value pairs.\n   * (compability with version < 1.9)\n   *\n   * @private\n   * @param  {jQuery} $obj\n   * @param  {Array} propertyNames - An array of one or more CSS properties.\n   * @return {Object}\n   */\n  jQueryCSS($obj, propertyNames) {\n    if (env.jqueryVersion < 1.9) {\n      const result = {};\n      $.each(propertyNames, (idx, propertyName) => {\n        result[propertyName] = $obj.css(propertyName);\n      });\n      return result;\n    }\n    return $obj.css(propertyNames);\n  }\n\n  /**\n   * returns style object from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  fromNode($node) {\n    const properties = ['font-family', 'font-size', 'text-align', 'list-style-type', 'line-height'];\n    const styleInfo = this.jQueryCSS($node, properties) || {};\n    styleInfo['font-size'] = parseInt(styleInfo['font-size'], 10);\n    return styleInfo;\n  }\n\n  /**\n   * paragraph level style\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} styleInfo\n   */\n  stylePara(rng, styleInfo) {\n    $.each(rng.nodes(dom.isPara, {\n      includeAncestor: true\n    }), (idx, para) => {\n      $(para).css(styleInfo);\n    });\n  }\n\n  /**\n   * insert and returns styleNodes on range.\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} [options] - options for styleNodes\n   * @param {String} [options.nodeName] - default: `SPAN`\n   * @param {Boolean} [options.expandClosestSibling] - default: `false`\n   * @param {Boolean} [options.onlyPartialContains] - default: `false`\n   * @return {Node[]}\n   */\n  styleNodes(rng, options) {\n    rng = rng.splitText();\n\n    const nodeName = (options && options.nodeName) || 'SPAN';\n    const expandClosestSibling = !!(options && options.expandClosestSibling);\n    const onlyPartialContains = !!(options && options.onlyPartialContains);\n\n    if (rng.isCollapsed()) {\n      return [rng.insertNode(dom.create(nodeName))];\n    }\n\n    let pred = dom.makePredByNodeName(nodeName);\n    const nodes = rng.nodes(dom.isText, {\n      fullyContains: true\n    }).map((text) => {\n      return dom.singleChildAncestor(text, pred) || dom.wrap(text, nodeName);\n    });\n\n    if (expandClosestSibling) {\n      if (onlyPartialContains) {\n        const nodesInRange = rng.nodes();\n        // compose with partial contains predication\n        pred = func.and(pred, (node) => {\n          return lists.contains(nodesInRange, node);\n        });\n      }\n\n      return nodes.map((node) => {\n        const siblings = dom.withClosestSiblings(node, pred);\n        const head = lists.head(siblings);\n        const tails = lists.tail(siblings);\n        $.each(tails, (idx, elem) => {\n          dom.appendChildNodes(head, elem.childNodes);\n          dom.remove(elem);\n        });\n        return lists.head(siblings);\n      });\n    } else {\n      return nodes;\n    }\n  }\n\n  /**\n   * get current style on cursor\n   *\n   * @param {WrappedRange} rng\n   * @return {Object} - object contains style properties.\n   */\n  current(rng) {\n    const $cont = $(!dom.isElement(rng.sc) ? rng.sc.parentNode : rng.sc);\n    let styleInfo = this.fromNode($cont);\n\n    // document.queryCommandState for toggle state\n    // [workaround] prevent Firefox nsresult: \"0x80004005 (NS_ERROR_FAILURE)\"\n    try {\n      styleInfo = $.extend(styleInfo, {\n        'font-bold': document.queryCommandState('bold') ? 'bold' : 'normal',\n        'font-italic': document.queryCommandState('italic') ? 'italic' : 'normal',\n        'font-underline': document.queryCommandState('underline') ? 'underline' : 'normal',\n        'font-subscript': document.queryCommandState('subscript') ? 'subscript' : 'normal',\n        'font-superscript': document.queryCommandState('superscript') ? 'superscript' : 'normal',\n        'font-strikethrough': document.queryCommandState('strikethrough') ? 'strikethrough' : 'normal',\n        'font-family': document.queryCommandValue('fontname') || styleInfo['font-family']\n      });\n    } catch (e) {}\n\n    // list-style-type to list-style(unordered, ordered)\n    if (!rng.isOnList()) {\n      styleInfo['list-style'] = 'none';\n    } else {\n      const orderedTypes = ['circle', 'disc', 'disc-leading-zero', 'square'];\n      const isUnordered = $.inArray(styleInfo['list-style-type'], orderedTypes) > -1;\n      styleInfo['list-style'] = isUnordered ? 'unordered' : 'ordered';\n    }\n\n    const para = dom.ancestor(rng.sc, dom.isPara);\n    if (para && para.style['line-height']) {\n      styleInfo['line-height'] = para.style.lineHeight;\n    } else {\n      const lineHeight = parseInt(styleInfo['line-height'], 10) / parseInt(styleInfo['font-size'], 10);\n      styleInfo['line-height'] = lineHeight.toFixed(1);\n    }\n\n    styleInfo.anchor = rng.isOnAnchor() && dom.ancestor(rng.sc, dom.isAnchor);\n    styleInfo.ancestors = dom.listAncestor(rng.sc, dom.isEditable);\n    styleInfo.range = rng;\n\n    return styleInfo;\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport func from '../core/func';\nimport dom from '../core/dom';\nimport range from '../core/range';\n\nexport default class Bullet {\n  /**\n   * toggle ordered list\n   */\n  insertOrderedList(editable) {\n    this.toggleList('OL', editable);\n  }\n\n  /**\n   * toggle unordered list\n   */\n  insertUnorderedList(editable) {\n    this.toggleList('UL', editable);\n  }\n\n  /**\n   * indent\n   */\n  indent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        this.wrapList(paras, head.parentNode.nodeName);\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            return (parseInt(val, 10) || 0) + 25;\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * outdent\n   */\n  outdent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        this.releaseList([paras]);\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            val = (parseInt(val, 10) || 0);\n            return val > 25 ? val - 25 : '';\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * toggle list\n   *\n   * @param {String} listName - OL or UL\n   */\n  toggleList(listName, editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    let paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const bookmark = rng.paraBookmark(paras);\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    // paragraph to list\n    if (lists.find(paras, dom.isPurePara)) {\n      let wrappedParas = [];\n      $.each(clustereds, (idx, paras) => {\n        wrappedParas = wrappedParas.concat(this.wrapList(paras, listName));\n      });\n      paras = wrappedParas;\n    // list to paragraph or change list style\n    } else {\n      const diffLists = rng.nodes(dom.isList, {\n        includeAncestor: true\n      }).filter((listNode) => {\n        return !$.nodeName(listNode, listName);\n      });\n\n      if (diffLists.length) {\n        $.each(diffLists, (idx, listNode) => {\n          dom.replace(listNode, listName);\n        });\n      } else {\n        paras = this.releaseList(clustereds, true);\n      }\n    }\n\n    range.createFromParaBookmark(bookmark, paras).select();\n  }\n\n  /**\n   * @param {Node[]} paras\n   * @param {String} listName\n   * @return {Node[]}\n   */\n  wrapList(paras, listName) {\n    const head = lists.head(paras);\n    const last = lists.last(paras);\n\n    const prevList = dom.isList(head.previousSibling) && head.previousSibling;\n    const nextList = dom.isList(last.nextSibling) && last.nextSibling;\n\n    const listNode = prevList || dom.insertAfter(dom.create(listName || 'UL'), last);\n\n    // P to LI\n    paras = paras.map((para) => {\n      return dom.isPurePara(para) ? dom.replace(para, 'LI') : para;\n    });\n\n    // append to list(<ul>, <ol>)\n    dom.appendChildNodes(listNode, paras);\n\n    if (nextList) {\n      dom.appendChildNodes(listNode, lists.from(nextList.childNodes));\n      dom.remove(nextList);\n    }\n\n    return paras;\n  }\n\n  /**\n   * @method releaseList\n   *\n   * @param {Array[]} clustereds\n   * @param {Boolean} isEscapseToBody\n   * @return {Node[]}\n   */\n  releaseList(clustereds, isEscapseToBody) {\n    let releasedParas = [];\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      const last = lists.last(paras);\n\n      const headList = isEscapseToBody ? dom.lastAncestor(head, dom.isList) : head.parentNode;\n      const lastList = headList.childNodes.length > 1 ? dom.splitTree(headList, {\n        node: last.parentNode,\n        offset: dom.position(last) + 1\n      }, {\n        isSkipPaddingBlankHTML: true\n      }) : null;\n\n      const middleList = dom.splitTree(headList, {\n        node: head.parentNode,\n        offset: dom.position(head)\n      }, {\n        isSkipPaddingBlankHTML: true\n      });\n\n      paras = isEscapseToBody ? dom.listDescendant(middleList, dom.isLi)\n        : lists.from(middleList.childNodes).filter(dom.isLi);\n\n      // LI to P\n      if (isEscapseToBody || !dom.isList(headList.parentNode)) {\n        paras = paras.map((para) => {\n          return dom.replace(para, 'P');\n        });\n      }\n\n      $.each(lists.from(paras).reverse(), (idx, para) => {\n        dom.insertAfter(para, headList);\n      });\n\n      // remove empty lists\n      const rootLists = lists.compact([headList, middleList, lastList]);\n      $.each(rootLists, (idx, rootList) => {\n        const listNodes = [rootList].concat(dom.listDescendant(rootList, dom.isList));\n        $.each(listNodes.reverse(), (idx, listNode) => {\n          if (!dom.nodeLength(listNode)) {\n            dom.remove(listNode, true);\n          }\n        });\n      });\n\n      releasedParas = releasedParas.concat(paras);\n    });\n\n    return releasedParas;\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport Bullet from '../editing/Bullet';\n\n/**\n * @class editing.Typing\n *\n * Typing\n *\n */\nexport default class Typing {\n  constructor() {\n    // a Bullet instance to toggle lists off\n    this.bullet = new Bullet();\n  }\n\n  /**\n   * insert tab\n   *\n   * @param {WrappedRange} rng\n   * @param {Number} tabsize\n   */\n  insertTab(rng, tabsize) {\n    const tab = dom.createText(new Array(tabsize + 1).join(dom.NBSP_CHAR));\n    rng = rng.deleteContents();\n    rng.insertNode(tab, true);\n\n    rng = range.create(tab, tabsize);\n    rng.select();\n  }\n\n  /**\n   * insert paragraph\n   */\n  insertParagraph(editable) {\n    let rng = range.create(editable);\n\n    // deleteContents on range.\n    rng = rng.deleteContents();\n\n    // Wrap range if it needs to be wrapped by paragraph\n    rng = rng.wrapBodyInlineWithPara();\n\n    // finding paragraph\n    const splitRoot = dom.ancestor(rng.sc, dom.isPara);\n\n    let nextPara;\n    // on paragraph: split paragraph\n    if (splitRoot) {\n      // if it is an empty line with li\n      if (dom.isEmpty(splitRoot) && dom.isLi(splitRoot)) {\n        // toogle UL/OL and escape\n        this.bullet.toggleList(splitRoot.parentNode.nodeName);\n        return;\n      // if it is an empty line with para on blockquote\n      } else if (dom.isEmpty(splitRoot) && dom.isPara(splitRoot) && dom.isBlockquote(splitRoot.parentNode)) {\n        // escape blockquote\n        dom.insertAfter(splitRoot, splitRoot.parentNode);\n        nextPara = splitRoot;\n      // if new line has content (not a line break)\n      } else {\n        nextPara = dom.splitTree(splitRoot, rng.getStartPoint());\n\n        let emptyAnchors = dom.listDescendant(splitRoot, dom.isEmptyAnchor);\n        emptyAnchors = emptyAnchors.concat(dom.listDescendant(nextPara, dom.isEmptyAnchor));\n\n        $.each(emptyAnchors, (idx, anchor) => {\n          dom.remove(anchor);\n        });\n\n        // replace empty heading, pre or custom-made styleTag with P tag\n        if ((dom.isHeading(nextPara) || dom.isPre(nextPara) || dom.isCustomStyleTag(nextPara)) && dom.isEmpty(nextPara)) {\n          nextPara = dom.replace(nextPara, 'p');\n        }\n      }\n    // no paragraph: insert empty paragraph\n    } else {\n      const next = rng.sc.childNodes[rng.so];\n      nextPara = $(dom.emptyPara)[0];\n      if (next) {\n        rng.sc.insertBefore(nextPara, next);\n      } else {\n        rng.sc.appendChild(nextPara);\n      }\n    }\n\n    range.create(nextPara, 0).normalize().select().scrollIntoView(editable);\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport lists from '../core/lists';\n\n/**\n * @class Create a virtual table to create what actions to do in change.\n * @param {object} startPoint Cell selected to apply change.\n * @param {enum} where  Where change will be applied Row or Col. Use enum: TableResultAction.where\n * @param {enum} action Action to be applied. Use enum: TableResultAction.requestAction\n * @param {object} domTable Dom element of table to make changes.\n */\nconst TableResultAction = function(startPoint, where, action, domTable) {\n  const _startPoint = { 'colPos': 0, 'rowPos': 0 };\n  const _virtualTable = [];\n  const _actionCellList = [];\n\n  /// ///////////////////////////////////////////\n  // Private functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Set the startPoint of action.\n   */\n  function setStartPoint() {\n    if (!startPoint || !startPoint.tagName || (startPoint.tagName.toLowerCase() !== 'td' && startPoint.tagName.toLowerCase() !== 'th')) {\n      console.error('Impossible to identify start Cell point.', startPoint);\n      return;\n    }\n    _startPoint.colPos = startPoint.cellIndex;\n    if (!startPoint.parentElement || !startPoint.parentElement.tagName || startPoint.parentElement.tagName.toLowerCase() !== 'tr') {\n      console.error('Impossible to identify start Row point.', startPoint);\n      return;\n    }\n    _startPoint.rowPos = startPoint.parentElement.rowIndex;\n  }\n\n  /**\n   * Define virtual table position info object.\n   *\n   * @param {int} rowIndex Index position in line of virtual table.\n   * @param {int} cellIndex Index position in column of virtual table.\n   * @param {object} baseRow Row affected by this position.\n   * @param {object} baseCell Cell affected by this position.\n   * @param {bool} isSpan Inform if it is an span cell/row.\n   */\n  function setVirtualTablePosition(rowIndex, cellIndex, baseRow, baseCell, isRowSpan, isColSpan, isVirtualCell) {\n    const objPosition = {\n      'baseRow': baseRow,\n      'baseCell': baseCell,\n      'isRowSpan': isRowSpan,\n      'isColSpan': isColSpan,\n      'isVirtual': isVirtualCell\n    };\n    if (!_virtualTable[rowIndex]) {\n      _virtualTable[rowIndex] = [];\n    }\n    _virtualTable[rowIndex][cellIndex] = objPosition;\n  }\n\n  /**\n   * Create action cell object.\n   *\n   * @param {object} virtualTableCellObj Object of specific position on virtual table.\n   * @param {enum} resultAction Action to be applied in that item.\n   */\n  function getActionCell(virtualTableCellObj, resultAction, virtualRowPosition, virtualColPosition) {\n    return {\n      'baseCell': virtualTableCellObj.baseCell,\n      'action': resultAction,\n      'virtualTable': {\n        'rowIndex': virtualRowPosition,\n        'cellIndex': virtualColPosition\n      }\n    };\n  }\n\n  /**\n   * Recover free index of row to append Cell.\n   *\n   * @param {int} rowIndex Index of row to find free space.\n   * @param {int} cellIndex Index of cell to find free space in table.\n   */\n  function recoverCellIndex(rowIndex, cellIndex) {\n    if (!_virtualTable[rowIndex]) {\n      return cellIndex;\n    }\n    if (!_virtualTable[rowIndex][cellIndex]) {\n      return cellIndex;\n    }\n\n    let newCellIndex = cellIndex;\n    while (_virtualTable[rowIndex][newCellIndex]) {\n      newCellIndex++;\n      if (!_virtualTable[rowIndex][newCellIndex]) {\n        return newCellIndex;\n      }\n    }\n  }\n\n  /**\n   * Recover info about row and cell and add information to virtual table.\n   *\n   * @param {object} row Row to recover information.\n   * @param {object} cell Cell to recover information.\n   */\n  function addCellInfoToVirtual(row, cell) {\n    const cellIndex = recoverCellIndex(row.rowIndex, cell.cellIndex);\n    const cellHasColspan = (cell.colSpan > 1);\n    const cellHasRowspan = (cell.rowSpan > 1);\n    const isThisSelectedCell = (row.rowIndex === _startPoint.rowPos && cell.cellIndex === _startPoint.colPos);\n    setVirtualTablePosition(row.rowIndex, cellIndex, row, cell, cellHasRowspan, cellHasColspan, false);\n\n    // Add span rows to virtual Table.\n    const rowspanNumber = cell.attributes.rowSpan ? parseInt(cell.attributes.rowSpan.value, 10) : 0;\n    if (rowspanNumber > 1) {\n      for (let rp = 1; rp < rowspanNumber; rp++) {\n        const rowspanIndex = row.rowIndex + rp;\n        adjustStartPoint(rowspanIndex, cellIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(rowspanIndex, cellIndex, row, cell, true, cellHasColspan, true);\n      }\n    }\n\n    // Add span cols to virtual table.\n    const colspanNumber = cell.attributes.colSpan ? parseInt(cell.attributes.colSpan.value, 10) : 0;\n    if (colspanNumber > 1) {\n      for (let cp = 1; cp < colspanNumber; cp++) {\n        const cellspanIndex = recoverCellIndex(row.rowIndex, (cellIndex + cp));\n        adjustStartPoint(row.rowIndex, cellspanIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(row.rowIndex, cellspanIndex, row, cell, cellHasRowspan, true, true);\n      }\n    }\n  }\n\n  /**\n   * Process validation and adjust of start point if needed\n   *\n   * @param {int} rowIndex\n   * @param {int} cellIndex\n   * @param {object} cell\n   * @param {bool} isSelectedCell\n   */\n  function adjustStartPoint(rowIndex, cellIndex, cell, isSelectedCell) {\n    if (rowIndex === _startPoint.rowPos && _startPoint.colPos >= cell.cellIndex && cell.cellIndex <= cellIndex && !isSelectedCell) {\n      _startPoint.colPos++;\n    }\n  }\n\n  /**\n   * Create virtual table of cells with all cells, including span cells.\n   */\n  function createVirtualTable() {\n    const rows = domTable.rows;\n    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n      const cells = rows[rowIndex].cells;\n      for (let cellIndex = 0; cellIndex < cells.length; cellIndex++) {\n        addCellInfoToVirtual(rows[rowIndex], cells[cellIndex]);\n      }\n    }\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getDeleteResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (!cell.isVirtual && cell.isRowSpan) {\n          return TableResultAction.resultAction.AddCell;\n        } else if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.RemoveCell;\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getAddResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isRowSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isColSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.AddCell;\n  }\n\n  function init() {\n    setStartPoint();\n    createVirtualTable();\n  }\n\n  /// ///////////////////////////////////////////\n  // Public functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Recover array os what to do in table.\n   */\n  this.getActionList = function() {\n    const fixedRow = (where === TableResultAction.where.Row) ? _startPoint.rowPos : -1;\n    const fixedCol = (where === TableResultAction.where.Column) ? _startPoint.colPos : -1;\n\n    let actualPosition = 0;\n    let canContinue = true;\n    while (canContinue) {\n      const rowPosition = (fixedRow >= 0) ? fixedRow : actualPosition;\n      const colPosition = (fixedCol >= 0) ? fixedCol : actualPosition;\n      const row = _virtualTable[rowPosition];\n      if (!row) {\n        canContinue = false;\n        return _actionCellList;\n      }\n      const cell = row[colPosition];\n      if (!cell) {\n        canContinue = false;\n        return _actionCellList;\n      }\n\n      // Define action to be applied in this cell\n      let resultAction = TableResultAction.resultAction.Ignore;\n      switch (action) {\n        case TableResultAction.requestAction.Add:\n          resultAction = getAddResultActionToCell(cell);\n          break;\n        case TableResultAction.requestAction.Delete:\n          resultAction = getDeleteResultActionToCell(cell);\n          break;\n      }\n      _actionCellList.push(getActionCell(cell, resultAction, rowPosition, colPosition));\n      actualPosition++;\n    }\n\n    return _actionCellList;\n  };\n\n  init();\n};\n/**\n*\n* Where action occours enum.\n*/\nTableResultAction.where = { 'Row': 0, 'Column': 1 };\n/**\n*\n* Requested action to apply enum.\n*/\nTableResultAction.requestAction = { 'Add': 0, 'Delete': 1 };\n/**\n*\n* Result action to be executed enum.\n*/\nTableResultAction.resultAction = { 'Ignore': 0, 'SubtractSpanCount': 1, 'RemoveCell': 2, 'AddCell': 3, 'SumSpanCount': 4 };\n\n/**\n *\n * @class editing.Table\n *\n * Table\n *\n */\nexport default class Table {\n  /**\n   * handle tab key\n   *\n   * @param {WrappedRange} rng\n   * @param {Boolean} isShift\n   */\n  tab(rng, isShift) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const table = dom.ancestor(cell, dom.isTable);\n    const cells = dom.listDescendant(table, dom.isCell);\n\n    const nextCell = lists[isShift ? 'prev' : 'next'](cells, cell);\n    if (nextCell) {\n      range.create(nextCell, 0).select();\n    }\n  }\n\n  /**\n   * Add a new row\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (top/bottom)\n   * @return {Node}\n   */\n  addRow(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n\n    const currentTr = $(cell).closest('tr');\n    const trAttributes = this.recoverAttributes(currentTr);\n    const html = $('<tr' + trAttributes + '></tr>');\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Add, $(currentTr).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let idCell = 0; idCell < actions.length; idCell++) {\n      const currentCell = actions[idCell];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          html.append('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          if (position === 'top') {\n            const baseCellTr = currentCell.baseCell.parent;\n            const isTopFromRowSpan = (!baseCellTr ? 0 : currentCell.baseCell.closest('tr').rowIndex) <= currentTr[0].rowIndex;\n            if (isTopFromRowSpan) {\n              const newTd = $('<div></div>').append($('<td' + tdAttributes + '>' + dom.blank + '</td>').removeAttr('rowspan')).html();\n              html.append(newTd);\n              break;\n            }\n          }\n          let rowspanNumber = parseInt(currentCell.baseCell.rowSpan, 10);\n          rowspanNumber++;\n          currentCell.baseCell.setAttribute('rowSpan', rowspanNumber);\n          break;\n      }\n    }\n\n    if (position === 'top') {\n      currentTr.before(html);\n    } else {\n      const cellHasRowspan = (cell.rowSpan > 1);\n      if (cellHasRowspan) {\n        const lastTrIndex = currentTr[0].rowIndex + (cell.rowSpan - 2);\n        $($(currentTr).parent().find('tr')[lastTrIndex]).after($(html));\n        return;\n      }\n      currentTr.after(html);\n    }\n  }\n\n  /**\n   * Add a new col\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (left/right)\n   * @return {Node}\n   */\n  addCol(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const rowsGroup = $(row).siblings();\n    rowsGroup.push(row);\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Add, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      const currentCell = actions[actionIndex];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          if (position === 'right') {\n            $(currentCell.baseCell).after('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          if (position === 'right') {\n            let colspanNumber = parseInt(currentCell.baseCell.colSpan, 10);\n            colspanNumber++;\n            currentCell.baseCell.setAttribute('colSpan', colspanNumber);\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n      }\n    }\n  }\n\n  /*\n  * Copy attributes from element.\n  *\n  * @param {object} Element to recover attributes.\n  * @return {string} Copied string elements.\n  */\n  recoverAttributes(el) {\n    let resultStr = '';\n\n    if (!el) {\n      return resultStr;\n    }\n\n    const attrList = el.attributes || [];\n\n    for (let i = 0; i < attrList.length; i++) {\n      if (attrList[i].name.toLowerCase() === 'id') {\n        continue;\n      }\n\n      if (attrList[i].specified) {\n        resultStr += ' ' + attrList[i].name + '=\\'' + attrList[i].value + '\\'';\n      }\n    }\n\n    return resultStr;\n  }\n\n  /**\n   * Delete current row\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteRow(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n    const rowPos = row[0].rowIndex;\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n\n      const baseCell = actions[actionIndex].baseCell;\n      const virtualPosition = actions[actionIndex].virtualTable;\n      const hasRowspan = (baseCell.rowSpan && baseCell.rowSpan > 1);\n      let rowspanNumber = (hasRowspan) ? parseInt(baseCell.rowSpan, 10) : 0;\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.AddCell:\n          const nextRow = row.next('tr')[0];\n          if (!nextRow) { continue; }\n          const cloneRow = row[0].cells[cellPos];\n          if (hasRowspan) {\n            if (rowspanNumber > 2) {\n              rowspanNumber--;\n              nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n              nextRow.cells[cellPos].setAttribute('rowSpan', rowspanNumber);\n              nextRow.cells[cellPos].innerHTML = '';\n            } else if (rowspanNumber === 2) {\n              nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n              nextRow.cells[cellPos].removeAttribute('rowSpan');\n              nextRow.cells[cellPos].innerHTML = '';\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          if (hasRowspan) {\n            if (rowspanNumber > 2) {\n              rowspanNumber--;\n              baseCell.setAttribute('rowSpan', rowspanNumber);\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            } else if (rowspanNumber === 2) {\n              baseCell.removeAttribute('rowSpan');\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          // Do not need remove cell because row will be deleted.\n          continue;\n      }\n    }\n    row.remove();\n  }\n\n  /**\n   * Delete current col\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteCol(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          const baseCell = actions[actionIndex].baseCell;\n          const hasColspan = (baseCell.colSpan && baseCell.colSpan > 1);\n          if (hasColspan) {\n            let colspanNumber = (baseCell.colSpan) ? parseInt(baseCell.colSpan, 10) : 0;\n            if (colspanNumber > 2) {\n              colspanNumber--;\n              baseCell.setAttribute('colSpan', colspanNumber);\n              if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            } else if (colspanNumber === 2) {\n              baseCell.removeAttribute('colSpan');\n              if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          dom.remove(actions[actionIndex].baseCell, true);\n          continue;\n      }\n    }\n  }\n\n  /**\n   * create empty table element\n   *\n   * @param {Number} rowCount\n   * @param {Number} colCount\n   * @return {Node}\n   */\n  createTable(colCount, rowCount, options) {\n    const tds = [];\n    let tdHTML;\n    for (let idxCol = 0; idxCol < colCount; idxCol++) {\n      tds.push('<td>' + dom.blank + '</td>');\n    }\n    tdHTML = tds.join('');\n\n    const trs = [];\n    let trHTML;\n    for (let idxRow = 0; idxRow < rowCount; idxRow++) {\n      trs.push('<tr>' + tdHTML + '</tr>');\n    }\n    trHTML = trs.join('');\n    const $table = $('<table>' + trHTML + '</table>');\n    if (options && options.tableClassName) {\n      $table.addClass(options.tableClassName);\n    }\n\n    return $table[0];\n  }\n\n  /**\n   * Delete current table\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteTable(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    $(cell).closest('table').remove();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport { readFileAsDataURL, createImage } from '../core/async';\nimport History from '../editing/History';\nimport Style from '../editing/Style';\nimport Typing from '../editing/Typing';\nimport Table from '../editing/Table';\nimport Bullet from '../editing/Bullet';\n\nconst KEY_BOGUS = 'bogus';\n\n/**\n * @class Editor\n */\nexport default class Editor {\n  constructor(context) {\n    this.context = context;\n\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.editable = this.$editable[0];\n    this.lastRange = null;\n\n    this.style = new Style();\n    this.table = new Table();\n    this.typing = new Typing();\n    this.bullet = new Bullet();\n    this.history = new History(this.$editable);\n\n    this.context.memo('help.undo', this.lang.help.undo);\n    this.context.memo('help.redo', this.lang.help.redo);\n    this.context.memo('help.tab', this.lang.help.tab);\n    this.context.memo('help.untab', this.lang.help.untab);\n    this.context.memo('help.insertParagraph', this.lang.help.insertParagraph);\n    this.context.memo('help.insertOrderedList', this.lang.help.insertOrderedList);\n    this.context.memo('help.insertUnorderedList', this.lang.help.insertUnorderedList);\n    this.context.memo('help.indent', this.lang.help.indent);\n    this.context.memo('help.outdent', this.lang.help.outdent);\n    this.context.memo('help.formatPara', this.lang.help.formatPara);\n    this.context.memo('help.insertHorizontalRule', this.lang.help.insertHorizontalRule);\n    this.context.memo('help.fontName', this.lang.help.fontName);\n\n    // native commands(with execCommand), generate function for execCommand\n    const commands = [\n      'bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript',\n      'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull',\n      'formatBlock', 'removeFormat', 'backColor'\n    ];\n\n    for (let idx = 0, len = commands.length; idx < len; idx++) {\n      this[commands[idx]] = ((sCmd) => {\n        return (value) => {\n          this.beforeCommand();\n          document.execCommand(sCmd, false, value);\n          this.afterCommand(true);\n        };\n      })(commands[idx]);\n      this.context.memo('help.' + commands[idx], this.lang.help[commands[idx]]);\n    }\n\n    this.fontName = this.wrapCommand((value) => {\n      return this.fontStyling('font-family', \"\\'\" + value + \"\\'\");\n    });\n\n    this.fontSize = this.wrapCommand((value) => {\n      return this.fontStyling('font-size', value + 'px');\n    });\n\n    for (let idx = 1; idx <= 6; idx++) {\n      this['formatH' + idx] = ((idx) => {\n        return () => {\n          this.formatBlock('H' + idx);\n        };\n      })(idx);\n      this.context.memo('help.formatH' + idx, this.lang.help['formatH' + idx]);\n    };\n\n    this.insertParagraph = this.wrapCommand(() => {\n      this.typing.insertParagraph(this.editable);\n    });\n\n    this.insertOrderedList = this.wrapCommand(() => {\n      this.bullet.insertOrderedList(this.editable);\n    });\n\n    this.insertUnorderedList = this.wrapCommand(() => {\n      this.bullet.insertUnorderedList(this.editable);\n    });\n\n    this.indent = this.wrapCommand(() => {\n      this.bullet.indent(this.editable);\n    });\n\n    this.outdent = this.wrapCommand(() => {\n      this.bullet.outdent(this.editable);\n    });\n\n    /**\n     * insertNode\n     * insert node\n     * @param {Node} node\n     */\n    this.insertNode = this.wrapCommand((node) => {\n      if (this.isLimited($(node).text().length)) {\n        return;\n      }\n      const rng = this.createRange();\n      rng.insertNode(node);\n      range.createFromNodeAfter(node).select();\n    });\n\n    /**\n     * insert text\n     * @param {String} text\n     */\n    this.insertText = this.wrapCommand((text) => {\n      if (this.isLimited(text.length)) {\n        return;\n      }\n      const rng = this.createRange();\n      const textNode = rng.insertNode(dom.createText(text));\n      range.create(textNode, dom.nodeLength(textNode)).select();\n    });\n    /**\n     * paste HTML\n     * @param {String} markup\n     */\n    this.pasteHTML = this.wrapCommand((markup) => {\n      if (this.isLimited(markup.length)) {\n        return;\n      }\n      const contents = this.createRange().pasteHTML(markup);\n      range.createFromNodeAfter(lists.last(contents)).select();\n    });\n\n    /**\n     * formatBlock\n     *\n     * @param {String} tagName\n     */\n    this.formatBlock = this.wrapCommand((tagName, $target) => {\n      const onApplyCustomStyle = this.options.callbacks.onApplyCustomStyle;\n      if (onApplyCustomStyle) {\n        onApplyCustomStyle.call(this, $target, this.context, this.onFormatBlock);\n      } else {\n        this.onFormatBlock(tagName, $target);\n      }\n    });\n\n    /**\n     * insert horizontal rule\n     */\n    this.insertHorizontalRule = this.wrapCommand(() => {\n      const hrNode = this.createRange().insertNode(dom.create('HR'));\n      if (hrNode.nextSibling) {\n        range.create(hrNode.nextSibling, 0).normalize().select();\n      }\n    });\n\n    /**\n     * lineHeight\n     * @param {String} value\n     */\n    this.lineHeight = this.wrapCommand((value) => {\n      this.style.stylePara(this.createRange(), {\n        lineHeight: value\n      });\n    });\n\n    /**\n     * create link (command)\n     *\n     * @param {Object} linkInfo\n     */\n    this.createLink = this.wrapCommand((linkInfo) => {\n      let linkUrl = linkInfo.url;\n      const linkText = linkInfo.text;\n      const isNewWindow = linkInfo.isNewWindow;\n      let rng = linkInfo.range || this.createRange();\n      const isTextChanged = rng.toString() !== linkText;\n\n      // handle spaced urls from input\n      if (typeof linkUrl === 'string') {\n        linkUrl = linkUrl.trim();\n      }\n\n      if (this.options.onCreateLink) {\n        linkUrl = this.options.onCreateLink(linkUrl);\n      } else {\n        // if url doesn't match an URL schema, set http:// as default\n        linkUrl = /^[A-Za-z][A-Za-z0-9+-.]*\\:[\\/\\/]?/.test(linkUrl)\n          ? linkUrl : 'http://' + linkUrl;\n      }\n\n      let anchors = [];\n      if (isTextChanged) {\n        rng = rng.deleteContents();\n        const anchor = rng.insertNode($('<A>' + linkText + '</A>')[0]);\n        anchors.push(anchor);\n      } else {\n        anchors = this.style.styleNodes(rng, {\n          nodeName: 'A',\n          expandClosestSibling: true,\n          onlyPartialContains: true\n        });\n      }\n\n      $.each(anchors, (idx, anchor) => {\n        $(anchor).attr('href', linkUrl);\n        if (isNewWindow) {\n          $(anchor).attr('target', '_blank');\n        } else {\n          $(anchor).removeAttr('target');\n        }\n      });\n\n      const startRange = range.createFromNodeBefore(lists.head(anchors));\n      const startPoint = startRange.getStartPoint();\n      const endRange = range.createFromNodeAfter(lists.last(anchors));\n      const endPoint = endRange.getEndPoint();\n\n      range.create(\n        startPoint.node,\n        startPoint.offset,\n        endPoint.node,\n        endPoint.offset\n      ).select();\n    });\n\n    /**\n     * setting color\n     *\n     * @param {Object} sObjColor  color code\n     * @param {String} sObjColor.foreColor foreground color\n     * @param {String} sObjColor.backColor background color\n     */\n    this.color = this.wrapCommand((colorInfo) => {\n      const foreColor = colorInfo.foreColor;\n      const backColor = colorInfo.backColor;\n\n      if (foreColor) { document.execCommand('foreColor', false, foreColor); }\n      if (backColor) { document.execCommand('backColor', false, backColor); }\n    });\n\n    /**\n     * Set foreground color\n     *\n     * @param {String} colorCode foreground color code\n     */\n    this.foreColor = this.wrapCommand((colorInfo) => {\n      document.execCommand('styleWithCSS', false, true);\n      document.execCommand('foreColor', false, colorInfo);\n    });\n\n    /**\n     * insert Table\n     *\n     * @param {String} dimension of table (ex : \"5x5\")\n     */\n    this.insertTable = this.wrapCommand((dim) => {\n      const dimension = dim.split('x');\n\n      const rng = this.createRange().deleteContents();\n      rng.insertNode(this.table.createTable(dimension[0], dimension[1], this.options));\n    });\n\n    /**\n     * remove media object and Figure Elements if media object is img with Figure.\n     */\n    this.removeMedia = this.wrapCommand(() => {\n      let $target = $(this.restoreTarget()).parent();\n      if ($target.parent('figure').length) {\n        $target.parent('figure').remove();\n      } else {\n        $target = $(this.restoreTarget()).detach();\n      }\n      this.context.triggerEvent('media.delete', $target, this.$editable);\n    });\n\n    /**\n     * float me\n     *\n     * @param {String} value\n     */\n    this.floatMe = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      $target.toggleClass('note-float-left', value === 'left');\n      $target.toggleClass('note-float-right', value === 'right');\n      $target.css('float', value);\n    });\n\n    /**\n     * resize overlay element\n     * @param {String} value\n     */\n    this.resize = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      $target.css({\n        width: value * 100 + '%',\n        height: ''\n      });\n    });\n  }\n\n  initialize() {\n    // bind custom events\n    this.$editable.on('keydown', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        this.context.triggerEvent('enter', event);\n      }\n      this.context.triggerEvent('keydown', event);\n\n      if (!event.isDefaultPrevented()) {\n        if (this.options.shortcuts) {\n          this.handleKeyMap(event);\n        } else {\n          this.preventDefaultEditableShortCuts(event);\n        }\n      }\n      if (this.isLimited(1, event)) {\n        return false;\n      }\n    }).on('keyup', (event) => {\n      this.context.triggerEvent('keyup', event);\n    }).on('focus', (event) => {\n      this.context.triggerEvent('focus', event);\n    }).on('blur', (event) => {\n      this.context.triggerEvent('blur', event);\n    }).on('mousedown', (event) => {\n      this.context.triggerEvent('mousedown', event);\n    }).on('mouseup', (event) => {\n      this.context.triggerEvent('mouseup', event);\n    }).on('scroll', (event) => {\n      this.context.triggerEvent('scroll', event);\n    }).on('paste', (event) => {\n      this.context.triggerEvent('paste', event);\n    });\n\n    // init content before set event\n    this.$editable.html(dom.html(this.$note) || dom.emptyPara);\n\n    this.$editable.on(env.inputEventName, func.debounce(() => {\n      this.context.triggerEvent('change', this.$editable.html());\n    }, 100));\n\n    this.$editor.on('focusin', (event) => {\n      this.context.triggerEvent('focusin', event);\n    }).on('focusout', (event) => {\n      this.context.triggerEvent('focusout', event);\n    });\n\n    if (!this.options.airMode) {\n      if (this.options.width) {\n        this.$editor.outerWidth(this.options.width);\n      }\n      if (this.options.height) {\n        this.$editable.outerHeight(this.options.height);\n      }\n      if (this.options.maxHeight) {\n        this.$editable.css('max-height', this.options.maxHeight);\n      }\n      if (this.options.minHeight) {\n        this.$editable.css('min-height', this.options.minHeight);\n      }\n    }\n\n    this.history.recordUndo();\n  }\n\n  destroy() {\n    this.$editable.off();\n  }\n\n  handleKeyMap(event) {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    const keys = [];\n\n    if (event.metaKey) { keys.push('CMD'); }\n    if (event.ctrlKey && !event.altKey) { keys.push('CTRL'); }\n    if (event.shiftKey) { keys.push('SHIFT'); }\n\n    const keyName = key.nameFromCode[event.keyCode];\n    if (keyName) {\n      keys.push(keyName);\n    }\n\n    const eventName = keyMap[keys.join('+')];\n    if (eventName) {\n      if (this.context.invoke(eventName) !== false) {\n        event.preventDefault();\n      }\n    } else if (key.isEdit(event.keyCode)) {\n      this.afterCommand();\n    }\n  }\n\n  preventDefaultEditableShortCuts(event) {\n    // B(Bold, 66) / I(Italic, 73) / U(Underline, 85)\n    if ((event.ctrlKey || event.metaKey) &&\n      lists.contains([66, 73, 85], event.keyCode)) {\n      event.preventDefault();\n    }\n  }\n\n  isLimited(pad, event) {\n    pad = pad || 0;\n\n    if (typeof event !== 'undefined') {\n      if (key.isMove(event.keyCode) ||\n          (event.ctrlKey || event.metaKey) ||\n          lists.contains([key.code.BACKSPACE, key.code.DELETE], event.keyCode)) {\n        return false;\n      }\n    }\n\n    if (this.options.maxTextLength > 0) {\n      if ((this.$editable.text().length + pad) >= this.options.maxTextLength) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * create range\n   * @return {WrappedRange}\n   */\n  createRange() {\n    this.focus();\n    return range.create(this.editable);\n  }\n\n  /**\n   * saveRange\n   *\n   * save current range\n   *\n   * @param {Boolean} [thenCollapse=false]\n   */\n  saveRange(thenCollapse) {\n    this.lastRange = this.createRange();\n    if (thenCollapse) {\n      this.lastRange.collapse().select();\n    }\n  }\n\n  /**\n   * restoreRange\n   *\n   * restore lately range\n   */\n  restoreRange() {\n    if (this.lastRange) {\n      this.lastRange.select();\n      this.focus();\n    }\n  }\n\n  saveTarget(node) {\n    this.$editable.data('target', node);\n  }\n\n  clearTarget() {\n    this.$editable.removeData('target');\n  }\n\n  restoreTarget() {\n    return this.$editable.data('target');\n  }\n\n  /**\n   * currentStyle\n   *\n   * current style\n   * @return {Object|Boolean} unfocus\n   */\n  currentStyle() {\n    let rng = range.create();\n    if (rng) {\n      rng = rng.normalize();\n    }\n    return rng ? this.style.current(rng) : this.style.fromNode(this.$editable);\n  }\n\n  /**\n   * style from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  styleFromNode($node) {\n    return this.style.fromNode($node);\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.undo();\n    this.context.triggerEvent('change', this.$editable.html());\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.redo();\n    this.context.triggerEvent('change', this.$editable.html());\n  }\n\n  /**\n   * before command\n   */\n  beforeCommand() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    // keep focus on editable before command execution\n    this.focus();\n  }\n\n  /**\n   * after command\n   * @param {Boolean} isPreventTrigger\n   */\n  afterCommand(isPreventTrigger) {\n    this.normalizeContent();\n    this.history.recordUndo();\n    if (!isPreventTrigger) {\n      this.context.triggerEvent('change', this.$editable.html());\n    }\n  }\n\n  /**\n   * handle tab key\n   */\n  tab() {\n    const rng = this.createRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n\n      if (!this.isLimited(this.options.tabSize)) {\n        this.beforeCommand();\n        this.typing.insertTab(rng, this.options.tabSize);\n        this.afterCommand();\n      }\n    }\n  }\n\n  /**\n   * handle shift+tab key\n   */\n  untab() {\n    const rng = this.createRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng, true);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n    }\n  }\n\n  /**\n   * run given function between beforeCommand and afterCommand\n   */\n  wrapCommand(fn) {\n    return () => {\n      this.beforeCommand();\n      fn.apply(this, arguments);\n      this.afterCommand();\n    };\n  }\n\n  /**\n   * insert image\n   *\n   * @param {String} src\n   * @param {String|Function} param\n   * @return {Promise}\n   */\n  insertImage(src, param) {\n    return createImage(src, param).then(($image) => {\n      this.beforeCommand();\n\n      if (typeof param === 'function') {\n        param($image);\n      } else {\n        if (typeof param === 'string') {\n          $image.attr('data-filename', param);\n        }\n        $image.css('width', Math.min(this.$editable.width(), $image.width()));\n      }\n\n      $image.show();\n      range.create(this.editable).insertNode($image[0]);\n      range.createFromNodeAfter($image[0]).select();\n      this.afterCommand();\n    }).fail((e) => {\n      this.context.triggerEvent('image.upload.error', e);\n    });\n  }\n\n  /**\n   * insertImages\n   * @param {File[]} files\n   */\n  insertImages(files) {\n    $.each(files, (idx, file) => {\n      const filename = file.name;\n      if (this.options.maximumImageFileSize && this.options.maximumImageFileSize < file.size) {\n        this.context.triggerEvent('image.upload.error', this.lang.image.maximumFileSizeError);\n      } else {\n        readFileAsDataURL(file).then((dataURL) => {\n          return this.insertImage(dataURL, filename);\n        }).fail(() => {\n          this.context.triggerEvent('image.upload.error');\n        });\n      }\n    });\n  }\n\n  /**\n   * insertImagesOrCallback\n   * @param {File[]} files\n   */\n  insertImagesOrCallback(files) {\n    const callbacks = this.options.callbacks;\n\n    // If onImageUpload this.options setted\n    if (callbacks.onImageUpload) {\n      this.context.triggerEvent('image.upload', files);\n      // else insert Image as dataURL\n    } else {\n      this.insertImages(files);\n    }\n  }\n\n  /**\n   * return selected plain text\n   * @return {String} text\n   */\n  getSelectedText() {\n    let rng = this.createRange();\n\n    // if range on anchor, expand range with anchor\n    if (rng.isOnAnchor()) {\n      rng = range.createFromNode(dom.ancestor(rng.sc, dom.isAnchor));\n    }\n\n    return rng.toString();\n  }\n\n  onFormatBlock(tagName, $target) {\n    // [workaround] for MSIE, IE need `<`\n    tagName = env.isMSIE ? '<' + tagName + '>' : tagName;\n    document.execCommand('FormatBlock', false, tagName);\n\n    // support custom class\n    if ($target && $target.length) {\n      const className = $target[0].className || '';\n      if (className) {\n        const currentRange = this.createRange();\n\n        const $parent = $([currentRange.sc, currentRange.ec]).closest(tagName);\n        $parent.addClass(className);\n      }\n    }\n  }\n\n  formatPara() {\n    this.formatBlock('P');\n  }\n\n  fontStyling(target, value) {\n    const rng = this.createRange();\n\n    if (rng) {\n      const spans = this.style.styleNodes(rng);\n      $(spans).css(target, value);\n\n      // [workaround] added styled bogus span for style\n      //  - also bogus character needed for cursor position\n      if (rng.isCollapsed()) {\n        const firstSpan = lists.head(spans);\n        if (firstSpan && !dom.nodeLength(firstSpan)) {\n          firstSpan.innerHTML = dom.ZERO_WIDTH_NBSP_CHAR;\n          range.createFromNodeAfter(firstSpan.firstChild).select();\n          this.$editable.data(KEY_BOGUS, firstSpan);\n        }\n      }\n    }\n  }\n\n  /**\n   * unlink\n   *\n   * @type command\n   */\n  unlink() {\n    let rng = this.createRange();\n    if (rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      rng = range.createFromNode(anchor);\n      rng.select();\n\n      this.beforeCommand();\n      document.execCommand('unlink');\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * returns link info\n   *\n   * @return {Object}\n   * @return {WrappedRange} return.range\n   * @return {String} return.text\n   * @return {Boolean} [return.isNewWindow=true]\n   * @return {String} [return.url=\"\"]\n   */\n  getLinkInfo() {\n    const rng = this.createRange().expand(dom.isAnchor);\n\n    // Get the first anchor on range(for edit).\n    const $anchor = $(lists.head(rng.nodes(dom.isAnchor)));\n    const linkInfo = {\n      range: rng,\n      text: rng.toString(),\n      url: $anchor.length ? $anchor.attr('href') : ''\n    };\n\n    // Define isNewWindow when anchor exists.\n    if ($anchor.length) {\n      linkInfo.isNewWindow = $anchor.attr('target') === '_blank';\n    }\n\n    return linkInfo;\n  }\n\n  addRow(position) {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addRow(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  addCol(position) {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addCol(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  deleteRow() {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteRow(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteCol() {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteCol(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteTable() {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteTable(rng);\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * @param {Position} pos\n   * @param {jQuery} $target - target element\n   * @param {Boolean} [bKeepRatio] - keep ratio\n   */\n  resizeTo(pos, $target, bKeepRatio) {\n    let imageSize;\n    if (bKeepRatio) {\n      const newRatio = pos.y / pos.x;\n      const ratio = $target.data('ratio');\n      imageSize = {\n        width: ratio > newRatio ? pos.x : pos.y / ratio,\n        height: ratio > newRatio ? pos.x * ratio : pos.y\n      };\n    } else {\n      imageSize = {\n        width: pos.x,\n        height: pos.y\n      };\n    }\n\n    $target.css(imageSize);\n  }\n\n  /**\n   * returns whether editable area has focus or not.\n   */\n  hasFocus() {\n    return this.$editable.is(':focus');\n  }\n\n  /**\n   * set focus\n   */\n  focus() {\n    // [workaround] Screen will move when page is scolled in IE.\n    //  - do focus when not focused\n    if (!this.hasFocus()) {\n      this.$editable.focus();\n    }\n  }\n\n  /**\n   * returns whether contents is empty or not.\n   * @return {Boolean}\n   */\n  isEmpty() {\n    return dom.isEmpty(this.$editable[0]) || dom.emptyPara === this.$editable.html();\n  }\n\n  /**\n   * Removes all contents and restores the editable instance to an _emptyPara_.\n   */\n  empty() {\n    this.context.invoke('code', dom.emptyPara);\n  }\n\n  /**\n   * normalize content\n   */\n  normalizeContent() {\n    this.$editable[0].normalize();\n  }\n}\n", "import lists from '../core/lists';\n\nexport default class Clipboard {\n  constructor(context) {\n    this.context = context;\n    this.$editable = context.layoutInfo.editable;\n  }\n\n  initialize() {\n    this.$editable.on('paste', this.pasteByEvent.bind(this));\n  }\n\n  /**\n   * paste by clipboard event\n   *\n   * @param {Event} event\n   */\n  pasteByEvent(event) {\n    const clipboardData = event.originalEvent.clipboardData;\n    if (clipboardData && clipboardData.items && clipboardData.items.length) {\n      const item = lists.head(clipboardData.items);\n      if (item.kind === 'file' && item.type.indexOf('image/') !== -1) {\n        this.context.invoke('editor.insertImagesOrCallback', [item.getAsFile()]);\n      }\n      this.context.invoke('editor.afterCommand');\n    }\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Dropzone {\n  constructor(context) {\n    this.context = context;\n    this.$eventListener = $(document);\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.documentEventHandlers = {};\n\n    this.$dropzone = $([\n      '<div class=\"note-dropzone\">',\n      '  <div class=\"note-dropzone-message\"/>',\n      '</div>'\n    ].join('')).prependTo(this.$editor);\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  initialize() {\n    if (this.options.disableDragAndDrop) {\n      // prevent default drop event\n      this.documentEventHandlers.onDrop = (e) => {\n        e.preventDefault();\n      };\n      // do not consider outside of dropzone\n      this.$eventListener = this.$dropzone;\n      this.$eventListener.on('drop', this.documentEventHandlers.onDrop);\n    } else {\n      this.attachDragAndDropEvent();\n    }\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  attachDragAndDropEvent() {\n    let collection = $();\n    const $dropzoneMessage = this.$dropzone.find('.note-dropzone-message');\n\n    this.documentEventHandlers.onDragenter = (e) => {\n      const isCodeview = this.context.invoke('codeview.isActivated');\n      const hasEditorSize = this.$editor.width() > 0 && this.$editor.height() > 0;\n      if (!isCodeview && !collection.length && hasEditorSize) {\n        this.$editor.addClass('dragover');\n        this.$dropzone.width(this.$editor.width());\n        this.$dropzone.height(this.$editor.height());\n        $dropzoneMessage.text(this.lang.image.dragImageHere);\n      }\n      collection = collection.add(e.target);\n    };\n\n    this.documentEventHandlers.onDragleave = (e) => {\n      collection = collection.not(e.target);\n      if (!collection.length) {\n        this.$editor.removeClass('dragover');\n      }\n    };\n\n    this.documentEventHandlers.onDrop = () => {\n      collection = $();\n      this.$editor.removeClass('dragover');\n    };\n\n    // show dropzone on dragenter when dragging a object to document\n    // -but only if the editor is visible, i.e. has a positive width and height\n    this.$eventListener.on('dragenter', this.documentEventHandlers.onDragenter)\n      .on('dragleave', this.documentEventHandlers.onDragleave)\n      .on('drop', this.documentEventHandlers.onDrop);\n\n    // change dropzone's message on hover.\n    this.$dropzone.on('dragenter', () => {\n      this.$dropzone.addClass('hover');\n      $dropzoneMessage.text(this.lang.image.dropImage);\n    }).on('dragleave', () => {\n      this.$dropzone.removeClass('hover');\n      $dropzoneMessage.text(this.lang.image.dragImageHere);\n    });\n\n    // attach dropImage\n    this.$dropzone.on('drop', (event) => {\n      const dataTransfer = event.originalEvent.dataTransfer;\n\n      // stop the browser from opening the dropped content\n      event.preventDefault();\n\n      if (dataTransfer && dataTransfer.files && dataTransfer.files.length) {\n        this.$editable.focus();\n        this.context.invoke('editor.insertImagesOrCallback', dataTransfer.files);\n      } else {\n        $.each(dataTransfer.types, (idx, type) => {\n          const content = dataTransfer.getData(type);\n\n          if (type.toLowerCase().indexOf('text') > -1) {\n            this.context.invoke('editor.pasteHTML', content);\n          } else {\n            $(content).each((idx, item) => {\n              this.context.invoke('editor.insertNode', item);\n            });\n          }\n        });\n      }\n    }).on('dragover', false); // prevent default dragover event\n  }\n\n  destroy() {\n    Object.keys(this.documentEventHandlers).forEach((key) => {\n      this.$eventListener.off(key.substr(2).toLowerCase(), this.documentEventHandlers[key]);\n    });\n    this.documentEventHandlers = {};\n  }\n}\n", "import env from '../core/env';\nimport dom from '../core/dom';\n\nlet CodeMirror;\nif (env.hasCodeMirror) {\n  if (env.isSupportAmd) {\n    require(['codemirror'], function(cm) {\n      CodeMirror = cm;\n    });\n  } else {\n    CodeMirror = window.CodeMirror;\n  }\n}\n\n/**\n * @class Codeview\n */\nexport default class CodeView {\n  constructor(context) {\n    this.context = context;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n    this.options = context.options;\n  }\n\n  sync() {\n    const isCodeview = this.isActivated();\n    if (isCodeview && env.hasCodeMirror) {\n      this.$codable.data('cmEditor').save();\n    }\n  }\n\n  /**\n   * @return {Boolean}\n   */\n  isActivated() {\n    return this.$editor.hasClass('codeview');\n  }\n\n  /**\n   * toggle codeview\n   */\n  toggle() {\n    if (this.isActivated()) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n    this.context.triggerEvent('codeview.toggled');\n  }\n\n  /**\n   * activate code view\n   */\n  activate() {\n    this.$codable.val(dom.html(this.$editable, this.options.prettifyHtml));\n    this.$codable.height(this.$editable.height());\n\n    this.context.invoke('toolbar.updateCodeview', true);\n    this.$editor.addClass('codeview');\n    this.$codable.focus();\n\n    // activate CodeMirror as codable\n    if (env.hasCodeMirror) {\n      const cmEditor = CodeMirror.fromTextArea(this.$codable[0], this.options.codemirror);\n\n      // CodeMirror TernServer\n      if (this.options.codemirror.tern) {\n        const server = new CodeMirror.TernServer(this.options.codemirror.tern);\n        cmEditor.ternServer = server;\n        cmEditor.on('cursorActivity', (cm) => {\n          server.updateArgHints(cm);\n        });\n      }\n\n      cmEditor.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', cmEditor.getValue(), event);\n      });\n\n      // CodeMirror hasn't Padding.\n      cmEditor.setSize(null, this.$editable.outerHeight());\n      this.$codable.data('cmEditor', cmEditor);\n    } else {\n      this.$codable.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', this.$codable.val(), event);\n      });\n    }\n  }\n\n  /**\n   * deactivate code view\n   */\n  deactivate() {\n    // deactivate CodeMirror as codable\n    if (env.hasCodeMirror) {\n      const cmEditor = this.$codable.data('cmEditor');\n      this.$codable.val(cmEditor.getValue());\n      cmEditor.toTextArea();\n    }\n\n    const value = dom.value(this.$codable, this.options.prettifyHtml) || dom.emptyPara;\n    const isChange = this.$editable.html() !== value;\n\n    this.$editable.html(value);\n    this.$editable.height(this.options.height ? this.$codable.height() : 'auto');\n    this.$editor.removeClass('codeview');\n\n    if (isChange) {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }\n\n    this.$editable.focus();\n\n    this.context.invoke('toolbar.updateCodeview', false);\n  }\n\n  destroy() {\n    if (this.isActivated()) {\n      this.deactivate();\n    }\n  }\n}\n", "import $ from 'jquery';\nconst EDITABLE_PADDING = 24;\n\nexport default class Statusbar {\n  constructor(context) {\n    this.$document = $(document);\n    this.$statusbar = context.layoutInfo.statusbar;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n  }\n\n  initialize() {\n    if (this.options.airMode || this.options.disableResizeEditor) {\n      this.destroy();\n      return;\n    }\n\n    this.$statusbar.on('mousedown', (event) => {\n      event.preventDefault();\n      event.stopPropagation();\n\n      const editableTop = this.$editable.offset().top - this.$document.scrollTop();\n      const onMouseMove = (event) => {\n        let height = event.clientY - (editableTop + EDITABLE_PADDING);\n\n        height = (this.options.minheight > 0) ? Math.max(height, this.options.minheight) : height;\n        height = (this.options.maxHeight > 0) ? Math.min(height, this.options.maxHeight) : height;\n\n        this.$editable.height(height);\n      };\n\n      this.$document.on('mousemove', onMouseMove).one('mouseup', () => {\n        this.$document.off('mousemove', onMouseMove);\n      });\n    });\n  }\n\n  destroy() {\n    this.$statusbar.off();\n    this.$statusbar.addClass('locked');\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Fullscreen {\n  constructor(context) {\n    this.context = context;\n\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n\n    this.$window = $(window);\n    this.$scrollbar = $('html, body');\n\n    this.onResize = () => {\n      this.resizeTo({\n        h: this.$window.height() - this.$toolbar.outerHeight()\n      });\n    };\n  }\n\n  resizeTo(size) {\n    this.$editable.css('height', size.h);\n    this.$codable.css('height', size.h);\n    if (this.$codable.data('cmeditor')) {\n      this.$codable.data('cmeditor').setsize(null, size.h);\n    }\n  }\n\n  /**\n   * toggle fullscreen\n   */\n  toggle() {\n    this.$editor.toggleClass('fullscreen');\n    if (this.isFullscreen()) {\n      this.$editable.data('orgHeight', this.$editable.css('height'));\n      this.$window.on('resize', this.onResize).trigger('resize');\n      this.$scrollbar.css('overflow', 'hidden');\n    } else {\n      this.$window.off('resize', this.onResize);\n      this.resizeTo({ h: this.$editable.data('orgHeight') });\n      this.$scrollbar.css('overflow', 'visible');\n    }\n\n    this.context.invoke('toolbar.updateFullscreen', this.isFullscreen());\n  }\n\n  isFullscreen() {\n    return this.$editor.hasClass('fullscreen');\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\n\nexport default class Handle {\n  constructor(context) {\n    this.context = context;\n    this.$document = $(document);\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        if (this.update(e.target)) {\n          e.preventDefault();\n        }\n      },\n      'summernote.keyup summernote.scroll summernote.change summernote.dialog.shown': () => {\n        this.update();\n      },\n      'summernote.disable': () => {\n        this.hide();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      }\n    };\n  }\n\n  initialize() {\n    this.$handle = $([\n      '<div class=\"note-handle\">',\n      '<div class=\"note-control-selection\">',\n      '<div class=\"note-control-selection-bg\"></div>',\n      '<div class=\"note-control-holder note-control-nw\"></div>',\n      '<div class=\"note-control-holder note-control-ne\"></div>',\n      '<div class=\"note-control-holder note-control-sw\"></div>',\n      '<div class=\"',\n      (this.options.disableResizeImage ? 'note-control-holder' : 'note-control-sizing'),\n      ' note-control-se\"></div>',\n      (this.options.disableResizeImage ? '' : '<div class=\"note-control-selection-info\"></div>'),\n      '</div>',\n      '</div>'\n    ].join('')).prependTo(this.$editingArea);\n\n    this.$handle.on('mousedown', (event) => {\n      if (dom.isControlSizing(event.target)) {\n        event.preventDefault();\n        event.stopPropagation();\n\n        const $target = this.$handle.find('.note-control-selection').data('target');\n        const posStart = $target.offset();\n        const scrollTop = this.$document.scrollTop();\n\n        const onMouseMove = (event) => {\n          this.context.invoke('editor.resizeTo', {\n            x: event.clientX - posStart.left,\n            y: event.clientY - (posStart.top - scrollTop)\n          }, $target, !event.shiftKey);\n\n          this.update($target[0]);\n        };\n\n        this.$document\n          .on('mousemove', onMouseMove)\n          .one('mouseup', (e) => {\n            e.preventDefault();\n            this.$document.off('mousemove', onMouseMove);\n            this.context.invoke('editor.afterCommand');\n          });\n\n        if (!$target.data('ratio')) { // original ratio.\n          $target.data('ratio', $target.height() / $target.width());\n        }\n      }\n    });\n\n    // Listen for scrolling on the handle overlay.\n    this.$handle.on('wheel', (e) => {\n      e.preventDefault();\n      this.update();\n    });\n  }\n\n  destroy() {\n    this.$handle.remove();\n  }\n\n  update(target) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isImage = dom.isImg(target);\n    const $selection = this.$handle.find('.note-control-selection');\n\n    this.context.invoke('imagePopover.update', target);\n\n    if (isImage) {\n      const $image = $(target);\n      const position = $image.position();\n      const pos = {\n        left: position.left + parseInt($image.css('marginLeft'), 10),\n        top: position.top + parseInt($image.css('marginTop'), 10)\n      };\n\n      // exclude margin\n      const imageSize = {\n        w: $image.outerWidth(false),\n        h: $image.outerHeight(false)\n      };\n\n      $selection.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n        width: imageSize.w,\n        height: imageSize.h\n      }).data('target', $image); // save current image element.\n\n      const origImageObj = new Image();\n      origImageObj.src = $image.attr('src');\n\n      const sizingText = imageSize.w + 'x' + imageSize.h + ' (' + this.lang.image.original + ': ' + origImageObj.width + 'x' + origImageObj.height + ')';\n      $selection.find('.note-control-selection-info').text(sizingText);\n      this.context.invoke('editor.saveTarget', target);\n    } else {\n      this.hide();\n    }\n\n    return isImage;\n  }\n\n  /**\n   * hide\n   *\n   * @param {jQuery} $handle\n   */\n  hide() {\n    this.context.invoke('editor.clearTarget');\n    this.$handle.children().hide();\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport key from '../core/key';\n\nconst defaultScheme = 'http://';\nconst linkPattern = /^([A-Za-z][A-Za-z0-9+-.]*\\:[\\/\\/]?|mailto:[A-Z0-9._%+-]+@)?(www\\.)?(.+)$/i;\n\nexport default class AutoLink {\n  constructor(context) {\n    this.context = context;\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      }\n    };\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n  }\n\n  destroy() {\n    this.lastWordRange = null;\n  }\n\n  replace() {\n    if (!this.lastWordRange) {\n      return;\n    }\n\n    const keyword = this.lastWordRange.toString();\n    const match = keyword.match(linkPattern);\n\n    if (match && (match[1] || match[2])) {\n      const link = match[1] ? keyword : defaultScheme + keyword;\n      const node = $('<a />').html(keyword).attr('href', link)[0];\n\n      this.lastWordRange.insertNode(node);\n      this.lastWordRange = null;\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  handleKeydown(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      this.lastWordRange = wordRange;\n    }\n  }\n\n  handleKeyup(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      this.replace();\n    }\n  }\n}\n", "import dom from '../core/dom';\n\n/**\n * textarea auto sync.\n */\nexport default class AutoSync {\n  constructor(context) {\n    this.$note = context.layoutInfo.note;\n    this.events = {\n      'summernote.change': () => {\n        this.$note.val(context.invoke('code'));\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return dom.isTextarea(this.$note[0]);\n  }\n}\n", "import $ from 'jquery';\nexport default class Placeholder {\n  constructor(context) {\n    this.context = context;\n\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n    this.events = {\n      'summernote.init summernote.change': () => {\n        this.update();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !!this.options.placeholder;\n  }\n\n  initialize() {\n    this.$placeholder = $('<div class=\"note-placeholder\">');\n    this.$placeholder.on('click', () => {\n      this.context.invoke('focus');\n    }).text(this.options.placeholder).prependTo(this.$editingArea);\n\n    this.update();\n  }\n\n  destroy() {\n    this.$placeholder.remove();\n  }\n\n  update() {\n    const isShow = !this.context.invoke('codeview.isActivated') && this.context.invoke('editor.isEmpty');\n    this.$placeholder.toggle(isShow);\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport env from '../core/env';\n\nexport default class Buttons {\n  constructor(context) {\n    this.ui = $.summernote.ui;\n    this.context = context;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.invertedKeyMap = func.invertObject(\n      this.options.keyMap[env.isMac ? 'mac' : 'pc']\n    );\n  }\n\n  representShortcut(editorMethod) {\n    let shortcut = this.invertedKeyMap[editorMethod];\n    if (!this.options.shortcuts || !shortcut) {\n      return '';\n    }\n\n    if (env.isMac) {\n      shortcut = shortcut.replace('CMD', '⌘').replace('SHIFT', '⇧');\n    }\n\n    shortcut = shortcut.replace('BACKSLASH', '\\\\')\n      .replace('SLASH', '/')\n      .replace('LEFTBRACKET', '[')\n      .replace('RIGHTBRACKET', ']');\n\n    return ' (' + shortcut + ')';\n  }\n\n  button(o) {\n    if (!this.options.tooltip && o.tooltip) {\n      delete o.tooltip;\n    }\n    o.container = this.options.container;\n    return this.ui.button(o);\n  }\n\n  initialize() {\n    this.addToolbarButtons();\n    this.addImagePopoverButtons();\n    this.addLinkPopoverButtons();\n    this.addTablePopoverButtons();\n    this.fontInstalledMap = {};\n  }\n\n  destroy() {\n    delete this.fontInstalledMap;\n  }\n\n  isFontInstalled(name) {\n    if (!this.fontInstalledMap.hasOwnProperty(name)) {\n      this.fontInstalledMap[name] = env.isFontInstalled(name) ||\n        lists.contains(this.options.fontNamesIgnoreCheck, name);\n    }\n\n    return this.fontInstalledMap[name];\n  }\n\n  isFontDeservedToAdd(name) {\n    const genericFamilies = ['sans-serif', 'serif', 'monospace', 'cursive', 'fantasy'];\n    name = name.toLowerCase();\n\n    return ((name !== '') && this.isFontInstalled(name) && ($.inArray(name, genericFamilies) === -1));\n  }\n\n  addToolbarButtons() {\n    this.context.memo('button.style', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            this.ui.icon(this.options.icons.magic), this.options\n          ),\n          tooltip: this.lang.style.style,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdown({\n          className: 'dropdown-style',\n          items: this.options.styleTags,\n          title: this.lang.style.style,\n          template: (item) => {\n            if (typeof item === 'string') {\n              item = { tag: item, title: (this.lang.style.hasOwnProperty(item) ? this.lang.style[item] : item) };\n            }\n\n            const tag = item.tag;\n            const title = item.title;\n            const style = item.style ? ' style=\"' + item.style + '\" ' : '';\n            const className = item.className ? ' class=\"' + item.className + '\"' : '';\n\n            return '<' + tag + style + className + '>' + title + '</' + tag + '>';\n          },\n          click: this.context.createInvokeHandler('editor.formatBlock')\n        })\n      ]).render();\n    });\n\n    for (let styleIdx = 0, styleLen = this.options.styleTags.length; styleIdx < styleLen; styleIdx++) {\n      const item = this.options.styleTags[styleIdx];\n\n      this.context.memo('button.style.' + item, () => {\n        return this.button({\n          className: 'note-btn-style-' + item,\n          contents: '<div data-value=\"' + item + '\">' + item.toUpperCase() + '</div>',\n          tooltip: this.lang.style[item],\n          click: this.context.createInvokeHandler('editor.formatBlock')\n        }).render();\n      });\n    }\n\n    this.context.memo('button.bold', () => {\n      return this.button({\n        className: 'note-btn-bold',\n        contents: this.ui.icon(this.options.icons.bold),\n        tooltip: this.lang.font.bold + this.representShortcut('bold'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.bold')\n      }).render();\n    });\n\n    this.context.memo('button.italic', () => {\n      return this.button({\n        className: 'note-btn-italic',\n        contents: this.ui.icon(this.options.icons.italic),\n        tooltip: this.lang.font.italic + this.representShortcut('italic'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.italic')\n      }).render();\n    });\n\n    this.context.memo('button.underline', () => {\n      return this.button({\n        className: 'note-btn-underline',\n        contents: this.ui.icon(this.options.icons.underline),\n        tooltip: this.lang.font.underline + this.representShortcut('underline'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.underline')\n      }).render();\n    });\n\n    this.context.memo('button.clear', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.eraser),\n        tooltip: this.lang.font.clear + this.representShortcut('removeFormat'),\n        click: this.context.createInvokeHandler('editor.removeFormat')\n      }).render();\n    });\n\n    this.context.memo('button.strikethrough', () => {\n      return this.button({\n        className: 'note-btn-strikethrough',\n        contents: this.ui.icon(this.options.icons.strikethrough),\n        tooltip: this.lang.font.strikethrough + this.representShortcut('strikethrough'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.strikethrough')\n      }).render();\n    });\n\n    this.context.memo('button.superscript', () => {\n      return this.button({\n        className: 'note-btn-superscript',\n        contents: this.ui.icon(this.options.icons.superscript),\n        tooltip: this.lang.font.superscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.superscript')\n      }).render();\n    });\n\n    this.context.memo('button.subscript', () => {\n      return this.button({\n        className: 'note-btn-subscript',\n        contents: this.ui.icon(this.options.icons.subscript),\n        tooltip: this.lang.font.subscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.subscript')\n      }).render();\n    });\n\n    this.context.memo('button.fontname', () => {\n      const styleInfo = this.context.invoke('editor.currentStyle');\n\n      // Add 'default' fonts into the fontnames array if not exist\n      $.each(styleInfo['font-family'].split(','), (idx, fontname) => {\n        fontname = fontname.trim().replace(/['\"]+/g, '');\n        if (this.isFontDeservedToAdd(fontname)) {\n          if ($.inArray(fontname, this.options.fontNames) === -1) {\n            this.options.fontNames.push(fontname);\n          }\n        }\n      });\n\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            '<span class=\"note-current-fontname\"/>', this.options\n          ),\n          tooltip: this.lang.font.name,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontname',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontNames.filter(this.isFontInstalled.bind(this)),\n          title: this.lang.font.name,\n          template: (item) => {\n            return '<span style=\"font-family: \\'' + item + '\\'\">' + item + '</span>';\n          },\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontName')\n        })\n      ]).render();\n    });\n\n    this.context.memo('button.fontsize', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('<span class=\"note-current-fontsize\"/>', this.options),\n          tooltip: this.lang.font.size,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontsize',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontSizes,\n          title: this.lang.font.size,\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontSize')\n        })\n      ]).render();\n    });\n\n    this.context.memo('button.color', () => {\n      return this.ui.buttonGroup({\n        className: 'note-color',\n        children: [\n          this.button({\n            className: 'note-current-color-button',\n            contents: this.ui.icon(this.options.icons.font + ' note-recent-color'),\n            tooltip: this.lang.color.recent,\n            click: (e) => {\n              const $button = $(e.currentTarget);\n              this.context.invoke('editor.color', {\n                backColor: $button.attr('data-backColor'),\n                foreColor: $button.attr('data-foreColor')\n              });\n            },\n            callback: ($button) => {\n              const $recentColor = $button.find('.note-recent-color');\n              $recentColor.css('background-color', '#FFFF00');\n              $button.attr('data-backColor', '#FFFF00');\n            }\n          }),\n          this.button({\n            className: 'dropdown-toggle',\n            contents: this.ui.dropdownButtonContents('', this.options),\n            tooltip: this.lang.color.more,\n            data: {\n              toggle: 'dropdown'\n            }\n          }),\n          this.ui.dropdown({\n            items: [\n              '<div class=\"note-palette\">',\n              '  <div class=\"note-palette-title\">' + this.lang.color.background + '</div>',\n              '  <div>',\n              '    <button type=\"button\" class=\"note-color-reset btn btn-light\" data-event=\"backColor\" data-value=\"inherit\">',\n              this.lang.color.transparent,\n              '    </button>',\n              '  </div>',\n              '  <div class=\"note-holder\" data-event=\"backColor\"/>',\n              '</div>',\n              '<div class=\"note-palette\">',\n              '  <div class=\"note-palette-title\">' + this.lang.color.foreground + '</div>',\n              '  <div>',\n              '    <button type=\"button\" class=\"note-color-reset btn btn-light\" data-event=\"removeFormat\" data-value=\"foreColor\">',\n              this.lang.color.resetToDefault,\n              '    </button>',\n              '  </div>',\n              '  <div class=\"note-holder\" data-event=\"foreColor\"/>',\n              '</div>'\n            ].join(''),\n            callback: ($dropdown) => {\n              $dropdown.find('.note-holder').each((idx, item) => {\n                const $holder = $(item);\n                $holder.append(this.ui.palette({\n                  colors: this.options.colors,\n                  colorsName: this.options.colorsName,\n                  eventName: $holder.data('event'),\n                  container: this.options.container,\n                  tooltip: this.options.tooltip\n                }).render());\n              });\n            },\n            click: (event) => {\n              const $button = $(event.target);\n              const eventName = $button.data('event');\n              const value = $button.data('value');\n\n              if (eventName && value) {\n                const key = eventName === 'backColor' ? 'background-color' : 'color';\n                const $color = $button.closest('.note-color').find('.note-recent-color');\n                const $currentButton = $button.closest('.note-color').find('.note-current-color-button');\n\n                $color.css(key, value);\n                $currentButton.attr('data-' + eventName, value);\n                this.context.invoke('editor.' + eventName, value);\n              }\n            }\n          })\n        ]\n      }).render();\n    });\n\n    this.context.memo('button.ul', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unorderedlist),\n        tooltip: this.lang.lists.unordered + this.representShortcut('insertUnorderedList'),\n        click: this.context.createInvokeHandler('editor.insertUnorderedList')\n      }).render();\n    });\n\n    this.context.memo('button.ol', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.orderedlist),\n        tooltip: this.lang.lists.ordered + this.representShortcut('insertOrderedList'),\n        click: this.context.createInvokeHandler('editor.insertOrderedList')\n      }).render();\n    });\n\n    const justifyLeft = this.button({\n      contents: this.ui.icon(this.options.icons.alignLeft),\n      tooltip: this.lang.paragraph.left + this.representShortcut('justifyLeft'),\n      click: this.context.createInvokeHandler('editor.justifyLeft')\n    });\n\n    const justifyCenter = this.button({\n      contents: this.ui.icon(this.options.icons.alignCenter),\n      tooltip: this.lang.paragraph.center + this.representShortcut('justifyCenter'),\n      click: this.context.createInvokeHandler('editor.justifyCenter')\n    });\n\n    const justifyRight = this.button({\n      contents: this.ui.icon(this.options.icons.alignRight),\n      tooltip: this.lang.paragraph.right + this.representShortcut('justifyRight'),\n      click: this.context.createInvokeHandler('editor.justifyRight')\n    });\n\n    const justifyFull = this.button({\n      contents: this.ui.icon(this.options.icons.alignJustify),\n      tooltip: this.lang.paragraph.justify + this.representShortcut('justifyFull'),\n      click: this.context.createInvokeHandler('editor.justifyFull')\n    });\n\n    const outdent = this.button({\n      contents: this.ui.icon(this.options.icons.outdent),\n      tooltip: this.lang.paragraph.outdent + this.representShortcut('outdent'),\n      click: this.context.createInvokeHandler('editor.outdent')\n    });\n\n    const indent = this.button({\n      contents: this.ui.icon(this.options.icons.indent),\n      tooltip: this.lang.paragraph.indent + this.representShortcut('indent'),\n      click: this.context.createInvokeHandler('editor.indent')\n    });\n\n    this.context.memo('button.justifyLeft', func.invoke(justifyLeft, 'render'));\n    this.context.memo('button.justifyCenter', func.invoke(justifyCenter, 'render'));\n    this.context.memo('button.justifyRight', func.invoke(justifyRight, 'render'));\n    this.context.memo('button.justifyFull', func.invoke(justifyFull, 'render'));\n    this.context.memo('button.outdent', func.invoke(outdent, 'render'));\n    this.context.memo('button.indent', func.invoke(indent, 'render'));\n\n    this.context.memo('button.paragraph', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.alignLeft), this.options),\n          tooltip: this.lang.paragraph.paragraph,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdown([\n          this.ui.buttonGroup({\n            className: 'note-align',\n            children: [justifyLeft, justifyCenter, justifyRight, justifyFull]\n          }),\n          this.ui.buttonGroup({\n            className: 'note-list',\n            children: [outdent, indent]\n          })\n        ])\n      ]).render();\n    });\n\n    this.context.memo('button.height', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.textHeight), this.options),\n          tooltip: this.lang.font.height,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdownCheck({\n          items: this.options.lineHeights,\n          checkClassName: this.options.icons.menuCheck,\n          className: 'dropdown-line-height',\n          title: this.lang.font.height,\n          click: this.context.createInvokeHandler('editor.lineHeight')\n        })\n      ]).render();\n    });\n\n    this.context.memo('button.table', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.table), this.options),\n          tooltip: this.lang.table.table,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdown({\n          title: this.lang.table.table,\n          className: 'note-table',\n          items: [\n            '<div class=\"note-dimension-picker\">',\n            '  <div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"/>',\n            '  <div class=\"note-dimension-picker-highlighted\"/>',\n            '  <div class=\"note-dimension-picker-unhighlighted\"/>',\n            '</div>',\n            '<div class=\"note-dimension-display\">1 x 1</div>'\n          ].join('')\n        })\n      ], {\n        callback: ($node) => {\n          const $catcher = $node.find('.note-dimension-picker-mousecatcher');\n          $catcher.css({\n            width: this.options.insertTableMaxSize.col + 'em',\n            height: this.options.insertTableMaxSize.row + 'em'\n          }).mousedown(this.context.createInvokeHandler('editor.insertTable'))\n            .on('mousemove', this.tableMoveHandler.bind(this));\n        }\n      }).render();\n    });\n\n    this.context.memo('button.link', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.link + this.representShortcut('linkDialog.show'),\n        click: this.context.createInvokeHandler('linkDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.picture', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.picture),\n        tooltip: this.lang.image.image,\n        click: this.context.createInvokeHandler('imageDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.video', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.video),\n        tooltip: this.lang.video.video,\n        click: this.context.createInvokeHandler('videoDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.hr', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.minus),\n        tooltip: this.lang.hr.insert + this.representShortcut('insertHorizontalRule'),\n        click: this.context.createInvokeHandler('editor.insertHorizontalRule')\n      }).render();\n    });\n\n    this.context.memo('button.fullscreen', () => {\n      return this.button({\n        className: 'btn-fullscreen',\n        contents: this.ui.icon(this.options.icons.arrowsAlt),\n        tooltip: this.lang.options.fullscreen,\n        click: this.context.createInvokeHandler('fullscreen.toggle')\n      }).render();\n    });\n\n    this.context.memo('button.codeview', () => {\n      return this.button({\n        className: 'btn-codeview',\n        contents: this.ui.icon(this.options.icons.code),\n        tooltip: this.lang.options.codeview,\n        click: this.context.createInvokeHandler('codeview.toggle')\n      }).render();\n    });\n\n    this.context.memo('button.redo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.redo),\n        tooltip: this.lang.history.redo + this.representShortcut('redo'),\n        click: this.context.createInvokeHandler('editor.redo')\n      }).render();\n    });\n\n    this.context.memo('button.undo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.undo),\n        tooltip: this.lang.history.undo + this.representShortcut('undo'),\n        click: this.context.createInvokeHandler('editor.undo')\n      }).render();\n    });\n\n    this.context.memo('button.help', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.question),\n        tooltip: this.lang.options.help,\n        click: this.context.createInvokeHandler('helpDialog.show')\n      }).render();\n    });\n  }\n\n  /**\n   * image : [\n   *   ['imagesize', ['imageSize100', 'imageSize50', 'imageSize25']],\n   *   ['float', ['floatLeft', 'floatRight', 'floatNone' ]],\n   *   ['remove', ['removeMedia']]\n   * ],\n   */\n  addImagePopoverButtons() {\n    // Image Size Buttons\n    this.context.memo('button.imageSize100', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">100%</span>',\n        tooltip: this.lang.image.resizeFull,\n        click: this.context.createInvokeHandler('editor.resize', '1')\n      }).render();\n    });\n    this.context.memo('button.imageSize50', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">50%</span>',\n        tooltip: this.lang.image.resizeHalf,\n        click: this.context.createInvokeHandler('editor.resize', '0.5')\n      }).render();\n    });\n    this.context.memo('button.imageSize25', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">25%</span>',\n        tooltip: this.lang.image.resizeQuarter,\n        click: this.context.createInvokeHandler('editor.resize', '0.25')\n      }).render();\n    });\n\n    // Float Buttons\n    this.context.memo('button.floatLeft', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.alignLeft),\n        tooltip: this.lang.image.floatLeft,\n        click: this.context.createInvokeHandler('editor.floatMe', 'left')\n      }).render();\n    });\n\n    this.context.memo('button.floatRight', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.alignRight),\n        tooltip: this.lang.image.floatRight,\n        click: this.context.createInvokeHandler('editor.floatMe', 'right')\n      }).render();\n    });\n\n    this.context.memo('button.floatNone', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.alignJustify),\n        tooltip: this.lang.image.floatNone,\n        click: this.context.createInvokeHandler('editor.floatMe', 'none')\n      }).render();\n    });\n\n    // Remove Buttons\n    this.context.memo('button.removeMedia', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.image.remove,\n        click: this.context.createInvokeHandler('editor.removeMedia')\n      }).render();\n    });\n  }\n\n  addLinkPopoverButtons() {\n    this.context.memo('button.linkDialogShow', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.edit,\n        click: this.context.createInvokeHandler('linkDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.unlink', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unlink),\n        tooltip: this.lang.link.unlink,\n        click: this.context.createInvokeHandler('editor.unlink')\n      }).render();\n    });\n  }\n\n  /**\n   * table : [\n   *  ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n   *  ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]\n   * ],\n   */\n  addTablePopoverButtons() {\n    this.context.memo('button.addRowUp', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowAbove),\n        tooltip: this.lang.table.addRowAbove,\n        click: this.context.createInvokeHandler('editor.addRow', 'top')\n      }).render();\n    });\n    this.context.memo('button.addRowDown', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowBelow),\n        tooltip: this.lang.table.addRowBelow,\n        click: this.context.createInvokeHandler('editor.addRow', 'bottom')\n      }).render();\n    });\n    this.context.memo('button.addColLeft', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colBefore),\n        tooltip: this.lang.table.addColLeft,\n        click: this.context.createInvokeHandler('editor.addCol', 'left')\n      }).render();\n    });\n    this.context.memo('button.addColRight', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colAfter),\n        tooltip: this.lang.table.addColRight,\n        click: this.context.createInvokeHandler('editor.addCol', 'right')\n      }).render();\n    });\n    this.context.memo('button.deleteRow', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowRemove),\n        tooltip: this.lang.table.delRow,\n        click: this.context.createInvokeHandler('editor.deleteRow')\n      }).render();\n    });\n    this.context.memo('button.deleteCol', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colRemove),\n        tooltip: this.lang.table.delCol,\n        click: this.context.createInvokeHandler('editor.deleteCol')\n      }).render();\n    });\n    this.context.memo('button.deleteTable', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.table.delTable,\n        click: this.context.createInvokeHandler('editor.deleteTable')\n      }).render();\n    });\n  }\n\n  build($container, groups) {\n    for (let groupIdx = 0, groupLen = groups.length; groupIdx < groupLen; groupIdx++) {\n      const group = groups[groupIdx];\n      const groupName = $.isArray(group) ? group[0] : group;\n      const buttons = $.isArray(group) ? ((group.length === 1) ? [group[0]] : group[1]) : [group];\n\n      const $group = this.ui.buttonGroup({\n        className: 'note-' + groupName\n      }).render();\n\n      for (let idx = 0, len = buttons.length; idx < len; idx++) {\n        const btn = this.context.memo('button.' + buttons[idx]);\n        if (btn) {\n          $group.append(typeof btn === 'function' ? btn(this.context) : btn);\n        }\n      }\n      $group.appendTo($container);\n    }\n  }\n\n  /**\n   * @param {jQuery} [$container]\n   */\n  updateCurrentStyle($container) {\n    const $cont = $container || this.$toolbar;\n\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    this.updateBtnStates($cont, {\n      '.note-btn-bold': () => {\n        return styleInfo['font-bold'] === 'bold';\n      },\n      '.note-btn-italic': () => {\n        return styleInfo['font-italic'] === 'italic';\n      },\n      '.note-btn-underline': () => {\n        return styleInfo['font-underline'] === 'underline';\n      },\n      '.note-btn-subscript': () => {\n        return styleInfo['font-subscript'] === 'subscript';\n      },\n      '.note-btn-superscript': () => {\n        return styleInfo['font-superscript'] === 'superscript';\n      },\n      '.note-btn-strikethrough': () => {\n        return styleInfo['font-strikethrough'] === 'strikethrough';\n      }\n    });\n\n    if (styleInfo['font-family']) {\n      const fontNames = styleInfo['font-family'].split(',').map((name) => {\n        return name.replace(/[\\'\\\"]/g, '')\n          .replace(/\\s+$/, '')\n          .replace(/^\\s+/, '');\n      });\n      const fontName = lists.find(fontNames, this.isFontInstalled.bind(this));\n\n      $cont.find('.dropdown-fontname a').each((idx, item) => {\n        const $item = $(item);\n        // always compare string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontName + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontname').text(fontName).css('font-family', fontName);\n    }\n\n    if (styleInfo['font-size']) {\n      const fontSize = styleInfo['font-size'];\n      $cont.find('.dropdown-fontsize a').each((idx, item) => {\n        const $item = $(item);\n        // always compare with string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontSize + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontsize').text(fontSize);\n    }\n\n    if (styleInfo['line-height']) {\n      const lineHeight = styleInfo['line-height'];\n      $cont.find('.dropdown-line-height li a').each((idx, item) => {\n        // always compare with string to avoid creating another func.\n        const isChecked = ($(item).data('value') + '') === (lineHeight + '');\n        this.className = isChecked ? 'checked' : '';\n      });\n    }\n  }\n\n  updateBtnStates($container, infos) {\n    $.each(infos, (selector, pred) => {\n      this.ui.toggleBtnActive($container.find(selector), pred());\n    });\n  }\n\n  tableMoveHandler(event) {\n    const PX_PER_EM = 18;\n    const $picker = $(event.target.parentNode); // target is mousecatcher\n    const $dimensionDisplay = $picker.next();\n    const $catcher = $picker.find('.note-dimension-picker-mousecatcher');\n    const $highlighted = $picker.find('.note-dimension-picker-highlighted');\n    const $unhighlighted = $picker.find('.note-dimension-picker-unhighlighted');\n\n    let posOffset;\n    // HTML5 with jQuery - e.offsetX is undefined in Firefox\n    if (event.offsetX === undefined) {\n      const posCatcher = $(event.target).offset();\n      posOffset = {\n        x: event.pageX - posCatcher.left,\n        y: event.pageY - posCatcher.top\n      };\n    } else {\n      posOffset = {\n        x: event.offsetX,\n        y: event.offsetY\n      };\n    }\n\n    const dim = {\n      c: Math.ceil(posOffset.x / PX_PER_EM) || 1,\n      r: Math.ceil(posOffset.y / PX_PER_EM) || 1\n    };\n\n    $highlighted.css({ width: dim.c + 'em', height: dim.r + 'em' });\n    $catcher.data('value', dim.c + 'x' + dim.r);\n\n    if (dim.c > 3 && dim.c < this.options.insertTableMaxSize.col) {\n      $unhighlighted.css({ width: dim.c + 1 + 'em' });\n    }\n\n    if (dim.r > 3 && dim.r < this.options.insertTableMaxSize.row) {\n      $unhighlighted.css({ height: dim.r + 1 + 'em' });\n    }\n\n    $dimensionDisplay.html(dim.c + ' x ' + dim.r);\n  }\n}\n", "import $ from 'jquery';\nexport default class Toolbar {\n  constructor(context) {\n    this.context = context;\n\n    this.$window = $(window);\n    this.$document = $(document);\n\n    this.ui = $.summernote.ui;\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.options = context.options;\n\n    this.followScroll = this.followScroll.bind(this);\n  }\n\n  shouldInitialize() {\n    return !this.options.airMode;\n  }\n\n  initialize() {\n    this.options.toolbar = this.options.toolbar || [];\n\n    if (!this.options.toolbar.length) {\n      this.$toolbar.hide();\n    } else {\n      this.context.invoke('buttons.build', this.$toolbar, this.options.toolbar);\n    }\n\n    if (this.options.toolbarContainer) {\n      this.$toolbar.appendTo(this.options.toolbarContainer);\n    }\n\n    this.changeContainer(false);\n\n    this.$note.on('summernote.keyup summernote.mouseup summernote.change', () => {\n      this.context.invoke('buttons.updateCurrentStyle');\n    });\n\n    this.context.invoke('buttons.updateCurrentStyle');\n    if (this.options.followingToolbar) {\n      this.$window.on('scroll resize', this.followScroll);\n    }\n  }\n\n  destroy() {\n    this.$toolbar.children().remove();\n\n    if (this.options.followingToolbar) {\n      this.$window.off('scroll resize', this.followScroll);\n    }\n  }\n\n  followScroll() {\n    if (this.$editor.hasClass('fullscreen')) {\n      return false;\n    }\n\n    const $toolbarWrapper = this.$toolbar.parent('.note-toolbar-wrapper');\n    const editorHeight = this.$editor.outerHeight();\n    const editorWidth = this.$editor.width();\n\n    const toolbarHeight = this.$toolbar.height();\n    $toolbarWrapper.css({\n      height: toolbarHeight\n    });\n\n    // check if the web app is currently using another static bar\n    let otherBarHeight = 0;\n    if (this.options.otherStaticBar) {\n      otherBarHeight = $(this.options.otherStaticBar).outerHeight();\n    }\n\n    const currentOffset = this.$document.scrollTop();\n    const editorOffsetTop = this.$editor.offset().top;\n    const editorOffsetBottom = editorOffsetTop + editorHeight;\n    const activateOffset = editorOffsetTop - otherBarHeight;\n    const deactivateOffsetBottom = editorOffsetBottom - otherBarHeight - toolbarHeight;\n\n    if ((currentOffset > activateOffset) && (currentOffset < deactivateOffsetBottom)) {\n      this.$toolbar.css({\n        position: 'fixed',\n        top: otherBarHeight,\n        width: editorWidth\n      });\n    } else {\n      this.$toolbar.css({\n        position: 'relative',\n        top: 0,\n        width: '100%'\n      });\n    }\n  }\n\n  changeContainer(isFullscreen) {\n    if (isFullscreen) {\n      this.$toolbar.prependTo(this.$editor);\n    } else {\n      if (this.options.toolbarContainer) {\n        this.$toolbar.appendTo(this.options.toolbarContainer);\n      }\n    }\n  }\n\n  updateFullscreen(isFullscreen) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-fullscreen'), isFullscreen);\n\n    this.changeContainer(isFullscreen);\n  }\n\n  updateCodeview(isCodeview) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-codeview'), isCodeview);\n    if (isCodeview) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n  }\n\n  activate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.btn-codeview');\n    }\n    this.ui.toggleBtn($btn, true);\n  }\n\n  deactivate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.btn-codeview');\n    }\n    this.ui.toggleBtn($btn, false);\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class LinkDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    context.memo('help.linkDialog.show', this.options.langInfo.help['linkDialog.show']);\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    const body = [\n      '<div class=\"form-group note-form-group\">',\n      `<label class=\"note-form-label\">${this.lang.link.textToDisplay}</label>`,\n      '<input class=\"note-link-text form-control note-form-control  note-input\" type=\"text\" />',\n      '</div>',\n      '<div class=\"form-group note-form-group\">',\n      `<label class=\"note-form-label\">${this.lang.link.url}</label>`,\n      '<input class=\"note-link-url form-control note-form-control note-input\" type=\"text\" value=\"http://\" />',\n      '</div>',\n      !this.options.disableLinkTarget\n        ? $('<div/>').append(this.ui.checkbox({\n          id: 'sn-checkbox-open-in-new-window',\n          text: this.lang.link.openInNewWindow,\n          checked: true\n        }).render()).html()\n        : ''\n    ].join('');\n\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-link-btn';\n    const footer = `<button type=\"submit\" href=\"#\" class=\"${buttonClass}\" disabled>${this.lang.link.insert}</button>`;\n\n    this.$dialog = this.ui.dialog({\n      className: 'link-dialog',\n      title: this.lang.link.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  /**\n   * toggle update button\n   */\n  toggleLinkBtn($linkBtn, $linkText, $linkUrl) {\n    this.ui.toggleBtn($linkBtn, $linkText.val() && $linkUrl.val());\n  }\n\n  /**\n   * Show link dialog and set event handlers on dialog controls.\n   *\n   * @param {Object} linkInfo\n   * @return {Promise}\n   */\n  showLinkDialog(linkInfo) {\n    return $.Deferred((deferred) => {\n      const $linkText = this.$dialog.find('.note-link-text');\n      const $linkUrl = this.$dialog.find('.note-link-url');\n      const $linkBtn = this.$dialog.find('.note-link-btn');\n      const $openInNewWindow = this.$dialog.find('input[type=checkbox]');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // if no url was given, copy text to url\n        if (!linkInfo.url) {\n          linkInfo.url = linkInfo.text;\n        }\n\n        $linkText.val(linkInfo.text);\n\n        const handleLinkTextUpdate = () => {\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n          // if linktext was modified by keyup,\n          // stop cloning text from linkUrl\n          linkInfo.text = $linkText.val();\n        };\n\n        $linkText.on('input', handleLinkTextUpdate).on('paste', () => {\n          setTimeout(handleLinkTextUpdate, 0);\n        });\n\n        const handleLinkUrlUpdate = () => {\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n          // display same link on `Text to display` input\n          // when create a new link\n          if (!linkInfo.text) {\n            $linkText.val($linkUrl.val());\n          }\n        };\n\n        $linkUrl.on('input', handleLinkUrlUpdate).on('paste', () => {\n          setTimeout(handleLinkUrlUpdate, 0);\n        }).val(linkInfo.url);\n\n        if (!env.isSupportTouch) {\n          $linkUrl.trigger('focus');\n        }\n\n        this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        this.bindEnterKey($linkUrl, $linkBtn);\n        this.bindEnterKey($linkText, $linkBtn);\n\n        const isChecked = linkInfo.isNewWindow !== undefined\n          ? linkInfo.isNewWindow : this.context.options.linkTargetBlank;\n\n        $openInNewWindow.prop('checked', isChecked);\n\n        $linkBtn.one('click', (event) => {\n          event.preventDefault();\n\n          deferred.resolve({\n            range: linkInfo.range,\n            url: $linkUrl.val(),\n            text: $linkText.val(),\n            isNewWindow: $openInNewWindow.is(':checked')\n          });\n          this.ui.hideDialog(this.$dialog);\n        });\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        // detach events\n        $linkText.off('input paste keypress');\n        $linkUrl.off('input paste keypress');\n        $linkBtn.off('click');\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  /**\n   * @param {Object} layoutInfo\n   */\n  show() {\n    const linkInfo = this.context.invoke('editor.getLinkInfo');\n\n    this.context.invoke('editor.saveRange');\n    this.showLinkDialog(linkInfo).then((linkInfo) => {\n      this.context.invoke('editor.restoreRange');\n      this.context.invoke('editor.createLink', linkInfo);\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class LinkPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.keyup summernote.mouseup summernote.change summernote.scroll': () => {\n        this.update();\n      },\n      'summernote.disable summernote.dialog.shown': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.link);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-link-popover',\n      callback: ($node) => {\n        const $content = $node.find('.popover-content,.note-popover-content');\n        $content.prepend('<span><a target=\"_blank\"></a>&nbsp;</span>');\n      }\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.link);\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update() {\n    // Prevent focusing on editable when invoke('code') is executed\n    if (!this.context.invoke('editor.hasFocus')) {\n      this.hide();\n      return;\n    }\n\n    const rng = this.context.invoke('editor.createRange');\n    if (rng.isCollapsed() && rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      const href = $(anchor).attr('href');\n      this.$popover.find('a').attr('href', href).html(href);\n\n      const pos = dom.posFromPlaceholder(anchor);\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class ImageDialog {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    let imageLimitation = '';\n    if (this.options.maximumImageFileSize) {\n      const unit = Math.floor(Math.log(this.options.maximumImageFileSize) / Math.log(1024));\n      const readableSize = (this.options.maximumImageFileSize / Math.pow(1024, unit)).toFixed(2) * 1 +\n                         ' ' + ' KMGTP'[unit] + 'B';\n      imageLimitation = `<small>${this.lang.image.maximumFileSize + ' : ' + readableSize}</small>`;\n    }\n\n    const body = [\n      '<div class=\"form-group note-form-group note-group-select-from-files\">',\n      '<label class=\"note-form-label\">' + this.lang.image.selectFromFiles + '</label>',\n      '<input class=\"note-image-input note-form-control note-input\" ',\n      ' type=\"file\" name=\"files\" accept=\"image/*\" multiple=\"multiple\" />',\n      imageLimitation,\n      '</div>',\n      '<div class=\"form-group note-group-image-url\" style=\"overflow:auto;\">',\n      '<label class=\"note-form-label\">' + this.lang.image.url + '</label>',\n      '<input class=\"note-image-url form-control note-form-control note-input ',\n      ' col-md-12\" type=\"text\" />',\n      '</div>'\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-image-btn';\n    const footer = `<button type=\"submit\" href=\"#\" class=\"${buttonClass}\" disabled>${this.lang.image.insert}</button>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.image.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showImageDialog().then((data) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      if (typeof data === 'string') { // image url\n        this.context.invoke('editor.insertImage', data);\n      } else { // array of files\n        this.context.invoke('editor.insertImagesOrCallback', data);\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show image dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showImageDialog() {\n    return $.Deferred((deferred) => {\n      const $imageInput = this.$dialog.find('.note-image-input');\n      const $imageUrl = this.$dialog.find('.note-image-url');\n      const $imageBtn = this.$dialog.find('.note-image-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // Cloning imageInput to clear element.\n        $imageInput.replaceWith($imageInput.clone().on('change', (event) => {\n          deferred.resolve(event.target.files || event.target.value);\n        }).val(''));\n\n        $imageBtn.click((event) => {\n          event.preventDefault();\n\n          deferred.resolve($imageUrl.val());\n        });\n\n        $imageUrl.on('keyup paste', () => {\n          const url = $imageUrl.val();\n          this.ui.toggleBtn($imageBtn, url);\n        }).val('');\n\n        if (!env.isSupportTouch) {\n          $imageUrl.trigger('focus');\n        }\n        this.bindEnterKey($imageUrl, $imageBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $imageInput.off('change');\n        $imageUrl.off('keyup paste keypress');\n        $imageBtn.off('click');\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\n/**\n * Image popover module\n *  mouse events that show/hide popover will be handled by Handle.js.\n *  Handle.js will receive the events and invoke 'imagePopover.update'.\n */\nexport default class ImagePopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n\n    this.editable = context.layoutInfo.editable[0];\n    this.options = context.options;\n\n    this.events = {\n      'summernote.disable': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.image);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-image-popover'\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n    this.context.invoke('buttons.build', $content, this.options.popover.image);\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target) {\n    if (dom.isImg(target)) {\n      const pos = dom.posFromPlaceholder(target);\n      const posEditor = dom.posFromPlaceholder(this.editable);\n      this.$popover.css({\n        display: 'block',\n        left: this.options.popatmouse ? event.pageX - 20 : pos.left,\n        top: this.options.popatmouse ? event.pageY : Math.min(pos.top, posEditor.top)\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class TablePopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        this.update(e.target);\n      },\n      'summernote.keyup summernote.scroll summernote.change': () => {\n        this.update();\n      },\n      'summernote.disable': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.table);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-table-popover'\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.table);\n\n    // [workaround] Disable Firefox's default table editor\n    if (env.isFF) {\n      document.execCommand('enableInlineTableEditing', false, false);\n    }\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isCell = dom.isCell(target);\n\n    if (isCell) {\n      const pos = dom.posFromPlaceholder(target);\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top\n      });\n    } else {\n      this.hide();\n    }\n\n    return isCell;\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class VideoDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    const body = [\n      '<div class=\"form-group note-form-group row-fluid\">',\n      `<label class=\"note-form-label\">${this.lang.video.url} <small class=\"text-muted\">${this.lang.video.providers}</small></label>`,\n      '<input class=\"note-video-url form-control note-form-control note-input\" type=\"text\" />',\n      '</div>'\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-video-btn';\n    const footer = `<button type=\"submit\" href=\"#\" class=\"${buttonClass}\" disabled>${this.lang.video.insert}</button>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.video.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  createVideoNode(url) {\n    // video url patterns(youtube, instagram, vimeo, dailymotion, youku, mp4, ogg, webm)\n    const ytRegExp = /^(?:https?:\\/\\/)?(?:www\\.)?(?:youtu\\.be\\/|youtube\\.com\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=))((\\w|-){11})(?:\\S+)?$/;\n    const ytMatch = url.match(ytRegExp);\n\n    const igRegExp = /(?:www\\.|\\/\\/)instagram\\.com\\/p\\/(.[a-zA-Z0-9_-]*)/;\n    const igMatch = url.match(igRegExp);\n\n    const vRegExp = /\\/\\/vine\\.co\\/v\\/([a-zA-Z0-9]+)/;\n    const vMatch = url.match(vRegExp);\n\n    const vimRegExp = /\\/\\/(player\\.)?vimeo\\.com\\/([a-z]*\\/)*(\\d+)[?]?.*/;\n    const vimMatch = url.match(vimRegExp);\n\n    const dmRegExp = /.+dailymotion.com\\/(video|hub)\\/([^_]+)[^#]*(#video=([^_&]+))?/;\n    const dmMatch = url.match(dmRegExp);\n\n    const youkuRegExp = /\\/\\/v\\.youku\\.com\\/v_show\\/id_(\\w+)=*\\.html/;\n    const youkuMatch = url.match(youkuRegExp);\n\n    const qqRegExp = /\\/\\/v\\.qq\\.com.*?vid=(.+)/;\n    const qqMatch = url.match(qqRegExp);\n\n    const qqRegExp2 = /\\/\\/v\\.qq\\.com\\/x?\\/?(page|cover).*?\\/([^\\/]+)\\.html\\??.*/;\n    const qqMatch2 = url.match(qqRegExp2);\n\n    const mp4RegExp = /^.+.(mp4|m4v)$/;\n    const mp4Match = url.match(mp4RegExp);\n\n    const oggRegExp = /^.+.(ogg|ogv)$/;\n    const oggMatch = url.match(oggRegExp);\n\n    const webmRegExp = /^.+.(webm)$/;\n    const webmMatch = url.match(webmRegExp);\n\n    let $video;\n    if (ytMatch && ytMatch[1].length === 11) {\n      const youtubeId = ytMatch[1];\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.youtube.com/embed/' + youtubeId)\n        .attr('width', '640').attr('height', '360');\n    } else if (igMatch && igMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', 'https://instagram.com/p/' + igMatch[1] + '/embed/')\n        .attr('width', '612').attr('height', '710')\n        .attr('scrolling', 'no')\n        .attr('allowtransparency', 'true');\n    } else if (vMatch && vMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', vMatch[0] + '/embed/simple')\n        .attr('width', '600').attr('height', '600')\n        .attr('class', 'vine-embed');\n    } else if (vimMatch && vimMatch[3].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('src', '//player.vimeo.com/video/' + vimMatch[3])\n        .attr('width', '640').attr('height', '360');\n    } else if (dmMatch && dmMatch[2].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.dailymotion.com/embed/video/' + dmMatch[2])\n        .attr('width', '640').attr('height', '360');\n    } else if (youkuMatch && youkuMatch[1].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '498')\n        .attr('width', '510')\n        .attr('src', '//player.youku.com/embed/' + youkuMatch[1]);\n    } else if ((qqMatch && qqMatch[1].length) || (qqMatch2 && qqMatch2[2].length)) {\n      const vid = ((qqMatch && qqMatch[1].length) ? qqMatch[1] : qqMatch2[2]);\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '310')\n        .attr('width', '500')\n        .attr('src', 'http://v.qq.com/iframe/player.html?vid=' + vid + '&amp;auto=0');\n    } else if (mp4Match || oggMatch || webmMatch) {\n      $video = $('<video controls>')\n        .attr('src', url)\n        .attr('width', '640').attr('height', '360');\n    } else {\n      // this is not a known video link. Now what, Cat? Now what?\n      return false;\n    }\n\n    $video.addClass('note-video-clip');\n\n    return $video[0];\n  }\n\n  show() {\n    const text = this.context.invoke('editor.getSelectedText');\n    this.context.invoke('editor.saveRange');\n    this.showVideoDialog(text).then((url) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      // build node\n      const $node = this.createVideoNode(url);\n\n      if ($node) {\n        // insert video node\n        this.context.invoke('editor.insertNode', $node);\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show image dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showVideoDialog(text) {\n    return $.Deferred((deferred) => {\n      const $videoUrl = this.$dialog.find('.note-video-url');\n      const $videoBtn = this.$dialog.find('.note-video-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        $videoUrl.val(text).on('input', () => {\n          this.ui.toggleBtn($videoBtn, $videoUrl.val());\n        });\n\n        if (!env.isSupportTouch) {\n          $videoUrl.trigger('focus');\n        }\n\n        $videoBtn.click((event) => {\n          event.preventDefault();\n\n          deferred.resolve($videoUrl.val());\n        });\n\n        this.bindEnterKey($videoUrl, $videoBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $videoUrl.off('input');\n        $videoBtn.off('click');\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\n\nexport default class HelpDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    const body = [\n      '<p class=\"text-center\">',\n      '<a href=\"http://summernote.org/\" target=\"_blank\">Summernote @@VERSION@@</a> · ',\n      '<a href=\"https://github.com/summernote/summernote\" target=\"_blank\">Project</a> · ',\n      '<a href=\"https://github.com/summernote/summernote/issues\" target=\"_blank\">Issues</a>',\n      '</p>'\n    ].join('');\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.options.help,\n      fade: this.options.dialogsFade,\n      body: this.createShortcutList(),\n      footer: body,\n      callback: ($node) => {\n        $node.find('.modal-body,.note-modal-body').css({\n          'max-height': 300,\n          'overflow': 'scroll'\n        });\n      }\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  createShortcutList() {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    return Object.keys(keyMap).map((key) => {\n      const command = keyMap[key];\n      const $row = $('<div><div class=\"help-list-item\"/></div>');\n      $row.append($('<label><kbd>' + key + '</kdb></label>').css({\n        'width': 180,\n        'margin-right': 10\n      })).append($('<span/>').html(this.context.memo('help.' + command) || command));\n      return $row.html();\n    }).join('');\n  }\n\n  /**\n   * show help dialog\n   *\n   * @return {Promise}\n   */\n  showHelpDialog() {\n    return $.Deferred((deferred) => {\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n        deferred.resolve();\n      });\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showHelpDialog().then(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nconst AIR_MODE_POPOVER_X_OFFSET = 20;\n\nexport default class AirPopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.keyup summernote.mouseup summernote.scroll': () => {\n        this.update();\n      },\n      'summernote.disable summernote.change summernote.dialog.shown': () => {\n        this.hide();\n      },\n      'summernote.focusout': (we, e) => {\n        // [workaround] Firefox doesn't support relatedTarget on focusout\n        //  - Ignore hide action on focus out in FF.\n        if (env.isFF) {\n          return;\n        }\n\n        if (!e.relatedTarget || !dom.ancestor(e.relatedTarget, func.eq(this.$popover[0]))) {\n          this.hide();\n        }\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return this.options.airMode && !lists.isEmpty(this.options.popover.air);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-air-popover'\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.air);\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update() {\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    if (styleInfo.range && !styleInfo.range.isCollapsed()) {\n      const rect = lists.last(styleInfo.range.getClientRects());\n      if (rect) {\n        const bnd = func.rect2bnd(rect);\n        this.$popover.css({\n          display: 'block',\n          left: Math.max(bnd.left + bnd.width / 2, 0) - AIR_MODE_POPOVER_X_OFFSET,\n          top: bnd.top + bnd.height\n        });\n        this.context.invoke('buttons.updateCurrentStyle', this.$popover);\n      }\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport key from '../core/key';\n\nconst POPOVER_DIST = 5;\n\nexport default class HintPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.hint = this.options.hint || [];\n    this.direction = this.options.hintDirection || 'bottom';\n    this.hints = $.isArray(this.hint) ? this.hint : [this.hint];\n\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n      'summernote.disable summernote.dialog.shown': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return this.hints.length > 0;\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n    this.$popover = this.ui.popover({\n      className: 'note-hint-popover',\n      hideArrow: true,\n      direction: ''\n    }).render().appendTo(this.options.container);\n\n    this.$popover.hide();\n    this.$content = this.$popover.find('.popover-content,.note-popover-content');\n    this.$content.on('click', '.note-hint-item', () => {\n      this.$content.find('.active').removeClass('active');\n      $(this).addClass('active');\n      this.replace();\n    });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  selectItem($item) {\n    this.$content.find('.active').removeClass('active');\n    $item.addClass('active');\n\n    this.$content[0].scrollTop = $item[0].offsetTop - (this.$content.innerHeight() / 2);\n  }\n\n  moveDown() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $next = $current.next();\n\n    if ($next.length) {\n      this.selectItem($next);\n    } else {\n      let $nextGroup = $current.parent().next();\n\n      if (!$nextGroup.length) {\n        $nextGroup = this.$content.find('.note-hint-group').first();\n      }\n\n      this.selectItem($nextGroup.find('.note-hint-item').first());\n    }\n  }\n\n  moveUp() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $prev = $current.prev();\n\n    if ($prev.length) {\n      this.selectItem($prev);\n    } else {\n      let $prevGroup = $current.parent().prev();\n\n      if (!$prevGroup.length) {\n        $prevGroup = this.$content.find('.note-hint-group').last();\n      }\n\n      this.selectItem($prevGroup.find('.note-hint-item').last());\n    }\n  }\n\n  replace() {\n    const $item = this.$content.find('.note-hint-item.active');\n\n    if ($item.length) {\n      const node = this.nodeFromItem($item);\n      // XXX: consider to move codes to editor for recording redo/undo.\n      this.lastWordRange.insertNode(node);\n      range.createFromNode(node).collapse().select();\n\n      this.lastWordRange = null;\n      this.hide();\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable[0]);\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  nodeFromItem($item) {\n    const hint = this.hints[$item.data('index')];\n    const item = $item.data('item');\n    let node = hint.content ? hint.content(item) : item;\n    if (typeof node === 'string') {\n      node = dom.createText(node);\n    }\n    return node;\n  }\n\n  createItemTemplates(hintIdx, items) {\n    const hint = this.hints[hintIdx];\n    return items.map((item, idx) => {\n      const $item = $('<div class=\"note-hint-item\"/>');\n      $item.append(hint.template ? hint.template(item) : item + '');\n      $item.data({\n        'index': hintIdx,\n        'item': item\n      });\n      return $item;\n    });\n  }\n\n  handleKeydown(e) {\n    if (!this.$popover.is(':visible')) {\n      return;\n    }\n\n    if (e.keyCode === key.code.ENTER) {\n      e.preventDefault();\n      this.replace();\n    } else if (e.keyCode === key.code.UP) {\n      e.preventDefault();\n      this.moveUp();\n    } else if (e.keyCode === key.code.DOWN) {\n      e.preventDefault();\n      this.moveDown();\n    }\n  }\n\n  searchKeyword(index, keyword, callback) {\n    const hint = this.hints[index];\n    if (hint && hint.match.test(keyword) && hint.search) {\n      const matches = hint.match.exec(keyword);\n      hint.search(matches[1], callback);\n    } else {\n      callback();\n    }\n  }\n\n  createGroup(idx, keyword) {\n    const $group = $('<div class=\"note-hint-group note-hint-group-' + idx + '\"/>');\n    this.searchKeyword(idx, keyword, (items) => {\n      items = items || [];\n      if (items.length) {\n        $group.html(this.createItemTemplates(idx, items));\n        this.show();\n      }\n    });\n\n    return $group;\n  }\n\n  handleKeyup(e) {\n    if (!lists.contains([key.code.ENTER, key.code.UP, key.code.DOWN], e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      const keyword = wordRange.toString();\n      if (this.hints.length && keyword) {\n        this.$content.empty();\n\n        const bnd = func.rect2bnd(lists.last(wordRange.getClientRects()));\n        if (bnd) {\n          this.$popover.hide();\n          this.lastWordRange = wordRange;\n          this.hints.forEach((hint, idx) => {\n            if (hint.match.test(keyword)) {\n              this.createGroup(idx, keyword).appendTo(this.$content);\n            }\n          });\n          // select first .note-hint-item\n          this.$content.find('.note-hint-item:first').addClass('active');\n\n          // set position for popover after group is created\n          if (this.direction === 'top') {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top - this.$popover.outerHeight() - POPOVER_DIST\n            });\n          } else {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top + bnd.height + POPOVER_DIST\n            });\n          }\n        }\n      } else {\n        this.hide();\n      }\n    }\n  }\n\n  show() {\n    this.$popover.show();\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport func from './core/func';\nimport lists from './core/lists';\nimport dom from './core/dom';\n\nexport default class Context {\n  /**\n   * @param {jQuery} $note\n   * @param {Object} options\n   */\n  constructor($note, options) {\n    this.ui = $.summernote.ui;\n    this.$note = $note;\n\n    this.memos = {};\n    this.modules = {};\n    this.layoutInfo = {};\n    this.options = options;\n\n    this.initialize();\n  }\n\n  /**\n   * create layout and initialize modules and other resources\n   */\n  initialize() {\n    this.layoutInfo = this.ui.createLayout(this.$note, this.options);\n    this._initialize();\n    this.$note.hide();\n    return this;\n  }\n\n  /**\n   * destroy modules and other resources and remove layout\n   */\n  destroy() {\n    this._destroy();\n    this.$note.removeData('summernote');\n    this.ui.removeLayout(this.$note, this.layoutInfo);\n  }\n\n  /**\n   * destory modules and other resources and initialize it again\n   */\n  reset() {\n    const disabled = this.isDisabled();\n    this.code(dom.emptyPara);\n    this._destroy();\n    this._initialize();\n\n    if (disabled) {\n      this.disable();\n    }\n  }\n\n  _initialize() {\n    // add optional buttons\n    const buttons = $.extend({}, this.options.buttons);\n    Object.keys(buttons).forEach((key) => {\n      this.memo('button.' + key, buttons[key]);\n    });\n\n    const modules = $.extend({}, this.options.modules, $.summernote.plugins || {});\n\n    // add and initialize modules\n    Object.keys(modules).forEach((key) => {\n      this.module(key, modules[key], true);\n    });\n\n    Object.keys(this.modules).forEach((key) => {\n      this.initializeModule(key);\n    });\n  }\n\n  _destroy() {\n    // destroy modules with reversed order\n    Object.keys(this.modules).reverse().forEach((key) => {\n      this.removeModule(key);\n    });\n\n    Object.keys(this.memos).forEach((key) => {\n      this.removeMemo(key);\n    });\n    // trigger custom onDestroy callback\n    this.triggerEvent('destroy', this);\n  }\n\n  code(html) {\n    const isActivated = this.invoke('codeview.isActivated');\n\n    if (html === undefined) {\n      this.invoke('codeview.sync');\n      return isActivated ? this.layoutInfo.codable.val() : this.layoutInfo.editable.html();\n    } else {\n      if (isActivated) {\n        this.layoutInfo.codable.val(html);\n      } else {\n        this.layoutInfo.editable.html(html);\n      }\n      this.$note.val(html);\n      this.triggerEvent('change', html);\n    }\n  }\n\n  isDisabled() {\n    return this.layoutInfo.editable.attr('contenteditable') === 'false';\n  }\n\n  enable() {\n    this.layoutInfo.editable.attr('contenteditable', true);\n    this.invoke('toolbar.activate', true);\n    this.triggerEvent('disable', false);\n  }\n\n  disable() {\n    // close codeview if codeview is opend\n    if (this.invoke('codeview.isActivated')) {\n      this.invoke('codeview.deactivate');\n    }\n    this.layoutInfo.editable.attr('contenteditable', false);\n    this.invoke('toolbar.deactivate', true);\n\n    this.triggerEvent('disable', true);\n  }\n\n  triggerEvent() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const callback = this.options.callbacks[func.namespaceToCamel(namespace, 'on')];\n    if (callback) {\n      callback.apply(this.$note[0], args);\n    }\n    this.$note.trigger('summernote.' + namespace, args);\n  }\n\n  initializeModule(key) {\n    const module = this.modules[key];\n    module.shouldInitialize = module.shouldInitialize || func.ok;\n    if (!module.shouldInitialize()) {\n      return;\n    }\n\n    // initialize module\n    if (module.initialize) {\n      module.initialize();\n    }\n\n    // attach events\n    if (module.events) {\n      dom.attachEvents(this.$note, module.events);\n    }\n  }\n\n  module(key, ModuleClass, withoutIntialize) {\n    if (arguments.length === 1) {\n      return this.modules[key];\n    }\n\n    this.modules[key] = new ModuleClass(this);\n\n    if (!withoutIntialize) {\n      this.initializeModule(key);\n    }\n  }\n\n  removeModule(key) {\n    const module = this.modules[key];\n    if (module.shouldInitialize()) {\n      if (module.events) {\n        dom.detachEvents(this.$note, module.events);\n      }\n\n      if (module.destroy) {\n        module.destroy();\n      }\n    }\n\n    delete this.modules[key];\n  }\n\n  memo(key, obj) {\n    if (arguments.length === 1) {\n      return this.memos[key];\n    }\n    this.memos[key] = obj;\n  }\n\n  removeMemo(key) {\n    if (this.memos[key] && this.memos[key].destroy) {\n      this.memos[key].destroy();\n    }\n\n    delete this.memos[key];\n  }\n\n  /**\n   * Some buttons need to change their visual style immediately once they get pressed\n   */\n  createInvokeHandlerAndUpdateState(namespace, value) {\n    return (event) => {\n      this.createInvokeHandler(namespace, value)(event);\n      this.invoke('buttons.updateCurrentStyle');\n    };\n  }\n\n  createInvokeHandler(namespace, value) {\n    return (event) => {\n      event.preventDefault();\n      const $target = $(event.target);\n      this.invoke(namespace, value || $target.closest('[data-value]').data('value'), $target);\n    };\n  }\n\n  invoke() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const splits = namespace.split('.');\n    const hasSeparator = splits.length > 1;\n    const moduleName = hasSeparator && lists.head(splits);\n    const methodName = hasSeparator ? lists.last(splits) : lists.head(splits);\n\n    const module = this.modules[moduleName || 'editor'];\n    if (!moduleName && this[methodName]) {\n      return this[methodName].apply(this, args);\n    } else if (module && module[methodName] && module.shouldInitialize()) {\n      return module[methodName].apply(module, args);\n    }\n  }\n}\n", "import $ from 'jquery';\nimport env from './base/core/env';\nimport lists from './base/core/lists';\nimport Context from './base/Context';\n\n$.fn.extend({\n  /**\n   * Summernote API\n   *\n   * @param {Object|String}\n   * @return {this}\n   */\n  summernote: function() {\n    const type = $.type(lists.head(arguments));\n    const isExternalAPICalled = type === 'string';\n    const hasInitOptions = type === 'object';\n\n    const options = $.extend({}, $.summernote.options, hasInitOptions ? lists.head(arguments) : {});\n\n    // Update options\n    options.langInfo = $.extend(true, {}, $.summernote.lang['en-US'], $.summernote.lang[options.lang]);\n    options.icons = $.extend(true, {}, $.summernote.options.icons, options.icons);\n    options.tooltip = options.tooltip === 'auto' ? !env.isSupportTouch : options.tooltip;\n\n    this.each((idx, note) => {\n      const $note = $(note);\n      if (!$note.data('summernote')) {\n        const context = new Context($note, options);\n        $note.data('summernote', context);\n        $note.data('summernote').triggerEvent('init', context.layoutInfo);\n      }\n    });\n\n    const $note = this.first();\n    if ($note.length) {\n      const context = $note.data('summernote');\n      if (isExternalAPICalled) {\n        return context.invoke.apply(context, lists.from(arguments));\n      } else if (options.focus) {\n        context.invoke('editor.focus');\n      }\n    }\n\n    return this;\n  }\n});\n", "import $ from 'jquery';\nimport ui from '../bs3/ui';\nimport dom from '../base/core/dom';\nimport '../base/summernote-en-US';\nimport Editor from '../base/module/Editor';\nimport Clipboard from '../base/module/Clipboard';\nimport Dropzone from '../base/module/Dropzone';\nimport Codeview from '../base/module/Codeview';\nimport Statusbar from '../base/module/Statusbar';\nimport Fullscreen from '../base/module/Fullscreen';\nimport Handle from '../base/module/Handle';\nimport AutoLink from '../base/module/AutoLink';\nimport AutoSync from '../base/module/AutoSync';\nimport Placeholder from '../base/module/Placeholder';\nimport Buttons from '../base/module/Buttons';\nimport Toolbar from '../base/module/Toolbar';\nimport LinkDialog from '../base/module/LinkDialog';\nimport LinkPopover from '../base/module/LinkPopover';\nimport ImageDialog from '../base/module/ImageDialog';\nimport ImagePopover from '../base/module/ImagePopover';\nimport TablePopover from '../base/module/TablePopover';\nimport VideoDialog from '../base/module/VideoDialog';\nimport HelpDialog from '../base/module/HelpDialog';\nimport AirPopover from '../base/module/AirPopover';\nimport HintPopover from '../base/module/HintPopover';\n\n$.summernote = $.extend($.summernote, {\n  version: '@@VERSION@@',\n  ui: ui,\n  dom: dom,\n\n  plugins: {},\n\n  options: {\n    modules: {\n      'editor': Editor,\n      'clipboard': Clipboard,\n      'dropzone': Dropzone,\n      'codeview': Codeview,\n      'statusbar': Statusbar,\n      'fullscreen': Fullscreen,\n      'handle': Handle,\n      // FIXME: HintPopover must be front of autolink\n      //  - Script error about range when Enter key is pressed on hint popover\n      'hintPopover': HintPopover,\n      'autoLink': AutoLink,\n      'autoSync': AutoSync,\n      'placeholder': Placeholder,\n      'buttons': Buttons,\n      'toolbar': Toolbar,\n      'linkDialog': LinkDialog,\n      'linkPopover': LinkPopover,\n      'imageDialog': ImageDialog,\n      'imagePopover': ImagePopover,\n      'tablePopover': TablePopover,\n      'videoDialog': VideoDialog,\n      'helpDialog': HelpDialog,\n      'airPopover': AirPopover\n    },\n\n    buttons: {},\n\n    lang: 'en-US',\n\n    followingToolbar: true,\n    otherStaticBar: '',\n\n    // toolbar\n    toolbar: [\n      ['style', ['style']],\n      ['font', ['bold', 'underline', 'clear']],\n      ['fontname', ['fontname']],\n      ['color', ['color']],\n      ['para', ['ul', 'ol', 'paragraph']],\n      ['table', ['table']],\n      ['insert', ['link', 'picture', 'video']],\n      ['view', ['fullscreen', 'codeview', 'help']]\n    ],\n\n    // popover\n    popatmouse: true,\n    popover: {\n      image: [\n        ['imagesize', ['imageSize100', 'imageSize50', 'imageSize25']],\n        ['float', ['floatLeft', 'floatRight', 'floatNone']],\n        ['remove', ['removeMedia']]\n      ],\n      link: [\n        ['link', ['linkDialogShow', 'unlink']]\n      ],\n      table: [\n        ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n        ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]\n      ],\n      air: [\n        ['color', ['color']],\n        ['font', ['bold', 'underline', 'clear']],\n        ['para', ['ul', 'paragraph']],\n        ['table', ['table']],\n        ['insert', ['link', 'picture']]\n      ]\n    },\n\n    // air mode: inline editor\n    airMode: false,\n\n    width: null,\n    height: null,\n    linkTargetBlank: true,\n\n    focus: false,\n    tabSize: 4,\n    styleWithSpan: true,\n    shortcuts: true,\n    textareaAutoSync: true,\n    hintDirection: 'bottom',\n    tooltip: 'auto',\n    container: 'body',\n    maxTextLength: 0,\n\n    styleTags: ['p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n    fontNames: [\n      'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',\n      'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande',\n      'Tahoma', 'Times New Roman', 'Verdana'\n    ],\n\n    fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36'],\n\n    // pallete colors(n x n)\n    colors: [\n      ['#000000', '#424242', '#636363', '#9C9C94', '#CEC6CE', '#EFEFEF', '#F7F7F7', '#FFFFFF'],\n      ['#FF0000', '#FF9C00', '#FFFF00', '#00FF00', '#00FFFF', '#0000FF', '#9C00FF', '#FF00FF'],\n      ['#F7C6CE', '#FFE7CE', '#FFEFC6', '#D6EFD6', '#CEDEE7', '#CEE7F7', '#D6D6E7', '#E7D6DE'],\n      ['#E79C9C', '#FFC69C', '#FFE79C', '#B5D6A5', '#A5C6CE', '#9CC6EF', '#B5A5D6', '#D6A5BD'],\n      ['#E76363', '#F7AD6B', '#FFD663', '#94BD7B', '#73A5AD', '#6BADDE', '#8C7BC6', '#C67BA5'],\n      ['#CE0000', '#E79439', '#EFC631', '#6BA54A', '#4A7B8C', '#3984C6', '#634AA5', '#A54A7B'],\n      ['#9C0000', '#B56308', '#BD9400', '#397B21', '#104A5A', '#085294', '#311873', '#731842'],\n      ['#630000', '#7B3900', '#846300', '#295218', '#083139', '#003163', '#21104A', '#4A1031']\n    ],\n\n    // http://chir.ag/projects/name-that-color/\n    colorsName: [\n      ['Black', 'Tundora', 'Dove Gray', 'Star Dust', 'Pale Slate', 'Gallery', 'Alabaster', 'White'],\n      ['Red', 'Orange Peel', 'Yellow', 'Green', 'Cyan', 'Blue', 'Electric Violet', 'Magenta'],\n      ['Azalea', 'Karry', 'Egg White', 'Zanah', 'Botticelli', 'Tropical Blue', 'Mischka', 'Twilight'],\n      ['Tonys Pink', 'Peach Orange', 'Cream Brulee', 'Sprout', 'Casper', 'Perano', 'Cold Purple', 'Careys Pink'],\n      ['Mandy', 'Rajah', 'Dandelion', 'Olivine', 'Gulf Stream', 'Viking', 'Blue Marguerite', 'Puce'],\n      ['Guardsman Red', 'Fire Bush', 'Golden Dream', 'Chelsea Cucumber', 'Smalt Blue', 'Boston Blue', 'Butterfly Bush', 'Cadillac'],\n      ['Sangria', 'Mai Tai', 'Buddha Gold', 'Forest Green', 'Eden', 'Venice Blue', 'Meteorite', 'Claret'],\n      ['Rosewood', 'Cinnamon', 'Olive', 'Parsley', 'Tiber', 'Midnight Blue', 'Valentino', 'Loulou']\n    ],\n\n    lineHeights: ['1.0', '1.2', '1.4', '1.5', '1.6', '1.8', '2.0', '3.0'],\n\n    tableClassName: 'table table-bordered',\n\n    insertTableMaxSize: {\n      col: 10,\n      row: 10\n    },\n\n    dialogsInBody: false,\n    dialogsFade: false,\n\n    maximumImageFileSize: null,\n\n    callbacks: {\n      onInit: null,\n      onFocus: null,\n      onBlur: null,\n      onBlurCodeview: null,\n      onEnter: null,\n      onKeyup: null,\n      onKeydown: null,\n      onImageUpload: null,\n      onImageUploadError: null\n    },\n\n    codemirror: {\n      mode: 'text/html',\n      htmlMode: true,\n      lineNumbers: true\n    },\n\n    keyMap: {\n      pc: {\n        'ENTER': 'insertParagraph',\n        'CTRL+Z': 'undo',\n        'CTRL+Y': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CTRL+B': 'bold',\n        'CTRL+I': 'italic',\n        'CTRL+U': 'underline',\n        'CTRL+SHIFT+S': 'strikethrough',\n        'CTRL+BACKSLASH': 'removeFormat',\n        'CTRL+SHIFT+L': 'justifyLeft',\n        'CTRL+SHIFT+E': 'justifyCenter',\n        'CTRL+SHIFT+R': 'justifyRight',\n        'CTRL+SHIFT+J': 'justifyFull',\n        'CTRL+SHIFT+NUM7': 'insertUnorderedList',\n        'CTRL+SHIFT+NUM8': 'insertOrderedList',\n        'CTRL+LEFTBRACKET': 'outdent',\n        'CTRL+RIGHTBRACKET': 'indent',\n        'CTRL+NUM0': 'formatPara',\n        'CTRL+NUM1': 'formatH1',\n        'CTRL+NUM2': 'formatH2',\n        'CTRL+NUM3': 'formatH3',\n        'CTRL+NUM4': 'formatH4',\n        'CTRL+NUM5': 'formatH5',\n        'CTRL+NUM6': 'formatH6',\n        'CTRL+ENTER': 'insertHorizontalRule',\n        'CTRL+K': 'linkDialog.show'\n      },\n\n      mac: {\n        'ENTER': 'insertParagraph',\n        'CMD+Z': 'undo',\n        'CMD+SHIFT+Z': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CMD+B': 'bold',\n        'CMD+I': 'italic',\n        'CMD+U': 'underline',\n        'CMD+SHIFT+S': 'strikethrough',\n        'CMD+BACKSLASH': 'removeFormat',\n        'CMD+SHIFT+L': 'justifyLeft',\n        'CMD+SHIFT+E': 'justifyCenter',\n        'CMD+SHIFT+R': 'justifyRight',\n        'CMD+SHIFT+J': 'justifyFull',\n        'CMD+SHIFT+NUM7': 'insertUnorderedList',\n        'CMD+SHIFT+NUM8': 'insertOrderedList',\n        'CMD+LEFTBRACKET': 'outdent',\n        'CMD+RIGHTBRACKET': 'indent',\n        'CMD+NUM0': 'formatPara',\n        'CMD+NUM1': 'formatH1',\n        'CMD+NUM2': 'formatH2',\n        'CMD+NUM3': 'formatH3',\n        'CMD+NUM4': 'formatH4',\n        'CMD+NUM5': 'formatH5',\n        'CMD+NUM6': 'formatH6',\n        'CMD+ENTER': 'insertHorizontalRule',\n        'CMD+K': 'linkDialog.show'\n      }\n    },\n    icons: {\n      'align': 'note-icon-align',\n      'alignCenter': 'note-icon-align-center',\n      'alignJustify': 'note-icon-align-justify',\n      'alignLeft': 'note-icon-align-left',\n      'alignRight': 'note-icon-align-right',\n      'rowBelow': 'note-icon-row-below',\n      'colBefore': 'note-icon-col-before',\n      'colAfter': 'note-icon-col-after',\n      'rowAbove': 'note-icon-row-above',\n      'rowRemove': 'note-icon-row-remove',\n      'colRemove': 'note-icon-col-remove',\n      'indent': 'note-icon-align-indent',\n      'outdent': 'note-icon-align-outdent',\n      'arrowsAlt': 'note-icon-arrows-alt',\n      'bold': 'note-icon-bold',\n      'caret': 'note-icon-caret',\n      'circle': 'note-icon-circle',\n      'close': 'note-icon-close',\n      'code': 'note-icon-code',\n      'eraser': 'note-icon-eraser',\n      'font': 'note-icon-font',\n      'frame': 'note-icon-frame',\n      'italic': 'note-icon-italic',\n      'link': 'note-icon-link',\n      'unlink': 'note-icon-chain-broken',\n      'magic': 'note-icon-magic',\n      'menuCheck': 'note-icon-menu-check',\n      'minus': 'note-icon-minus',\n      'orderedlist': 'note-icon-orderedlist',\n      'pencil': 'note-icon-pencil',\n      'picture': 'note-icon-picture',\n      'question': 'note-icon-question',\n      'redo': 'note-icon-redo',\n      'square': 'note-icon-square',\n      'strikethrough': 'note-icon-strikethrough',\n      'subscript': 'note-icon-subscript',\n      'superscript': 'note-icon-superscript',\n      'table': 'note-icon-table',\n      'textHeight': 'note-icon-text-height',\n      'trash': 'note-icon-trash',\n      'underline': 'note-icon-underline',\n      'undo': 'note-icon-undo',\n      'unorderedlist': 'note-icon-unorderedlist',\n      'video': 'note-icon-video'\n    }\n  }\n});\n\nimport '../summernote'; // eslint-disable-line\n"], "names": ["$", "Codeview"], "mappings": ";;;;;;;;;;;;;;;;;AAEA;IACE,kBAAY,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;QAC7C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC1B;IAED,yBAAM,GAAN,UAAO,OAAO;QACZ,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACxC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACrCA,GAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAC,CAAC,EAAE,CAAC;gBAC7B,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5B,CAAC,CAAC;SACJ;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAM,YAAU,GAAG,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAK;gBAC1B,KAAK,CAAC,MAAM,CAAC,YAAU,CAAC,MAAM,GAAG,YAAU,GAAG,KAAK,CAAC,CAAC;aACtD,CAAC,CAAC;SACJ;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACpC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACvB;QAED,OAAO,KAAK,CAAC;KACd;IACH,eAAC;CAAA,IAAA;AAED,eAAe;IACb,MAAM,EAAE,UAAC,MAAM,EAAE,QAAQ;QACvB,OAAO;YACL,IAAM,OAAO,GAAG,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/E,IAAI,QAAQ,GAAGA,GAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC3D,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;gBAC/B,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;aAC7B;YACD,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC1D,CAAC;KACH;CACF,CAAC;;AC9DF,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,6CAA6C,CAAC,CAAC;AAC9E,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,qHAAqH,CAAC,CAAC;AACvJ,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC;AACxE,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,uEAAuE,CAAC,CAAC;AACzG,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,0FAA0F,CAAC,CAAC;AAC7H,IAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;IAChC,yDAAyD;IACzD,4CAA4C;IAC5C,mGAAmG;IACnG,kCAAkC;IAClC,kCAAkC;IAClC,kCAAkC;IAClC,UAAU;IACV,QAAQ;CACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAEZ,IAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAChE,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC;IAClC,2DAA2D;IAC3D,0FAA0F;CAC3F,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAEZ,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,wCAAwC,CAAC,CAAC;AAE9E,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,wCAAwC,EAAE,UAAS,KAAK,EAAE,OAAO;IAChG,IAAM,MAAM,GAAGA,GAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAS,IAAI;QACvE,IAAM,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;QACrE,IAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QACjE,IAAM,MAAM,GAAG,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QAEpE,IAAM,SAAS,GAAG,cAAc,GAAG,KAAK,GAAG,GAAG,CAAC;QAC/C,IAAM,UAAU,GAAG,CAAC,MAAM,KAAK,SAAS,IAAI,gBAAgB,GAAG,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC;QACjF,OAAO,kCAAkC,GAAG,IAAI,GAAG,gBAAgB,IAAI,SAAS,GAAG,UAAU,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,WAAW,CAAC;KAC9H,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;IAE5B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAC,YAAY,EAAE,OAAO,CAAC,KAAK,EAAC,CAAC,CAAC;CACxD,CAAC,CAAC;AAEH,IAAM,sBAAsB,GAAG,UAAS,QAAQ,EAAE,OAAO;IACvD,OAAO,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;CAC3D,CAAC;AAEF,IAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,mDAAmD,EAAE,UAAS,KAAK,EAAE,OAAO;IAChH,IAAM,MAAM,GAAGA,GAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAS,IAAI;QACvE,IAAM,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;QACrE,IAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QACjE,OAAO,kCAAkC,GAAG,IAAI,GAAG,4BAA4B,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,WAAW,CAAC;KAC7J,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAC,YAAY,EAAE,OAAO,CAAC,KAAK,EAAC,CAAC,CAAC;CACxD,CAAC,CAAC;AAEH,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,mCAAmC,EAAE,UAAS,KAAK,EAAE,OAAO;IAC1F,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,EAAE;QACvE,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACnC,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,EAAE;YAC/D,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC;gBACX,8CAA8C;gBAC9C,0BAA0B,EAAE,KAAK,EAAE,IAAI;gBACvC,cAAc,EAAE,SAAS,EAAE,IAAI;gBAC/B,cAAc,EAAE,KAAK,EAAE,IAAI;gBAC3B,SAAS,EAAE,SAAS,EAAE,IAAI;gBAC1B,cAAc,EAAE,SAAS,EAAE,IAAI;gBAC/B,8CAA8C;aAC/C,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACb;QACD,QAAQ,CAAC,IAAI,CAAC,8BAA8B,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC;KAC7E;IACD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;IAE9B,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;YACpC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,QAAQ;SACpB,CAAC,CAAC;KACJ;CACF,CAAC,CAAC;AAEH,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,sEAAsE,EAAE,UAAS,KAAK,EAAE,OAAO;IAC5H,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KACxB;IACD,KAAK,CAAC,IAAI,CAAC;QACT,YAAY,EAAE,OAAO,CAAC,KAAK;KAC5B,CAAC,CAAC;IACH,KAAK,CAAC,IAAI,CAAC;QACT,4BAA4B;QAC5B,+BAA+B;SAC9B,OAAO,CAAC,KAAK;cACV,gCAAgC;gBACpC,uHAAuH;gBACvH,gCAAgC,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO;gBAC1D,YAAY,GAAG,EAAE;QAEjB,8BAA8B,GAAG,OAAO,CAAC,IAAI,GAAG,QAAQ;SACvD,OAAO,CAAC,MAAM;cACX,gCAAgC,GAAG,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE;QAErE,UAAU;QACV,QAAQ;KACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;CACb,CAAC,CAAC;AAEH,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC9B,uCAAuC;IACvC,wBAAwB;IACxB,0DAA0D;IAC1D,QAAQ;CACT,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,UAAS,KAAK,EAAE,OAAO;IACjC,IAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS,KAAK,WAAW,GAAG,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;IAE1F,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAE1B,IAAI,OAAO,CAAC,SAAS,EAAE;QACrB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;KAC7B;CACF,CAAC,CAAC;AAEH,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,8BAA8B,EAAE,UAAS,KAAK,EAAE,OAAO;IACtF,KAAK,CAAC,IAAI,CAAC;QACT,SAAS,IAAI,OAAO,CAAC,EAAE,GAAG,QAAQ,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;QACjE,yCAAyC,IAAI,OAAO,CAAC,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;SACzF,OAAO,CAAC,OAAO,GAAG,UAAU,GAAG,EAAE;QAClC,iBAAiB,IAAI,OAAO,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,KAAK;SAC/D,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,EAAE;QACjC,UAAU;KACX,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;CACb,CAAC,CAAC;AAEH,IAAM,IAAI,GAAG,UAAS,aAAa,EAAE,OAAO;IAC1C,OAAO,GAAG,OAAO,IAAI,GAAG,CAAC;IACzB,OAAO,GAAG,GAAG,OAAO,GAAG,UAAU,GAAG,aAAa,GAAG,KAAK,CAAC;CAC3D,CAAC;AACF,IAAM,EAAE,GAAG;IACT,MAAM,EAAE,MAAM;IACd,OAAO,EAAE,OAAO;IAChB,WAAW,EAAE,WAAW;IACxB,OAAO,EAAE,OAAO;IAChB,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE,SAAS;IACpB,SAAS,EAAE,SAAS;IACpB,WAAW,EAAE,WAAW;IACxB,WAAW,EAAE,WAAW;IACxB,QAAQ,EAAE,QAAQ;IAClB,sBAAsB,EAAE,sBAAsB;IAC9C,aAAa,EAAE,aAAa;IAC5B,OAAO,EAAE,OAAO;IAChB,MAAM,EAAE,MAAM;IACd,OAAO,EAAE,OAAO;IAChB,QAAQ,EAAE,QAAQ;IAClB,IAAI,EAAE,IAAI;IACV,OAAO,EAAE,EAAE;IAEX,MAAM,EAAE,UAAS,KAAK,EAAE,OAAO;QAC7B,OAAO,QAAQ,CAAC,MAAM,CAAC,4FAA4F,EAAE,UAAS,KAAK,EAAE,OAAO;YAC1I,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;gBAC9B,KAAK,CAAC,IAAI,CAAC;oBACT,KAAK,EAAE,OAAO,CAAC,OAAO;oBACtB,YAAY,EAAE,OAAO,CAAC,OAAO;iBAC9B,CAAC,CAAC,OAAO,CAAC;oBACT,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,QAAQ;iBACpB,CAAC,CAAC;aACJ;SACF,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KACpB;IAED,SAAS,EAAE,UAAS,IAAI,EAAE,QAAQ;QAChC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;KAClC;IAED,eAAe,EAAE,UAAS,IAAI,EAAE,QAAQ;QACtC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;KACtC;IAED,aAAa,EAAE,UAAS,OAAO,EAAE,OAAO;QACtC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;KACxC;IAED,cAAc,EAAE,UAAS,OAAO,EAAE,OAAO;QACvC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;KACzC;IAED,UAAU,EAAE,UAAS,OAAO;QAC1B,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KACvB;IAED,UAAU,EAAE,UAAS,OAAO;QAC1B,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KACvB;IAED,YAAY,EAAE,UAAS,KAAK,EAAE,OAAO;QACnC,IAAM,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC,SAAS,CAAC;YAC9C,EAAE,CAAC,WAAW,CAAC;gBACb,EAAE,CAAC,WAAW,EAAE;aACjB,CAAC;SACH,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;YACb,EAAE,CAAC,OAAO,EAAE;YACZ,EAAE,CAAC,WAAW,CAAC;gBACb,EAAE,CAAC,OAAO,EAAE;gBACZ,EAAE,CAAC,QAAQ,EAAE;aACd,CAAC;YACF,EAAE,CAAC,SAAS,EAAE;SACf,CAAC,EAAE,MAAM,EAAE,CAAC;QAEb,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAE3B,OAAO;YACL,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;YACtC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC/C,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACxC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;YACtC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;SAC3C,CAAC;KACH;IAED,YAAY,EAAE,UAAS,KAAK,EAAE,UAAU;QACtC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QACvC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAC3B,KAAK,CAAC,IAAI,EAAE,CAAC;KACd;CACF;;AC1OD;;;;;;;;AAQA,YAAY,KAAK;IACf,OAAO,UAAS,KAAK;QACnB,OAAO,KAAK,KAAK,KAAK,CAAC;KACxB,CAAC;CACH;AAED,aAAa,KAAK,EAAE,KAAK;IACvB,OAAO,KAAK,KAAK,KAAK,CAAC;CACxB;AAED,cAAc,QAAQ;IACpB,OAAO,UAAS,KAAK,EAAE,KAAK;QAC1B,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC;KAC5C,CAAC;CACH;AAED;IACE,OAAO,IAAI,CAAC;CACb;AAED;IACE,OAAO,KAAK,CAAC;CACd;AAED,aAAa,CAAC;IACZ,OAAO;QACL,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;KAC/B,CAAC;CACH;AAED,aAAa,EAAE,EAAE,EAAE;IACjB,OAAO,UAAS,IAAI;QAClB,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;KAC7B,CAAC;CACH;AAED,cAAc,CAAC;IACb,OAAO,CAAC,CAAC;CACV;AAED,gBAAgB,GAAG,EAAE,MAAM;IACzB,OAAO;QACL,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;KAC1C,CAAC;CACH;AAED,IAAI,SAAS,GAAG,CAAC,CAAC;;;;;;AAOlB,kBAAkB,MAAM;IACtB,IAAM,EAAE,GAAG,EAAE,SAAS,GAAG,EAAE,CAAC;IAC5B,OAAO,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;CAClC;;;;;;;;;;;;;;AAeD,kBAAkB,IAAI;IACpB,IAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC9B,OAAO;QACL,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,SAAS,EAAE;QACrC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,UAAU,EAAE;QACxC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;QAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG;KAC/B,CAAC;CACH;;;;;;AAOD,sBAAsB,GAAG;IACvB,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,KAAK,IAAM,GAAG,IAAI,GAAG,EAAE;QACrB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC3B,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;SAC1B;KACF;IACD,OAAO,QAAQ,CAAC;CACjB;;;;;;AAOD,0BAA0B,SAAS,EAAE,MAAM;IACzC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IACtB,OAAO,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAS,IAAI;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC/D,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;CACb;;;;;;;;;;;AAYD,kBAAkB,IAAI,EAAE,IAAI,EAAE,SAAS;IAAvC,iBAkBC;IAjBC,IAAI,OAAO,CAAC;IACZ,OAAO;QACL,IAAM,OAAO,GAAG,KAAI,CAAC;QACrB,IAAM,IAAI,GAAG,SAAS,CAAC;QACvB,IAAM,KAAK,GAAG;YACZ,OAAO,GAAG,IAAI,CAAC;YACf,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;aAC3B;SACF,CAAC;QACF,IAAM,OAAO,GAAG,SAAS,IAAI,CAAC,OAAO,CAAC;QACtC,YAAY,CAAC,OAAO,CAAC,CAAC;QACtB,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAClC,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAC3B;KACF,CAAC;CACH;AAED,WAAe;IACb,EAAE,IAAA;IACF,GAAG,KAAA;IACH,IAAI,MAAA;IACJ,EAAE,IAAA;IACF,IAAI,MAAA;IACJ,IAAI,MAAA;IACJ,GAAG,KAAA;IACH,GAAG,KAAA;IACH,MAAM,QAAA;IACN,QAAQ,UAAA;IACR,QAAQ,UAAA;IACR,YAAY,cAAA;IACZ,gBAAgB,kBAAA;IAChB,QAAQ,UAAA;CACT,CAAC;;AC9JF;;;;;AAKA,cAAc,KAAK;IACjB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;CACjB;;;;;;AAOD,cAAc,KAAK;IACjB,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;CAChC;;;;;;AAOD,iBAAiB,KAAK;IACpB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;CACzC;;;;;;AAOD,cAAc,KAAK;IACjB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CACvB;;;;AAKD,cAAc,KAAK,EAAE,IAAI;IACvB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YACd,OAAO,IAAI,CAAC;SACb;KACF;CACF;;;;AAKD,aAAa,KAAK,EAAE,IAAI;IACtB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;YACrB,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;CACb;;;;AAKD,iBAAiB,KAAK,EAAE,IAAI;IAC1B,OAAOA,GAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;CAC/B;;;;AAKD,kBAAkB,KAAK,EAAE,IAAI;IAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;CACpC;;;;;;;AAQD,aAAa,KAAK,EAAE,EAAE;IACpB,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC;IACrB,OAAO,KAAK,CAAC,MAAM,CAAC,UAAS,IAAI,EAAE,CAAC;QAClC,OAAO,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;KACrB,EAAE,CAAC,CAAC,CAAC;CACP;;;;;AAMD,cAAc,UAAU;IACtB,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IACjC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;IACb,OAAO,EAAE,GAAG,GAAG,MAAM,EAAE;QACrB,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;KAC/B;IACD,OAAO,MAAM,CAAC;CACf;;;;AAKD,mBAAiB,KAAK;IACpB,OAAO,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;CAChC;;;;;;;;AASD,mBAAmB,KAAK,EAAE,EAAE;IAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QAAE,OAAO,EAAE,CAAC;KAAE;IACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,OAAO,KAAK,CAAC,MAAM,CAAC,UAAS,IAAI,EAAE,CAAC;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACtB,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACzB;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SACzB;QACD,OAAO,IAAI,CAAC;KACb,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACrB;;;;;;;AAQD,iBAAiB,KAAK;IACpB,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SAAE;KAC9C;IACD,OAAO,OAAO,CAAC;CAChB;;;;;;AAOD,gBAAgB,KAAK;IACnB,IAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;QACtD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1B;KACF;IAED,OAAO,OAAO,CAAC;CAChB;;;;;AAMD,cAAc,KAAK,EAAE,IAAI;IACvB,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACjC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IAEhC,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CACvB;;;;;AAMD,cAAc,KAAK,EAAE,IAAI;IACvB,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACjC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IAEhC,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CACvB;;;;;;;;;AAUD,YAAe;IACb,IAAI,MAAA;IACJ,IAAI,MAAA;IACJ,OAAO,SAAA;IACP,IAAI,MAAA;IACJ,IAAI,MAAA;IACJ,IAAI,MAAA;IACJ,IAAI,MAAA;IACJ,QAAQ,UAAA;IACR,GAAG,KAAA;IACH,GAAG,KAAA;IACH,IAAI,MAAA;IACJ,OAAO,WAAA;IACP,SAAS,WAAA;IACT,OAAO,SAAA;IACP,MAAM,QAAA;CACP,CAAC;;AChNF,IAAM,YAAY,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,GAAG,CAAC;;;;;;;AAQhE,yBAAyB,QAAQ;IAC/B,IAAM,YAAY,GAAG,QAAQ,KAAK,eAAe,GAAG,aAAa,GAAG,eAAe,CAAC;IACpF,IAAM,OAAO,GAAGA,GAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC;QAC7B,QAAQ,EAAE,UAAU;QACpB,IAAI,EAAE,SAAS;QACf,GAAG,EAAE,SAAS;QACd,QAAQ,EAAE,OAAO;KAClB,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAEpD,IAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;IACtE,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,GAAG,GAAG,GAAG,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;IAE/E,OAAO,CAAC,MAAM,EAAE,CAAC;IAEjB,OAAO,aAAa,KAAK,KAAK,CAAC;CAChC;AAED,IAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AACtC,IAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/C,IAAI,cAAc,CAAC;AACnB,IAAI,MAAM,EAAE;IACV,IAAI,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACjD,IAAI,OAAO,EAAE;QACX,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;IACD,OAAO,GAAG,qCAAqC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAChE,IAAI,OAAO,EAAE;QACX,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;KACzC;CACF;AAED,IAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAE3C,IAAI,aAAa,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;AACxC,IAAI,CAAC,aAAa,IAAI,YAAY,EAAE;;IAElC,IAAI,OAAO,mBAAmB,KAAK,UAAU,EAAE;QAC7C,IAAI;;;YAGF,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC9B,aAAa,GAAG,IAAI,CAAC;SACtB;QAAC,OAAO,CAAC,EAAE;;SAEX;KACF;SAAM,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;;QAEzC,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE;YAC1C,IAAI;;;gBAGF,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAC9B,aAAa,GAAG,IAAI,CAAC;aACtB;YAAC,OAAO,CAAC,EAAE;;aAEX;;SAEF;aAAM,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,WAAW,EAAE;YACnD,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;SACjD;KACF;CACF;AAED,IAAM,cAAc,IACjB,CAAC,cAAc,IAAI,MAAM;KACxB,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;KAC7B,SAAS,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;;;AAIrC,IAAM,cAAc,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI,6DAA6D,GAAG,OAAO,CAAC;;;;;;;;;AAUpH,UAAe;IACb,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/C,MAAM,QAAA;IACN,MAAM,QAAA;IACN,IAAI,EAAE,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;IAC3C,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;IACvC,QAAQ,EAAE,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9C,QAAQ,EAAE,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9C,QAAQ,EAAE,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9C,cAAc,gBAAA;IACd,aAAa,EAAE,UAAU,CAACA,GAAC,CAAC,EAAE,CAAC,MAAM,CAAC;IACtC,YAAY,cAAA;IACZ,cAAc,gBAAA;IACd,aAAa,eAAA;IACb,eAAe,iBAAA;IACf,iBAAiB,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW;IACzC,cAAc,gBAAA;CACf,CAAC;;ACrGF,IAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC3C,IAAM,oBAAoB,GAAG,QAAQ,CAAC;;;;;;;;;AAUtC,oBAAoB,IAAI;IACtB,OAAO,IAAI,IAAIA,GAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;CAClD;;;;;;;;;AAUD,yBAAyB,IAAI;IAC3B,OAAO,IAAI,IAAIA,GAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;CACxD;;;;;;;;;AAUD,4BAA4B,QAAQ;IAClC,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAClC,OAAO,UAAS,IAAI;QAClB,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC;KACzD,CAAC;CACH;;;;;;;;;AAUD,gBAAgB,IAAI;IAClB,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;CACpC;;;;;;;;;AAUD,mBAAmB,IAAI;IACrB,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;CACpC;;;;;AAMD,gBAAgB,IAAI;IAClB,OAAO,IAAI,IAAI,qCAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;CACxF;AAED,gBAAgB,IAAI;IAClB,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;;IAGD,OAAO,IAAI,IAAI,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;CACxE;AAED,mBAAmB,IAAI;IACrB,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;CAC5D;AAED,IAAM,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAExC,IAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAEtC,oBAAoB,IAAI;IACtB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACpC;AAED,IAAM,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;AAE5C,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAE1C,kBAAkB,IAAI;IACpB,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;QACtB,CAAC,MAAM,CAAC,IAAI,CAAC;QACb,CAAC,IAAI,CAAC,IAAI,CAAC;QACX,CAAC,MAAM,CAAC,IAAI,CAAC;QACb,CAAC,OAAO,CAAC,IAAI,CAAC;QACd,CAAC,YAAY,CAAC,IAAI,CAAC;QACnB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;CACtB;AAED,gBAAgB,IAAI;IAClB,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;CAC5D;AAED,IAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAEtC,gBAAgB,IAAI;IAClB,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;CAC5D;AAED,IAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAEtD,yBAAyB,IAAI;IAC3B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;CAC/D;AAED,IAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAEzC,sBAAsB,IAAI;IACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;CACnD;AAED,sBAAsB,IAAI;IACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;CAClD;AAED,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;;;;;;;;AAS1C,0BAA0B,KAAK,EAAE,KAAK;IACpC,OAAO,KAAK,CAAC,WAAW,KAAK,KAAK;QAC3B,KAAK,CAAC,eAAe,KAAK,KAAK,CAAC;CACxC;;;;;;;;AASD,6BAA6B,IAAI,EAAE,IAAI;IACrC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;IAEvB,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;QACtD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACrC;IACD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QAC9C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACjC;IACD,OAAO,QAAQ,CAAC;CACjB;;;;;;AAOD,IAAM,SAAS,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,cAAc,GAAG,EAAE,GAAG,QAAQ,GAAG,MAAM,CAAC;;;;;;;;AAS5E,oBAAoB,IAAI;IACtB,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;KAC9B;IAED,IAAI,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;KAC/B;IAED,OAAO,CAAC,CAAC;CACV;;;;;;;AAQD,iBAAiB,IAAI;IACnB,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAE7B,IAAI,GAAG,KAAK,CAAC,EAAE;QACb,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;;QAErE,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE;;QAEtE,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;CACd;;;;AAKD,0BAA0B,IAAI;IAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC5B;CACF;;;;;;;AAQD,kBAAkB,IAAI,EAAE,IAAI;IAC1B,OAAO,IAAI,EAAE;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAChC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;YAAE,MAAM;SAAE;QAEhC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,OAAO,IAAI,CAAC;CACb;;;;;;;AAQD,6BAA6B,IAAI,EAAE,IAAI;IACrC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IAEvB,OAAO,IAAI,EAAE;QACX,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAAE,MAAM;SAAE;QACtC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAChC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;YAAE,MAAM;SAAE;QAEhC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,OAAO,IAAI,CAAC;CACb;;;;;;;AAQD,sBAAsB,IAAI,EAAE,IAAI;IAC9B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;IAEzB,IAAM,SAAS,GAAG,EAAE,CAAC;IACrB,QAAQ,CAAC,IAAI,EAAE,UAAS,EAAE;QACxB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;YACnB,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACpB;QAED,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;KACjB,CAAC,CAAC;IACH,OAAO,SAAS,CAAC;CAClB;;;;AAKD,sBAAsB,IAAI,EAAE,IAAI;IAC9B,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACrC,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;CAC3C;;;;;;;AAQD,wBAAwB,KAAK,EAAE,KAAK;IAClC,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IACtC,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE;QACvC,IAAIA,GAAC,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC;SAAE;KAChD;IACD,OAAO,IAAI,CAAC;CACb;;;;;;;AAQD,kBAAkB,IAAI,EAAE,IAAI;IAC1B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;IAEzB,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,OAAO,IAAI,EAAE;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YAAE,MAAM;SAAE;QAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjB,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,OAAO,KAAK,CAAC;CACd;;;;;;;AAQD,kBAAkB,IAAI,EAAE,IAAI;IAC1B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;IAEzB,IAAM,KAAK,GAAG,EAAE,CAAC;IACjB,OAAO,IAAI,EAAE;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YAAE,MAAM;SAAE;QAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjB,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,OAAO,KAAK,CAAC;CACd;;;;;;;AAQD,wBAAwB,IAAI,EAAE,IAAI;IAChC,IAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;;IAGvB,CAAC,gBAAgB,OAAO;QACtB,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YACrC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3B;QACD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACnE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;SACjC;KACF,EAAE,IAAI,CAAC,CAAC;IAET,OAAO,WAAW,CAAC;CACpB;;;;;;;;AASD,cAAc,IAAI,EAAE,WAAW;IAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;IAC/B,IAAM,OAAO,GAAGA,GAAC,CAAC,GAAG,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9C,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAE1B,OAAO,OAAO,CAAC;CAChB;;;;;;;AAQD,qBAAqB,IAAI,EAAE,SAAS;IAClC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC;IACnC,IAAI,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;IAClC,IAAI,IAAI,EAAE;QACR,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACjC;SAAM;QACL,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;KAC1B;IACD,OAAO,IAAI,CAAC;CACb;;;;;;;AAQD,0BAA0B,IAAI,EAAE,MAAM;IACpCA,GAAC,CAAC,IAAI,CAAC,MAAM,EAAE,UAAS,GAAG,EAAE,KAAK;QAChC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACzB,CAAC,CAAC;IACH,OAAO,IAAI,CAAC;CACb;;;;;;;AAQD,yBAAyB,KAAK;IAC5B,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;CAC3B;;;;;;;AAQD,0BAA0B,KAAK;IAC7B,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;CAChD;;;;;;;AAQD,qBAAqB,KAAK;IACxB,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;CAC1D;;;;;;;;AASD,sBAAsB,IAAI,EAAE,QAAQ;IAClC,OAAO,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE;QAChC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxB,OAAO,KAAK,CAAC;SACd;QACD,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,OAAO,IAAI,CAAC;CACb;;;;;;;;AASD,uBAAuB,IAAI,EAAE,QAAQ;IACnC,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE;QAChC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACtD,OAAO,KAAK,CAAC;SACd;QACD,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,OAAO,IAAI,CAAC;CACb;;;;;;;AAQD,2BAA2B,KAAK,EAAE,QAAQ;IACxC,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;CACrE;;;;;;;AAQD,4BAA4B,KAAK,EAAE,QAAQ;IACzC,OAAO,gBAAgB,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;CACvE;;;;;;AAOD,kBAAkB,IAAI;IACpB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,QAAQ,IAAI,GAAG,IAAI,CAAC,eAAe,GAAG;QACpC,MAAM,IAAI,CAAC,CAAC;KACb;IACD,OAAO,MAAM,CAAC;CACf;AAED,qBAAqB,IAAI;IACvB,OAAO,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;CAC9D;;;;;;;;AASD,mBAAmB,KAAK,EAAE,iBAAiB;IACzC,IAAI,IAAI,CAAC;IACT,IAAI,MAAM,CAAC;IAEX,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;QAC7B,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC/B;SAAM,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAClC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/C,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;KAC3B;SAAM;QACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,GAAG,iBAAiB,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KACnD;IAED,OAAO;QACL,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,MAAM;KACf,CAAC;CACH;;;;;;;;AASD,mBAAmB,KAAK,EAAE,iBAAiB;IACzC,IAAI,IAAI,EAAE,MAAM,CAAC;IAEjB,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,EAAE;QAC3C,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;QAC7B,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACnC;SAAM,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAClC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3C,MAAM,GAAG,CAAC,CAAC;KACZ;SAAM;QACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAClB,MAAM,GAAG,iBAAiB,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KACxE;IAED,OAAO;QACL,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,MAAM;KACf,CAAC;CACH;;;;;;;;AASD,qBAAqB,MAAM,EAAE,MAAM;IACjC,OAAO,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;CACvE;;;;;;;AAQD,wBAAwB,KAAK;IAC3B,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACzE,OAAO,IAAI,CAAC;KACb;IAED,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzD,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACtD,IAAI,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;QACxE,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;CACd;;;;;;;;AASD,wBAAwB,KAAK,EAAE,IAAI;IACjC,OAAO,KAAK,EAAE;QACZ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACf,OAAO,KAAK,CAAC;SACd;QAED,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;KAC1B;IAED,OAAO,IAAI,CAAC;CACb;;;;;;;;AASD,wBAAwB,KAAK,EAAE,IAAI;IACjC,OAAO,KAAK,EAAE;QACZ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;YACf,OAAO,KAAK,CAAC;SACd;QAED,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;KAC1B;IAED,OAAO,IAAI,CAAC;CACb;;;;;;;AAQD,qBAAqB,KAAK;IACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACvB,OAAO,KAAK,CAAC;KACd;IAED,IAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzD,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,SAAS,CAAC,CAAC;CAC/C;;;;;;;;;AAUD,mBAAmB,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,iBAAiB;IACjE,IAAI,KAAK,GAAG,UAAU,CAAC;IAEvB,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,CAAC;QAEf,IAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;YAChC,MAAM;SACP;QAED,IAAM,YAAY,GAAG,iBAAiB;YACnB,UAAU,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;YAC9B,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;QAChD,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;KACxC;CACF;;;;;;;;;AAUD,wBAAwB,QAAQ,EAAE,IAAI;IACpC,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACxD,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;CAC1C;;;;;;;;;AAUD,wBAAwB,QAAQ,EAAE,OAAO;IACvC,IAAI,OAAO,GAAG,QAAQ,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAClD,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;YAC3C,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAC7D;aAAM;YACL,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;KACF;IACD,OAAO,OAAO,CAAC;CAChB;;;;;;;;;;;;AAaD,mBAAmB,KAAK,EAAE,OAAO;IAC/B,IAAM,sBAAsB,GAAG,OAAO,IAAI,OAAO,CAAC,sBAAsB,CAAC;IACzE,IAAM,mBAAmB,GAAG,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC;;IAGnE,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC,EAAE;QACrE,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC,IAAI,CAAC;SACnB;aAAM,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;SAC/B;KACF;;IAGD,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC3C;SAAM;QACL,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACtD,IAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACnE,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QAE7C,IAAI,CAAC,sBAAsB,EAAE;YAC3B,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7B,gBAAgB,CAAC,KAAK,CAAC,CAAC;SACzB;QAED,OAAO,KAAK,CAAC;KACd;CACF;;;;;;;;;;;;;AAcD,mBAAmB,IAAI,EAAE,KAAK,EAAE,OAAO;;IAErC,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAE1D,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QACrB,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QACjC,OAAO,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAClC;IAED,OAAO,SAAS,CAAC,MAAM,CAAC,UAAS,IAAI,EAAE,MAAM;QAC3C,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;YACvB,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SAClC;QAED,OAAO,SAAS,CAAC;YACf,IAAI,EAAE,MAAM;YACZ,MAAM,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;SACnD,EAAE,OAAO,CAAC,CAAC;KACb,CAAC,CAAC;CACJ;;;;;;;;AASD,oBAAoB,KAAK,EAAE,QAAQ;;;;IAIjC,IAAM,IAAI,GAAG,QAAQ,GAAG,MAAM,GAAG,eAAe,CAAC;IACjD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjD,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC;IAExD,IAAI,SAAS,EAAE,SAAS,CAAC;IACzB,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE;QACrB,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5C,SAAS,GAAG,WAAW,CAAC;KACzB;SAAM;QACL,SAAS,GAAG,WAAW,CAAC;QACxB,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;KAClC;;IAGD,IAAI,KAAK,GAAG,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE;QACnD,sBAAsB,EAAE,QAAQ;QAChC,mBAAmB,EAAE,QAAQ;KAC9B,CAAC,CAAC;;IAGH,IAAI,CAAC,KAAK,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,EAAE;QACtC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC7C;IAED,OAAO;QACL,SAAS,EAAE,KAAK;QAChB,SAAS,EAAE,SAAS;KACrB,CAAC;CACH;AAED,gBAAgB,QAAQ;IACtB,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;CACzC;AAED,oBAAoB,IAAI;IACtB,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;CACtC;;;;;;;;;AAUD,gBAAgB,IAAI,EAAE,aAAa;IACjC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAAE,OAAO;KAAE;IAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;QAAE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KAAE;IAE/D,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;IAC/B,IAAI,CAAC,aAAa,EAAE;QAClB,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAChC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAChD,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SACrC;KACF;IAED,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;CAC1B;;;;;;;AAQD,qBAAqB,IAAI,EAAE,IAAI;IAC7B,OAAO,IAAI,EAAE;QACX,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACnC,MAAM;SACP;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,CAAC;QACb,IAAI,GAAG,MAAM,CAAC;KACf;CACF;;;;;;;;;;AAWD,iBAAiB,IAAI,EAAE,QAAQ;IAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,EAAE;QAC1D,OAAO,IAAI,CAAC;KACb;IAED,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IAEjC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;QACtB,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;KAC5C;IAED,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACvD,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC3B,MAAM,CAAC,IAAI,CAAC,CAAC;IAEb,OAAO,OAAO,CAAC;CAChB;AAED,IAAM,UAAU,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;;;;;AAMlD,eAAe,KAAK,EAAE,eAAe;IACnC,IAAM,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;IAC9D,IAAI,eAAe,EAAE;QACnB,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;KACnC;IACD,OAAO,GAAG,CAAC;CACZ;;;;;;;;;AAUD,cAAc,KAAK,EAAE,gBAAgB;IACnC,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAE1B,IAAI,gBAAgB,EAAE;QACpB,IAAM,QAAQ,GAAG,uCAAuC,CAAC;QACzD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAS,KAAK,EAAE,QAAQ,EAAE,IAAI;YAC9D,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC1B,IAAM,sBAAsB,GAAG,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC1C,CAAC,CAAC,QAAQ,CAAC;YACxC,IAAM,WAAW,GAAG,2CAA2C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3E,OAAO,KAAK,IAAI,CAAC,sBAAsB,IAAI,WAAW,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;SACtE,CAAC,CAAC;QACH,MAAM,GAAGA,GAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;IAED,OAAO,MAAM,CAAC;CACf;AAED,4BAA4B,WAAW;IACrC,IAAM,YAAY,GAAGA,GAAC,CAAC,WAAW,CAAC,CAAC;IACpC,IAAM,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC;IAClC,IAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAE9C,OAAO;QACL,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,MAAM;KACtB,CAAC;CACH;AAED,sBAAsB,KAAK,EAAE,MAAM;IACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAS,GAAG;QACtC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KAC5B,CAAC,CAAC;CACJ;AAED,sBAAsB,KAAK,EAAE,MAAM;IACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAS,GAAG;QACtC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KAC7B,CAAC,CAAC;CACJ;;;;;;;;;AAUD,0BAA0B,IAAI;IAC5B,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;CACjF;AAED,UAAe;;IAEb,SAAS,WAAA;;IAET,oBAAoB,sBAAA;;IAEpB,KAAK,EAAE,SAAS;;IAEhB,SAAS,EAAE,QAAM,SAAS,SAAM;IAChC,kBAAkB,oBAAA;IAClB,UAAU,YAAA;IACV,eAAe,iBAAA;IACf,MAAM,QAAA;IACN,SAAS,WAAA;IACT,MAAM,QAAA;IACN,MAAM,QAAA;IACN,UAAU,YAAA;IACV,SAAS,WAAA;IACT,QAAQ,UAAA;IACR,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;IAC3B,YAAY,cAAA;IACZ,MAAM,QAAA;IACN,YAAY,cAAA;IACZ,KAAK,OAAA;IACL,MAAM,QAAA;IACN,OAAO,SAAA;IACP,MAAM,QAAA;IACN,MAAM,QAAA;IACN,YAAY,cAAA;IACZ,eAAe,iBAAA;IACf,QAAQ,UAAA;IACR,KAAK,EAAE,kBAAkB,CAAC,KAAK,CAAC;IAChC,IAAI,MAAA;IACJ,IAAI,EAAE,kBAAkB,CAAC,IAAI,CAAC;IAC9B,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC;IAClC,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;IAC5B,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;IAC5B,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;IAC5B,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;IAC5B,KAAK,EAAE,kBAAkB,CAAC,KAAK,CAAC;IAChC,UAAU,YAAA;IACV,OAAO,SAAA;IACP,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC1C,gBAAgB,kBAAA;IAChB,mBAAmB,qBAAA;IACnB,UAAU,YAAA;IACV,eAAe,iBAAA;IACf,gBAAgB,kBAAA;IAChB,WAAW,aAAA;IACX,YAAY,cAAA;IACZ,aAAa,eAAA;IACb,iBAAiB,mBAAA;IACjB,kBAAkB,oBAAA;IAClB,SAAS,WAAA;IACT,SAAS,WAAA;IACT,WAAW,aAAA;IACX,cAAc,gBAAA;IACd,cAAc,gBAAA;IACd,cAAc,gBAAA;IACd,WAAW,aAAA;IACX,SAAS,WAAA;IACT,QAAQ,UAAA;IACR,mBAAmB,qBAAA;IACnB,YAAY,cAAA;IACZ,YAAY,cAAA;IACZ,QAAQ,UAAA;IACR,QAAQ,UAAA;IACR,cAAc,gBAAA;IACd,cAAc,gBAAA;IACd,IAAI,MAAA;IACJ,WAAW,aAAA;IACX,gBAAgB,kBAAA;IAChB,QAAQ,UAAA;IACR,WAAW,aAAA;IACX,cAAc,gBAAA;IACd,cAAc,gBAAA;IACd,SAAS,WAAA;IACT,UAAU,YAAA;IACV,MAAM,QAAA;IACN,UAAU,YAAA;IACV,MAAM,QAAA;IACN,WAAW,aAAA;IACX,OAAO,SAAA;IACP,IAAI,MAAA;IACJ,KAAK,OAAA;IACL,kBAAkB,oBAAA;IAClB,YAAY,cAAA;IACZ,YAAY,cAAA;IACZ,gBAAgB,kBAAA;CACjB,CAAC;;AC7jCFA,GAAC,CAAC,UAAU,GAAGA,GAAC,CAAC,UAAU,IAAI;IAC7B,IAAI,EAAE,EAAE;CACT,CAAC;AAEFA,GAAC,CAAC,MAAM,CAACA,GAAC,CAAC,UAAU,CAAC,IAAI,EAAE;IAC1B,OAAO,EAAE;QACP,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,WAAW;YACtB,KAAK,EAAE,mBAAmB;YAC1B,MAAM,EAAE,aAAa;YACrB,IAAI,EAAE,aAAa;YACnB,aAAa,EAAE,eAAe;YAC9B,SAAS,EAAE,WAAW;YACtB,WAAW,EAAE,aAAa;YAC1B,IAAI,EAAE,WAAW;SAClB;QACD,KAAK,EAAE;YACL,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,cAAc;YACtB,UAAU,EAAE,aAAa;YACzB,UAAU,EAAE,aAAa;YACzB,aAAa,EAAE,gBAAgB;YAC/B,SAAS,EAAE,YAAY;YACvB,UAAU,EAAE,aAAa;YACzB,SAAS,EAAE,YAAY;YACvB,YAAY,EAAE,gBAAgB;YAC9B,WAAW,EAAE,eAAe;YAC5B,cAAc,EAAE,kBAAkB;YAClC,SAAS,EAAE,aAAa;YACxB,aAAa,EAAE,yBAAyB;YACxC,SAAS,EAAE,oBAAoB;YAC/B,eAAe,EAAE,mBAAmB;YACpC,eAAe,EAAE,mBAAmB;YACpC,oBAAoB,EAAE,6BAA6B;YACnD,GAAG,EAAE,WAAW;YAChB,MAAM,EAAE,cAAc;YACtB,QAAQ,EAAE,UAAU;SACrB;QACD,KAAK,EAAE;YACL,KAAK,EAAE,OAAO;YACd,SAAS,EAAE,YAAY;YACvB,MAAM,EAAE,cAAc;YACtB,GAAG,EAAE,WAAW;YAChB,SAAS,EAAE,yDAAyD;SACrE;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,MAAM,EAAE,aAAa;YACrB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,iBAAiB;YAChC,GAAG,EAAE,kCAAkC;YACvC,eAAe,EAAE,oBAAoB;SACtC;QACD,KAAK,EAAE;YACL,KAAK,EAAE,OAAO;YACd,WAAW,EAAE,eAAe;YAC5B,WAAW,EAAE,eAAe;YAC5B,UAAU,EAAE,iBAAiB;YAC7B,WAAW,EAAE,kBAAkB;YAC/B,MAAM,EAAE,YAAY;YACpB,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,cAAc;SACzB;QACD,EAAE,EAAE;YACF,MAAM,EAAE,wBAAwB;SACjC;QACD,KAAK,EAAE;YACL,KAAK,EAAE,OAAO;YACd,CAAC,EAAE,QAAQ;YACX,UAAU,EAAE,OAAO;YACnB,GAAG,EAAE,MAAM;YACX,EAAE,EAAE,UAAU;YACd,EAAE,EAAE,UAAU;YACd,EAAE,EAAE,UAAU;YACd,EAAE,EAAE,UAAU;YACd,EAAE,EAAE,UAAU;YACd,EAAE,EAAE,UAAU;SACf;QACD,KAAK,EAAE;YACL,SAAS,EAAE,gBAAgB;YAC3B,OAAO,EAAE,cAAc;SACxB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE,aAAa;YACzB,QAAQ,EAAE,WAAW;SACtB;QACD,SAAS,EAAE;YACT,SAAS,EAAE,WAAW;YACtB,OAAO,EAAE,SAAS;YAClB,MAAM,EAAE,QAAQ;YAChB,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,cAAc;YACtB,KAAK,EAAE,aAAa;YACpB,OAAO,EAAE,cAAc;SACxB;QACD,KAAK,EAAE;YACL,MAAM,EAAE,cAAc;YACtB,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,kBAAkB;YAC9B,UAAU,EAAE,kBAAkB;YAC9B,WAAW,EAAE,aAAa;YAC1B,cAAc,EAAE,iBAAiB;YACjC,KAAK,EAAE,OAAO;YACd,cAAc,EAAE,kBAAkB;SACnC;QACD,QAAQ,EAAE;YACR,SAAS,EAAE,oBAAoB;YAC/B,KAAK,EAAE,OAAO;YACd,cAAc,EAAE,iBAAiB;YACjC,MAAM,EAAE,QAAQ;YAChB,mBAAmB,EAAE,sBAAsB;YAC3C,aAAa,EAAE,gBAAgB;YAC/B,SAAS,EAAE,YAAY;SACxB;QACD,IAAI,EAAE;YACJ,iBAAiB,EAAE,kBAAkB;YACrC,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE,yBAAyB;YACjC,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,kBAAkB;YAC1B,QAAQ,EAAE,oBAAoB;YAC9B,WAAW,EAAE,uBAAuB;YACpC,eAAe,EAAE,2BAA2B;YAC5C,cAAc,EAAE,eAAe;YAC/B,aAAa,EAAE,gBAAgB;YAC/B,eAAe,EAAE,kBAAkB;YACnC,cAAc,EAAE,iBAAiB;YACjC,aAAa,EAAE,gBAAgB;YAC/B,qBAAqB,EAAE,uBAAuB;YAC9C,mBAAmB,EAAE,qBAAqB;YAC1C,SAAS,EAAE,8BAA8B;YACzC,QAAQ,EAAE,6BAA6B;YACvC,YAAY,EAAE,sDAAsD;YACpE,UAAU,EAAE,sCAAsC;YAClD,UAAU,EAAE,sCAAsC;YAClD,UAAU,EAAE,sCAAsC;YAClD,UAAU,EAAE,sCAAsC;YAClD,UAAU,EAAE,sCAAsC;YAClD,UAAU,EAAE,sCAAsC;YAClD,sBAAsB,EAAE,wBAAwB;YAChD,iBAAiB,EAAE,kBAAkB;SACtC;QACD,OAAO,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;SACb;QACD,WAAW,EAAE;YACX,WAAW,EAAE,oBAAoB;YACjC,MAAM,EAAE,2BAA2B;SACpC;KACF;CACF,CAAC,CAAC;;AC3JH,IAAM,OAAO,GAAG;IACd,WAAW,EAAE,CAAC;IACd,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE,EAAE;;IAGZ,MAAM,EAAE,EAAE;IACV,IAAI,EAAE,EAAE;IACR,OAAO,EAAE,EAAE;IACX,MAAM,EAAE,EAAE;;IAGV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;;IAGV,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IACP,GAAG,EAAE,EAAE;IAEP,OAAO,EAAE,GAAG;IACZ,aAAa,EAAE,GAAG;IAClB,WAAW,EAAE,GAAG;IAChB,cAAc,EAAE,GAAG;CACpB,CAAC;;;;;;;;;AAUF,UAAe;;;;;;;IAOb,MAAM,EAAE,UAAC,OAAO;QACd,OAAO,KAAK,CAAC,QAAQ,CAAC;YACpB,OAAO,CAAC,SAAS;YACjB,OAAO,CAAC,GAAG;YACX,OAAO,CAAC,KAAK;YACb,OAAO,CAAC,KAAK;YACb,OAAO,CAAC,MAAM;SACf,EAAE,OAAO,CAAC,CAAC;KACb;;;;;;;IAOD,MAAM,EAAE,UAAC,OAAO;QACd,OAAO,KAAK,CAAC,QAAQ,CAAC;YACpB,OAAO,CAAC,IAAI;YACZ,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,KAAK;YACb,OAAO,CAAC,IAAI;SACb,EAAE,OAAO,CAAC,CAAC;KACb;;;;;IAKD,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;IACxC,IAAI,EAAE,OAAO;CACd,CAAC;;ACrFF;;;;;;;;;AASA,0BAA0B,SAAS,EAAE,OAAO;IAC1C,IAAI,SAAS,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;IAC1C,IAAI,MAAM,CAAC;IAEX,IAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;IAC/C,IAAI,aAAa,CAAC;IAClB,IAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACpD,KAAK,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QACrD,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;YAClC,SAAS;SACV;QACD,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7C,IAAI,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE;YAC3D,MAAM;SACP;QACD,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;KACpC;IAED,IAAI,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;QACtD,IAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvD,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,cAAc,CAAC,iBAAiB,CAAC,aAAa,IAAI,SAAS,CAAC,CAAC;QAC7D,cAAc,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACxC,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC;QAE/E,IAAM,WAAW,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;QAC1C,WAAW,CAAC,WAAW,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QACxD,IAAI,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;QAE/D,OAAO,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE;YAC1E,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;SACvC;;QAGD,IAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC;QAEpC,IAAI,OAAO,IAAI,WAAW,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC;YAC3E,SAAS,KAAK,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE;YAC5C,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;SACvC;QAED,SAAS,GAAG,WAAW,CAAC;QACxB,MAAM,GAAG,SAAS,CAAC;KACpB;IAED,OAAO;QACL,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,MAAM;KACf,CAAC;CACH;;;;;;AAOD,0BAA0B,KAAK;IAC7B,IAAM,aAAa,GAAG,UAAS,SAAS,EAAE,MAAM;QAC9C,IAAI,IAAI,EAAE,iBAAiB,CAAC;QAE5B,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACzB,IAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;YACpE,IAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC;YAChE,IAAI,GAAG,aAAa,IAAI,SAAS,CAAC,UAAU,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;YAC/D,iBAAiB,GAAG,CAAC,aAAa,CAAC;SACpC;aAAM;YACL,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;YACjD,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACpB,OAAO,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aAC/B;YAED,MAAM,GAAG,CAAC,CAAC;YACX,iBAAiB,GAAG,KAAK,CAAC;SAC3B;QAED,OAAO;YACL,IAAI,EAAE,IAAI;YACV,eAAe,EAAE,iBAAiB;YAClC,MAAM,EAAE,MAAM;SACf,CAAC;KACH,CAAC;IAEF,IAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;IAClD,IAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAErD,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACzC,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9C,OAAO,SAAS,CAAC;CAClB;;;;;;;;;;AAWD;IACE,sBAAY,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACxB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;;QAGb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;;QAElD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;QAE1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;;QAE9C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;QAE1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KAC3C;;IAGD,kCAAW,GAAX;QACE,IAAI,GAAG,CAAC,iBAAiB,EAAE;YACzB,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACxC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACpC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YAElC,OAAO,QAAQ,CAAC;SACjB;aAAM;YACL,IAAM,SAAS,GAAG,gBAAgB,CAAC;gBACjC,IAAI,EAAE,IAAI,CAAC,EAAE;gBACb,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB,CAAC,CAAC;YAEH,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,gBAAgB,CAAC;gBACjD,IAAI,EAAE,IAAI,CAAC,EAAE;gBACb,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB,CAAC,CAAC,CAAC;YAEJ,OAAO,SAAS,CAAC;SAClB;KACF;IAED,gCAAS,GAAT;QACE,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,EAAE,EAAE,IAAI,CAAC,EAAE;SACZ,CAAC;KACH;IAED,oCAAa,GAAb;QACE,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,EAAE;YACb,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAC;KACH;IAED,kCAAW,GAAX;QACE,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,EAAE;YACb,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAC;KACH;;;;IAKD,6BAAM,GAAN;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACrC,IAAI,GAAG,CAAC,iBAAiB,EAAE;YACzB,IAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1C,IAAI,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE;gBAC5B,SAAS,CAAC,eAAe,EAAE,CAAC;aAC7B;YACD,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;SAC/B;aAAM;YACL,SAAS,CAAC,MAAM,EAAE,CAAC;SACpB;QAED,OAAO,IAAI,CAAC;KACb;;;;;;IAOD,qCAAc,GAAd,UAAe,SAAS;QACtB,IAAM,MAAM,GAAGA,GAAC,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;QACrC,IAAI,SAAS,CAAC,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACpD,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;SACnF;QAED,OAAO,IAAI,CAAC;KACb;;;;IAKD,gCAAS,GAAT;;;;;;QAME,IAAM,eAAe,GAAG,UAAS,KAAK,EAAE,aAAa;YACnD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC;iBACpD,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;iBAC3E,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC;iBACzE,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE;gBACrF,OAAO,KAAK,CAAC;aACd;;YAGD,IAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;iBAChG,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,aAAa,CAAC,EAAE;;gBAEtG,IAAI,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAC7B,OAAO,KAAK,CAAC;iBACd;;gBAED,aAAa,GAAG,CAAC,aAAa,CAAC;aAChC;YAED,IAAM,SAAS,GAAG,aAAa,GAAG,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC;kBAC1F,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC;YACjE,OAAO,SAAS,IAAI,KAAK,CAAC;SAC3B,CAAC;QAEF,IAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5D,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,CAAC;QAE/F,OAAO,IAAI,YAAY,CACrB,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,MAAM,EACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,CAChB,CAAC;KACH;;;;;;;;;;IAWD,4BAAK,GAAL,UAAM,IAAI,EAAE,OAAO;QACjB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;QAEvB,IAAM,eAAe,GAAG,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC;QAC3D,IAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,aAAa,CAAC;;QAGvD,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEpC,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAM,aAAa,GAAG,EAAE,CAAC;QAEzB,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAS,KAAK;YAChD,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC9B,OAAO;aACR;YAED,IAAI,IAAI,CAAC;YACT,IAAI,aAAa,EAAE;gBACjB,IAAI,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;oBAC9B,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBAChC;gBACD,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5E,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;iBACnB;aACF;iBAAM,IAAI,eAAe,EAAE;gBAC1B,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACvC;iBAAM;gBACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aACnB;YAED,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAClB;SACF,EAAE,IAAI,CAAC,CAAC;QAET,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC5B;;;;;IAMD,qCAAc,GAAd;QACE,OAAO,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;KAC7C;;;;;;;IAQD,6BAAM,GAAN,UAAO,IAAI;QACT,IAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAClD,IAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAEhD,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW,EAAE;YAClC,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7D;QAED,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAExC,IAAI,aAAa,EAAE;YACjB,cAAc,CAAC,EAAE,GAAG,aAAa,CAAC;YAClC,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC;SACvB;QAED,IAAI,WAAW,EAAE;YACf,cAAc,CAAC,EAAE,GAAG,WAAW,CAAC;YAChC,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;SACjD;QAED,OAAO,IAAI,YAAY,CACrB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,CAClB,CAAC;KACH;;;;;IAMD,+BAAQ,GAAR,UAAS,iBAAiB;QACxB,IAAI,iBAAiB,EAAE;YACrB,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7D;aAAM;YACL,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7D;KACF;;;;IAKD,gCAAS,GAAT;QACE,IAAM,eAAe,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;QAC5C,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAExC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;YAC/D,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC5B;QAED,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE;YACjE,cAAc,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/C,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC;YAEtB,IAAI,eAAe,EAAE;gBACnB,cAAc,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,CAAC;gBACtC,cAAc,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;aACvC;SACF;QAED,OAAO,IAAI,YAAY,CACrB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,CAClB,CAAC;KACH;;;;;IAMD,qCAAc,GAAd;QACE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,OAAO,IAAI,CAAC;SACb;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC7B,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;YAC5B,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;;QAGH,IAAM,KAAK,GAAG,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,UAAS,KAAK;YAClE,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SAC3C,CAAC,CAAC;QAEH,IAAM,YAAY,GAAG,EAAE,CAAC;QACxBA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAS,GAAG,EAAE,IAAI;;YAE9B,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACzD,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAC3B;YACD,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACzB,CAAC,CAAC;;QAGHA,GAAC,CAAC,IAAI,CAAC,YAAY,EAAE,UAAS,GAAG,EAAE,IAAI;YACrC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SACzB,CAAC,CAAC;QAEH,OAAO,IAAI,YAAY,CACrB,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,MAAM,CACb,CAAC,SAAS,EAAE,CAAC;KACf;;;;IAKD,+BAAQ,GAAR,UAAS,IAAI;QACX,OAAO;YACL,IAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YAC7C,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;SACjE,CAAC;KACH;;;;;IAMD,mCAAY,GAAZ,UAAa,IAAI;QACf,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE;YAC9C,OAAO,KAAK,CAAC;SACd;QAED,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACzC,OAAO,IAAI,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;KAChD;;;;IAKD,kCAAW,GAAX;QACE,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;KACnD;;;;;;IAOD,6CAAsB,GAAtB;QACE,IAAI,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACxD,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;YAClC,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SACvE;;;;;;QAOD,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC7B,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACpD,OAAO,GAAG,CAAC;SACZ;;QAGD,IAAI,WAAW,CAAC;QAChB,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACxB,IAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;gBAC9B,WAAW,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;aAC5E;SACF;aAAM;YACL,WAAW,GAAG,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SAC9D;;QAGD,IAAI,cAAc,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC;QAC3E,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;;QAGhG,IAAI,cAAc,CAAC,MAAM,EAAE;YACzB,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC;YACvD,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;SACxD;QAED,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;KACzB;;;;;;;IAQD,iCAAU,GAAV,UAAW,IAAI;QACb,IAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,cAAc,EAAE,CAAC;QAC3D,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAC9D;aAAM;YACL,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAClC;QAED,OAAO,IAAI,CAAC;KACb;;;;IAKD,gCAAS,GAAT,UAAU,MAAM;QACd,IAAM,iBAAiB,GAAGA,GAAC,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAE5D,IAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,cAAc,EAAE,CAAC;QAE3D,OAAO,UAAU,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,UAAS,SAAS;YAChD,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;SAClC,CAAC,CAAC,OAAO,EAAE,CAAC;KACd;;;;;;IAOD,+BAAQ,GAAR;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACrC,OAAO,GAAG,CAAC,iBAAiB,GAAG,SAAS,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC;KACtE;;;;;;;IAQD,mCAAY,GAAZ,UAAa,SAAS;QACpB,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAElC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC;SACb;QAED,IAAM,UAAU,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAS,KAAK;YAC5D,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAChC,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE;YACb,QAAQ,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAS,KAAK;gBACpD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAChC,CAAC,CAAC;SACJ;QAED,OAAO,IAAI,YAAY,CACrB,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,MAAM,EACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,CAChB,CAAC;KACH;;;;;;IAOD,+BAAQ,GAAR,UAAS,QAAQ;QACf,OAAO;YACL,CAAC,EAAE;gBACD,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;gBAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB;YACD,CAAC,EAAE;gBACD,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;gBAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB;SACF,CAAC;KACH;;;;;;IAOD,mCAAY,GAAZ,UAAa,KAAK;QAChB,OAAO;YACL,CAAC,EAAE;gBACD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChE,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB;YACD,CAAC,EAAE;gBACD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChE,MAAM,EAAE,IAAI,CAAC,EAAE;aAChB;SACF,CAAC;KACH;;;;;IAMD,qCAAc,GAAd;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACrC,OAAO,SAAS,CAAC,cAAc,EAAE,CAAC;KACnC;IACH,mBAAC;CAAA,IAAA;;;;;;;;AASD,YAAe;;;;;;;;;;IAUb,MAAM,EAAE,UAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAC7B,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACzC;aAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;SACzC;aAAM;YACL,IAAI,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3C,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;aACxE;YACD,OAAO,YAAY,CAAC;SACrB;KACF;IAED,mBAAmB,EAAE;QACnB,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QACnB,IAAI,GAAG,CAAC,iBAAiB,EAAE;YACzB,IAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC,EAAE;gBAC5C,OAAO,IAAI,CAAC;aACb;iBAAM,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;;;gBAG3C,OAAO,IAAI,CAAC;aACb;YAED,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC1C,EAAE,GAAG,SAAS,CAAC,cAAc,CAAC;YAC9B,EAAE,GAAG,SAAS,CAAC,WAAW,CAAC;YAC3B,EAAE,GAAG,SAAS,CAAC,YAAY,CAAC;YAC5B,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC;SAC1B;aAAM;YACL,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YACnD,IAAM,YAAY,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC3C,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAM,cAAc,GAAG,SAAS,CAAC;YACjC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE9B,IAAI,UAAU,GAAG,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YACxD,IAAI,QAAQ,GAAG,gBAAgB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;;YAGrD,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC;gBAC9D,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAC/D,QAAQ,CAAC,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC,IAAI,EAAE;gBACjD,UAAU,GAAG,QAAQ,CAAC;aACvB;YAED,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC;YACrB,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC;YACvB,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC;YACnB,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC;SACtB;QAED,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KACzC;;;;;;;;;IAUD,cAAc,EAAE,UAAS,IAAI;QAC3B,IAAI,EAAE,GAAG,IAAI,CAAC;QACd,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,IAAI,EAAE,GAAG,IAAI,CAAC;QACd,IAAI,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;;QAG5B,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YAClB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACjC,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;SACpB;QACD,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAChB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACjC,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;SACpB;aAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACzB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;YAC7B,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;SACpB;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KACpC;;;;;;;IAQD,oBAAoB,EAAE,UAAS,IAAI;QACjC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACjD;;;;;;;IAQD,mBAAmB,EAAE,UAAS,IAAI;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;KAC7C;;;;;;;;;;IAWD,kBAAkB,EAAE,UAAS,QAAQ,EAAE,QAAQ;QAC7C,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACzD,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7B,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACzD,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7B,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KACzC;;;;;;;;;;IAWD,sBAAsB,EAAE,UAAS,QAAQ,EAAE,KAAK;QAC9C,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7B,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7B,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAClE,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAElE,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KACzC;CACF,CAAC;;ACrxBF;;;;;;;;AAQA,2BAAkC,IAAI;IACpC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;QACzBA,GAAC,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE,EAAE;YACzB,MAAM,EAAE,UAAC,CAAC;gBACR,IAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;gBAChC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAC3B;YACD,OAAO,EAAE,UAAC,GAAG;gBACX,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACtB;SACF,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACxB,CAAC,CAAC,OAAO,EAAE,CAAC;CACd;;;;;;;;;AAUD,qBAA4B,GAAG;IAC7B,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;QACzB,IAAM,IAAI,GAAGA,GAAC,CAAC,OAAO,CAAC,CAAC;QAExB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACxB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxB,CAAC,CAAC,GAAG,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;YAC1B,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACvB,CAAC,CAAC,GAAG,CAAC;YACL,OAAO,EAAE,MAAM;SAChB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAC7C,CAAC,CAAC,OAAO,EAAE,CAAC;CACd;;AC5Cc;IACb,iBAAY,SAAS;QACnB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9B;IAED,8BAAY,GAAZ;QACE,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAM,aAAa,GAAG,EAAC,CAAC,EAAE,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAC,EAAE,CAAC,EAAE,EAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAC,EAAC,CAAC;QAE3E,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YAC/B,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC;SAC9D,CAAC;KACH;IAED,+BAAa,GAAb,UAAc,QAAQ;QACpB,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACxC;QACD,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC9B,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;SACrE;KACF;;;;;;IAOD,wBAAM,GAAN;;QAEE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;YACnE,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;;QAGD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;;QAGrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;KAClD;;;;;IAMD,uBAAK,GAAL;;QAEE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;QAGhB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;;QAGtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;QAGxB,IAAI,CAAC,UAAU,EAAE,CAAC;KACnB;;;;IAKD,sBAAI,GAAJ;;QAEE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;YACnE,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;SAClD;KACF;;;;IAKD,sBAAI,GAAJ;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE;YAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;SAClD;KACF;;;;IAKD,4BAAU,GAAV;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;;QAGnB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YACxC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SACpD;;QAGD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;KACtC;IACH,cAAC;CAAA;;AClGc;IAAA;KAuJd;;;;;;;;;;;;;;IAzIC,yBAAS,GAAT,UAAU,IAAI,EAAE,aAAa;QAC3B,IAAI,GAAG,CAAC,aAAa,GAAG,GAAG,EAAE;YAC3B,IAAM,QAAM,GAAG,EAAE,CAAC;YAClBA,GAAC,CAAC,IAAI,CAAC,aAAa,EAAE,UAAC,GAAG,EAAE,YAAY;gBACtC,QAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;aAC/C,CAAC,CAAC;YACH,OAAO,QAAM,CAAC;SACf;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KAChC;;;;;;;IAQD,wBAAQ,GAAR,UAAS,KAAK;QACZ,IAAM,UAAU,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;QAChG,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC;QAC1D,SAAS,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9D,OAAO,SAAS,CAAC;KAClB;;;;;;;IAQD,yBAAS,GAAT,UAAU,GAAG,EAAE,SAAS;QACtBA,GAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;YAC3B,eAAe,EAAE,IAAI;SACtB,CAAC,EAAE,UAAC,GAAG,EAAE,IAAI;YACZA,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SACxB,CAAC,CAAC;KACJ;;;;;;;;;;;IAYD,0BAAU,GAAV,UAAW,GAAG,EAAE,OAAO;QACrB,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;QAEtB,IAAM,QAAQ,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,CAAC;QACzD,IAAM,oBAAoB,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,oBAAoB,CAAC,CAAC;QACzE,IAAM,mBAAmB,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAEvE,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;YACrB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;SAC/C;QAED,IAAI,IAAI,GAAG,GAAG,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;YAClC,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC,GAAG,CAAC,UAAC,IAAI;YACV,OAAO,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SACxE,CAAC,CAAC;QAEH,IAAI,oBAAoB,EAAE;YACxB,IAAI,mBAAmB,EAAE;gBACvB,IAAM,cAAY,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;;gBAEjC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,UAAC,IAAI;oBACzB,OAAO,KAAK,CAAC,QAAQ,CAAC,cAAY,EAAE,IAAI,CAAC,CAAC;iBAC3C,CAAC,CAAC;aACJ;YAED,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;gBACpB,IAAM,QAAQ,GAAG,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACrD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAClC,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnCA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;oBACtB,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC5C,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBAClB,CAAC,CAAC;gBACH,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC7B,CAAC,CAAC;SACJ;aAAM;YACL,OAAO,KAAK,CAAC;SACd;KACF;;;;;;;IAQD,uBAAO,GAAP,UAAQ,GAAG;QACT,IAAM,KAAK,GAAGA,GAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,UAAU,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QACrE,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;;;QAIrC,IAAI;YACF,SAAS,GAAGA,GAAC,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC9B,WAAW,EAAE,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,QAAQ;gBACnE,aAAa,EAAE,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,QAAQ;gBACzE,gBAAgB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,QAAQ;gBAClF,gBAAgB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,QAAQ;gBAClF,kBAAkB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,QAAQ;gBACxF,oBAAoB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,QAAQ;gBAC9F,aAAa,EAAE,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,aAAa,CAAC;aAClF,CAAC,CAAC;SACJ;QAAC,OAAO,CAAC,EAAE,GAAE;;QAGd,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;YACnB,SAAS,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;SAClC;aAAM;YACL,IAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE,QAAQ,CAAC,CAAC;YACvE,IAAM,WAAW,GAAGA,GAAC,CAAC,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/E,SAAS,CAAC,YAAY,CAAC,GAAG,WAAW,GAAG,WAAW,GAAG,SAAS,CAAC;SACjE;QAED,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;YACrC,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;SAClD;aAAM;YACL,IAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;YACjG,SAAS,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SAClD;QAED,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1E,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/D,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;QAEtB,OAAO,SAAS,CAAC;KAClB;IACH,YAAC;CAAA;;ACvJc;IAAA;KAkMd;;;;IA9LC,kCAAiB,GAAjB,UAAkB,QAAQ;QACxB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACjC;;;;IAKD,oCAAmB,GAAnB,UAAoB,QAAQ;QAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KACjC;;;;IAKD,uBAAM,GAAN,UAAO,QAAQ;QAAf,iBAoBC;QAnBC,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;QAE5D,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/D,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAEnEA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;YAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAClB,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;aAChD;iBAAM;gBACLA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;oBACtBA,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,UAAC,GAAG,EAAE,GAAG;wBACjC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;qBACtC,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,EAAE,CAAC;KACd;;;;IAKD,wBAAO,GAAP,UAAQ,QAAQ;QAAhB,iBAqBC;QApBC,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;QAE5D,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/D,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAEnEA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;YAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAClB,KAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3B;iBAAM;gBACLA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;oBACtBA,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,UAAC,GAAG,EAAE,GAAG;wBACjC,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC/B,OAAO,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;qBACjC,CAAC,CAAC;iBACJ,CAAC,CAAC;aACJ;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,EAAE,CAAC;KACd;;;;;;IAOD,2BAAU,GAAV,UAAW,QAAQ,EAAE,QAAQ;QAA7B,iBAgCC;QA/BC,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;QAE5D,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7D,IAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACzC,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;QAGnE,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE;YACrC,IAAI,cAAY,GAAG,EAAE,CAAC;YACtBA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;gBAC5B,cAAY,GAAG,cAAY,CAAC,MAAM,CAAC,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;aACpE,CAAC,CAAC;YACH,KAAK,GAAG,cAAY,CAAC;;SAEtB;aAAM;YACL,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;gBACtC,eAAe,EAAE,IAAI;aACtB,CAAC,CAAC,MAAM,CAAC,UAAC,QAAQ;gBACjB,OAAO,CAACA,GAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aACxC,CAAC,CAAC;YAEH,IAAI,SAAS,CAAC,MAAM,EAAE;gBACpBA,GAAC,CAAC,IAAI,CAAC,SAAS,EAAE,UAAC,GAAG,EAAE,QAAQ;oBAC9B,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;iBACjC,CAAC,CAAC;aACJ;iBAAM;gBACL,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC5C;SACF;QAED,KAAK,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;KACxD;;;;;;IAOD,yBAAQ,GAAR,UAAS,KAAK,EAAE,QAAQ;QACtB,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC;QAC1E,IAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC;QAElE,IAAM,QAAQ,GAAG,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;;QAGjF,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;YACrB,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;SAC9D,CAAC,CAAC;;QAGH,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEtC,IAAI,QAAQ,EAAE;YACZ,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;YAChE,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SACtB;QAED,OAAO,KAAK,CAAC;KACd;;;;;;;;IASD,4BAAW,GAAX,UAAY,UAAU,EAAE,eAAe;QACrC,IAAI,aAAa,GAAG,EAAE,CAAC;QAEvBA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;YAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE/B,IAAM,QAAQ,GAAG,eAAe,GAAG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;YACxF,IAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACxE,IAAI,EAAE,IAAI,CAAC,UAAU;gBACrB,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;aAC/B,EAAE;gBACD,sBAAsB,EAAE,IAAI;aAC7B,CAAC,GAAG,IAAI,CAAC;YAEV,IAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACzC,IAAI,EAAE,IAAI,CAAC,UAAU;gBACrB,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;aAC3B,EAAE;gBACD,sBAAsB,EAAE,IAAI;aAC7B,CAAC,CAAC;YAEH,KAAK,GAAG,eAAe,GAAG,GAAG,CAAC,cAAc,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC;kBAC9D,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;YAGvD,IAAI,eAAe,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBACvD,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;oBACrB,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;iBAC/B,CAAC,CAAC;aACJ;YAEDA,GAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,UAAC,GAAG,EAAE,IAAI;gBAC5C,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACjC,CAAC,CAAC;;YAGH,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;YAClEA,GAAC,CAAC,IAAI,CAAC,SAAS,EAAE,UAAC,GAAG,EAAE,QAAQ;gBAC9B,IAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC9EA,GAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,UAAC,GAAG,EAAE,QAAQ;oBACxC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;wBAC7B,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;qBAC5B;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;YAEH,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC7C,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;KACtB;IACH,aAAC;CAAA;;ACnMD;;;;;;AAMe;IACb;;QAEE,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;KAC5B;;;;;;;IAQD,0BAAS,GAAT,UAAU,GAAG,EAAE,OAAO;QACpB,IAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;QACvE,GAAG,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;QAC3B,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAE1B,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACjC,GAAG,CAAC,MAAM,EAAE,CAAC;KACd;;;;IAKD,gCAAe,GAAf,UAAgB,QAAQ;QACtB,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;;QAGjC,GAAG,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;;QAG3B,GAAG,GAAG,GAAG,CAAC,sBAAsB,EAAE,CAAC;;QAGnC,IAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,QAAQ,CAAC;;QAEb,IAAI,SAAS,EAAE;;YAEb,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;gBAEjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACtD,OAAO;;aAER;iBAAM,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;;gBAEpG,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;gBACjD,QAAQ,GAAG,SAAS,CAAC;;aAEtB;iBAAM;gBACL,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;gBAEzD,IAAI,YAAY,GAAG,GAAG,CAAC,cAAc,CAAC,SAAS,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;gBACpE,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;gBAEpFA,GAAC,CAAC,IAAI,CAAC,YAAY,EAAE,UAAC,GAAG,EAAE,MAAM;oBAC/B,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBACpB,CAAC,CAAC;;gBAGH,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAC/G,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;iBACvC;aACF;;SAEF;aAAM;YACL,IAAM,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACvC,QAAQ,GAAGA,GAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,IAAI,EAAE;gBACR,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;aACrC;iBAAM;gBACL,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aAC9B;SACF;QAED,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;KACzE;IACH,aAAC;CAAA;;ACpFD;;;;;;;AAOA,IAAM,iBAAiB,GAAG,UAAS,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ;IACpE,IAAM,WAAW,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;IACjD,IAAM,aAAa,GAAG,EAAE,CAAC;IACzB,IAAM,eAAe,GAAG,EAAE,CAAC;;;;;;;IAS3B;QACE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,EAAE;YAClI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,UAAU,CAAC,CAAC;YACtE,OAAO;SACR;QACD,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC;QAC1C,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,IAAI,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;YAC7H,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC;YACrE,OAAO;SACR;QACD,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC;KACxD;;;;;;;;;;IAWD,iCAAiC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa;QAC1G,IAAM,WAAW,GAAG;YAClB,SAAS,EAAE,OAAO;YAClB,UAAU,EAAE,QAAQ;YACpB,WAAW,EAAE,SAAS;YACtB,WAAW,EAAE,SAAS;YACtB,WAAW,EAAE,aAAa;SAC3B,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;YAC5B,aAAa,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SAC9B;QACD,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;KAClD;;;;;;;IAQD,uBAAuB,mBAAmB,EAAE,YAAY,EAAE,kBAAkB,EAAE,kBAAkB;QAC9F,OAAO;YACL,UAAU,EAAE,mBAAmB,CAAC,QAAQ;YACxC,QAAQ,EAAE,YAAY;YACtB,cAAc,EAAE;gBACd,UAAU,EAAE,kBAAkB;gBAC9B,WAAW,EAAE,kBAAkB;aAChC;SACF,CAAC;KACH;;;;;;;IAQD,0BAA0B,QAAQ,EAAE,SAAS;QAC3C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;YACvC,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,YAAY,GAAG,SAAS,CAAC;QAC7B,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;YAC5C,YAAY,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;gBAC1C,OAAO,YAAY,CAAC;aACrB;SACF;KACF;;;;;;;IAQD,8BAA8B,GAAG,EAAE,IAAI;QACrC,IAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACjE,IAAM,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QAC1C,IAAM,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QAC1C,IAAM,kBAAkB,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1G,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;;QAGnG,IAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,aAAa,GAAG,CAAC,EAAE;YACrB,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,EAAE;gBACzC,IAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACvC,gBAAgB,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBACpE,uBAAuB,CAAC,YAAY,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;aACzF;SACF;;QAGD,IAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAChG,IAAI,aAAa,GAAG,CAAC,EAAE;YACrB,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,EAAE;gBACzC,IAAM,aAAa,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,GAAG,SAAS,GAAG,EAAE,EAAE,CAAC;gBACvE,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBACxE,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aAC7F;SACF;KACF;;;;;;;;;IAUD,0BAA0B,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,cAAc;QACjE,IAAI,QAAQ,KAAK,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,cAAc,EAAE;YAC7H,WAAW,CAAC,MAAM,EAAE,CAAC;SACtB;KACF;;;;IAKD;QACE,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YACzD,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;YACnC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBAC7D,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;aACxD;SACF;KACF;;;;;;IAOD,qCAAqC,IAAI;QACvC,QAAQ,KAAK;YACX,KAAK,iBAAiB,CAAC,KAAK,CAAC,MAAM;gBACjC,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,OAAO,iBAAiB,CAAC,YAAY,CAAC,iBAAiB,CAAC;iBACzD;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAAG;gBAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;oBACrC,OAAO,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC;iBAC/C;qBAAM,IAAI,IAAI,CAAC,SAAS,EAAE;oBACzB,OAAO,iBAAiB,CAAC,YAAY,CAAC,iBAAiB,CAAC;iBACzD;gBACD,MAAM;SACT;QACD,OAAO,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC;KAClD;;;;;;IAOD,kCAAkC,IAAI;QACpC,QAAQ,KAAK;YACX,KAAK,iBAAiB,CAAC,KAAK,CAAC,MAAM;gBACjC,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,OAAO,iBAAiB,CAAC,YAAY,CAAC,YAAY,CAAC;iBACpD;qBAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;oBAC3C,OAAO,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;iBAC9C;gBACD,MAAM;YACR,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAAG;gBAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,OAAO,iBAAiB,CAAC,YAAY,CAAC,YAAY,CAAC;iBACpD;qBAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;oBAC3C,OAAO,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;iBAC9C;gBACD,MAAM;SACT;QACD,OAAO,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC;KAC/C;IAED;QACE,aAAa,EAAE,CAAC;QAChB,kBAAkB,EAAE,CAAC;KACtB;;;;;;;IASD,IAAI,CAAC,aAAa,GAAG;QACnB,IAAM,QAAQ,GAAG,CAAC,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnF,IAAM,QAAQ,GAAG,CAAC,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEtF,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,OAAO,WAAW,EAAE;YAClB,IAAM,WAAW,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,cAAc,CAAC;YAChE,IAAM,WAAW,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,cAAc,CAAC;YAChE,IAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;YACvC,IAAI,CAAC,GAAG,EAAE;gBACR,WAAW,GAAG,KAAK,CAAC;gBACpB,OAAO,eAAe,CAAC;aACxB;YACD,IAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;YAC9B,IAAI,CAAC,IAAI,EAAE;gBACT,WAAW,GAAG,KAAK,CAAC;gBACpB,OAAO,eAAe,CAAC;aACxB;;YAGD,IAAI,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;YACzD,QAAQ,MAAM;gBACZ,KAAK,iBAAiB,CAAC,aAAa,CAAC,GAAG;oBACtC,YAAY,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,iBAAiB,CAAC,aAAa,CAAC,MAAM;oBACzC,YAAY,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;oBACjD,MAAM;aACT;YACD,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;YAClF,cAAc,EAAE,CAAC;SAClB;QAED,OAAO,eAAe,CAAC;KACxB,CAAC;IAEF,IAAI,EAAE,CAAC;CACR,CAAC;;;;;AAKF,iBAAiB,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;;;;;AAKpD,iBAAiB,CAAC,aAAa,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;;;;;AAK5D,iBAAiB,CAAC,YAAY,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,mBAAmB,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;;;;;;;;AAS5G;IAAA;KAkSd;;;;;;;IA3RC,mBAAG,GAAH,UAAI,GAAG,EAAE,OAAO;QACd,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAM,KAAK,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpD,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAI,QAAQ,EAAE;YACZ,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SACpC;KACF;;;;;;;;IASD,sBAAM,GAAN,UAAO,GAAG,EAAE,QAAQ;QAClB,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAE5D,IAAM,SAAS,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACxC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACvD,IAAM,IAAI,GAAGA,GAAC,CAAC,KAAK,GAAG,YAAY,GAAG,QAAQ,CAAC,CAAC;QAEhD,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,GAAG,EACpE,iBAAiB,CAAC,aAAa,CAAC,GAAG,EAAEA,GAAC,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAEvC,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;YACtD,IAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YACpC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAClE,QAAQ,WAAW,CAAC,MAAM;gBACxB,KAAK,iBAAiB,CAAC,YAAY,CAAC,OAAO;oBACzC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;oBAC9D,MAAM;gBACR,KAAK,iBAAiB,CAAC,YAAY,CAAC,YAAY;oBAC9C,IAAI,QAAQ,KAAK,KAAK,EAAE;wBACtB,IAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAC/C,IAAM,gBAAgB,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;wBAClH,IAAI,gBAAgB,EAAE;4BACpB,IAAM,KAAK,GAAGA,GAAC,CAAC,aAAa,CAAC,CAAC,MAAM,CAACA,GAAC,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;4BACxH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BACnB,MAAM;yBACP;qBACF;oBACD,IAAI,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;oBAC/D,aAAa,EAAE,CAAC;oBAChB,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;oBAC5D,MAAM;aACT;SACF;QAED,IAAI,QAAQ,KAAK,KAAK,EAAE;YACtB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACxB;aAAM;YACL,IAAM,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAC1C,IAAI,cAAc,EAAE;gBAClB,IAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;gBAC/DA,GAAC,CAACA,GAAC,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAChE,OAAO;aACR;YACD,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACvB;KACF;;;;;;;;IASD,sBAAM,GAAN,UAAO,GAAG,EAAE,QAAQ;QAClB,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAM,GAAG,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAM,SAAS,GAAGA,GAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;QACpC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEpB,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,MAAM,EACvE,iBAAiB,CAAC,aAAa,CAAC,GAAG,EAAEA,GAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAEvC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YACrE,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;YACzC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAClE,QAAQ,WAAW,CAAC,MAAM;gBACxB,KAAK,iBAAiB,CAAC,YAAY,CAAC,OAAO;oBACzC,IAAI,QAAQ,KAAK,OAAO,EAAE;wBACxBA,GAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;qBACjF;yBAAM;wBACLA,GAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;qBAClF;oBACD,MAAM;gBACR,KAAK,iBAAiB,CAAC,YAAY,CAAC,YAAY;oBAC9C,IAAI,QAAQ,KAAK,OAAO,EAAE;wBACxB,IAAI,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;wBAC/D,aAAa,EAAE,CAAC;wBAChB,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;qBAC7D;yBAAM;wBACLA,GAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;qBAClF;oBACD,MAAM;aACT;SACF;KACF;;;;;;;IAQD,iCAAiB,GAAjB,UAAkB,EAAE;QAClB,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,SAAS,CAAC;SAClB;QAED,IAAM,QAAQ,GAAG,EAAE,CAAC,UAAU,IAAI,EAAE,CAAC;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;gBAC3C,SAAS;aACV;YAED,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;gBACzB,SAAS,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;aACxE;SACF;QAED,OAAO,SAAS,CAAC;KAClB;;;;;;;IAQD,yBAAS,GAAT,UAAU,GAAG;QACX,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAM,GAAG,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAM,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACtD,IAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAE/B,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,GAAG,EACpE,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAEA,GAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAEvC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YACrE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACzB,SAAS;aACV;YAED,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC;YAC/C,IAAM,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC;YAC1D,IAAM,UAAU,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAC9D,IAAI,aAAa,GAAG,CAAC,UAAU,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;YACtE,QAAQ,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM;gBACjC,KAAK,iBAAiB,CAAC,YAAY,CAAC,MAAM;oBACxC,SAAS;gBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,OAAO;oBACzC,IAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClC,IAAI,CAAC,OAAO,EAAE;wBAAE,SAAS;qBAAE;oBAC3B,IAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBACvC,IAAI,UAAU,EAAE;wBACd,IAAI,aAAa,GAAG,CAAC,EAAE;4BACrB,aAAa,EAAE,CAAC;4BAChB,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;4BACvD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;4BAC9D,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC;yBACvC;6BAAM,IAAI,aAAa,KAAK,CAAC,EAAE;4BAC9B,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;4BACvD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;4BAClD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC;yBACvC;qBACF;oBACD,SAAS;gBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,iBAAiB;oBACnD,IAAI,UAAU,EAAE;wBACd,IAAI,aAAa,GAAG,CAAC,EAAE;4BACrB,aAAa,EAAE,CAAC;4BAChB,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;4BAChD,IAAI,eAAe,CAAC,QAAQ,KAAK,MAAM,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;gCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;6BAAE;yBACxG;6BAAM,IAAI,aAAa,KAAK,CAAC,EAAE;4BAC9B,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;4BACpC,IAAI,eAAe,CAAC,QAAQ,KAAK,MAAM,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;gCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;6BAAE;yBACxG;qBACF;oBACD,SAAS;gBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,UAAU;;oBAE5C,SAAS;aACZ;SACF;QACD,GAAG,CAAC,MAAM,EAAE,CAAC;KACd;;;;;;;IAQD,yBAAS,GAAT,UAAU,GAAG;QACX,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAM,GAAG,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAM,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAEtD,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,MAAM,EACvE,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAEA,GAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAEvC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YACrE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACzB,SAAS;aACV;YACD,QAAQ,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM;gBACjC,KAAK,iBAAiB,CAAC,YAAY,CAAC,MAAM;oBACxC,SAAS;gBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,iBAAiB;oBACnD,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC;oBAC/C,IAAM,UAAU,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;oBAC9D,IAAI,UAAU,EAAE;wBACd,IAAI,aAAa,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;wBAC5E,IAAI,aAAa,GAAG,CAAC,EAAE;4BACrB,aAAa,EAAE,CAAC;4BAChB,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;4BAChD,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;gCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;6BAAE;yBACjE;6BAAM,IAAI,aAAa,KAAK,CAAC,EAAE;4BAC9B,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;4BACpC,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;gCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;6BAAE;yBACjE;qBACF;oBACD,SAAS;gBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,UAAU;oBAC5C,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;oBAChD,SAAS;aACZ;SACF;KACF;;;;;;;;IASD,2BAAW,GAAX,UAAY,QAAQ,EAAE,QAAQ,EAAE,OAAO;QACrC,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAI,MAAM,CAAC;QACX,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,MAAM,EAAE,EAAE;YAChD,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;SACxC;QACD,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEtB,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAI,MAAM,CAAC;QACX,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,MAAM,EAAE,EAAE;YAChD,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC;SACrC;QACD,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,IAAM,MAAM,GAAGA,GAAC,CAAC,SAAS,GAAG,MAAM,GAAG,UAAU,CAAC,CAAC;QAClD,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE;YACrC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACzC;QAED,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;KAClB;;;;;;;IAQD,2BAAW,GAAX,UAAY,GAAG;QACb,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5DA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;KACnC;IACH,YAAC;CAAA;;AC/iBD,IAAM,SAAS,GAAG,OAAO,CAAC;;;;AAKX;IACb,gBAAY,OAAO;QAAnB,iBAmSC;QAlSC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAElC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC1E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC9E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACpF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;QAG5D,IAAM,QAAQ,GAAG;YACf,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW;YAC1E,aAAa,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa;YAC7D,aAAa,EAAE,cAAc,EAAE,WAAW;SAC3C,CAAC;QAEF,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YACzD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAC,IAAI;gBAC1B,OAAO,UAAC,KAAK;oBACX,KAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;oBACzC,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;iBACzB,CAAC;aACH,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAC3E;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;YACrC,OAAO,KAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;SAC7D,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;YACrC,OAAO,KAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;SACpD,CAAC,CAAC;QAEH,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;YACjC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,UAAC,GAAG;gBAC3B,OAAO;oBACL,KAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;iBAC7B,CAAC;aACH,EAAE,GAAG,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;SAC1E;QAAA,AAAC;QAEF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC;YACtC,KAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;SAC5C,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;YACxC,KAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC;YAC1C,KAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;SAChD,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;YAC7B,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;SACnC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;YAC9B,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;SACpC,CAAC,CAAC;;;;;;QAOH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,IAAI;YACtC,IAAI,KAAI,CAAC,SAAS,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE;gBACzC,OAAO;aACR;YACD,IAAM,GAAG,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC;YAC/B,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrB,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;SAC1C,CAAC,CAAC;;;;;QAMH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,IAAI;YACtC,IAAI,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC/B,OAAO;aACR;YACD,IAAM,GAAG,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC;YAC/B,IAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACtD,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SAC3D,CAAC,CAAC;;;;;QAKH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,MAAM;YACvC,IAAI,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBACjC,OAAO;aACR;YACD,IAAM,QAAQ,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YACtD,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;SAC1D,CAAC,CAAC;;;;;;QAOH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,OAAO,EAAE,OAAO;YACnD,IAAM,kBAAkB,GAAG,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,kBAAkB,CAAC;YACrE,IAAI,kBAAkB,EAAE;gBACtB,kBAAkB,CAAC,IAAI,CAAC,KAAI,EAAE,OAAO,EAAE,KAAI,CAAC,OAAO,EAAE,KAAI,CAAC,aAAa,CAAC,CAAC;aAC1E;iBAAM;gBACL,KAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aACtC;SACF,CAAC,CAAC;;;;QAKH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC;YAC3C,IAAM,MAAM,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/D,IAAI,MAAM,CAAC,WAAW,EAAE;gBACtB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC;aAC1D;SACF,CAAC,CAAC;;;;;QAMH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;YACvC,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAI,CAAC,WAAW,EAAE,EAAE;gBACvC,UAAU,EAAE,KAAK;aAClB,CAAC,CAAC;SACJ,CAAC,CAAC;;;;;;QAOH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,QAAQ;YAC1C,IAAI,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;YAC3B,IAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC/B,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;YACzC,IAAI,GAAG,GAAG,QAAQ,CAAC,KAAK,IAAI,KAAI,CAAC,WAAW,EAAE,CAAC;YAC/C,IAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC;;YAGlD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBAC/B,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;aAC1B;YAED,IAAI,KAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gBAC7B,OAAO,GAAG,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aAC9C;iBAAM;;gBAEL,OAAO,GAAG,mCAAmC,CAAC,IAAI,CAAC,OAAO,CAAC;sBACvD,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;aACnC;YAED,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,aAAa,EAAE;gBACjB,GAAG,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;gBAC3B,IAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAACA,GAAC,CAAC,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACtB;iBAAM;gBACL,OAAO,GAAG,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE;oBACnC,QAAQ,EAAE,GAAG;oBACb,oBAAoB,EAAE,IAAI;oBAC1B,mBAAmB,EAAE,IAAI;iBAC1B,CAAC,CAAC;aACJ;YAEDA,GAAC,CAAC,IAAI,CAAC,OAAO,EAAE,UAAC,GAAG,EAAE,MAAM;gBAC1BA,GAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAChC,IAAI,WAAW,EAAE;oBACfA,GAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;iBACpC;qBAAM;oBACLA,GAAC,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;iBAChC;aACF,CAAC,CAAC;YAEH,IAAM,UAAU,GAAG,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YACnE,IAAM,UAAU,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;YAC9C,IAAM,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAChE,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YAExC,KAAK,CAAC,MAAM,CACV,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,MAAM,EACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,CAChB,CAAC,MAAM,EAAE,CAAC;SACZ,CAAC,CAAC;;;;;;;;QASH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,SAAS;YACtC,IAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;YACtC,IAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;YAEtC,IAAI,SAAS,EAAE;gBAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;aAAE;YACvE,IAAI,SAAS,EAAE;gBAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;aAAE;SACxE,CAAC,CAAC;;;;;;QAOH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,SAAS;YAC1C,QAAQ,CAAC,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAClD,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SACrD,CAAC,CAAC;;;;;;QAOH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,GAAG;YACtC,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAEjC,IAAM,GAAG,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;YAChD,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SAClF,CAAC,CAAC;;;;QAKH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,IAAI,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;YAC/C,IAAI,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;gBACnC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;aACnC;iBAAM;gBACL,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;aAC5C;YACD,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,EAAE,KAAI,CAAC,SAAS,CAAC,CAAC;SACpE,CAAC,CAAC;;;;;;QAOH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;YACpC,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACxC,OAAO,CAAC,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC;YACzD,OAAO,CAAC,WAAW,CAAC,kBAAkB,EAAE,KAAK,KAAK,OAAO,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC7B,CAAC,CAAC;;;;;QAMH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;YACnC,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC;gBACV,KAAK,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG;gBACxB,MAAM,EAAE,EAAE;aACX,CAAC,CAAC;SACJ,CAAC,CAAC;KACJ;IAED,2BAAU,GAAV;QAAA,iBA+DC;;QA7DC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK;YACjC,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;gBACpC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aAC3C;YACD,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAE5C,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE;gBAC/B,IAAI,KAAI,CAAC,OAAO,CAAC,SAAS,EAAE;oBAC1B,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;iBAC1B;qBAAM;oBACL,KAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;iBAC7C;aACF;YACD,IAAI,KAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;gBAC5B,OAAO,KAAK,CAAC;aACd;SACF,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,KAAK;YACnB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC3C,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,KAAK;YACnB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC3C,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;YAClB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC1C,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,UAAC,KAAK;YACvB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SAC/C,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK;YACrB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAC7C,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAC,KAAK;YACpB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC5C,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,KAAK;YACnB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAC3C,CAAC,CAAC;;QAGH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;QAE3D,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC;YAClD,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;SAC5D,EAAE,GAAG,CAAC,CAAC,CAAC;QAET,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK;YAC/B,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SAC7C,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;YACtB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC7C;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACvB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACjD;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC1D;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC1D;SACF;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;KAC3B;IAED,wBAAO,GAAP;QACE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;KACtB;IAED,6BAAY,GAAZ,UAAa,KAAK;QAChB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;QAC7D,IAAM,IAAI,GAAG,EAAE,CAAC;QAEhB,IAAI,KAAK,CAAC,OAAO,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAAE;QACxC,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAAE;QAC1D,IAAI,KAAK,CAAC,QAAQ,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAAE;QAE3C,IAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACpB;QAED,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACzC,IAAI,SAAS,EAAE;YACb,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,KAAK,EAAE;gBAC5C,KAAK,CAAC,cAAc,EAAE,CAAC;aACxB;SACF;aAAM,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YACpC,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;KACF;IAED,gDAA+B,GAA/B,UAAgC,KAAK;;QAEnC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO;YACjC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;YAC7C,KAAK,CAAC,cAAc,EAAE,CAAC;SACxB;KACF;IAED,0BAAS,GAAT,UAAU,GAAG,EAAE,KAAK;QAClB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;QAEf,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAChC,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;iBACxB,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;gBAChC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;gBACxE,OAAO,KAAK,CAAC;aACd;SACF;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;YAClC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;gBACtE,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;KACd;;;;;IAKD,4BAAW,GAAX;QACE,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACpC;;;;;;;;IASD,0BAAS,GAAT,UAAU,YAAY;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC;SACpC;KACF;;;;;;IAOD,6BAAY,GAAZ;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;KACF;IAED,2BAAU,GAAV,UAAW,IAAI;QACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KACrC;IAED,4BAAW,GAAX;QACE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KACrC;IAED,8BAAa,GAAb;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACtC;;;;;;;IAQD,6BAAY,GAAZ;QACE,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QACzB,IAAI,GAAG,EAAE;YACP,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;SACvB;QACD,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC5E;;;;;;;IAQD,8BAAa,GAAb,UAAc,KAAK;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KACnC;;;;IAKD,qBAAI,GAAJ;QACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;KAC5D;;;;IAKD,qBAAI,GAAJ;QACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;KAC5D;;;;IAKD,8BAAa,GAAb;QACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;;QAEnE,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;;;;;IAMD,6BAAY,GAAZ,UAAa,gBAAgB;QAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;SAC5D;KACF;;;;IAKD,oBAAG,GAAH;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACrB;aAAM;YACL,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE;gBAC9B,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACjD,IAAI,CAAC,YAAY,EAAE,CAAC;aACrB;SACF;KACF;;;;IAKD,sBAAK,GAAL;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAC3B;aAAM;YACL,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE;gBAC9B,OAAO,KAAK,CAAC;aACd;SACF;KACF;;;;IAKD,4BAAW,GAAX,UAAY,EAAE;QAAd,iBAMC;QALC,OAAO;YACL,KAAI,CAAC,aAAa,EAAE,CAAC;YACrB,EAAE,CAAC,KAAK,CAAC,KAAI,EAAE,SAAS,CAAC,CAAC;YAC1B,KAAI,CAAC,YAAY,EAAE,CAAC;SACrB,CAAC;KACH;;;;;;;;IASD,4BAAW,GAAX,UAAY,GAAG,EAAE,KAAK;QAAtB,iBAoBC;QAnBC,OAAO,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,UAAC,MAAM;YACzC,KAAI,CAAC,aAAa,EAAE,CAAC;YAErB,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;gBAC/B,KAAK,CAAC,MAAM,CAAC,CAAC;aACf;iBAAM;gBACL,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;iBACrC;gBACD,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,KAAI,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACvE;YAED,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,KAAK,CAAC,MAAM,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YAC9C,KAAI,CAAC,YAAY,EAAE,CAAC;SACrB,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC;YACR,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;SACpD,CAAC,CAAC;KACJ;;;;;IAMD,6BAAY,GAAZ,UAAa,KAAK;QAAlB,iBAaC;QAZCA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;YACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;YAC3B,IAAI,KAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,KAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,EAAE;gBACtF,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;aACvF;iBAAM;gBACL,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAC,OAAO;oBACnC,OAAO,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;iBAC5C,CAAC,CAAC,IAAI,CAAC;oBACN,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;iBACjD,CAAC,CAAC;aACJ;SACF,CAAC,CAAC;KACJ;;;;;IAMD,uCAAsB,GAAtB,UAAuB,KAAK;QAC1B,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;;QAGzC,IAAI,SAAS,CAAC,aAAa,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;;SAElD;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SAC1B;KACF;;;;;IAMD,gCAAe,GAAf;QACE,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;;QAG7B,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE;YACpB,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;SAChE;QAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACvB;IAED,8BAAa,GAAb,UAAc,OAAO,EAAE,OAAO;;QAE5B,OAAO,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC;QACrD,QAAQ,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;;QAGpD,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;YAC7B,IAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC;YAC7C,IAAI,SAAS,EAAE;gBACb,IAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBAExC,IAAM,OAAO,GAAGA,GAAC,CAAC,CAAC,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACvE,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;aAC7B;SACF;KACF;IAED,2BAAU,GAAV;QACE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;KACvB;IAED,4BAAW,GAAX,UAAY,MAAM,EAAE,KAAK;QACvB,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE/B,IAAI,GAAG,EAAE;YACP,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACzCA,GAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;;;YAI5B,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;gBACrB,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpC,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;oBAC3C,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,oBAAoB,CAAC;oBAC/C,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAC;oBACzD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;iBAC3C;aACF;SACF;KACF;;;;;;IAOD,uBAAM,GAAN;QACE,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7B,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE;YACpB,IAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClD,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACnC,GAAG,CAAC,MAAM,EAAE,CAAC;YAEb,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;KACF;;;;;;;;;;IAWD,4BAAW,GAAX;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;;QAGpD,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvD,IAAM,QAAQ,GAAG;YACf,KAAK,EAAE,GAAG;YACV,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE;YACpB,GAAG,EAAE,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;SAChD,CAAC;;QAGF,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC;SAC5D;QAED,OAAO,QAAQ,CAAC;KACjB;IAED,uBAAM,GAAN,UAAO,QAAQ;QACb,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;KACF;IAED,uBAAM,GAAN,UAAO,QAAQ;QACb,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;KACF;IAED,0BAAS,GAAT;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;KACF;IAED,0BAAS,GAAT;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;KACF;IAED,4BAAW,GAAX;QACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,YAAY,EAAE,CAAC;SACrB;KACF;;;;;;IAOD,yBAAQ,GAAR,UAAS,GAAG,EAAE,OAAO,EAAE,UAAU;QAC/B,IAAI,SAAS,CAAC;QACd,IAAI,UAAU,EAAE;YACd,IAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC/B,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,SAAS,GAAG;gBACV,KAAK,EAAE,KAAK,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK;gBAC/C,MAAM,EAAE,KAAK,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC;aACjD,CAAC;SACH;aAAM;YACL,SAAS,GAAG;gBACV,KAAK,EAAE,GAAG,CAAC,CAAC;gBACZ,MAAM,EAAE,GAAG,CAAC,CAAC;aACd,CAAC;SACH;QAED,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KACxB;;;;IAKD,yBAAQ,GAAR;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;KACpC;;;;IAKD,sBAAK,GAAL;;;QAGE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;KACF;;;;;IAMD,wBAAO,GAAP;QACE,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;KAClF;;;;IAKD,sBAAK,GAAL;QACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;KAC5C;;;;IAKD,iCAAgB,GAAhB;QACE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;KAC/B;IACH,aAAC;CAAA;;AC11Bc;IACb,mBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;KAC9C;IAED,8BAAU,GAAV;QACE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1D;;;;;;IAOD,gCAAY,GAAZ,UAAa,KAAK;QAChB,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC;QACxD,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM,EAAE;YACtE,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC9D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,+BAA+B,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;aAC1E;YACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;SAC5C;KACF;IACH,gBAAC;CAAA;;ACzBc;IACb,kBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAClC,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;QAEhC,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC;YACjB,6BAA6B;YAC7B,wCAAwC;YACxC,QAAQ;SACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACrC;;;;IAKD,6BAAU,GAAV;QACE,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;;YAEnC,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,UAAC,CAAC;gBACpC,CAAC,CAAC,cAAc,EAAE,CAAC;aACpB,CAAC;;YAEF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;SACnE;aAAM;YACL,IAAI,CAAC,sBAAsB,EAAE,CAAC;SAC/B;KACF;;;;IAKD,yCAAsB,GAAtB;QAAA,iBAmEC;QAlEC,IAAI,UAAU,GAAGA,GAAC,EAAE,CAAC;QACrB,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEvE,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,UAAC,CAAC;YACzC,IAAM,UAAU,GAAG,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;YAC/D,IAAM,aAAa,GAAG,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAC5E,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,aAAa,EAAE;gBACtD,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAClC,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC3C,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC7C,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;aACtD;YACD,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;SACvC,CAAC;QAEF,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,UAAC,CAAC;YACzC,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBACtB,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;aACtC;SACF,CAAC;QAEF,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG;YAClC,UAAU,GAAGA,GAAC,EAAE,CAAC;YACjB,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;SACtC,CAAC;;;QAIF,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC;aACxE,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC;aACvD,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;;QAGjD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE;YAC7B,KAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;SAClD,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE;YACjB,KAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACpC,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;SACtD,CAAC,CAAC;;QAGH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;YAC9B,IAAM,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC;;YAGtD,KAAK,CAAC,cAAc,EAAE,CAAC;YAEvB,IAAI,YAAY,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE;gBACnE,KAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;gBACvB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,+BAA+B,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;aAC1E;iBAAM;gBACLA,GAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;oBACnC,IAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAE3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;wBAC3C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;qBAClD;yBAAM;wBACLA,GAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;4BACxB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;yBAChD,CAAC,CAAC;qBACJ;iBACF,CAAC,CAAC;aACJ;SACF,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KAC1B;IAED,0BAAO,GAAP;QAAA,iBAKC;QAJC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAClD,KAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,KAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;SACvF,CAAC,CAAC;QACH,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;KACjC;IACH,eAAC;CAAA;;AC/GD,IAAI,UAAU,CAAC;AACf,IAAI,GAAG,CAAC,aAAa,EAAE;IACrB,IAAI,GAAG,CAAC,YAAY,EAAE;QACpB,OAAO,CAAC,CAAC,YAAY,CAAC,EAAE,UAAS,EAAE;YACjC,UAAU,GAAG,EAAE,CAAC;SACjB,CAAC,CAAC;KACJ;SAAM;QACL,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;KAChC;CACF;;;;AAKc;IACb,kBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;KAChC;IAED,uBAAI,GAAJ;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACtC,IAAI,UAAU,IAAI,GAAG,CAAC,aAAa,EAAE;YACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;SACvC;KACF;;;;IAKD,8BAAW,GAAX;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAC1C;;;;IAKD,yBAAM,GAAN;QACE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;aAAM;YACL,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjB;QACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;KAC/C;;;;IAKD,2BAAQ,GAAR;QAAA,iBAiCC;QAhCC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAE9C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;;QAGtB,IAAI,GAAG,CAAC,aAAa,EAAE;YACrB,IAAM,UAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;YAGpF,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;gBAChC,IAAM,QAAM,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACvE,UAAQ,CAAC,UAAU,GAAG,QAAM,CAAC;gBAC7B,UAAQ,CAAC,EAAE,CAAC,gBAAgB,EAAE,UAAC,EAAE;oBAC/B,QAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;iBAC3B,CAAC,CAAC;aACJ;YAED,UAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;gBACxB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,EAAE,UAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;aACxE,CAAC,CAAC;;YAGH,UAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,UAAQ,CAAC,CAAC;SAC1C;aAAM;YACL,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;gBAC7B,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,EAAE,KAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;aACxE,CAAC,CAAC;SACJ;KACF;;;;IAKD,6BAAU,GAAV;;QAEE,IAAI,GAAG,CAAC,aAAa,EAAE;YACrB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvC,QAAQ,CAAC,UAAU,EAAE,CAAC;SACvB;QAED,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC;QACnF,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC;QAEjD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;QAC7E,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAErC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5E;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAEvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;KACtD;IAED,0BAAO,GAAP;QACE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;KACF;IACH,eAAC;CAAA;;ACzHD,IAAM,gBAAgB,GAAG,EAAE,CAAC;AAEb;IACb,mBAAY,OAAO;QACjB,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;KAChC;IAED,8BAAU,GAAV;QAAA,iBAwBC;QAvBC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;YAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;SACR;QAED,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,UAAC,KAAK;YACpC,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,KAAK,CAAC,eAAe,EAAE,CAAC;YAExB,IAAM,WAAW,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,KAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAC7E,IAAM,WAAW,GAAG,UAAC,KAAK;gBACxB,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,IAAI,WAAW,GAAG,gBAAgB,CAAC,CAAC;gBAE9D,MAAM,GAAG,CAAC,KAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;gBAC1F,MAAM,GAAG,CAAC,KAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;gBAE1F,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAC/B,CAAC;YAEF,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE;gBACzD,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;aAC9C,CAAC,CAAC;SACJ,CAAC,CAAC;KACJ;IAED,2BAAO,GAAP;QACE,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;KACpC;IACH,gBAAC;CAAA;;ACvCc;IACb,oBAAY,OAAO;QAAnB,iBAgBC;QAfC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAE3C,IAAI,CAAC,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,UAAU,GAAGA,GAAC,CAAC,YAAY,CAAC,CAAC;QAElC,IAAI,CAAC,QAAQ,GAAG;YACd,KAAI,CAAC,QAAQ,CAAC;gBACZ,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,KAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;aACvD,CAAC,CAAC;SACJ,CAAC;KACH;IAED,6BAAQ,GAAR,UAAS,IAAI;QACX,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;SACtD;KACF;;;;IAKD,2BAAM,GAAN;QACE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC3D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;SAC3C;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;SAC5C;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;KACtE;IAED,iCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;KAC5C;IACH,iBAAC;CAAA;;AC/Cc;IACb,gBAAY,OAAO;QAAnB,iBAuBC;QAtBC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAElC,IAAI,CAAC,MAAM,GAAG;YACZ,sBAAsB,EAAE,UAAC,EAAE,EAAE,CAAC;gBAC5B,IAAI,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;oBACzB,CAAC,CAAC,cAAc,EAAE,CAAC;iBACpB;aACF;YACD,8EAA8E,EAAE;gBAC9E,KAAI,CAAC,MAAM,EAAE,CAAC;aACf;YACD,oBAAoB,EAAE;gBACpB,KAAI,CAAC,IAAI,EAAE,CAAC;aACb;YACD,6BAA6B,EAAE;gBAC7B,KAAI,CAAC,MAAM,EAAE,CAAC;aACf;SACF,CAAC;KACH;IAED,2BAAU,GAAV;QAAA,iBAqDC;QApDC,IAAI,CAAC,OAAO,GAAGA,GAAC,CAAC;YACf,2BAA2B;YAC3B,sCAAsC;YACtC,+CAA+C;YAC/C,yDAAyD;YACzD,yDAAyD;YACzD,yDAAyD;YACzD,cAAc;aACb,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,qBAAqB,GAAG,qBAAqB;YAChF,0BAA0B;aACzB,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,EAAE,GAAG,iDAAiD;YACzF,QAAQ;YACR,QAAQ;SACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEzC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,UAAC,KAAK;YACjC,IAAI,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;gBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,KAAK,CAAC,eAAe,EAAE,CAAC;gBAExB,IAAM,SAAO,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC5E,IAAM,UAAQ,GAAG,SAAO,CAAC,MAAM,EAAE,CAAC;gBAClC,IAAM,WAAS,GAAG,KAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBAE7C,IAAM,aAAW,GAAG,UAAC,KAAK;oBACxB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE;wBACrC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,UAAQ,CAAC,IAAI;wBAChC,CAAC,EAAE,KAAK,CAAC,OAAO,IAAI,UAAQ,CAAC,GAAG,GAAG,WAAS,CAAC;qBAC9C,EAAE,SAAO,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBAE7B,KAAI,CAAC,MAAM,CAAC,SAAO,CAAC,CAAC,CAAC,CAAC,CAAC;iBACzB,CAAC;gBAEF,KAAI,CAAC,SAAS;qBACX,EAAE,CAAC,WAAW,EAAE,aAAW,CAAC;qBAC5B,GAAG,CAAC,SAAS,EAAE,UAAC,CAAC;oBAChB,CAAC,CAAC,cAAc,EAAE,CAAC;oBACnB,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,aAAW,CAAC,CAAC;oBAC7C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;iBAC5C,CAAC,CAAC;gBAEL,IAAI,CAAC,SAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;oBAC1B,SAAO,CAAC,IAAI,CAAC,OAAO,EAAE,SAAO,CAAC,MAAM,EAAE,GAAG,SAAO,CAAC,KAAK,EAAE,CAAC,CAAC;iBAC3D;aACF;SACF,CAAC,CAAC;;QAGH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,CAAC;YACzB,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,KAAI,CAAC,MAAM,EAAE,CAAC;SACf,CAAC,CAAC;KACJ;IAED,wBAAO,GAAP;QACE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;KACvB;IAED,uBAAM,GAAN,UAAO,MAAM;QACX,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAClC,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAEhE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAEnD,IAAI,OAAO,EAAE;YACX,IAAM,MAAM,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC;YACzB,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YACnC,IAAM,GAAG,GAAG;gBACV,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;gBAC5D,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC;aAC1D,CAAC;;YAGF,IAAM,SAAS,GAAG;gBAChB,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBAC3B,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;aAC7B,CAAC;YAEF,UAAU,CAAC,GAAG,CAAC;gBACb,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,KAAK,EAAE,SAAS,CAAC,CAAC;gBAClB,MAAM,EAAE,SAAS,CAAC,CAAC;aACpB,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE1B,IAAM,YAAY,GAAG,IAAI,KAAK,EAAE,CAAC;YACjC,YAAY,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAM,UAAU,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,YAAY,CAAC,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC;YACnJ,UAAU,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;SAClD;aAAM;YACL,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;QAED,OAAO,OAAO,CAAC;KAChB;;;;;;IAOD,qBAAI,GAAJ;QACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;KAChC;IACH,aAAC;CAAA;;AC1ID,IAAM,aAAa,GAAG,SAAS,CAAC;AAChC,IAAM,WAAW,GAAG,2EAA2E,CAAC;AAEjF;IACb,kBAAY,OAAO;QAAnB,iBAYC;QAXC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG;YACZ,kBAAkB,EAAE,UAAC,EAAE,EAAE,CAAC;gBACxB,IAAI,CAAC,CAAC,CAAC,kBAAkB,EAAE,EAAE;oBAC3B,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;iBACrB;aACF;YACD,oBAAoB,EAAE,UAAC,EAAE,EAAE,CAAC;gBAC1B,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;aACvB;SACF,CAAC;KACH;IAED,6BAAU,GAAV;QACE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B;IAED,0BAAO,GAAP;QACE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;KAC3B;IAED,0BAAO,GAAP;QACE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,OAAO;SACR;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;QAC9C,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAEzC,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YACnC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,aAAa,GAAG,OAAO,CAAC;YAC1D,IAAM,IAAI,GAAGA,GAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SACrC;KACF;IAED,gCAAa,GAAb,UAAc,CAAC;QACb,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE;YAC/D,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,EAAE,CAAC;YAC3E,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;SAChC;KACF;IAED,8BAAW,GAAX,UAAY,CAAC;QACX,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE;YAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;KACF;IACH,eAAC;CAAA;;AC1DD;;;AAGe;IACb,kBAAY,OAAO;QAAnB,iBAOC;QANC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG;YACZ,mBAAmB,EAAE;gBACnB,KAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;aACxC;SACF,CAAC;KACH;IAED,mCAAgB,GAAhB;QACE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IACH,eAAC;CAAA;;ACjBc;IACb,qBAAY,OAAO;QAAnB,iBAaC;QAZC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG;YACZ,mCAAmC,EAAE;gBACnC,KAAI,CAAC,MAAM,EAAE,CAAC;aACf;YACD,6BAA6B,EAAE;gBAC7B,KAAI,CAAC,MAAM,EAAE,CAAC;aACf;SACF,CAAC;KACH;IAED,sCAAgB,GAAhB;QACE,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;KACnC;IAED,gCAAU,GAAV;QAAA,iBAOC;QANC,IAAI,CAAC,YAAY,GAAGA,GAAC,CAAC,gCAAgC,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE;YAC5B,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC9B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE/D,IAAI,CAAC,MAAM,EAAE,CAAC;KACf;IAED,6BAAO,GAAP;QACE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;KAC5B;IAED,4BAAM,GAAN;QACE,IAAM,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACrG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAClC;IACH,kBAAC;CAAA;;ACjCc;IACb,iBAAY,OAAO;QACjB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CACrC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAC9C,CAAC;KACH;IAED,mCAAiB,GAAjB,UAAkB,YAAY;QAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;YACxC,OAAO,EAAE,CAAC;SACX;QAED,IAAI,GAAG,CAAC,KAAK,EAAE;YACb,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;SAC/D;QAED,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;aAC3C,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC;aAC3B,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;QAEhC,OAAO,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC;KAC9B;IAED,wBAAM,GAAN,UAAO,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE;YACtC,OAAO,CAAC,CAAC,OAAO,CAAC;SAClB;QACD,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACrC,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAC1B;IAED,4BAAU,GAAV;QACE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;KAC5B;IAED,yBAAO,GAAP;QACE,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IAED,iCAAe,GAAf,UAAgB,IAAI;QAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC/C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC;gBACrD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KACpC;IAED,qCAAmB,GAAnB,UAAoB,IAAI;QACtB,IAAM,eAAe,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACnF,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE1B,QAAQ,CAAC,IAAI,KAAK,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;KACnG;IAED,mCAAiB,GAAjB;QAAA,iBAycC;QAxcC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;YAChC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACzB,KAAI,CAAC,MAAM,CAAC;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CACtC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAI,CAAC,OAAO,CACrD;oBACD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;oBAC9B,IAAI,EAAE;wBACJ,MAAM,EAAE,UAAU;qBACnB;iBACF,CAAC;gBACF,KAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;oBACf,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS;oBAC7B,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;oBAC5B,QAAQ,EAAE,UAAC,IAAI;wBACb,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;4BAC5B,IAAI,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,GAAG,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;yBACpG;wBAED,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;wBACrB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;wBACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;wBAC/D,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE,CAAC;wBAE1E,OAAO,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;qBACvE;oBACD,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;iBAC9D,CAAC;aACH,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;gCAEM,QAAQ,EAAM,QAAQ;YAC7B,IAAM,IAAI,GAAG,OAAK,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAE9C,OAAK,OAAO,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,EAAE;gBACxC,OAAO,KAAI,CAAC,MAAM,CAAC;oBACjB,SAAS,EAAE,iBAAiB,GAAG,IAAI;oBACnC,QAAQ,EAAE,mBAAmB,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,QAAQ;oBAC3E,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;iBAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;aACb,CAAC,CAAC;SACJ;;QAXD,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,GAAG,QAAQ,EAAE,QAAQ,EAAE;oBAAvF,QAAQ,EAAM,QAAQ;SAW9B;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;YAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,eAAe;gBAC1B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAC7D,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,aAAa,CAAC;aACrE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;YACjC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,iBAAiB;gBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;gBACjD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBACjE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,eAAe,CAAC;aACvE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,oBAAoB;gBAC/B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,KAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;gBACvE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,kBAAkB,CAAC;aAC1E,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;YAChC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;gBACjD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBACtE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,qBAAqB,CAAC;aAC/D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE;YACxC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,wBAAwB;gBACnC,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC;gBACxD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;gBAC/E,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,sBAAsB,CAAC;aAC9E,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACtC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,sBAAsB;gBACjC,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;gBACtD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;gBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,oBAAoB,CAAC;aAC5E,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,oBAAoB;gBAC/B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;gBACjC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,kBAAkB,CAAC;aAC1E,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACnC,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;;YAG7DA,GAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAC,GAAG,EAAE,QAAQ;gBACxD,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACjD,IAAI,KAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE;oBACtC,IAAIA,GAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;wBACtD,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBACvC;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACzB,KAAI,CAAC,MAAM,CAAC;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CACtC,uCAAuC,EAAE,KAAI,CAAC,OAAO,CACtD;oBACD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC5B,IAAI,EAAE;wBACJ,MAAM,EAAE,UAAU;qBACnB;iBACF,CAAC;gBACF,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC;oBACpB,SAAS,EAAE,mBAAmB;oBAC9B,cAAc,EAAE,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;oBAC5C,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;oBACrE,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC1B,QAAQ,EAAE,UAAC,IAAI;wBACb,OAAO,8BAA8B,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;qBAC1E;oBACD,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,iBAAiB,CAAC;iBACzE,CAAC;aACH,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACnC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACzB,KAAI,CAAC,MAAM,CAAC;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,uCAAuC,EAAE,KAAI,CAAC,OAAO,CAAC;oBAC/F,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC5B,IAAI,EAAE;wBACJ,MAAM,EAAE,UAAU;qBACnB;iBACF,CAAC;gBACF,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC;oBACpB,SAAS,EAAE,mBAAmB;oBAC9B,cAAc,EAAE,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;oBAC5C,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS;oBAC7B,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;oBAC1B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,iBAAiB,CAAC;iBACzE,CAAC;aACH,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;YAChC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACzB,SAAS,EAAE,YAAY;gBACvB,QAAQ,EAAE;oBACR,KAAI,CAAC,MAAM,CAAC;wBACV,SAAS,EAAE,2BAA2B;wBACtC,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,oBAAoB,CAAC;wBACtE,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;wBAC/B,KAAK,EAAE,UAAC,CAAC;4BACP,IAAM,OAAO,GAAGA,GAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;4BACnC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE;gCAClC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;gCACzC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;6BAC1C,CAAC,CAAC;yBACJ;wBACD,QAAQ,EAAE,UAAC,OAAO;4BAChB,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;4BACxD,YAAY,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;4BAChD,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;yBAC3C;qBACF,CAAC;oBACF,KAAI,CAAC,MAAM,CAAC;wBACV,SAAS,EAAE,iBAAiB;wBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,EAAE,EAAE,KAAI,CAAC,OAAO,CAAC;wBAC1D,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;wBAC7B,IAAI,EAAE;4BACJ,MAAM,EAAE,UAAU;yBACnB;qBACF,CAAC;oBACF,KAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;wBACf,KAAK,EAAE;4BACL,4BAA4B;4BAC5B,oCAAoC,GAAG,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;4BAC5E,SAAS;4BACT,+GAA+G;4BAC/G,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;4BAC3B,eAAe;4BACf,UAAU;4BACV,qDAAqD;4BACrD,QAAQ;4BACR,4BAA4B;4BAC5B,oCAAoC,GAAG,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;4BAC5E,SAAS;4BACT,oHAAoH;4BACpH,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc;4BAC9B,eAAe;4BACf,UAAU;4BACV,qDAAqD;4BACrD,QAAQ;yBACT,CAAC,IAAI,CAAC,EAAE,CAAC;wBACV,QAAQ,EAAE,UAAC,SAAS;4BAClB,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;gCAC5C,IAAM,OAAO,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;gCACxB,OAAO,CAAC,MAAM,CAAC,KAAI,CAAC,EAAE,CAAC,OAAO,CAAC;oCAC7B,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,MAAM;oCAC3B,UAAU,EAAE,KAAI,CAAC,OAAO,CAAC,UAAU;oCACnC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;oCAChC,SAAS,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS;oCACjC,OAAO,EAAE,KAAI,CAAC,OAAO,CAAC,OAAO;iCAC9B,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;6BACd,CAAC,CAAC;yBACJ;wBACD,KAAK,EAAE,UAAC,KAAK;4BACX,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;4BAChC,IAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BACxC,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BAEpC,IAAI,SAAS,IAAI,KAAK,EAAE;gCACtB,IAAM,GAAG,GAAG,SAAS,KAAK,WAAW,GAAG,kBAAkB,GAAG,OAAO,CAAC;gCACrE,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gCACzE,IAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gCAEzF,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gCACvB,cAAc,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;gCAChD,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;6BACnD;yBACF;qBACF,CAAC;iBACH;aACF,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YAC7B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC;gBACxD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;gBAClF,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,4BAA4B,CAAC;aACtE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YAC7B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;gBACtD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;gBAC9E,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,0BAA0B,CAAC;aACpE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;YACpD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;YACzE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;SAC9D,CAAC,CAAC;QAEH,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;YACtD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAC7E,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,sBAAsB,CAAC;SAChE,CAAC,CAAC;QAEH,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;YAC/B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;YACrD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;YAC3E,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,qBAAqB,CAAC;SAC/D,CAAC,CAAC;QAEH,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;YACvD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;YAC5E,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;SAC9D,CAAC,CAAC;QAEH,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;YAClD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;YACxE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;SAC1D,CAAC,CAAC;QAEH,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;YACjD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YACtE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC;SACzD,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;QAChF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;QAElE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACzB,KAAI,CAAC,MAAM,CAAC;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC;oBAClG,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS;oBACtC,IAAI,EAAE;wBACJ,MAAM,EAAE,UAAU;qBACnB;iBACF,CAAC;gBACF,KAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;oBACf,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;wBAClB,SAAS,EAAE,YAAY;wBACvB,QAAQ,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,CAAC;qBAClE,CAAC;oBACF,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;wBAClB,SAAS,EAAE,WAAW;wBACtB,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;qBAC5B,CAAC;iBACH,CAAC;aACH,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;YACjC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACzB,KAAI,CAAC,MAAM,CAAC;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC;oBACnG,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;oBAC9B,IAAI,EAAE;wBACJ,MAAM,EAAE,UAAU;qBACnB;iBACF,CAAC;gBACF,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC;oBACpB,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,WAAW;oBAC/B,cAAc,EAAE,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;oBAC5C,SAAS,EAAE,sBAAsB;oBACjC,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;oBAC5B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;iBAC7D,CAAC;aACH,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;YAChC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACzB,KAAI,CAAC,MAAM,CAAC;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC;oBAC9F,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;oBAC9B,IAAI,EAAE;wBACJ,MAAM,EAAE,UAAU;qBACnB;iBACF,CAAC;gBACF,KAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;oBACf,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;oBAC5B,SAAS,EAAE,YAAY;oBACvB,KAAK,EAAE;wBACL,qCAAqC;wBACrC,+FAA+F;wBAC/F,oDAAoD;wBACpD,sDAAsD;wBACtD,QAAQ;wBACR,iDAAiD;qBAClD,CAAC,IAAI,CAAC,EAAE,CAAC;iBACX,CAAC;aACH,EAAE;gBACD,QAAQ,EAAE,UAAC,KAAK;oBACd,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;oBACnE,QAAQ,CAAC,GAAG,CAAC;wBACX,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI;wBACjD,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI;qBACnD,CAAC,CAAC,SAAS,CAAC,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;yBACjE,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;iBACtD;aACF,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;YAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;gBACxE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;aAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAClC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;gBAClD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;gBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;aAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;YAChC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;gBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;gBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;aAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YAC7B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;gBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,KAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC;gBAC7E,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,6BAA6B,CAAC;aACvE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;YACrC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,gBAAgB;gBAC3B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU;gBACrC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;aAC7D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACnC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,cAAc;gBACzB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;gBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;aAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;YAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAChE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,aAAa,CAAC;aACvD,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;YAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAChE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,aAAa,CAAC;aACvD,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;YAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;gBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;aAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;KACJ;;;;;;;;IASD,wCAAsB,GAAtB;QAAA,iBAyDC;;QAvDC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACvC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,4CAA4C;gBACtD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;gBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,GAAG,CAAC;aAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACtC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,2CAA2C;gBACrD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;gBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,KAAK,CAAC;aAChE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACtC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,2CAA2C;gBACrD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;gBACtC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,MAAM,CAAC;aACjE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;;QAGH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;gBAClC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,MAAM,CAAC;aAClE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;YACrC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;gBACrD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;gBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,OAAO,CAAC;aACnE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;gBACvD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;gBAClC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,MAAM,CAAC;aAClE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;;QAGH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACtC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;gBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;gBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;aAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;KACJ;IAED,uCAAqB,GAArB;QAAA,iBAgBC;QAfC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACzC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;gBAC5B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;aAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;YACjC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;gBACjD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;gBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC;aACzD,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;KACJ;;;;;;;IAQD,wCAAsB,GAAtB;QAAA,iBAyDC;QAxDC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACnC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;gBACpC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,KAAK,CAAC;aAChE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;YACrC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;gBACpC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,QAAQ,CAAC;aACnE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;YACrC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;gBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,MAAM,CAAC;aACjE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACtC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;gBACpC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,OAAO,CAAC;aAClE,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;gBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;aAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACpC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;gBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;aAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACtC,OAAO,KAAI,CAAC,MAAM,CAAC;gBACjB,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;gBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;gBACjC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;aAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;SACb,CAAC,CAAC;KACJ;IAED,uBAAK,GAAL,UAAM,UAAU,EAAE,MAAM;QACtB,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,QAAQ,GAAG,QAAQ,EAAE,QAAQ,EAAE,EAAE;YAChF,IAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC/B,IAAM,SAAS,GAAGA,GAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACtD,IAAM,OAAO,GAAGA,GAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5F,IAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC;gBACjC,SAAS,EAAE,OAAO,GAAG,SAAS;aAC/B,CAAC,CAAC,MAAM,EAAE,CAAC;YAEZ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBACxD,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBACxD,IAAI,GAAG,EAAE;oBACP,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;iBACpE;aACF;YACD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC7B;KACF;;;;IAKD,oCAAkB,GAAlB,UAAmB,UAAU;QAA7B,iBA6DC;QA5DC,IAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC;QAE1C,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QAC7D,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;YAC1B,gBAAgB,EAAE;gBAChB,OAAO,SAAS,CAAC,WAAW,CAAC,KAAK,MAAM,CAAC;aAC1C;YACD,kBAAkB,EAAE;gBAClB,OAAO,SAAS,CAAC,aAAa,CAAC,KAAK,QAAQ,CAAC;aAC9C;YACD,qBAAqB,EAAE;gBACrB,OAAO,SAAS,CAAC,gBAAgB,CAAC,KAAK,WAAW,CAAC;aACpD;YACD,qBAAqB,EAAE;gBACrB,OAAO,SAAS,CAAC,gBAAgB,CAAC,KAAK,WAAW,CAAC;aACpD;YACD,uBAAuB,EAAE;gBACvB,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,aAAa,CAAC;aACxD;YACD,yBAAyB,EAAE;gBACzB,OAAO,SAAS,CAAC,oBAAoB,CAAC,KAAK,eAAe,CAAC;aAC5D;SACF,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE;YAC5B,IAAM,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAC,IAAI;gBAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;qBAC/B,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;qBACnB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;aACxB,CAAC,CAAC;YACH,IAAM,UAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAExE,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;gBAChD,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;;gBAEtB,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,UAAQ,GAAG,EAAE,CAAC,CAAC;gBACjE,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aACzC,CAAC,CAAC;YACH,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,UAAQ,CAAC,CAAC,GAAG,CAAC,aAAa,EAAE,UAAQ,CAAC,CAAC;SAClF;QAED,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE;YAC1B,IAAM,UAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;YACxC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;gBAChD,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;;gBAEtB,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,UAAQ,GAAG,EAAE,CAAC,CAAC;gBACjE,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aACzC,CAAC,CAAC;YACH,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,UAAQ,CAAC,CAAC;SACrD;QAED,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE;YAC5B,IAAM,YAAU,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC;YAC5C,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;;gBAEtD,IAAM,SAAS,GAAG,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,YAAU,GAAG,EAAE,CAAC,CAAC;gBACrE,KAAI,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC;aAC7C,CAAC,CAAC;SACJ;KACF;IAED,iCAAe,GAAf,UAAgB,UAAU,EAAE,KAAK;QAAjC,iBAIC;QAHCA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,QAAQ,EAAE,IAAI;YAC3B,KAAI,CAAC,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;SAC5D,CAAC,CAAC;KACJ;IAED,kCAAgB,GAAhB,UAAiB,KAAK;QACpB,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACzC,IAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrE,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACxE,IAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAE5E,IAAI,SAAS,CAAC;;QAEd,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;YAC/B,IAAM,UAAU,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;YAC5C,SAAS,GAAG;gBACV,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI;gBAChC,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG;aAChC,CAAC;SACH;aAAM;YACL,SAAS,GAAG;gBACV,CAAC,EAAE,KAAK,CAAC,OAAO;gBAChB,CAAC,EAAE,KAAK,CAAC,OAAO;aACjB,CAAC;SACH;QAED,IAAM,GAAG,GAAG;YACV,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;YAC1C,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;SAC3C,CAAC;QAEF,YAAY,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;QAChE,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE;YAC5D,cAAc,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;SACjD;QAED,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE;YAC5D,cAAc,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;SAClD;QAED,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;KAC/C;IACH,cAAC;CAAA;;AC3yBc;IACb,iBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;QAE7B,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAE/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAClD;IAED,kCAAgB,GAAhB;QACE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;KAC9B;IAED,4BAAU,GAAV;QAAA,iBAuBC;QAtBC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;YAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3E;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;SACvD;QAED,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAE5B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,uDAAuD,EAAE;YACrE,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SACrD;KACF;IAED,yBAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SACtD;KACF;IAED,8BAAY,GAAZ;QACE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YACvC,OAAO,KAAK,CAAC;SACd;QAED,IAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;QACtE,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAChD,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAEzC,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC7C,eAAe,CAAC,GAAG,CAAC;YAClB,MAAM,EAAE,aAAa;SACtB,CAAC,CAAC;;QAGH,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC/B,cAAc,GAAGA,GAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;SAC/D;QAED,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACjD,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC;QAClD,IAAM,kBAAkB,GAAG,eAAe,GAAG,YAAY,CAAC;QAC1D,IAAM,cAAc,GAAG,eAAe,GAAG,cAAc,CAAC;QACxD,IAAM,sBAAsB,GAAG,kBAAkB,GAAG,cAAc,GAAG,aAAa,CAAC;QAEnF,IAAI,CAAC,aAAa,GAAG,cAAc,MAAM,aAAa,GAAG,sBAAsB,CAAC,EAAE;YAChF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAChB,QAAQ,EAAE,OAAO;gBACjB,GAAG,EAAE,cAAc;gBACnB,KAAK,EAAE,WAAW;aACnB,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAChB,QAAQ,EAAE,UAAU;gBACpB,GAAG,EAAE,CAAC;gBACN,KAAK,EAAE,MAAM;aACd,CAAC,CAAC;SACJ;KACF;IAED,iCAAe,GAAf,UAAgB,YAAY;QAC1B,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACvC;aAAM;YACL,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;gBACjC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;aACvD;SACF;KACF;IAED,kCAAgB,GAAhB,UAAiB,YAAY;QAC3B,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,YAAY,CAAC,CAAC;QAE7E,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;KACpC;IAED,gCAAc,GAAd,UAAe,UAAU;QACvB,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,UAAU,CAAC,CAAC;QACzE,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;aAAM;YACL,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjB;KACF;IAED,0BAAQ,GAAR,UAAS,iBAAiB;QACxB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,EAAE;YACtB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAC/B;IAED,4BAAU,GAAV,UAAW,iBAAiB;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,EAAE;YACtB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAChC;IACH,cAAC;CAAA;;ACnIc;IACb,oBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAElC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;KACrF;IAED,+BAAU,GAAV;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAE1E,IAAM,IAAI,GAAG;YACX,0CAA0C;YAC1C,sCAAkC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,aAAU;YACxE,yFAAyF;YACzF,QAAQ;YACR,0CAA0C;YAC1C,sCAAkC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,aAAU;YAC9D,uGAAuG;YACvG,QAAQ;YACR,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB;kBAC3BA,GAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;oBACpC,EAAE,EAAE,gCAAgC;oBACpC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe;oBACpC,OAAO,EAAE,IAAI;iBACd,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE;kBACjB,EAAE;SACP,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEX,IAAM,WAAW,GAAG,yDAAyD,CAAC;QAC9E,IAAM,MAAM,GAAG,gDAAyC,WAAW,oBAAc,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,cAAW,CAAC;QAElH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;YAC5B,SAAS,EAAE,aAAa;YACxB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;YAC5B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;YAC9B,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,MAAM;SACf,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAClC;IAED,4BAAO,GAAP;QACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;KACvB;IAED,iCAAY,GAAZ,UAAa,MAAM,EAAE,IAAI;QACvB,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;YAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;gBACpC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACvB;SACF,CAAC,CAAC;KACJ;;;;IAKD,kCAAa,GAAb,UAAc,QAAQ,EAAE,SAAS,EAAE,QAAQ;QACzC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;KAChE;;;;;;;IAQD,mCAAc,GAAd,UAAe,QAAQ;QAAvB,iBAgFC;QA/EC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;YACzB,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACvD,IAAM,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrD,IAAM,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrD,IAAM,gBAAgB,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAEnE,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;gBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;;gBAG1C,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;oBACjB,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;iBAC9B;gBAED,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAE7B,IAAM,oBAAoB,GAAG;oBAC3B,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;;;oBAGlD,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;iBACjC,CAAC;gBAEF,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;oBACtD,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;iBACrC,CAAC,CAAC;gBAEH,IAAM,mBAAmB,GAAG;oBAC1B,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;;;oBAGlD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;wBAClB,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;qBAC/B;iBACF,CAAC;gBAEF,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;oBACpD,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;iBACpC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAErB,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;oBACvB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iBAC3B;gBAED,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAClD,KAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACtC,KAAI,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAEvC,IAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,KAAK,SAAS;sBAChD,QAAQ,CAAC,WAAW,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC;gBAEhE,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAE5C,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,UAAC,KAAK;oBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;oBAEvB,QAAQ,CAAC,OAAO,CAAC;wBACf,KAAK,EAAE,QAAQ,CAAC,KAAK;wBACrB,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE;wBACnB,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE;wBACrB,WAAW,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,CAAC;qBAC7C,CAAC,CAAC;oBACH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;iBAClC,CAAC,CAAC;aACJ,CAAC,CAAC;YAEH,KAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,OAAO,EAAE;;gBAEnC,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBACtC,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBACrC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEtB,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;oBAClC,QAAQ,CAAC,MAAM,EAAE,CAAC;iBACnB;aACF,CAAC,CAAC;YAEH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;SAClC,CAAC,CAAC,OAAO,EAAE,CAAC;KACd;;;;IAKD,yBAAI,GAAJ;QAAA,iBAUC;QATC,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAC,QAAQ;YAC1C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;YAC3C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;SACpD,CAAC,CAAC,IAAI,CAAC;YACN,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;SAC5C,CAAC,CAAC;KACJ;IACH,iBAAC;CAAA;;ACzKc;IACb,qBAAY,OAAO;QAAnB,iBAaC;QAZC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG;YACZ,yEAAyE,EAAE;gBACzE,KAAI,CAAC,MAAM,EAAE,CAAC;aACf;YACD,4CAA4C,EAAE;gBAC5C,KAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF,CAAC;KACH;IAED,sCAAgB,GAAhB;QACE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAClD;IAED,gCAAU,GAAV;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;YAC9B,SAAS,EAAE,mBAAmB;YAC9B,QAAQ,EAAE,UAAC,KAAK;gBACd,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACtE,QAAQ,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;aAChE;SACF,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAE9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAC3E;IAED,6BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;KACxB;IAED,4BAAM,GAAN;;QAEE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;YAC3C,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO;SACR;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACtD,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE;YACzC,IAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAM,IAAI,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,IAAM,GAAG,GAAG,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAChB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,GAAG,EAAE,GAAG,CAAC,GAAG;aACb,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;KACF;IAED,0BAAI,GAAJ;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IACH,kBAAC;CAAA;;AChEc;IACb,qBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;KACnC;IAED,gCAAU,GAAV;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAE1E,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACrC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACtF,IAAM,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;gBAC3E,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YAC9C,eAAe,GAAG,aAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,GAAG,YAAY,cAAU,CAAC;SAC9F;QAED,IAAM,IAAI,GAAG;YACX,uEAAuE;YACvE,iCAAiC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,UAAU;YAChF,+DAA+D;YAC/D,mEAAmE;YACnE,eAAe;YACf,QAAQ;YACR,sEAAsE;YACtE,iCAAiC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU;YACpE,yEAAyE;YACzE,4BAA4B;YAC5B,QAAQ;SACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACX,IAAM,WAAW,GAAG,0DAA0D,CAAC;QAC/E,IAAM,MAAM,GAAG,gDAAyC,WAAW,oBAAc,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,cAAW,CAAC;QAEnH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;YAC5B,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;YAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;YAC9B,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,MAAM;SACf,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAClC;IAED,6BAAO,GAAP;QACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;KACvB;IAED,kCAAY,GAAZ,UAAa,MAAM,EAAE,IAAI;QACvB,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;YAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;gBACpC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACvB;SACF,CAAC,CAAC;KACJ;IAED,0BAAI,GAAJ;QAAA,iBAeC;QAdC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,UAAC,IAAI;;YAE/B,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;YACjC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;YAE3C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;aACjD;iBAAM;gBACL,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC;aAC5D;SACF,CAAC,CAAC,IAAI,CAAC;YACN,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;SAC5C,CAAC,CAAC;KACJ;;;;;;;IAQD,qCAAe,GAAf;QAAA,iBA2CC;QA1CC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;YACzB,IAAM,WAAW,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC3D,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACvD,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEvD,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;gBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;;gBAG1C,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAC,KAAK;oBAC7D,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;iBAC5D,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEZ,SAAS,CAAC,KAAK,CAAC,UAAC,KAAK;oBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;oBAEvB,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;iBACnC,CAAC,CAAC;gBAEH,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE;oBAC1B,IAAM,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;oBAC5B,KAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;iBACnC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAEX,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;oBACvB,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iBAC5B;gBACD,KAAI,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aACzC,CAAC,CAAC;YAEH,KAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,OAAO,EAAE;gBACnC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC1B,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBACtC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEvB,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;oBAClC,QAAQ,CAAC,MAAM,EAAE,CAAC;iBACnB;aACF,CAAC,CAAC;YAEH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;SAClC,CAAC,CAAC;KACJ;IACH,kBAAC;CAAA;;AC9HD;;;;;AAKe;IACb,sBAAY,OAAO;QAAnB,iBAYC;QAXC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAE1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAE/B,IAAI,CAAC,MAAM,GAAG;YACZ,oBAAoB,EAAE;gBACpB,KAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF,CAAC;KACH;IAED,uCAAgB,GAAhB;QACE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACnD;IAED,iCAAU,GAAV;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;YAC9B,SAAS,EAAE,oBAAoB;SAChC,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC5E;IAED,8BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;KACxB;IAED,6BAAM,GAAN,UAAO,MAAM;QACX,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACrB,IAAM,GAAG,GAAG,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAM,SAAS,GAAG,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAChB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI;gBAC3D,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC;aAC9E,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;KACF;IAED,2BAAI,GAAJ;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IACH,mBAAC;CAAA;;ACpDc;IACb,sBAAY,OAAO;QAAnB,iBAgBC;QAfC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG;YACZ,sBAAsB,EAAE,UAAC,EAAE,EAAE,CAAC;gBAC5B,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;aACvB;YACD,sDAAsD,EAAE;gBACtD,KAAI,CAAC,MAAM,EAAE,CAAC;aACf;YACD,oBAAoB,EAAE;gBACpB,KAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF,CAAC;KACH;IAED,uCAAgB,GAAhB;QACE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACnD;IAED,iCAAU,GAAV;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;YAC9B,SAAS,EAAE,oBAAoB;SAChC,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAE9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;QAG3E,IAAI,GAAG,CAAC,IAAI,EAAE;YACZ,QAAQ,CAAC,WAAW,CAAC,0BAA0B,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;SAChE;KACF;IAED,8BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;KACxB;IAED,6BAAM,GAAN,UAAO,MAAM;QACX,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAElC,IAAI,MAAM,EAAE;YACV,IAAM,GAAG,GAAG,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAChB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,GAAG,EAAE,GAAG,CAAC,GAAG;aACb,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;QAED,OAAO,MAAM,CAAC;KACf;IAED,2BAAI,GAAJ;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IACH,mBAAC;CAAA;;AClEc;IACb,qBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;KACnC;IAED,gCAAU,GAAV;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAE1E,IAAM,IAAI,GAAG;YACX,oDAAoD;YACpD,sCAAkC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,qCAA8B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,qBAAkB;YAC9H,wFAAwF;YACxF,QAAQ;SACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACX,IAAM,WAAW,GAAG,0DAA0D,CAAC;QAC/E,IAAM,MAAM,GAAG,gDAAyC,WAAW,oBAAc,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,cAAW,CAAC;QAEnH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;YAC5B,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;YAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;YAC9B,IAAI,EAAE,IAAI;YACV,MAAM,EAAE,MAAM;SACf,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAClC;IAED,6BAAO,GAAP;QACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;KACvB;IAED,kCAAY,GAAZ,UAAa,MAAM,EAAE,IAAI;QACvB,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;YAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;gBACpC,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACvB;SACF,CAAC,CAAC;KACJ;IAED,qCAAe,GAAf,UAAgB,GAAG;;QAEjB,IAAM,QAAQ,GAAG,sHAAsH,CAAC;QACxI,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAM,QAAQ,GAAG,oDAAoD,CAAC;QACtE,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAM,OAAO,GAAG,iCAAiC,CAAC;QAClD,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAElC,IAAM,SAAS,GAAG,mDAAmD,CAAC;QACtE,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAM,QAAQ,GAAG,gEAAgE,CAAC;QAClF,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAM,WAAW,GAAG,6CAA6C,CAAC;QAClE,IAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAE1C,IAAM,QAAQ,GAAG,2BAA2B,CAAC;QAC7C,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEpC,IAAM,SAAS,GAAG,2DAA2D,CAAC;QAC9E,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAM,SAAS,GAAG,gBAAgB,CAAC;QACnC,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAM,SAAS,GAAG,gBAAgB,CAAC;QACnC,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEtC,IAAM,UAAU,GAAG,aAAa,CAAC;QACjC,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAExC,IAAI,MAAM,CAAC;QACX,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;YACvC,IAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;iBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;iBACtB,IAAI,CAAC,KAAK,EAAE,0BAA0B,GAAG,SAAS,CAAC;iBACnD,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC/C;aAAM,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACvC,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;iBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;iBACtB,IAAI,CAAC,KAAK,EAAE,0BAA0B,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;iBAChE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;iBAC1C,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;iBACvB,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;SACtC;aAAM,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACrC,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;iBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;iBACtB,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;iBACxC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;iBAC1C,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;SAChC;aAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACzC,MAAM,GAAGA,GAAC,CAAC,mEAAmE,CAAC;iBAC5E,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;iBACtB,IAAI,CAAC,KAAK,EAAE,2BAA2B,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;iBACtD,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC/C;aAAM,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACvC,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;iBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;iBACtB,IAAI,CAAC,KAAK,EAAE,oCAAoC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC9D,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC/C;aAAM,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YAC7C,MAAM,GAAGA,GAAC,CAAC,mEAAmE,CAAC;iBAC5E,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;iBACtB,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;iBACrB,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;iBACpB,IAAI,CAAC,KAAK,EAAE,2BAA2B,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7D;aAAM,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,MAAM,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;YAC7E,IAAM,GAAG,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,MAAM,GAAGA,GAAC,CAAC,mEAAmE,CAAC;iBAC5E,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;iBACtB,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;iBACrB,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;iBACpB,IAAI,CAAC,KAAK,EAAE,yCAAyC,GAAG,GAAG,GAAG,aAAa,CAAC,CAAC;SACjF;aAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC5C,MAAM,GAAGA,GAAC,CAAC,kBAAkB,CAAC;iBAC3B,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC;iBAChB,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAC/C;aAAM;;YAEL,OAAO,KAAK,CAAC;SACd;QAED,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QAEnC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;KAClB;IAED,0BAAI,GAAJ;QAAA,iBAkBC;QAjBC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG;;YAElC,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;YACjC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;;YAG3C,IAAM,KAAK,GAAG,KAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAExC,IAAI,KAAK,EAAE;;gBAET,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;aACjD;SACF,CAAC,CAAC,IAAI,CAAC;YACN,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;SAC5C,CAAC,CAAC;KACJ;;;;;;;IAQD,qCAAe,GAAf,UAAgB,IAAI;QAApB,iBAoCC;QAnCC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;YACzB,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACvD,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEvD,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;gBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAE1C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;oBAC9B,KAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;iBAC/C,CAAC,CAAC;gBAEH,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;oBACvB,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;iBAC5B;gBAED,SAAS,CAAC,KAAK,CAAC,UAAC,KAAK;oBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;oBAEvB,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;iBACnC,CAAC,CAAC;gBAEH,KAAI,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;aACzC,CAAC,CAAC;YAEH,KAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,OAAO,EAAE;gBACnC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACvB,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEvB,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;oBAClC,QAAQ,CAAC,MAAM,EAAE,CAAC;iBACnB;aACF,CAAC,CAAC;YAEH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;SAClC,CAAC,CAAC;KACJ;IACH,kBAAC;CAAA;;ACzMc;IACb,oBAAY,OAAO;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;KACnC;IAED,+BAAU,GAAV;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAE1E,IAAM,IAAI,GAAG;YACX,yBAAyB;YACzB,2EAAgF;YAChF,mFAAmF;YACnF,sFAAsF;YACtF,MAAM;SACP,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;YAC5B,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;YAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;YAC9B,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC/B,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,UAAC,KAAK;gBACd,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,GAAG,CAAC;oBAC7C,YAAY,EAAE,GAAG;oBACjB,UAAU,EAAE,QAAQ;iBACrB,CAAC,CAAC;aACJ;SACF,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAClC;IAED,4BAAO,GAAP;QACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;KACvB;IAED,uCAAkB,GAAlB;QAAA,iBAWC;QAVC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;QAC7D,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAC,GAAG;YACjC,IAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAM,IAAI,GAAGA,GAAC,CAAC,0CAA0C,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAACA,GAAC,CAAC,cAAc,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC,GAAG,CAAC;gBACzD,OAAO,EAAE,GAAG;gBACZ,cAAc,EAAE,EAAE;aACnB,CAAC,CAAC,CAAC,MAAM,CAACA,GAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC;YAC/E,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;SACpB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACb;;;;;;IAOD,mCAAc,GAAd;QAAA,iBAQC;QAPC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;YACzB,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;gBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC1C,QAAQ,CAAC,OAAO,EAAE,CAAC;aACpB,CAAC,CAAC;YACH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;SAClC,CAAC,CAAC,OAAO,EAAE,CAAC;KACd;IAED,yBAAI,GAAJ;QAAA,iBAKC;QAJC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC;YACzB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;SAC5C,CAAC,CAAC;KACJ;IACH,iBAAC;CAAA;;ACxED,IAAM,yBAAyB,GAAG,EAAE,CAAC;AAEtB;IACb,oBAAY,OAAO;QAAnB,iBAuBC;QAtBC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG;YACZ,uDAAuD,EAAE;gBACvD,KAAI,CAAC,MAAM,EAAE,CAAC;aACf;YACD,8DAA8D,EAAE;gBAC9D,KAAI,CAAC,IAAI,EAAE,CAAC;aACb;YACD,qBAAqB,EAAE,UAAC,EAAE,EAAE,CAAC;;;gBAG3B,IAAI,GAAG,CAAC,IAAI,EAAE;oBACZ,OAAO;iBACR;gBAED,IAAI,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACjF,KAAI,CAAC,IAAI,EAAE,CAAC;iBACb;aACF;SACF,CAAC;KACH;IAED,qCAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KACzE;IAED,+BAAU,GAAV;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;YAC9B,SAAS,EAAE,kBAAkB;SAC9B,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAExD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC1E;IAED,4BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;KACxB;IAED,2BAAM,GAAN;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;QAC7D,IAAI,SAAS,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;YACrD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;YAC1D,IAAI,IAAI,EAAE;gBACR,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAChB,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,yBAAyB;oBACvE,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM;iBAC1B,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,4BAA4B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aAClE;SACF;aAAM;YACL,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;KACF;IAED,yBAAI,GAAJ;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IACH,iBAAC;CAAA;;ACjED,IAAM,YAAY,GAAG,CAAC,CAAC;AAER;IACb,qBAAY,OAAO;QAAnB,iBAuBC;QAtBC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;QACxD,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE5D,IAAI,CAAC,MAAM,GAAG;YACZ,kBAAkB,EAAE,UAAC,EAAE,EAAE,CAAC;gBACxB,IAAI,CAAC,CAAC,CAAC,kBAAkB,EAAE,EAAE;oBAC3B,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;iBACrB;aACF;YACD,oBAAoB,EAAE,UAAC,EAAE,EAAE,CAAC;gBAC1B,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;aACvB;YACD,4CAA4C,EAAE;gBAC5C,KAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF,CAAC;KACH;IAED,sCAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KAC9B;IAED,gCAAU,GAAV;QAAA,iBAeC;QAdC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;YAC9B,SAAS,EAAE,mBAAmB;YAC9B,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,EAAE;SACd,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE7C,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC7E,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE;YAC3C,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACpDA,GAAC,CAAC,KAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC3B,KAAI,CAAC,OAAO,EAAE,CAAC;SAChB,CAAC,CAAC;KACJ;IAED,6BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;KACxB;IAED,gCAAU,GAAV,UAAW,KAAK;QACd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACpD,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEzB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;KACrF;IAED,8BAAQ,GAAR;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC9D,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACxB;aAAM;YACL,IAAI,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;YAE1C,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBACtB,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,KAAK,EAAE,CAAC;aAC7D;YAED,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;SAC7D;KACF;IAED,4BAAM,GAAN;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC9D,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACxB;aAAM;YACL,IAAI,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;YAE1C,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;gBACtB,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,EAAE,CAAC;aAC5D;YAED,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;SAC5D;KACF;IAED,6BAAO,GAAP;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAE3D,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;;YAEtC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACpC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC;YAE/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SACrC;KACF;IAED,kCAAY,GAAZ,UAAa,KAAK;QAChB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC7C,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QACpD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC7B;QACD,OAAO,IAAI,CAAC;KACb;IAED,yCAAmB,GAAnB,UAAoB,OAAO,EAAE,KAAK;QAChC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACjC,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,EAAE,GAAG;YACzB,IAAM,KAAK,GAAGA,GAAC,CAAC,+BAA+B,CAAC,CAAC;YACjD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;YAC9D,KAAK,CAAC,IAAI,CAAC;gBACT,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;SACd,CAAC,CAAC;KACJ;IAED,mCAAa,GAAb,UAAc,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;YACjC,OAAO;SACR;QAED,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;YAChC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;aAAM,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YACpC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;aAAM,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;YACtC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjB;KACF;IAED,mCAAa,GAAb,UAAc,KAAK,EAAE,OAAO,EAAE,QAAQ;QACpC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACnD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACnC;aAAM;YACL,QAAQ,EAAE,CAAC;SACZ;KACF;IAED,iCAAW,GAAX,UAAY,GAAG,EAAE,OAAO;QAAxB,iBAWC;QAVC,IAAM,MAAM,GAAGA,GAAC,CAAC,8CAA8C,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,UAAC,KAAK;YACrC,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,MAAM,EAAE;gBAChB,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;gBAClD,KAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;KACf;IAED,iCAAW,GAAX,UAAY,CAAC;QAAb,iBAoCC;QAnCC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE;YAC5E,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,EAAE,CAAC;YAC3E,IAAM,SAAO,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,SAAO,EAAE;gBAChC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAEtB,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBAClE,IAAI,GAAG,EAAE;oBACP,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACrB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;oBAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,GAAG;wBAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAO,CAAC,EAAE;4BAC5B,KAAI,CAAC,WAAW,CAAC,GAAG,EAAE,SAAO,CAAC,CAAC,QAAQ,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;yBACxD;qBACF,CAAC,CAAC;;oBAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;;oBAG/D,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;wBAC5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;4BAChB,IAAI,EAAE,GAAG,CAAC,IAAI;4BACd,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,YAAY;yBAC1D,CAAC,CAAC;qBACJ;yBAAM;wBACL,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;4BAChB,IAAI,EAAE,GAAG,CAAC,IAAI;4BACd,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,YAAY;yBACzC,CAAC,CAAC;qBACJ;iBACF;aACF;iBAAM;gBACL,IAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF;KACF;IAED,0BAAI,GAAJ;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IAED,0BAAI,GAAJ;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtB;IACH,kBAAC;CAAA;;AC5Nc;;;;;IAKb,iBAAY,KAAK,EAAE,OAAO;QACxB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,UAAU,EAAE,CAAC;KACnB;;;;IAKD,4BAAU,GAAV;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;KACb;;;;IAKD,yBAAO,GAAP;QACE,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACpC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;KACnD;;;;IAKD,uBAAK,GAAL;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;KACF;IAED,6BAAW,GAAX;QAAA,iBAiBC;;QAfC,IAAM,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC/B,KAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C,CAAC,CAAC;QAEH,IAAM,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAEA,GAAC,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;;QAG/E,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC/B,KAAI,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;SACtC,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YACpC,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;SAC5B,CAAC,CAAC;KACJ;IAED,0BAAQ,GAAR;QAAA,iBAWC;;QATC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAC,GAAG;YAC9C,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;SACxB,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAClC,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SACtB,CAAC,CAAC;;QAEH,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KACpC;IAED,sBAAI,GAAJ,UAAK,IAAI;QACP,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;QAExD,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YAC7B,OAAO,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SACtF;aAAM;YACL,IAAI,WAAW,EAAE;gBACf,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACnC;iBAAM;gBACL,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACrC;YACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACrB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACnC;KACF;IAED,4BAAU,GAAV;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAAC;KACrE;IAED,wBAAM,GAAN;QACE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KACrC;IAED,yBAAO,GAAP;;QAEE,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;SACpC;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;KACpC;IAED,8BAAY,GAAZ;QACE,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAE/C,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;KACrD;IAED,kCAAgB,GAAhB,UAAiB,GAAG;QAClB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,EAAE,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,EAAE;YAC9B,OAAO;SACR;;QAGD,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,MAAM,CAAC,UAAU,EAAE,CAAC;SACrB;;QAGD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SAC7C;KACF;IAED,wBAAM,GAAN,UAAO,GAAG,EAAE,WAAW,EAAE,gBAAgB;QACvC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,CAAC,gBAAgB,EAAE;YACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;SAC5B;KACF;IAED,8BAAY,GAAZ,UAAa,GAAG;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,MAAM,CAAC,gBAAgB,EAAE,EAAE;YAC7B,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;aAC7C;YAED,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,MAAM,CAAC,OAAO,EAAE,CAAC;aAClB;SACF;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC1B;IAED,sBAAI,GAAJ,UAAK,GAAG,EAAE,GAAG;QACX,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxB;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACvB;IAED,4BAAU,GAAV,UAAW,GAAG;QACZ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;SAC3B;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACxB;;;;IAKD,mDAAiC,GAAjC,UAAkC,SAAS,EAAE,KAAK;QAAlD,iBAKC;QAJC,OAAO,UAAC,KAAK;YACX,KAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;YAClD,KAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;SAC3C,CAAC;KACH;IAED,qCAAmB,GAAnB,UAAoB,SAAS,EAAE,KAAK;QAApC,iBAMC;QALC,OAAO,UAAC,KAAK;YACX,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAChC,KAAI,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;SACzF,CAAC;KACH;IAED,wBAAM,GAAN;QACE,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAE/C,IAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAM,UAAU,GAAG,YAAY,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,IAAM,UAAU,GAAG,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE1E,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3C;aAAM,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,gBAAgB,EAAE,EAAE;YACpE,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC/C;KACF;IACH,cAAC;CAAA;;ACjODA,GAAC,CAAC,EAAE,CAAC,MAAM,CAAC;;;;;;;IAOV,UAAU,EAAE;QACV,IAAM,IAAI,GAAGA,GAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC3C,IAAM,mBAAmB,GAAG,IAAI,KAAK,QAAQ,CAAC;QAC9C,IAAM,cAAc,GAAG,IAAI,KAAK,QAAQ,CAAC;QAEzC,IAAM,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,EAAE,EAAEA,GAAC,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;;QAGhG,OAAO,CAAC,QAAQ,GAAGA,GAAC,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,EAAEA,GAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAEA,GAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACnG,OAAO,CAAC,KAAK,GAAGA,GAAC,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,EAAEA,GAAC,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9E,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC;QAErF,IAAI,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;YAClB,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBAC7B,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC5C,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;gBAClC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;aACnE;SACF,CAAC,CAAC;QAEH,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,IAAI,mBAAmB,EAAE;gBACvB,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aAC7D;iBAAM,IAAI,OAAO,CAAC,KAAK,EAAE;gBACxB,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;aAChC;SACF;QAED,OAAO,IAAI,CAAC;KACb;CACF,CAAC,CAAC;;ACnBHA,GAAC,CAAC,UAAU,GAAGA,GAAC,CAAC,MAAM,CAACA,GAAC,CAAC,UAAU,EAAE;IACpC,OAAO,EAAE,QAAa;IACtB,EAAE,EAAE,EAAE;IACN,GAAG,EAAE,GAAG;IAER,OAAO,EAAE,EAAE;IAEX,OAAO,EAAE;QACP,OAAO,EAAE;YACP,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,SAAS;YACtB,UAAU,EAAE,QAAQ;YACpB,UAAU,EAAEC,QAAQ;YACpB,WAAW,EAAE,SAAS;YACtB,YAAY,EAAE,UAAU;YACxB,QAAQ,EAAE,MAAM;;;YAGhB,aAAa,EAAE,WAAW;YAC1B,UAAU,EAAE,QAAQ;YACpB,UAAU,EAAE,QAAQ;YACpB,aAAa,EAAE,WAAW;YAC1B,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,OAAO;YAClB,YAAY,EAAE,UAAU;YACxB,aAAa,EAAE,WAAW;YAC1B,aAAa,EAAE,WAAW;YAC1B,cAAc,EAAE,YAAY;YAC5B,cAAc,EAAE,YAAY;YAC5B,aAAa,EAAE,WAAW;YAC1B,YAAY,EAAE,UAAU;YACxB,YAAY,EAAE,UAAU;SACzB;QAED,OAAO,EAAE,EAAE;QAEX,IAAI,EAAE,OAAO;QAEb,gBAAgB,EAAE,IAAI;QACtB,cAAc,EAAE,EAAE;;QAGlB,OAAO,EAAE;YACP,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,CAAC;YAC1B,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YACnC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YACxC,CAAC,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;SAC7C;;QAGD,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE;YACP,KAAK,EAAE;gBACL,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;gBAC7D,CAAC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;gBACnD,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC;aAC5B;YACD,IAAI,EAAE;gBACJ,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;aACvC;YACD,KAAK,EAAE;gBACL,CAAC,KAAK,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;gBAChE,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;aACtD;YACD,GAAG,EAAE;gBACH,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;gBACpB,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;gBACxC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC7B,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;gBACpB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;aAChC;SACF;;QAGD,OAAO,EAAE,KAAK;QAEd,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;QACZ,eAAe,EAAE,IAAI;QAErB,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,CAAC;QACV,aAAa,EAAE,IAAI;QACnB,SAAS,EAAE,IAAI;QACf,gBAAgB,EAAE,IAAI;QACtB,aAAa,EAAE,QAAQ;QACvB,OAAO,EAAE,MAAM;QACf,SAAS,EAAE,MAAM;QACjB,aAAa,EAAE,CAAC;QAEhB,SAAS,EAAE,CAAC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAEzE,SAAS,EAAE;YACT,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa;YACtD,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe;YACxD,QAAQ,EAAE,iBAAiB,EAAE,SAAS;SACvC;QAED,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;QAG/D,MAAM,EAAE;YACN,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;SACzF;;QAGD,UAAU,EAAE;YACV,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;YAC7F,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,SAAS,CAAC;YACvF,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,CAAC;YAC/F,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC;YAC1G,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,CAAC;YAC9F,CAAC,eAAe,EAAE,WAAW,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAE,UAAU,CAAC;YAC7H,CAAC,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,CAAC;YACnG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,CAAC;SAC9F;QAED,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QAErE,cAAc,EAAE,sBAAsB;QAEtC,kBAAkB,EAAE;YAClB,GAAG,EAAE,EAAE;YACP,GAAG,EAAE,EAAE;SACR;QAED,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,KAAK;QAElB,oBAAoB,EAAE,IAAI;QAE1B,SAAS,EAAE;YACT,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI;YACZ,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,IAAI;SACzB;QAED,UAAU,EAAE;YACV,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,IAAI;SAClB;QAED,MAAM,EAAE;YACN,EAAE,EAAE;gBACF,OAAO,EAAE,iBAAiB;gBAC1B,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,KAAK;gBACZ,WAAW,EAAE,OAAO;gBACpB,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,WAAW;gBACrB,cAAc,EAAE,eAAe;gBAC/B,gBAAgB,EAAE,cAAc;gBAChC,cAAc,EAAE,aAAa;gBAC7B,cAAc,EAAE,eAAe;gBAC/B,cAAc,EAAE,cAAc;gBAC9B,cAAc,EAAE,aAAa;gBAC7B,iBAAiB,EAAE,qBAAqB;gBACxC,iBAAiB,EAAE,mBAAmB;gBACtC,kBAAkB,EAAE,SAAS;gBAC7B,mBAAmB,EAAE,QAAQ;gBAC7B,WAAW,EAAE,YAAY;gBACzB,WAAW,EAAE,UAAU;gBACvB,WAAW,EAAE,UAAU;gBACvB,WAAW,EAAE,UAAU;gBACvB,WAAW,EAAE,UAAU;gBACvB,WAAW,EAAE,UAAU;gBACvB,WAAW,EAAE,UAAU;gBACvB,YAAY,EAAE,sBAAsB;gBACpC,QAAQ,EAAE,iBAAiB;aAC5B;YAED,GAAG,EAAE;gBACH,OAAO,EAAE,iBAAiB;gBAC1B,OAAO,EAAE,MAAM;gBACf,aAAa,EAAE,MAAM;gBACrB,KAAK,EAAE,KAAK;gBACZ,WAAW,EAAE,OAAO;gBACpB,OAAO,EAAE,MAAM;gBACf,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE,WAAW;gBACpB,aAAa,EAAE,eAAe;gBAC9B,eAAe,EAAE,cAAc;gBAC/B,aAAa,EAAE,aAAa;gBAC5B,aAAa,EAAE,eAAe;gBAC9B,aAAa,EAAE,cAAc;gBAC7B,aAAa,EAAE,aAAa;gBAC5B,gBAAgB,EAAE,qBAAqB;gBACvC,gBAAgB,EAAE,mBAAmB;gBACrC,iBAAiB,EAAE,SAAS;gBAC5B,kBAAkB,EAAE,QAAQ;gBAC5B,UAAU,EAAE,YAAY;gBACxB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,UAAU;gBACtB,WAAW,EAAE,sBAAsB;gBACnC,OAAO,EAAE,iBAAiB;aAC3B;SACF;QACD,KAAK,EAAE;YACL,OAAO,EAAE,iBAAiB;YAC1B,aAAa,EAAE,wBAAwB;YACvC,cAAc,EAAE,yBAAyB;YACzC,WAAW,EAAE,sBAAsB;YACnC,YAAY,EAAE,uBAAuB;YACrC,UAAU,EAAE,qBAAqB;YACjC,WAAW,EAAE,sBAAsB;YACnC,UAAU,EAAE,qBAAqB;YACjC,UAAU,EAAE,qBAAqB;YACjC,WAAW,EAAE,sBAAsB;YACnC,WAAW,EAAE,sBAAsB;YACnC,QAAQ,EAAE,wBAAwB;YAClC,SAAS,EAAE,yBAAyB;YACpC,WAAW,EAAE,sBAAsB;YACnC,MAAM,EAAE,gBAAgB;YACxB,OAAO,EAAE,iBAAiB;YAC1B,QAAQ,EAAE,kBAAkB;YAC5B,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,gBAAgB;YACxB,QAAQ,EAAE,kBAAkB;YAC5B,MAAM,EAAE,gBAAgB;YACxB,OAAO,EAAE,iBAAiB;YAC1B,QAAQ,EAAE,kBAAkB;YAC5B,MAAM,EAAE,gBAAgB;YACxB,QAAQ,EAAE,wBAAwB;YAClC,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,sBAAsB;YACnC,OAAO,EAAE,iBAAiB;YAC1B,aAAa,EAAE,uBAAuB;YACtC,QAAQ,EAAE,kBAAkB;YAC5B,SAAS,EAAE,mBAAmB;YAC9B,UAAU,EAAE,oBAAoB;YAChC,MAAM,EAAE,gBAAgB;YACxB,QAAQ,EAAE,kBAAkB;YAC5B,eAAe,EAAE,yBAAyB;YAC1C,WAAW,EAAE,qBAAqB;YAClC,aAAa,EAAE,uBAAuB;YACtC,OAAO,EAAE,iBAAiB;YAC1B,YAAY,EAAE,uBAAuB;YACrC,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,qBAAqB;YAClC,MAAM,EAAE,gBAAgB;YACxB,eAAe,EAAE,yBAAyB;YAC1C,OAAO,EAAE,iBAAiB;SAC3B;KACF;CACF,CAAC,CAAC;;;;"}