{"version": 3, "file": "vue-resize.esm-min.js", "lineCount": 12, "mappings": "AAAAA,QAASA,kDAA0B,EAAG,CACpC,IAAIC,EAAKC,MAAOC,CAAAA,SAAUC,CAAAA,SAA1B,CACIC,EAAOJ,CAAGK,CAAAA,OAAH,CAAW,OAAX,CAEX,IAAW,CAAX,CAAID,CAAJ,CAEE,MAAOE,SAAA,CAASN,CAAGO,CAAAA,SAAH,CAAaH,CAAb,CAAoB,CAApB,CAAuBJ,CAAGK,CAAAA,OAAH,CAAW,GAAX,CAAgBD,CAAhB,CAAvB,CAAT,CAAwD,EAAxD,CAKT,IAAc,CAAd,CAFcJ,CAAGK,CAAAA,OAAHG,CAAW,UAAXA,CAEd,CAGE,MADIC,EACG,CADET,CAAGK,CAAAA,OAAH,CAAW,KAAX,CACF,CAAAC,QAAA,CAASN,CAAGO,CAAAA,SAAH,CAAaE,CAAb,CAAkB,CAAlB,CAAqBT,CAAGK,CAAAA,OAAH,CAAW,GAAX,CAAgBI,CAAhB,CAArB,CAAT,CAAoD,EAApD,CAGLC,EAAAA,CAAOV,CAAGK,CAAAA,OAAH,CAAW,OAAX,CAEX,OAAW,EAAX,CAAIK,CAAJ,CAESJ,QAAA,CAASN,CAAGO,CAAAA,SAAH,CAAaG,CAAb,CAAoB,CAApB,CAAuBV,CAAGK,CAAAA,OAAH,CAAW,GAAX,CAAgBK,CAAhB,CAAvB,CAAT,CAAwD,EAAxD,CAFT,CAMO,EAzB6B,CA8BtC,IAAIC,2BAEJC;QAASA,kCAAW,EAAG,CAChBA,iCAAWC,CAAAA,IAAhB,GACED,iCAAWC,CAAAA,IACX,CADkB,CAAA,CAClB,CAAAF,2BAAA,CAAwC,EAAxC,GAAOZ,iDAAA,EAFT,CADqB;AAOvB,IAAIe,8BAAS,CACXC,KAAM,gBADK,CAGXC,MAAO,CACLC,YAAa,CACXC,KAAMC,OADK,CAEXC,QAAS,CAAA,CAFE,CADR,CAMLC,YAAa,CACXH,KAAMC,OADK,CAEXC,QAAS,CAAA,CAFE,CANR,CAWLE,aAAc,CACZJ,KAAMC,OADM,CAEZC,QAAS,CAAA,CAFG,CAXT,CAHI,CAoBX,QAAAG,QAAQ,EAAG,CAAA,IAAA,EAAA,IACTX,kCAAA,EACA,KAAKY,CAAAA,SAAL,CAAe,QAAA,EAAM,CACnB,CAAKC,CAAAA,EAAL,CAAU,CAAKC,CAAAA,GAAIC,CAAAA,WACnB,EAAKC,CAAAA,EAAL,CAAU,CAAKF,CAAAA,GAAIG,CAAAA,YACf,EAAKZ,CAAAA,WAAT,EACE,CAAKa,CAAAA,QAAL,EAJiB,CAArB,CAOA,KAAMC,EAASC,QAASC,CAAAA,aAAT,CAAuB,QAAvB,CACf,KAAKC,CAAAA,aAAL,CAAqBH,CACrBA,EAAOI,CAAAA,YAAP,CAAoB,aAApB,CAAmC,MAAnC,CACAJ,EAAOI,CAAAA,YAAP,CAAoB,UAApB,CAAgC,EAAhC,CACAJ,EAAOK,CAAAA,MAAP,CAAgB,IAAKC,CAAAA,iBACrBN;CAAOb,CAAAA,IAAP,CAAc,WACVP,4BAAJ,EACE,IAAKe,CAAAA,GAAIY,CAAAA,WAAT,CAAqBP,CAArB,CAEFA,EAAOQ,CAAAA,IAAP,CAAc,aACT5B,4BAAL,EACE,IAAKe,CAAAA,GAAIY,CAAAA,WAAT,CAAqBP,CAArB,CApBO,CApBA,CA4CX,cAAAS,QAAc,EAAG,CACf,IAAKC,CAAAA,oBAAL,EADe,CA5CN,CAgDXC,QAAS,CACP,iBAAAC,QAAiB,EAAG,CAClB,GAAK,CAAC,IAAKtB,CAAAA,WAAX,EAA0B,IAAKI,CAAAA,EAA/B,GAAsC,IAAKC,CAAAA,GAAIC,CAAAA,WAA/C,EAAgE,CAAC,IAAKL,CAAAA,YAAtE,EAAsF,IAAKM,CAAAA,EAA3F,GAAkG,IAAKF,CAAAA,GAAIG,CAAAA,YAA3G,CACE,IAAKJ,CAAAA,EAEL,CAFU,IAAKC,CAAAA,GAAIC,CAAAA,WAEnB,CADA,IAAKC,CAAAA,EACL,CADU,IAAKF,CAAAA,GAAIG,CAAAA,YACnB,CAAA,IAAKC,CAAAA,QAAL,EAJgB,CADb,CASP,SAAAA,QAAS,EAAG,CACV,IAAKc,CAAAA,KAAL,CAAW,QAAX,CAAqB,CACnBC,MAAO,IAAKpB,CAAAA,EADO,CAEnBqB,OAAQ,IAAKlB,CAAAA,EAFM,CAArB,CADU,CATL,CAgBP,kBAAAS,QAAkB,EAAG,CACnB,IAAKH,CAAAA,aAAca,CAAAA,eAAgBC,CAAAA,WAAYC,CAAAA,gBAA/C,CAAgE,QAAhE;AAA0E,IAAKN,CAAAA,gBAA/E,CACA,KAAKA,CAAAA,gBAAL,EAFmB,CAhBd,CAqBP,qBAAAF,QAAqB,EAAG,CAClB,IAAKP,CAAAA,aAAT,EAA0B,IAAKA,CAAAA,aAAcE,CAAAA,MAA7C,GACM,CAACzB,2BAKL,EALa,IAAKuB,CAAAA,aAAca,CAAAA,eAKhC,EAJE,IAAKb,CAAAA,aAAca,CAAAA,eAAgBC,CAAAA,WAAYE,CAAAA,mBAA/C,CAAmE,QAAnE,CAA6E,IAAKP,CAAAA,gBAAlF,CAIF,CAFA,IAAKjB,CAAAA,GAAIyB,CAAAA,WAAT,CAAqB,IAAKjB,CAAAA,aAA1B,CAEA,CAAA,IAAKA,CAAAA,aAAL,CADA,IAAKA,CAAAA,aAAcE,CAAAA,MACnB,CAD4B,IAL9B,CADsB,CArBjB,CAhDE,CAkFbgB;QAASA,0CAAkB,CAACC,CAAD,CAAWC,CAAX,CAAkBxC,CAAlB,CAA0ByC,CAA1B,CAAmCC,CAAnC,CAAyDC,CAAzD,CAEzBC,CAFyB,CAEbC,CAFa,CAEGC,CAFH,CAEsBC,CAFtB,CAE4C,CAC3C,SAA1B,GAAI,MAAOH,EAAX,GACEE,CAEA,CAFoBD,CAEpB,CADAA,CACA,CADiBD,CACjB,CAAAA,CAAA,CAAa,CAAA,CAHf,CAOA,KAAII,EAA4B,UAAlB,GAAA,MAAOhD,EAAP,CAA+BA,CAAOgD,CAAAA,OAAtC,CAAgDhD,CAE1DuC,EAAJ,EAAgBA,CAASU,CAAAA,MAAzB,GACED,CAAQC,CAAAA,MAIR,CAJiBV,CAASU,CAAAA,MAI1B,CAHAD,CAAQE,CAAAA,eAGR,CAH0BX,CAASW,CAAAA,eAGnC,CAFAF,CAAQG,CAAAA,SAER,CAFoB,CAAA,CAEpB,CAAIT,CAAJ,GACEM,CAAQI,CAAAA,UADV,CACuB,CAAA,CADvB,CALF,CAWIX,EAAJ,GACEO,CAAQK,CAAAA,QADV,CACqBZ,CADrB,CAMA,IAAIE,CAAJ,CAAsB,CAEpB,IAAAW,EAAOA,QAAa,CAACC,CAAD,CAAU,CAO5B,CALAA,CAKA,CALUA,CAKV,EAJA,IAAKC,CAAAA,MAIL,EAJe,IAAKA,CAAAA,MAAOC,CAAAA,UAI3B,EAHA,IAAKC,CAAAA,MAGL,EAHe,IAAKA,CAAAA,MAAOF,CAAAA,MAG3B,EAHqC,IAAKE,CAAAA,MAAOF,CAAAA,MAAOC,CAAAA,UAGxD,GAA+C,WAA/C,GAAgB,MAAOE,oBAAvB,GACEJ,CADF,CACYI,mBADZ,CAKInB,EAAJ,EACEA,CAAMoB,CAAAA,IAAN,CAAW,IAAX,CAAiBd,CAAA,CAAkBS,CAAlB,CAAjB,CAIEA,EAAJ,EAAeA,CAAQM,CAAAA,qBAAvB;AACEN,CAAQM,CAAAA,qBAAsBC,CAAAA,GAA9B,CAAkCnB,CAAlC,CAlB0B,CAwB9BK,EAAQe,CAAAA,YAAR,CAAuBT,CA1BH,CAAtB,IA2BWd,EAAJ,GACLc,CADK,CACEV,CAAA,CAAa,QAAS,CAACW,CAAD,CAAU,CACrCf,CAAMoB,CAAAA,IAAN,CAAW,IAAX,CAAiBb,CAAA,CAAqBQ,CAArB,CAA8B,IAAKS,CAAAA,KAAMC,CAAAA,QAASC,CAAAA,UAAlD,CAAjB,CADqC,CAAhC,CAEH,QAAS,CAACX,CAAD,CAAU,CACrBf,CAAMoB,CAAAA,IAAN,CAAW,IAAX,CAAiBf,CAAA,CAAeU,CAAf,CAAjB,CADqB,CAHlB,CAQP,IAAID,CAAJ,CACE,GAAIN,CAAQI,CAAAA,UAAZ,CAAwB,CAEtB,IAAIe,EAAiBnB,CAAQC,CAAAA,MAE7BD,EAAQC,CAAAA,MAAR,CAAiBmB,QAAiC,CAACC,CAAD,CAAId,CAAJ,CAAa,CAC7DD,CAAKM,CAAAA,IAAL,CAAUL,CAAV,CACA,OAAOY,EAAA,CAAeE,CAAf,CAAkBd,CAAlB,CAFsD,CAJzC,CAAxB,IAUMe,EACJ,CADetB,CAAQuB,CAAAA,YACvB,CAAAvB,CAAQuB,CAAAA,YAAR,CAAuBD,CAAA,CAAW,EAAGE,CAAAA,MAAH,CAAUF,CAAV,CAAoBhB,CAApB,CAAX,CAAuC,CAACA,CAAD,CAIlE,OAAOtD,EA9E8D;AAkFvE,IAAMyE,sCAAiBzE,6BAAvB,CAEI0E,sCAAiBA,QAAQ,EAAG,CAE9B,IAAI5D,EADM6D,IACGC,CAAAA,cAEb,OAAO,CAHGD,IAEGE,CAAAA,KAAMC,CAAAA,EACZ,EADkBhE,CAClB,EAAG,KAAH,CAAU,CACfiE,YAAa,iBADE,CAEfC,MAAO,CAAEC,SAAU,IAAZ,CAFQ,CAAV,CAJuB,CAFhC,CAWIC,+CAA0B,EAC9BR,sCAAeS,CAAAA,aAAf,CAA+B,CAAA,CAG7B;IAAMC,6CAAwBC,IAAAA,EAA9B,CAEMC,wCAAmB,iBAFzB,CAIMC,iDAA4BF,IAAAA,EAJlC,CAMMG,sDAAiC,CAAA,CANvC,CAeMC,yCAAiCnD,yCAAA,CACrC,CAAEW,OAAQyB,qCAAV,CAA0BxB,gBAAiBgC,8CAA3C,CADqC,CAErCE,4CAFqC,CAGrCX,6BAHqC,CAIrCa,uCAJqC;AAKrCE,qDALqC,CAMrCD,gDANqC,CAOrC,CAAA,CAPqC,CAQrCF,IAAAA,EARqC,CASrCA,IAAAA,EATqC,CAUrCA,IAAAA,EAVqC,CAazCK,SAASA,+BAAO,CAACC,CAAD,CAAM,CAEpBA,CAAIC,CAAAA,SAAJ,CAAc,iBAAd,CAAiCH,wCAAjC,CACAE,EAAIC,CAAAA,SAAJ,CAAc,gBAAd,CAAgCH,wCAAhC,CAHoB,CAMtB,IAAII,8BAAS,CAEXC,QAAS,OAFE,CAGXJ,QAASA,8BAHE,CAAb,CAMIK,iCAAY,IAEM;WAAtB,GAAI,MAAO5G,OAAX,CACE4G,gCADF,CACc5G,MAAOwG,CAAAA,GADrB,CAE6B,WAF7B,GAEW,MAAOK,OAFlB,GAGED,gCAHF,CAGcC,MAAOL,CAAAA,GAHrB,CAMII,iCAAJ,EACEA,gCAAUE,CAAAA,GAAV,CAAcJ,6BAAd,CAGF,KAAAK,4CAAeL,6BAAf,CAhRAM,sBAAA,CAiRSV,eAAAA,wCAjRT,CAgRA,sBAAA,CAAA,OAAA,CAAA,2CAC8CC;qBAAAA,CAAAA,OAAAA,CAAAA;", "sources": ["vue-resize.esm.js"], "names": ["getInternetExplorerVersion", "ua", "window", "navigator", "userAgent", "msie", "indexOf", "parseInt", "substring", "trident", "rv", "edge", "isIE", "initCompat", "init", "script", "name", "props", "emitOnMount", "type", "Boolean", "default", "<PERSON><PERSON><PERSON><PERSON>", "ignoreHeight", "mounted", "$nextTick", "_w", "$el", "offsetWidth", "_h", "offsetHeight", "emitSize", "object", "document", "createElement", "_resizeObject", "setAttribute", "onload", "addResizeHandlers", "append<PERSON><PERSON><PERSON>", "data", "<PERSON><PERSON><PERSON><PERSON>", "removeResizeHandlers", "methods", "compareAndNotify", "$emit", "width", "height", "contentDocument", "defaultView", "addEventListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "normalizeComponent", "template", "style", "scopeId", "isFunctionalTemplate", "moduleIdentifier", "shadowMode", "createInjector", "createInjectorSSR", "createInjectorShadow", "options", "render", "staticRenderFns", "_compiled", "functional", "_scopeId", "hook", "context", "$vnode", "ssrContext", "parent", "__VUE_SSR_CONTEXT__", "call", "_registeredComponents", "add", "_ssrRegister", "$root", "$options", "shadowRoot", "originalRender", "options.render", "h", "existing", "beforeCreate", "concat", "__vue_script__", "__vue_render__", "_vm", "$createElement", "_self", "_c", "staticClass", "attrs", "tabindex", "__vue_staticRenderFns__", "_withStripped", "__vue_inject_styles__", "undefined", "__vue_scope_id__", "__vue_module_identifier__", "__vue_is_functional_template__", "__vue_component__", "install", "<PERSON><PERSON>", "component", "plugin", "version", "GlobalVue", "global", "use", "$jscompDefaultExport", "$jscomp$tmp$exports$module$name"]}