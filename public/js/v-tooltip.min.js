var VTooltip=function(e){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}
    /**!
     * @fileOverview Kickass library to create and place poppers near their reference elements.
     * @version 1.16.1
     * @license
     * Copyright (c) 2016 <PERSON> and contributors
     *
     * Permission is hereby granted, free of charge, to any person obtaining a copy
     * of this software and associated documentation files (the "Software"), to deal
     * in the Software without restriction, including without limitation the rights
     * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
     * copies of the Software, and to permit persons to whom the Software is
     * furnished to do so, subject to the following conditions:
     *
     * The above copyright notice and this permission notice shall be included in all
     * copies or substantial portions of the Software.
     *
     * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
     * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
     * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
     * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
     * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
     * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
     * SOFTWARE.
     */
    var r="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,i=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(r&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0}();var s=r&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then((function(){t=!1,e()})))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout((function(){t=!1,e()}),i))}};function a(e){return e&&"[object Function]"==={}.toString.call(e)}function u(e,t){if(1!==e.nodeType)return[];var n=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?n[t]:n}function p(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function c(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=u(e),n=t.overflow,o=t.overflowX,r=t.overflowY;return/(auto|scroll|overlay)/.test(n+r+o)?e:c(p(e))}function l(e){return e&&e.referenceNode?e.referenceNode:e}var f=r&&!(!window.MSInputMethodContext||!document.documentMode),d=r&&/MSIE 10/.test(navigator.userAgent);function h(e){return 11===e?f:10===e?d:f||d}function v(e){if(!e)return document.documentElement;for(var t=h(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var o=n&&n.nodeName;return o&&"BODY"!==o&&"HTML"!==o?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===u(n,"position")?v(n):n:e?e.ownerDocument.documentElement:document.documentElement}function m(e){return null!==e.parentNode?m(e.parentNode):e}function g(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,o=n?e:t,r=n?t:e,i=document.createRange();i.setStart(o,0),i.setEnd(r,0);var s,a,u=i.commonAncestorContainer;if(e!==u&&t!==u||o.contains(r))return"BODY"===(a=(s=u).nodeName)||"HTML"!==a&&v(s.firstElementChild)!==s?v(u):u;var p=m(e);return p.host?g(p.host,t):g(e,m(t).host)}function b(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top",n="top"===t?"scrollTop":"scrollLeft",o=e.nodeName;if("BODY"===o||"HTML"===o){var r=e.ownerDocument.documentElement,i=e.ownerDocument.scrollingElement||r;return i[n]}return e[n]}function y(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=b(t,"top"),r=b(t,"left"),i=n?-1:1;return e.top+=o*i,e.bottom+=o*i,e.left+=r*i,e.right+=r*i,e}function _(e,t){var n="x"===t?"Left":"Top",o="Left"===n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+o+"Width"])}function w(e,t,n,o){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],h(10)?parseInt(n["offset"+e])+parseInt(o["margin"+("Height"===e?"Top":"Left")])+parseInt(o["margin"+("Height"===e?"Bottom":"Right")]):0)}function O(e){var t=e.body,n=e.documentElement,o=h(10)&&getComputedStyle(n);return{height:w("Height",t,n,o),width:w("Width",t,n,o)}}var E=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},j=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),C=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},T=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e};function $(e){return T({},e,{right:e.left+e.width,bottom:e.top+e.height})}function S(e){var t={};try{if(h(10)){t=e.getBoundingClientRect();var n=b(e,"top"),o=b(e,"left");t.top+=n,t.left+=o,t.bottom+=n,t.right+=o}else t=e.getBoundingClientRect()}catch(e){}var r={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},i="HTML"===e.nodeName?O(e.ownerDocument):{},s=i.width||e.clientWidth||r.width,a=i.height||e.clientHeight||r.height,p=e.offsetWidth-s,c=e.offsetHeight-a;if(p||c){var l=u(e);p-=_(l,"x"),c-=_(l,"y"),r.width-=p,r.height-=c}return $(r)}function x(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=h(10),r="HTML"===t.nodeName,i=S(e),s=S(t),a=c(e),p=u(t),l=parseFloat(p.borderTopWidth),f=parseFloat(p.borderLeftWidth);n&&r&&(s.top=Math.max(s.top,0),s.left=Math.max(s.left,0));var d=$({top:i.top-s.top-l,left:i.left-s.left-f,width:i.width,height:i.height});if(d.marginTop=0,d.marginLeft=0,!o&&r){var v=parseFloat(p.marginTop),m=parseFloat(p.marginLeft);d.top-=l-v,d.bottom-=l-v,d.left-=f-m,d.right-=f-m,d.marginTop=v,d.marginLeft=m}return(o&&!n?t.contains(a):t===a&&"BODY"!==a.nodeName)&&(d=y(d,t)),d}function k(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.ownerDocument.documentElement,o=x(e,n),r=Math.max(n.clientWidth,window.innerWidth||0),i=Math.max(n.clientHeight,window.innerHeight||0),s=t?0:b(n),a=t?0:b(n,"left"),u={top:s-o.top+o.marginTop,left:a-o.left+o.marginLeft,width:r,height:i};return $(u)}function P(e){var t=e.nodeName;if("BODY"===t||"HTML"===t)return!1;if("fixed"===u(e,"position"))return!0;var n=p(e);return!!n&&P(n)}function N(e){if(!e||!e.parentElement||h())return document.documentElement;for(var t=e.parentElement;t&&"none"===u(t,"transform");)t=t.parentElement;return t||document.documentElement}function A(e,t,n,o){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i={top:0,left:0},s=r?N(e):g(e,l(t));if("viewport"===o)i=k(s,r);else{var a=void 0;"scrollParent"===o?"BODY"===(a=c(p(t))).nodeName&&(a=e.ownerDocument.documentElement):a="window"===o?e.ownerDocument.documentElement:o;var u=x(a,s,r);if("HTML"!==a.nodeName||P(s))i=u;else{var f=O(e.ownerDocument),d=f.height,h=f.width;i.top+=u.top-u.marginTop,i.bottom=d+u.top,i.left+=u.left-u.marginLeft,i.right=h+u.left}}var v="number"==typeof(n=n||0);return i.left+=v?n:n.left||0,i.top+=v?n:n.top||0,i.right-=v?n:n.right||0,i.bottom-=v?n:n.bottom||0,i}function L(e){return e.width*e.height}function D(e,t,n,o,r){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf("auto"))return e;var s=A(n,o,i,r),a={top:{width:s.width,height:t.top-s.top},right:{width:s.right-t.right,height:s.height},bottom:{width:s.width,height:s.bottom-t.bottom},left:{width:t.left-s.left,height:s.height}},u=Object.keys(a).map((function(e){return T({key:e},a[e],{area:L(a[e])})})).sort((function(e,t){return t.area-e.area})),p=u.filter((function(e){var t=e.width,o=e.height;return t>=n.clientWidth&&o>=n.clientHeight})),c=p.length>0?p[0].key:u[0].key,l=e.split("-")[1];return c+(l?"-"+l:"")}function I(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=o?N(t):g(t,l(n));return x(n,r,o)}function z(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),o=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+o,height:e.offsetHeight+n}}function H(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,(function(e){return t[e]}))}function M(e,t,n){n=n.split("-")[0];var o=z(e),r={width:o.width,height:o.height},i=-1!==["right","left"].indexOf(n),s=i?"top":"left",a=i?"left":"top",u=i?"height":"width",p=i?"width":"height";return r[s]=t[s]+t[u]/2-o[u]/2,r[a]=n===a?t[a]-o[p]:t[H(a)],r}function B(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function F(e,t,n){return(void 0===n?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex((function(e){return e[t]===n}));var o=B(e,(function(e){return e[t]===n}));return e.indexOf(o)}(e,"name",n))).forEach((function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var n=e.function||e.fn;e.enabled&&a(n)&&(t.offsets.popper=$(t.offsets.popper),t.offsets.reference=$(t.offsets.reference),t=n(t,e))})),t}function R(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=I(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=D(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=M(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=F(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function V(e,t){return e.some((function(e){var n=e.name;return e.enabled&&n===t}))}function W(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),o=0;o<t.length;o++){var r=t[o],i=r?""+r+n:e;if(void 0!==document.body.style[i])return i}return null}function U(){return this.state.isDestroyed=!0,V(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[W("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function q(e){var t=e.ownerDocument;return t?t.defaultView:window}function G(e,t,n,o){var r="BODY"===e.nodeName,i=r?e.ownerDocument.defaultView:e;i.addEventListener(t,n,{passive:!0}),r||G(c(i.parentNode),t,n,o),o.push(i)}function Y(e,t,n,o){n.updateBound=o,q(e).addEventListener("resize",n.updateBound,{passive:!0});var r=c(e);return G(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function X(){this.state.eventsEnabled||(this.state=Y(this.reference,this.options,this.state,this.scheduleUpdate))}function J(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=function(e,t){return q(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach((function(e){e.removeEventListener("scroll",t.updateBound)})),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}(this.reference,this.state))}function K(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function Q(e,t){Object.keys(t).forEach((function(n){var o="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&K(t[n])&&(o="px"),e.style[n]=t[n]+o}))}var Z=r&&/Firefox/i.test(navigator.userAgent);function ee(e,t,n){var o=B(e,(function(e){return e.name===t})),r=!!o&&e.some((function(e){return e.name===n&&e.enabled&&e.order<o.order}));if(!r){var i="`"+t+"`",s="`"+n+"`";console.warn(s+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")}return r}var te=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],ne=te.slice(3);function oe(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=ne.indexOf(e),o=ne.slice(n+1).concat(ne.slice(0,n));return t?o.reverse():o}var re="flip",ie="clockwise",se="counterclockwise";function ae(e,t,n,o){var r=[0,0],i=-1!==["right","left"].indexOf(o),s=e.split(/(\+|\-)/).map((function(e){return e.trim()})),a=s.indexOf(B(s,(function(e){return-1!==e.search(/,|\s/)})));s[a]&&-1===s[a].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var u=/\s*,\s*|\s+/,p=-1!==a?[s.slice(0,a).concat([s[a].split(u)[0]]),[s[a].split(u)[1]].concat(s.slice(a+1))]:[s];return(p=p.map((function(e,o){var r=(1===o?!i:i)?"height":"width",s=!1;return e.reduce((function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,s=!0,e):s?(e[e.length-1]+=t,s=!1,e):e.concat(t)}),[]).map((function(e){return function(e,t,n,o){var r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),i=+r[1],s=r[2];if(!i)return e;if(0===s.indexOf("%")){var a=void 0;switch(s){case"%p":a=n;break;case"%":case"%r":default:a=o}return $(a)[t]/100*i}if("vh"===s||"vw"===s)return("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*i;return i}(e,r,t,n)}))}))).forEach((function(e,t){e.forEach((function(n,o){K(n)&&(r[t]+=n*("-"===e[o-1]?-1:1))}))})),r}var ue={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,n=t.split("-")[0],o=t.split("-")[1];if(o){var r=e.offsets,i=r.reference,s=r.popper,a=-1!==["bottom","top"].indexOf(n),u=a?"left":"top",p=a?"width":"height",c={start:C({},u,i[u]),end:C({},u,i[u]+i[p]-s[p])};e.offsets.popper=T({},s,c[o])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,o=e.placement,r=e.offsets,i=r.popper,s=r.reference,a=o.split("-")[0],u=void 0;return u=K(+n)?[+n,0]:ae(n,i,s,a),"left"===a?(i.top+=u[0],i.left-=u[1]):"right"===a?(i.top+=u[0],i.left+=u[1]):"top"===a?(i.left+=u[0],i.top-=u[1]):"bottom"===a&&(i.left+=u[0],i.top+=u[1]),e.popper=i,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var n=t.boundariesElement||v(e.instance.popper);e.instance.reference===n&&(n=v(n));var o=W("transform"),r=e.instance.popper.style,i=r.top,s=r.left,a=r[o];r.top="",r.left="",r[o]="";var u=A(e.instance.popper,e.instance.reference,t.padding,n,e.positionFixed);r.top=i,r.left=s,r[o]=a,t.boundaries=u;var p=t.priority,c=e.offsets.popper,l={primary:function(e){var n=c[e];return c[e]<u[e]&&!t.escapeWithReference&&(n=Math.max(c[e],u[e])),C({},e,n)},secondary:function(e){var n="right"===e?"left":"top",o=c[n];return c[e]>u[e]&&!t.escapeWithReference&&(o=Math.min(c[n],u[e]-("right"===e?c.width:c.height))),C({},n,o)}};return p.forEach((function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";c=T({},c,l[t](e))})),e.offsets.popper=c,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,o=t.reference,r=e.placement.split("-")[0],i=Math.floor,s=-1!==["top","bottom"].indexOf(r),a=s?"right":"bottom",u=s?"left":"top",p=s?"width":"height";return n[a]<i(o[u])&&(e.offsets.popper[u]=i(o[u])-n[p]),n[u]>i(o[a])&&(e.offsets.popper[u]=i(o[a])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var n;if(!ee(e.instance.modifiers,"arrow","keepTogether"))return e;var o=t.element;if("string"==typeof o){if(!(o=e.instance.popper.querySelector(o)))return e}else if(!e.instance.popper.contains(o))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var r=e.placement.split("-")[0],i=e.offsets,s=i.popper,a=i.reference,p=-1!==["left","right"].indexOf(r),c=p?"height":"width",l=p?"Top":"Left",f=l.toLowerCase(),d=p?"left":"top",h=p?"bottom":"right",v=z(o)[c];a[h]-v<s[f]&&(e.offsets.popper[f]-=s[f]-(a[h]-v)),a[f]+v>s[h]&&(e.offsets.popper[f]+=a[f]+v-s[h]),e.offsets.popper=$(e.offsets.popper);var m=a[f]+a[c]/2-v/2,g=u(e.instance.popper),b=parseFloat(g["margin"+l]),y=parseFloat(g["border"+l+"Width"]),_=m-e.offsets.popper[f]-b-y;return _=Math.max(Math.min(s[c]-v,_),0),e.arrowElement=o,e.offsets.arrow=(C(n={},f,Math.round(_)),C(n,d,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(e,t){if(V(e.instance.modifiers,"inner"))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var n=A(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),o=e.placement.split("-")[0],r=H(o),i=e.placement.split("-")[1]||"",s=[];switch(t.behavior){case re:s=[o,r];break;case ie:s=oe(o);break;case se:s=oe(o,!0);break;default:s=t.behavior}return s.forEach((function(a,u){if(o!==a||s.length===u+1)return e;o=e.placement.split("-")[0],r=H(o);var p=e.offsets.popper,c=e.offsets.reference,l=Math.floor,f="left"===o&&l(p.right)>l(c.left)||"right"===o&&l(p.left)<l(c.right)||"top"===o&&l(p.bottom)>l(c.top)||"bottom"===o&&l(p.top)<l(c.bottom),d=l(p.left)<l(n.left),h=l(p.right)>l(n.right),v=l(p.top)<l(n.top),m=l(p.bottom)>l(n.bottom),g="left"===o&&d||"right"===o&&h||"top"===o&&v||"bottom"===o&&m,b=-1!==["top","bottom"].indexOf(o),y=!!t.flipVariations&&(b&&"start"===i&&d||b&&"end"===i&&h||!b&&"start"===i&&v||!b&&"end"===i&&m),_=!!t.flipVariationsByContent&&(b&&"start"===i&&h||b&&"end"===i&&d||!b&&"start"===i&&m||!b&&"end"===i&&v),w=y||_;(f||g||w)&&(e.flipped=!0,(f||g)&&(o=s[u+1]),w&&(i=function(e){return"end"===e?"start":"start"===e?"end":e}(i)),e.placement=o+(i?"-"+i:""),e.offsets.popper=T({},e.offsets.popper,M(e.instance.popper,e.offsets.reference,e.placement)),e=F(e.instance.modifiers,e,"flip"))})),e},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],o=e.offsets,r=o.popper,i=o.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return r[s?"left":"top"]=i[n]-(a?r[s?"width":"height"]:0),e.placement=H(t),e.offsets.popper=$(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(!ee(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=B(e.instance.modifiers,(function(e){return"preventOverflow"===e.name})).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,o=t.y,r=e.offsets.popper,i=B(e.instance.modifiers,(function(e){return"applyStyle"===e.name})).gpuAcceleration;void 0!==i&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s=void 0!==i?i:t.gpuAcceleration,a=v(e.instance.popper),u=S(a),p={position:r.position},c=function(e,t){var n=e.offsets,o=n.popper,r=n.reference,i=Math.round,s=Math.floor,a=function(e){return e},u=i(r.width),p=i(o.width),c=-1!==["left","right"].indexOf(e.placement),l=-1!==e.placement.indexOf("-"),f=t?c||l||u%2==p%2?i:s:a,d=t?i:a;return{left:f(u%2==1&&p%2==1&&!l&&t?o.left-1:o.left),top:d(o.top),bottom:d(o.bottom),right:f(o.right)}}(e,window.devicePixelRatio<2||!Z),l="bottom"===n?"top":"bottom",f="right"===o?"left":"right",d=W("transform"),h=void 0,m=void 0;if(m="bottom"===l?"HTML"===a.nodeName?-a.clientHeight+c.bottom:-u.height+c.bottom:c.top,h="right"===f?"HTML"===a.nodeName?-a.clientWidth+c.right:-u.width+c.right:c.left,s&&d)p[d]="translate3d("+h+"px, "+m+"px, 0)",p[l]=0,p[f]=0,p.willChange="transform";else{var g="bottom"===l?-1:1,b="right"===f?-1:1;p[l]=m*g,p[f]=h*b,p.willChange=l+", "+f}var y={"x-placement":e.placement};return e.attributes=T({},y,e.attributes),e.styles=T({},p,e.styles),e.arrowStyles=T({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,n;return Q(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach((function(e){!1!==n[e]?t.setAttribute(e,n[e]):t.removeAttribute(e)})),e.arrowElement&&Object.keys(e.arrowStyles).length&&Q(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,n,o,r){var i=I(r,t,e,n.positionFixed),s=D(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",s),Q(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},pe=function(){function e(t,n){var o=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};E(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(o.update)},this.update=s(this.update.bind(this)),this.options=T({},e.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(T({},e.Defaults.modifiers,r.modifiers)).forEach((function(t){o.options.modifiers[t]=T({},e.Defaults.modifiers[t]||{},r.modifiers?r.modifiers[t]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(e){return T({name:e},o.options.modifiers[e])})).sort((function(e,t){return e.order-t.order})),this.modifiers.forEach((function(e){e.enabled&&a(e.onLoad)&&e.onLoad(o.reference,o.popper,o.options,e,o.state)})),this.update();var i=this.options.eventsEnabled;i&&this.enableEventListeners(),this.state.eventsEnabled=i}return j(e,[{key:"update",value:function(){return R.call(this)}},{key:"destroy",value:function(){return U.call(this)}},{key:"enableEventListeners",value:function(){return X.call(this)}},{key:"disableEventListeners",value:function(){return J.call(this)}}]),e}();pe.Utils=("undefined"!=typeof window?window:global).PopperUtils,pe.placements=te,pe.Defaults=ue;var ce=function(){};function le(e){return"string"==typeof e&&(e=e.split(" ")),e}function fe(e,t){var n,o=le(t);n=e.className instanceof ce?le(e.className.baseVal):le(e.className),o.forEach((function(e){-1===n.indexOf(e)&&n.push(e)})),e instanceof SVGElement?e.setAttribute("class",n.join(" ")):e.className=n.join(" ")}function de(e,t){var n,o=le(t);n=e.className instanceof ce?le(e.className.baseVal):le(e.className),o.forEach((function(e){var t=n.indexOf(e);-1!==t&&n.splice(t,1)})),e instanceof SVGElement?e.setAttribute("class",n.join(" ")):e.className=n.join(" ")}"undefined"!=typeof window&&(ce=window.SVGAnimatedString);var he=!1;if("undefined"!=typeof window){he=!1;try{var ve=Object.defineProperty({},"passive",{get:function(){he=!0}});window.addEventListener("test",null,ve)}catch(e){}}var me=function(){this.__data__=[],this.size=0};var ge=function(e,t){return e===t||e!=e&&t!=t};var be=function(e,t){for(var n=e.length;n--;)if(ge(e[n][0],t))return n;return-1},ye=Array.prototype.splice;var _e=function(e){var t=this.__data__,n=be(t,e);return!(n<0)&&(n==t.length-1?t.pop():ye.call(t,n,1),--this.size,!0)};var we=function(e){var t=this.__data__,n=be(t,e);return n<0?void 0:t[n][1]};var Oe=function(e){return be(this.__data__,e)>-1};var Ee=function(e,t){var n=this.__data__,o=be(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this};function je(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}je.prototype.clear=me,je.prototype.delete=_e,je.prototype.get=we,je.prototype.has=Oe,je.prototype.set=Ee;var Ce=je;var Te=function(){this.__data__=new Ce,this.size=0};var $e=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n};var Se=function(e){return this.__data__.get(e)};var xe=function(e){return this.__data__.has(e)},ke="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Pe(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var Ne="object"==typeof ke&&ke&&ke.Object===Object&&ke,Ae="object"==typeof self&&self&&self.Object===Object&&self,Le=Ne||Ae||Function("return this")(),De=Le.Symbol,Ie=Object.prototype,ze=Ie.hasOwnProperty,He=Ie.toString,Me=De?De.toStringTag:void 0;var Be=function(e){var t=ze.call(e,Me),n=e[Me];try{e[Me]=void 0;var o=!0}catch(e){}var r=He.call(e);return o&&(t?e[Me]=n:delete e[Me]),r},Fe=Object.prototype.toString;var Re=function(e){return Fe.call(e)},Ve=De?De.toStringTag:void 0;var We=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Ve&&Ve in Object(e)?Be(e):Re(e)};var Ue=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};var qe,Ge=function(e){if(!Ue(e))return!1;var t=We(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},Ye=Le["__core-js_shared__"],Xe=(qe=/[^.]+$/.exec(Ye&&Ye.keys&&Ye.keys.IE_PROTO||""))?"Symbol(src)_1."+qe:"";var Je=function(e){return!!Xe&&Xe in e},Ke=Function.prototype.toString;var Qe=function(e){if(null!=e){try{return Ke.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Ze=/^\[object .+?Constructor\]$/,et=Function.prototype,tt=Object.prototype,nt=et.toString,ot=tt.hasOwnProperty,rt=RegExp("^"+nt.call(ot).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var it=function(e){return!(!Ue(e)||Je(e))&&(Ge(e)?rt:Ze).test(Qe(e))};var st=function(e,t){return null==e?void 0:e[t]};var at=function(e,t){var n=st(e,t);return it(n)?n:void 0},ut=at(Le,"Map"),pt=at(Object,"create");var ct=function(){this.__data__=pt?pt(null):{},this.size=0};var lt=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ft=Object.prototype.hasOwnProperty;var dt=function(e){var t=this.__data__;if(pt){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return ft.call(t,e)?t[e]:void 0},ht=Object.prototype.hasOwnProperty;var vt=function(e){var t=this.__data__;return pt?void 0!==t[e]:ht.call(t,e)};var mt=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=pt&&void 0===t?"__lodash_hash_undefined__":t,this};function gt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}gt.prototype.clear=ct,gt.prototype.delete=lt,gt.prototype.get=dt,gt.prototype.has=vt,gt.prototype.set=mt;var bt=gt;var yt=function(){this.size=0,this.__data__={hash:new bt,map:new(ut||Ce),string:new bt}};var _t=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var wt=function(e,t){var n=e.__data__;return _t(t)?n["string"==typeof t?"string":"hash"]:n.map};var Ot=function(e){var t=wt(this,e).delete(e);return this.size-=t?1:0,t};var Et=function(e){return wt(this,e).get(e)};var jt=function(e){return wt(this,e).has(e)};var Ct=function(e,t){var n=wt(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this};function Tt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}Tt.prototype.clear=yt,Tt.prototype.delete=Ot,Tt.prototype.get=Et,Tt.prototype.has=jt,Tt.prototype.set=Ct;var $t=Tt;var St=function(e,t){var n=this.__data__;if(n instanceof Ce){var o=n.__data__;if(!ut||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new $t(o)}return n.set(e,t),this.size=n.size,this};function xt(e){var t=this.__data__=new Ce(e);this.size=t.size}xt.prototype.clear=Te,xt.prototype.delete=$e,xt.prototype.get=Se,xt.prototype.has=xe,xt.prototype.set=St;var kt=xt;var Pt=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this};var Nt=function(e){return this.__data__.has(e)};function At(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new $t;++t<n;)this.add(e[t])}At.prototype.add=At.prototype.push=Pt,At.prototype.has=Nt;var Lt=At;var Dt=function(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1};var It=function(e,t){return e.has(t)};var zt=function(e,t,n,o,r,i){var s=1&n,a=e.length,u=t.length;if(a!=u&&!(s&&u>a))return!1;var p=i.get(e),c=i.get(t);if(p&&c)return p==t&&c==e;var l=-1,f=!0,d=2&n?new Lt:void 0;for(i.set(e,t),i.set(t,e);++l<a;){var h=e[l],v=t[l];if(o)var m=s?o(v,h,l,t,e,i):o(h,v,l,e,t,i);if(void 0!==m){if(m)continue;f=!1;break}if(d){if(!Dt(t,(function(e,t){if(!It(d,t)&&(h===e||r(h,e,n,o,i)))return d.push(t)}))){f=!1;break}}else if(h!==v&&!r(h,v,n,o,i)){f=!1;break}}return i.delete(e),i.delete(t),f},Ht=Le.Uint8Array;var Mt=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,o){n[++t]=[o,e]})),n};var Bt=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n},Ft=De?De.prototype:void 0,Rt=Ft?Ft.valueOf:void 0;var Vt=function(e,t,n,o,r,i,s){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!i(new Ht(e),new Ht(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return ge(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var a=Mt;case"[object Set]":var u=1&o;if(a||(a=Bt),e.size!=t.size&&!u)return!1;var p=s.get(e);if(p)return p==t;o|=2,s.set(e,t);var c=zt(a(e),a(t),o,r,i,s);return s.delete(e),c;case"[object Symbol]":if(Rt)return Rt.call(e)==Rt.call(t)}return!1};var Wt=function(e,t){for(var n=-1,o=t.length,r=e.length;++n<o;)e[r+n]=t[n];return e},Ut=Array.isArray;var qt=function(e,t,n){var o=t(e);return Ut(e)?o:Wt(o,n(e))};var Gt=function(e,t){for(var n=-1,o=null==e?0:e.length,r=0,i=[];++n<o;){var s=e[n];t(s,n,e)&&(i[r++]=s)}return i};var Yt=function(){return[]},Xt=Object.prototype.propertyIsEnumerable,Jt=Object.getOwnPropertySymbols,Kt=Jt?function(e){return null==e?[]:(e=Object(e),Gt(Jt(e),(function(t){return Xt.call(e,t)})))}:Yt;var Qt=function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o};var Zt=function(e){return null!=e&&"object"==typeof e};var en=function(e){return Zt(e)&&"[object Arguments]"==We(e)},tn=Object.prototype,nn=tn.hasOwnProperty,on=tn.propertyIsEnumerable,rn=en(function(){return arguments}())?en:function(e){return Zt(e)&&nn.call(e,"callee")&&!on.call(e,"callee")};var sn=function(){return!1},an=Pe((function(e,t){var n=t&&!t.nodeType&&t,o=n&&e&&!e.nodeType&&e,r=o&&o.exports===n?Le.Buffer:void 0,i=(r?r.isBuffer:void 0)||sn;e.exports=i})),un=/^(?:0|[1-9]\d*)$/;var pn=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&un.test(e))&&e>-1&&e%1==0&&e<t};var cn=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},ln={};ln["[object Float32Array]"]=ln["[object Float64Array]"]=ln["[object Int8Array]"]=ln["[object Int16Array]"]=ln["[object Int32Array]"]=ln["[object Uint8Array]"]=ln["[object Uint8ClampedArray]"]=ln["[object Uint16Array]"]=ln["[object Uint32Array]"]=!0,ln["[object Arguments]"]=ln["[object Array]"]=ln["[object ArrayBuffer]"]=ln["[object Boolean]"]=ln["[object DataView]"]=ln["[object Date]"]=ln["[object Error]"]=ln["[object Function]"]=ln["[object Map]"]=ln["[object Number]"]=ln["[object Object]"]=ln["[object RegExp]"]=ln["[object Set]"]=ln["[object String]"]=ln["[object WeakMap]"]=!1;var fn=function(e){return Zt(e)&&cn(e.length)&&!!ln[We(e)]};var dn=function(e){return function(t){return e(t)}},hn=Pe((function(e,t){var n=t&&!t.nodeType&&t,o=n&&e&&!e.nodeType&&e,r=o&&o.exports===n&&Ne.process,i=function(){try{var e=o&&o.require&&o.require("util").types;return e||r&&r.binding&&r.binding("util")}catch(e){}}();e.exports=i})),vn=hn&&hn.isTypedArray,mn=vn?dn(vn):fn,gn=Object.prototype.hasOwnProperty;var bn=function(e,t){var n=Ut(e),o=!n&&rn(e),r=!n&&!o&&an(e),i=!n&&!o&&!r&&mn(e),s=n||o||r||i,a=s?Qt(e.length,String):[],u=a.length;for(var p in e)!t&&!gn.call(e,p)||s&&("length"==p||r&&("offset"==p||"parent"==p)||i&&("buffer"==p||"byteLength"==p||"byteOffset"==p)||pn(p,u))||a.push(p);return a},yn=Object.prototype;var _n=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||yn)};var wn=function(e,t){return function(n){return e(t(n))}},On=wn(Object.keys,Object),En=Object.prototype.hasOwnProperty;var jn=function(e){if(!_n(e))return On(e);var t=[];for(var n in Object(e))En.call(e,n)&&"constructor"!=n&&t.push(n);return t};var Cn=function(e){return null!=e&&cn(e.length)&&!Ge(e)};var Tn=function(e){return Cn(e)?bn(e):jn(e)};var $n=function(e){return qt(e,Tn,Kt)},Sn=Object.prototype.hasOwnProperty;var xn=function(e,t,n,o,r,i){var s=1&n,a=$n(e),u=a.length;if(u!=$n(t).length&&!s)return!1;for(var p=u;p--;){var c=a[p];if(!(s?c in t:Sn.call(t,c)))return!1}var l=i.get(e),f=i.get(t);if(l&&f)return l==t&&f==e;var d=!0;i.set(e,t),i.set(t,e);for(var h=s;++p<u;){var v=e[c=a[p]],m=t[c];if(o)var g=s?o(m,v,c,t,e,i):o(v,m,c,e,t,i);if(!(void 0===g?v===m||r(v,m,n,o,i):g)){d=!1;break}h||(h="constructor"==c)}if(d&&!h){var b=e.constructor,y=t.constructor;b==y||!("constructor"in e)||!("constructor"in t)||"function"==typeof b&&b instanceof b&&"function"==typeof y&&y instanceof y||(d=!1)}return i.delete(e),i.delete(t),d},kn=at(Le,"DataView"),Pn=at(Le,"Promise"),Nn=at(Le,"Set"),An=at(Le,"WeakMap"),Ln="[object Map]",Dn="[object Promise]",In="[object Set]",zn="[object WeakMap]",Hn="[object DataView]",Mn=Qe(kn),Bn=Qe(ut),Fn=Qe(Pn),Rn=Qe(Nn),Vn=Qe(An),Wn=We;(kn&&Wn(new kn(new ArrayBuffer(1)))!=Hn||ut&&Wn(new ut)!=Ln||Pn&&Wn(Pn.resolve())!=Dn||Nn&&Wn(new Nn)!=In||An&&Wn(new An)!=zn)&&(Wn=function(e){var t=We(e),n="[object Object]"==t?e.constructor:void 0,o=n?Qe(n):"";if(o)switch(o){case Mn:return Hn;case Bn:return Ln;case Fn:return Dn;case Rn:return In;case Vn:return zn}return t});var Un=Wn,qn="[object Arguments]",Gn="[object Array]",Yn="[object Object]",Xn=Object.prototype.hasOwnProperty;var Jn=function(e,t,n,o,r,i){var s=Ut(e),a=Ut(t),u=s?Gn:Un(e),p=a?Gn:Un(t),c=(u=u==qn?Yn:u)==Yn,l=(p=p==qn?Yn:p)==Yn,f=u==p;if(f&&an(e)){if(!an(t))return!1;s=!0,c=!1}if(f&&!c)return i||(i=new kt),s||mn(e)?zt(e,t,n,o,r,i):Vt(e,t,u,n,o,r,i);if(!(1&n)){var d=c&&Xn.call(e,"__wrapped__"),h=l&&Xn.call(t,"__wrapped__");if(d||h){var v=d?e.value():e,m=h?t.value():t;return i||(i=new kt),r(v,m,n,o,i)}}return!!f&&(i||(i=new kt),xn(e,t,n,o,r,i))};var Kn=function e(t,n,o,r,i){return t===n||(null==t||null==n||!Zt(t)&&!Zt(n)?t!=t&&n!=n:Jn(t,n,o,r,e,i))};var Qn=function(e,t){return Kn(e,t)};function Zn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function eo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Zn(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Zn(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var to={container:!1,delay:0,html:!1,placement:"top",title:"",template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",offset:0},no=[],oo=function(){function e(t,o){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),n(this,"_events",[]),n(this,"_setTooltipNodeEvent",(function(e,t,n,o){var i=e.relatedreference||e.toElement||e.relatedTarget;return!!r._tooltipNode.contains(i)&&(r._tooltipNode.addEventListener(e.type,(function n(i){var s=i.relatedreference||i.toElement||i.relatedTarget;r._tooltipNode.removeEventListener(e.type,n),t.contains(s)||r._scheduleHide(t,o.delay,o,i)})),!0)})),o=eo(eo({},to),o),t.jquery&&(t=t[0]),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.reference=t,this.options=o,this._isOpen=!1,this._init()}var t,r,i;return t=e,(r=[{key:"show",value:function(){this._show(this.reference,this.options)}},{key:"hide",value:function(){this._hide()}},{key:"dispose",value:function(){this._dispose()}},{key:"toggle",value:function(){return this._isOpen?this.hide():this.show()}},{key:"setClasses",value:function(e){this._classes=e}},{key:"setContent",value:function(e){this.options.title=e,this._tooltipNode&&this._setContent(e,this.options)}},{key:"setOptions",value:function(e){var t=!1,n=e&&e.classes||mo.options.defaultClass;Qn(this._classes,n)||(this.setClasses(n),t=!0),e=po(e);var o=!1,r=!1;for(var i in this.options.offset===e.offset&&this.options.placement===e.placement||(o=!0),(this.options.template!==e.template||this.options.trigger!==e.trigger||this.options.container!==e.container||t)&&(r=!0),e)this.options[i]=e[i];if(this._tooltipNode)if(r){var s=this._isOpen;this.dispose(),this._init(),s&&this.show()}else o&&this.popperInstance.update()}},{key:"_init",value:function(){var e="string"==typeof this.options.trigger?this.options.trigger.split(" "):[];this._isDisposed=!1,this._enableDocumentTouch=-1===e.indexOf("manual"),e=e.filter((function(e){return-1!==["click","hover","focus"].indexOf(e)})),this._setEventListeners(this.reference,e,this.options),this.$_originalTitle=this.reference.getAttribute("title"),this.reference.removeAttribute("title"),this.reference.setAttribute("data-original-title",this.$_originalTitle)}},{key:"_create",value:function(e,t){var n=this,o=window.document.createElement("div");o.innerHTML=t.trim();var r=o.childNodes[0];return r.id=this.options.ariaId||"tooltip_".concat(Math.random().toString(36).substr(2,10)),r.setAttribute("aria-hidden","true"),this.options.autoHide&&-1!==this.options.trigger.indexOf("hover")&&(r.addEventListener("mouseenter",(function(t){return n._scheduleHide(e,n.options.delay,n.options,t)})),r.addEventListener("click",(function(t){return n._scheduleHide(e,n.options.delay,n.options,t)}))),r}},{key:"_setContent",value:function(e,t){var n=this;this.asyncContent=!1,this._applyContent(e,t).then((function(){n.popperInstance&&n.popperInstance.update()}))}},{key:"_applyContent",value:function(e,t){var n=this;return new Promise((function(o,r){var i=t.html,s=n._tooltipNode;if(s){var a=s.querySelector(n.options.innerSelector);if(1===e.nodeType){if(i){for(;a.firstChild;)a.removeChild(a.firstChild);a.appendChild(e)}}else{if("function"==typeof e){var u=e();return void(u&&"function"==typeof u.then?(n.asyncContent=!0,t.loadingClass&&fe(s,t.loadingClass),t.loadingContent&&n._applyContent(t.loadingContent,t),u.then((function(e){return t.loadingClass&&de(s,t.loadingClass),n._applyContent(e,t)})).then(o).catch(r)):n._applyContent(u,t).then(o).catch(r))}i?a.innerHTML=e:a.innerText=e}o()}}))}},{key:"_show",value:function(e,t){if(!t||"string"!=typeof t.container||document.querySelector(t.container)){clearTimeout(this._disposeTimer),delete(t=Object.assign({},t)).offset;var n=!0;this._tooltipNode&&(fe(this._tooltipNode,this._classes),n=!1);var o=this._ensureShown(e,t);return n&&this._tooltipNode&&fe(this._tooltipNode,this._classes),fe(e,["v-tooltip-open"]),o}}},{key:"_ensureShown",value:function(e,t){var n=this;if(this._isOpen)return this;if(this._isOpen=!0,no.push(this),this._tooltipNode)return this._tooltipNode.style.display="",this._tooltipNode.setAttribute("aria-hidden","false"),this.popperInstance.enableEventListeners(),this.popperInstance.update(),this.asyncContent&&this._setContent(t.title,t),this;var o=e.getAttribute("title")||t.title;if(!o)return this;var r=this._create(e,t.template);this._tooltipNode=r,e.setAttribute("aria-describedby",r.id);var i=this._findContainer(t.container,e);this._append(r,i);var s=eo(eo({},t.popperOptions),{},{placement:t.placement});return s.modifiers=eo(eo({},s.modifiers),{},{arrow:{element:this.options.arrowSelector}}),t.boundariesElement&&(s.modifiers.preventOverflow={boundariesElement:t.boundariesElement}),this.popperInstance=new pe(e,r,s),this._setContent(o,t),requestAnimationFrame((function(){!n._isDisposed&&n.popperInstance?(n.popperInstance.update(),requestAnimationFrame((function(){n._isDisposed?n.dispose():n._isOpen&&r.setAttribute("aria-hidden","false")}))):n.dispose()})),this}},{key:"_noLongerOpen",value:function(){var e=no.indexOf(this);-1!==e&&no.splice(e,1)}},{key:"_hide",value:function(){var e=this;if(!this._isOpen)return this;this._isOpen=!1,this._noLongerOpen(),this._tooltipNode.style.display="none",this._tooltipNode.setAttribute("aria-hidden","true"),this.popperInstance&&this.popperInstance.disableEventListeners(),clearTimeout(this._disposeTimer);var t=mo.options.disposeTimeout;return null!==t&&(this._disposeTimer=setTimeout((function(){e._tooltipNode&&(e._tooltipNode.removeEventListener("mouseenter",e.hide),e._tooltipNode.removeEventListener("click",e.hide),e._removeTooltipNode())}),t)),de(this.reference,["v-tooltip-open"]),this}},{key:"_removeTooltipNode",value:function(){if(this._tooltipNode){var e=this._tooltipNode.parentNode;e&&(e.removeChild(this._tooltipNode),this.reference.removeAttribute("aria-describedby")),this._tooltipNode=null}}},{key:"_dispose",value:function(){var e=this;return this._isDisposed=!0,this.reference.removeAttribute("data-original-title"),this.$_originalTitle&&this.reference.setAttribute("title",this.$_originalTitle),this._events.forEach((function(t){var n=t.func,o=t.event;e.reference.removeEventListener(o,n)})),this._events=[],this._tooltipNode?(this._hide(),this._tooltipNode.removeEventListener("mouseenter",this.hide),this._tooltipNode.removeEventListener("click",this.hide),this.popperInstance.destroy(),this.popperInstance.options.removeOnDestroy||this._removeTooltipNode()):this._noLongerOpen(),this}},{key:"_findContainer",value:function(e,t){return"string"==typeof e?e=window.document.querySelector(e):!1===e&&(e=t.parentNode),e}},{key:"_append",value:function(e,t){t.appendChild(e)}},{key:"_setEventListeners",value:function(e,t,n){var o=this,r=[],i=[];t.forEach((function(e){switch(e){case"hover":r.push("mouseenter"),i.push("mouseleave"),o.options.hideOnTargetClick&&i.push("click");break;case"focus":r.push("focus"),i.push("blur"),o.options.hideOnTargetClick&&i.push("click");break;case"click":r.push("click"),i.push("click")}})),r.forEach((function(t){var r=function(t){!0!==o._isOpen&&(t.usedByTooltip=!0,o._scheduleShow(e,n.delay,n,t))};o._events.push({event:t,func:r}),e.addEventListener(t,r)})),i.forEach((function(t){var r=function(t){!0!==t.usedByTooltip&&o._scheduleHide(e,n.delay,n,t)};o._events.push({event:t,func:r}),e.addEventListener(t,r)}))}},{key:"_onDocumentTouch",value:function(e){this._enableDocumentTouch&&this._scheduleHide(this.reference,this.options.delay,this.options,e)}},{key:"_scheduleShow",value:function(e,t,n){var o=this,r=t&&t.show||t||0;clearTimeout(this._scheduleTimer),this._scheduleTimer=window.setTimeout((function(){return o._show(e,n)}),r)}},{key:"_scheduleHide",value:function(e,t,n,o){var r=this,i=t&&t.hide||t||0;clearTimeout(this._scheduleTimer),this._scheduleTimer=window.setTimeout((function(){if(!1!==r._isOpen&&r._tooltipNode.ownerDocument.body.contains(r._tooltipNode)){if("mouseleave"===o.type&&r._setTooltipNodeEvent(o,e,t,n))return;r._hide(e,n)}}),i)}}])&&o(t.prototype,r),i&&o(t,i),e}();function ro(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function io(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ro(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ro(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}"undefined"!=typeof document&&document.addEventListener("touchstart",(function(e){for(var t=0;t<no.length;t++)no[t]._onDocumentTouch(e)}),!he||{passive:!0,capture:!0});var so={enabled:!0},ao=["top","top-start","top-end","right","right-start","right-end","bottom","bottom-start","bottom-end","left","left-start","left-end"],uo={defaultPlacement:"top",defaultClass:"vue-tooltip-theme",defaultTargetClass:"has-tooltip",defaultHtml:!0,defaultTemplate:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',defaultArrowSelector:".tooltip-arrow, .tooltip__arrow",defaultInnerSelector:".tooltip-inner, .tooltip__inner",defaultDelay:0,defaultTrigger:"hover focus",defaultOffset:0,defaultContainer:"body",defaultBoundariesElement:void 0,defaultPopperOptions:{},defaultLoadingClass:"tooltip-loading",defaultLoadingContent:"...",autoHide:!0,defaultHideOnTargetClick:!0,disposeTimeout:5e3,popover:{defaultPlacement:"bottom",defaultClass:"vue-popover-theme",defaultBaseClass:"tooltip popover",defaultWrapperClass:"wrapper",defaultInnerClass:"tooltip-inner popover-inner",defaultArrowClass:"tooltip-arrow popover-arrow",defaultOpenClass:"open",defaultDelay:0,defaultTrigger:"click",defaultOffset:0,defaultContainer:"body",defaultBoundariesElement:void 0,defaultPopperOptions:{},defaultAutoHide:!0,defaultHandleResize:!0}};function po(e){var n={placement:void 0!==e.placement?e.placement:mo.options.defaultPlacement,delay:void 0!==e.delay?e.delay:mo.options.defaultDelay,html:void 0!==e.html?e.html:mo.options.defaultHtml,template:void 0!==e.template?e.template:mo.options.defaultTemplate,arrowSelector:void 0!==e.arrowSelector?e.arrowSelector:mo.options.defaultArrowSelector,innerSelector:void 0!==e.innerSelector?e.innerSelector:mo.options.defaultInnerSelector,trigger:void 0!==e.trigger?e.trigger:mo.options.defaultTrigger,offset:void 0!==e.offset?e.offset:mo.options.defaultOffset,container:void 0!==e.container?e.container:mo.options.defaultContainer,boundariesElement:void 0!==e.boundariesElement?e.boundariesElement:mo.options.defaultBoundariesElement,autoHide:void 0!==e.autoHide?e.autoHide:mo.options.autoHide,hideOnTargetClick:void 0!==e.hideOnTargetClick?e.hideOnTargetClick:mo.options.defaultHideOnTargetClick,loadingClass:void 0!==e.loadingClass?e.loadingClass:mo.options.defaultLoadingClass,loadingContent:void 0!==e.loadingContent?e.loadingContent:mo.options.defaultLoadingContent,popperOptions:io({},void 0!==e.popperOptions?e.popperOptions:mo.options.defaultPopperOptions)};if(n.offset){var o=t(n.offset),r=n.offset;("number"===o||"string"===o&&-1===r.indexOf(","))&&(r="0, ".concat(r)),n.popperOptions.modifiers||(n.popperOptions.modifiers={}),n.popperOptions.modifiers.offset={offset:r}}return n.trigger&&-1!==n.trigger.indexOf("click")&&(n.hideOnTargetClick=!1),n}function co(e,t){for(var n=e.placement,o=0;o<ao.length;o++){var r=ao[o];t[r]&&(n=r)}return n}function lo(e){var n=t(e);return"string"===n?e:!(!e||"object"!==n)&&e.content}function fo(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=lo(n),i=void 0!==n.classes?n.classes:mo.options.defaultClass,s=io({title:r},po(io(io({},"object"===t(n)?n:{}),{},{placement:co(n,o)}))),a=e._tooltip=new oo(e,s);a.setClasses(i),a._vueEl=e;var u=void 0!==n.targetClasses?n.targetClasses:mo.options.defaultTargetClass;return e._tooltipTargetClasses=u,fe(e,u),a}function ho(e){e._tooltip&&(e._tooltip.dispose(),delete e._tooltip,delete e._tooltipOldShow),e._tooltipTargetClasses&&(de(e,e._tooltipTargetClasses),delete e._tooltipTargetClasses)}function vo(e,t){var n=t.value;t.oldValue;var o,r=t.modifiers,i=lo(n);i&&so.enabled?(e._tooltip?((o=e._tooltip).setContent(i),o.setOptions(io(io({},n),{},{placement:co(n,r)}))):o=fo(e,n,r),void 0!==n.show&&n.show!==e._tooltipOldShow&&(e._tooltipOldShow=n.show,n.show?o.show():o.hide())):ho(e)}var mo={options:uo,bind:vo,update:vo,unbind:function(e){ho(e)}};function go(e){e.addEventListener("click",yo),e.addEventListener("touchstart",_o,!!he&&{passive:!0})}function bo(e){e.removeEventListener("click",yo),e.removeEventListener("touchstart",_o),e.removeEventListener("touchend",wo),e.removeEventListener("touchcancel",Oo)}function yo(e){var t=e.currentTarget;e.closePopover=!t.$_vclosepopover_touch,e.closeAllPopover=t.$_closePopoverModifiers&&!!t.$_closePopoverModifiers.all}function _o(e){if(1===e.changedTouches.length){var t=e.currentTarget;t.$_vclosepopover_touch=!0;var n=e.changedTouches[0];t.$_vclosepopover_touchPoint=n,t.addEventListener("touchend",wo),t.addEventListener("touchcancel",Oo)}}function wo(e){var t=e.currentTarget;if(t.$_vclosepopover_touch=!1,1===e.changedTouches.length){var n=e.changedTouches[0],o=t.$_vclosepopover_touchPoint;e.closePopover=Math.abs(n.screenY-o.screenY)<20&&Math.abs(n.screenX-o.screenX)<20,e.closeAllPopover=t.$_closePopoverModifiers&&!!t.$_closePopoverModifiers.all}}function Oo(e){e.currentTarget.$_vclosepopover_touch=!1}var Eo,jo={bind:function(e,t){var n=t.value,o=t.modifiers;e.$_closePopoverModifiers=o,(void 0===n||n)&&go(e)},update:function(e,t){var n=t.value,o=t.oldValue,r=t.modifiers;e.$_closePopoverModifiers=r,n!==o&&(void 0===n||n?go(e):bo(e))},unbind:function(e){bo(e)}};function Co(){Co.init||(Co.init=!0,Eo=-1!==function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);if(e.indexOf("Trident/")>0){var n=e.indexOf("rv:");return parseInt(e.substring(n+3,e.indexOf(".",n)),10)}var o=e.indexOf("Edge/");return o>0?parseInt(e.substring(o+5,e.indexOf(".",o)),10):-1}())}function To(e,t,n,o,r,i,s,a,u,p){"boolean"!=typeof s&&(u=a,a=s,s=!1);var c,l="function"==typeof n?n.options:n;if(e&&e.render&&(l.render=e.render,l.staticRenderFns=e.staticRenderFns,l._compiled=!0,r&&(l.functional=!0)),o&&(l._scopeId=o),i?(c=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,u(e)),e&&e._registeredComponents&&e._registeredComponents.add(i)},l._ssrRegister=c):t&&(c=s?function(e){t.call(this,p(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,a(e))}),c)if(l.functional){var f=l.render;l.render=function(e,t){return c.call(t),f(e,t)}}else{var d=l.beforeCreate;l.beforeCreate=d?[].concat(d,c):[c]}return n}var $o={name:"ResizeObserver",props:{emitOnMount:{type:Boolean,default:!1},ignoreWidth:{type:Boolean,default:!1},ignoreHeight:{type:Boolean,default:!1}},mounted:function(){var e=this;Co(),this.$nextTick((function(){e._w=e.$el.offsetWidth,e._h=e.$el.offsetHeight,e.emitOnMount&&e.emitSize()}));var t=document.createElement("object");this._resizeObject=t,t.setAttribute("aria-hidden","true"),t.setAttribute("tabindex",-1),t.onload=this.addResizeHandlers,t.type="text/html",Eo&&this.$el.appendChild(t),t.data="about:blank",Eo||this.$el.appendChild(t)},beforeDestroy:function(){this.removeResizeHandlers()},methods:{compareAndNotify:function(){(!this.ignoreWidth&&this._w!==this.$el.offsetWidth||!this.ignoreHeight&&this._h!==this.$el.offsetHeight)&&(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitSize())},emitSize:function(){this.$emit("notify",{width:this._w,height:this._h})},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!Eo&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}},So=function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})};So._withStripped=!0;var xo=To({render:So,staticRenderFns:[]},undefined,$o,"data-v-8859cc6c",false,undefined,!1,void 0,void 0,void 0);var ko={version:"1.0.1",install:function(e){e.component("resize-observer",xo),e.component("ResizeObserver",xo)}},Po=null;function No(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ao(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?No(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):No(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function Lo(e){var t=mo.options.popover[e];return void 0===t?mo.options[e]:t}"undefined"!=typeof window?Po=window.Vue:"undefined"!=typeof global&&(Po=global.Vue),Po&&Po.use(ko);var Do=!1;"undefined"!=typeof window&&"undefined"!=typeof navigator&&(Do=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream);var Io=[],zo=function(){};"undefined"!=typeof window&&(zo=window.Element);var Ho={name:"VPopover",components:{ResizeObserver:xo},props:{open:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},placement:{type:String,default:function(){return Lo("defaultPlacement")}},delay:{type:[String,Number,Object],default:function(){return Lo("defaultDelay")}},offset:{type:[String,Number],default:function(){return Lo("defaultOffset")}},trigger:{type:String,default:function(){return Lo("defaultTrigger")}},container:{type:[String,Object,zo,Boolean],default:function(){return Lo("defaultContainer")}},boundariesElement:{type:[String,zo],default:function(){return Lo("defaultBoundariesElement")}},popperOptions:{type:Object,default:function(){return Lo("defaultPopperOptions")}},popoverClass:{type:[String,Array],default:function(){return Lo("defaultClass")}},popoverBaseClass:{type:[String,Array],default:function(){return mo.options.popover.defaultBaseClass}},popoverInnerClass:{type:[String,Array],default:function(){return mo.options.popover.defaultInnerClass}},popoverWrapperClass:{type:[String,Array],default:function(){return mo.options.popover.defaultWrapperClass}},popoverArrowClass:{type:[String,Array],default:function(){return mo.options.popover.defaultArrowClass}},autoHide:{type:Boolean,default:function(){return mo.options.popover.defaultAutoHide}},handleResize:{type:Boolean,default:function(){return mo.options.popover.defaultHandleResize}},openGroup:{type:String,default:null},openClass:{type:[String,Array],default:function(){return mo.options.popover.defaultOpenClass}},ariaId:{default:null}},data:function(){return{isOpen:!1,id:Math.random().toString(36).substr(2,10)}},computed:{cssClass:function(){return n({},this.openClass,this.isOpen)},popoverId:function(){return"popover_".concat(null!=this.ariaId?this.ariaId:this.id)}},watch:{open:function(e){e?this.show():this.hide()},disabled:function(e,t){e!==t&&(e?this.hide():this.open&&this.show())},container:function(e){if(this.isOpen&&this.popperInstance){var t=this.$refs.popover,n=this.$refs.trigger,o=this.$_findContainer(this.container,n);if(!o)return void console.warn("No container for popover",this);o.appendChild(t),this.popperInstance.scheduleUpdate()}},trigger:function(e){this.$_removeEventListeners(),this.$_addEventListeners()},placement:function(e){var t=this;this.$_updatePopper((function(){t.popperInstance.options.placement=e}))},offset:"$_restartPopper",boundariesElement:"$_restartPopper",popperOptions:{handler:"$_restartPopper",deep:!0}},created:function(){this.$_isDisposed=!1,this.$_mounted=!1,this.$_events=[],this.$_preventOpen=!1},mounted:function(){var e=this.$refs.popover;e.parentNode&&e.parentNode.removeChild(e),this.$_init(),this.open&&this.show()},deactivated:function(){this.hide()},beforeDestroy:function(){this.dispose()},methods:{show:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.event;t.skipDelay;var o=t.force,r=void 0!==o&&o;!r&&this.disabled||(this.$_scheduleShow(n),this.$emit("show")),this.$emit("update:open",!0),this.$_beingShowed=!0,requestAnimationFrame((function(){e.$_beingShowed=!1}))},hide:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.event;e.skipDelay,this.$_scheduleHide(t),this.$emit("hide"),this.$emit("update:open",!1)},dispose:function(){if(this.$_isDisposed=!0,this.$_removeEventListeners(),this.hide({skipDelay:!0}),this.popperInstance&&(this.popperInstance.destroy(),!this.popperInstance.options.removeOnDestroy)){var e=this.$refs.popover;e.parentNode&&e.parentNode.removeChild(e)}this.$_mounted=!1,this.popperInstance=null,this.isOpen=!1,this.$emit("dispose")},$_init:function(){-1===this.trigger.indexOf("manual")&&this.$_addEventListeners()},$_show:function(){var e=this,t=this.$refs.trigger,n=this.$refs.popover;if(clearTimeout(this.$_disposeTimer),!this.isOpen){if(this.popperInstance&&(this.isOpen=!0,this.popperInstance.enableEventListeners(),this.popperInstance.scheduleUpdate()),!this.$_mounted){var o=this.$_findContainer(this.container,t);if(!o)return void console.warn("No container for popover",this);o.appendChild(n),this.$_mounted=!0,this.isOpen=!1,this.popperInstance&&requestAnimationFrame((function(){e.hidden||(e.isOpen=!0)}))}if(!this.popperInstance){var r=Ao(Ao({},this.popperOptions),{},{placement:this.placement});if(r.modifiers=Ao(Ao({},r.modifiers),{},{arrow:Ao(Ao({},r.modifiers&&r.modifiers.arrow),{},{element:this.$refs.arrow})}),this.offset){var i=this.$_getOffset();r.modifiers.offset=Ao(Ao({},r.modifiers&&r.modifiers.offset),{},{offset:i})}this.boundariesElement&&(r.modifiers.preventOverflow=Ao(Ao({},r.modifiers&&r.modifiers.preventOverflow),{},{boundariesElement:this.boundariesElement})),this.popperInstance=new pe(t,n,r),requestAnimationFrame((function(){if(e.hidden)return e.hidden=!1,void e.$_hide();!e.$_isDisposed&&e.popperInstance?(e.popperInstance.scheduleUpdate(),requestAnimationFrame((function(){if(e.hidden)return e.hidden=!1,void e.$_hide();e.$_isDisposed?e.dispose():e.isOpen=!0}))):e.dispose()}))}var s=this.openGroup;if(s)for(var a,u=0;u<Io.length;u++)(a=Io[u]).openGroup!==s&&(a.hide(),a.$emit("close-group"));Io.push(this),this.$emit("apply-show")}},$_hide:function(){var e=this;if(this.isOpen){var t=Io.indexOf(this);-1!==t&&Io.splice(t,1),this.isOpen=!1,this.popperInstance&&this.popperInstance.disableEventListeners(),clearTimeout(this.$_disposeTimer);var n=mo.options.popover.disposeTimeout||mo.options.disposeTimeout;null!==n&&(this.$_disposeTimer=setTimeout((function(){var t=e.$refs.popover;t&&(t.parentNode&&t.parentNode.removeChild(t),e.$_mounted=!1)}),n)),this.$emit("apply-hide")}},$_findContainer:function(e,t){return"string"==typeof e?e=window.document.querySelector(e):!1===e&&(e=t.parentNode),e},$_getOffset:function(){var e=t(this.offset),n=this.offset;return("number"===e||"string"===e&&-1===n.indexOf(","))&&(n="0, ".concat(n)),n},$_addEventListeners:function(){var e=this,t=this.$refs.trigger,n=[],o=[];("string"==typeof this.trigger?this.trigger.split(" ").filter((function(e){return-1!==["click","hover","focus"].indexOf(e)})):[]).forEach((function(e){switch(e){case"hover":n.push("mouseenter"),o.push("mouseleave");break;case"focus":n.push("focus"),o.push("blur");break;case"click":n.push("click"),o.push("click")}})),n.forEach((function(n){var o=function(t){e.isOpen||(t.usedByTooltip=!0,!e.$_preventOpen&&e.show({event:t}),e.hidden=!1)};e.$_events.push({event:n,func:o}),t.addEventListener(n,o)})),o.forEach((function(n){var o=function(t){t.usedByTooltip||(e.hide({event:t}),e.hidden=!0)};e.$_events.push({event:n,func:o}),t.addEventListener(n,o)}))},$_scheduleShow:function(){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(clearTimeout(this.$_scheduleTimer),e)this.$_show();else{var t=parseInt(this.delay&&this.delay.show||this.delay||0);this.$_scheduleTimer=setTimeout(this.$_show.bind(this),t)}},$_scheduleHide:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(clearTimeout(this.$_scheduleTimer),n)this.$_hide();else{var o=parseInt(this.delay&&this.delay.hide||this.delay||0);this.$_scheduleTimer=setTimeout((function(){if(e.isOpen){if(t&&"mouseleave"===t.type)if(e.$_setTooltipNodeEvent(t))return;e.$_hide()}}),o)}},$_setTooltipNodeEvent:function(e){var t=this,n=this.$refs.trigger,o=this.$refs.popover,r=e.relatedreference||e.toElement||e.relatedTarget;return!!o.contains(r)&&(o.addEventListener(e.type,(function r(i){var s=i.relatedreference||i.toElement||i.relatedTarget;o.removeEventListener(e.type,r),n.contains(s)||t.hide({event:i})})),!0)},$_removeEventListeners:function(){var e=this.$refs.trigger;this.$_events.forEach((function(t){var n=t.func,o=t.event;e.removeEventListener(o,n)})),this.$_events=[]},$_updatePopper:function(e){this.popperInstance&&(e(),this.isOpen&&this.popperInstance.scheduleUpdate())},$_restartPopper:function(){if(this.popperInstance){var e=this.isOpen;this.dispose(),this.$_isDisposed=!1,this.$_init(),e&&this.show({skipDelay:!0,force:!0})}},$_handleGlobalClose:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.$_beingShowed||(this.hide({event:e}),e.closePopover?this.$emit("close-directive"):this.$emit("auto-hide"),n&&(this.$_preventOpen=!0,setTimeout((function(){t.$_preventOpen=!1}),300)))},$_handleResize:function(){this.isOpen&&this.popperInstance&&(this.popperInstance.scheduleUpdate(),this.$emit("resize"))}}};function Mo(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=function(n){var o=Io[n];if(o.$refs.popover){var r=o.$refs.popover.contains(e.target);requestAnimationFrame((function(){(e.closeAllPopover||e.closePopover&&r||o.autoHide&&!r)&&o.$_handleGlobalClose(e,t)}))}},o=0;o<Io.length;o++)n(o)}function Bo(e,t,n,o,r,i,s,a,u,p){"boolean"!=typeof s&&(u=a,a=s,s=!1);const c="function"==typeof n?n.options:n;let l;if(e&&e.render&&(c.render=e.render,c.staticRenderFns=e.staticRenderFns,c._compiled=!0,r&&(c.functional=!0)),o&&(c._scopeId=o),i?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,u(e)),e&&e._registeredComponents&&e._registeredComponents.add(i)},c._ssrRegister=l):t&&(l=s?function(e){t.call(this,p(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,a(e))}),l)if(c.functional){const e=c.render;c.render=function(t,n){return l.call(n),e(t,n)}}else{const e=c.beforeCreate;c.beforeCreate=e?[].concat(e,l):[l]}return n}"undefined"!=typeof document&&"undefined"!=typeof window&&(Do?document.addEventListener("touchend",(function(e){Mo(e,!0)}),!he||{passive:!0,capture:!0}):window.addEventListener("click",(function(e){Mo(e)}),!0));var Fo=Bo({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"v-popover",class:e.cssClass},[n("div",{ref:"trigger",staticClass:"trigger",staticStyle:{display:"inline-block"},attrs:{"aria-describedby":e.isOpen?e.popoverId:void 0,tabindex:-1!==e.trigger.indexOf("focus")?0:void 0}},[e._t("default")],2),e._v(" "),n("div",{ref:"popover",class:[e.popoverBaseClass,e.popoverClass,e.cssClass],style:{visibility:e.isOpen?"visible":"hidden"},attrs:{id:e.popoverId,"aria-hidden":e.isOpen?"false":"true",tabindex:e.autoHide?0:void 0},on:{keyup:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"esc",27,t.key,["Esc","Escape"]))return null;e.autoHide&&e.hide()}}},[n("div",{class:e.popoverWrapperClass},[n("div",{ref:"inner",class:e.popoverInnerClass,staticStyle:{position:"relative"}},[n("div",[e._t("popover",null,{isOpen:e.isOpen})],2),e._v(" "),e.handleResize?n("ResizeObserver",{on:{notify:e.$_handleResize}}):e._e()],1),e._v(" "),n("div",{ref:"arrow",class:e.popoverArrowClass})])])])},staticRenderFns:[]},undefined,Ho,undefined,false,undefined,!1,void 0,void 0,void 0),Ro=function(){try{var e=at(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var Vo=function(e,t,n){"__proto__"==t&&Ro?Ro(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n};var Wo=function(e,t,n){(void 0!==n&&!ge(e[t],n)||void 0===n&&!(t in e))&&Vo(e,t,n)};var Uo=function(e){return function(t,n,o){for(var r=-1,i=Object(t),s=o(t),a=s.length;a--;){var u=s[e?a:++r];if(!1===n(i[u],u,i))break}return t}}(),qo=Pe((function(e,t){var n=t&&!t.nodeType&&t,o=n&&e&&!e.nodeType&&e,r=o&&o.exports===n?Le.Buffer:void 0,i=r?r.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,o=i?i(n):new e.constructor(n);return e.copy(o),o}}));var Go=function(e){var t=new e.constructor(e.byteLength);return new Ht(t).set(new Ht(e)),t};var Yo=function(e,t){var n=t?Go(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)};var Xo=function(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t},Jo=Object.create,Ko=function(){function e(){}return function(t){if(!Ue(t))return{};if(Jo)return Jo(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}(),Qo=wn(Object.getPrototypeOf,Object);var Zo=function(e){return"function"!=typeof e.constructor||_n(e)?{}:Ko(Qo(e))};var er=function(e){return Zt(e)&&Cn(e)},tr=Function.prototype,nr=Object.prototype,or=tr.toString,rr=nr.hasOwnProperty,ir=or.call(Object);var sr=function(e){if(!Zt(e)||"[object Object]"!=We(e))return!1;var t=Qo(e);if(null===t)return!0;var n=rr.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&or.call(n)==ir};var ar=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]},ur=Object.prototype.hasOwnProperty;var pr=function(e,t,n){var o=e[t];ur.call(e,t)&&ge(o,n)&&(void 0!==n||t in e)||Vo(e,t,n)};var cr=function(e,t,n,o){var r=!n;n||(n={});for(var i=-1,s=t.length;++i<s;){var a=t[i],u=o?o(n[a],e[a],a,n,e):void 0;void 0===u&&(u=e[a]),r?Vo(n,a,u):pr(n,a,u)}return n};var lr=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t},fr=Object.prototype.hasOwnProperty;var dr=function(e){if(!Ue(e))return lr(e);var t=_n(e),n=[];for(var o in e)("constructor"!=o||!t&&fr.call(e,o))&&n.push(o);return n};var hr=function(e){return Cn(e)?bn(e,!0):dr(e)};var vr=function(e){return cr(e,hr(e))};var mr=function(e,t,n,o,r,i,s){var a=ar(e,n),u=ar(t,n),p=s.get(u);if(p)Wo(e,n,p);else{var c=i?i(a,u,n+"",e,t,s):void 0,l=void 0===c;if(l){var f=Ut(u),d=!f&&an(u),h=!f&&!d&&mn(u);c=u,f||d||h?Ut(a)?c=a:er(a)?c=Xo(a):d?(l=!1,c=qo(u,!0)):h?(l=!1,c=Yo(u,!0)):c=[]:sr(u)||rn(u)?(c=a,rn(a)?c=vr(a):Ue(a)&&!Ge(a)||(c=Zo(u))):l=!1}l&&(s.set(u,c),r(c,u,o,i,s),s.delete(u)),Wo(e,n,c)}};var gr=function e(t,n,o,r,i){t!==n&&Uo(n,(function(s,a){if(i||(i=new kt),Ue(s))mr(t,n,a,o,e,r,i);else{var u=r?r(ar(t,a),s,a+"",t,n,i):void 0;void 0===u&&(u=s),Wo(t,a,u)}}),hr)};var br=function(e){return e};var yr=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)},_r=Math.max;var wr=function(e,t,n){return t=_r(void 0===t?e.length-1:t,0),function(){for(var o=arguments,r=-1,i=_r(o.length-t,0),s=Array(i);++r<i;)s[r]=o[t+r];r=-1;for(var a=Array(t+1);++r<t;)a[r]=o[r];return a[t]=n(s),yr(e,this,a)}};var Or=function(e){return function(){return e}},Er=Ro?function(e,t){return Ro(e,"toString",{configurable:!0,enumerable:!1,value:Or(t),writable:!0})}:br,jr=Date.now;var Cr=function(e){var t=0,n=0;return function(){var o=jr(),r=16-(o-n);if(n=o,r>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(Er);var Tr=function(e,t){return Cr(wr(e,t,br),e+"")};var $r=function(e,t,n){if(!Ue(n))return!1;var o=typeof t;return!!("number"==o?Cn(n)&&pn(t,n.length):"string"==o&&t in n)&&ge(n[t],e)};var Sr=function(e){return Tr((function(t,n){var o=-1,r=n.length,i=r>1?n[r-1]:void 0,s=r>2?n[2]:void 0;for(i=e.length>3&&"function"==typeof i?(r--,i):void 0,s&&$r(n[0],n[1],s)&&(i=r<3?void 0:i,r=1),t=Object(t);++o<r;){var a=n[o];a&&e(t,a,o,i)}return t}))}((function(e,t,n){gr(e,t,n)}));function xr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!xr.installed){xr.installed=!0;var n={};Sr(n,uo,t),Ar.options=n,mo.options=n,e.directive("tooltip",mo),e.directive("close-popover",jo),e.component("VPopover",Fo)}}!function(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!=typeof document){var o=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===n&&o.firstChild?o.insertBefore(r,o.firstChild):o.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}}(".resize-observer[data-v-8859cc6c]{position:absolute;top:0;left:0;z-index:-1;width:100%;height:100%;border:none;background-color:transparent;pointer-events:none;display:block;overflow:hidden;opacity:0}.resize-observer[data-v-8859cc6c] object{display:block;position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1}");var kr=mo,Pr=jo,Nr=Fo,Ar={install:xr,get enabled(){return so.enabled},set enabled(e){so.enabled=e}},Lr=null;return"undefined"!=typeof window?Lr=window.Vue:"undefined"!=typeof global&&(Lr=global.Vue),Lr&&Lr.use(Ar),e.VClosePopover=Pr,e.VPopover=Nr,e.VTooltip=kr,e.createTooltip=fo,e.default=Ar,e.destroyTooltip=ho,e.install=xr,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
