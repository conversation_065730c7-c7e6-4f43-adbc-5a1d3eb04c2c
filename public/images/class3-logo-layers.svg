<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="230"
   height="64"
   version="1.1"
   id="svg18"
   sodipodi:docname="class3-logo-layers.svg"
   inkscape:version="0.92.5 (2060ec1f9f, 2020-04-08)"
   inkscape:export-filename="/home/<USER>/work/scola/class20/public/images/class3-logo-layers.png"
   inkscape:export-xdpi="96"
   inkscape:export-ydpi="96">
  <metadata
     id="metadata24">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs22" />
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1557"
     inkscape:window-height="759"
     id="namedview20"
     showgrid="false"
     inkscape:zoom="0.7700831"
     inkscape:cx="-193.03777"
     inkscape:cy="29.304405"
     inkscape:window-x="1974"
     inkscape:window-y="160"
     inkscape:window-maximized="0"
     inkscape:current-layer="layer1" />
  <!-- Created with Method Draw - http://github.com/duopixel/Method-Draw/ -->
  <g
     id="g8"
     transform="translate(-67.726616,-31.804405)">
    <title
       id="title2">background</title>
    <rect
       id="canvas_background"
       height="135"
       width="363"
       y="-1"
       x="-1"
       style="fill:none" />
    <g
       display="none"
       overflow="visible"
       y="0"
       x="0"
       height="100%"
       width="100%"
       id="canvasGrid"
       style="display:none;overflow:visible">
      <rect
         y="0"
         x="0"
         height="100%"
         width="100%"
         id="rect5"
         style="fill:url(#gridpattern);stroke-width:0" />
    </g>
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer1"
     inkscape:label="Background"
     style="display:inline"
     transform="translate(-67.726616,-31.804405)">
    <rect
       rx="0"
       id="svg_1"
       height="59"
       width="225"
       y="34.304405"
       x="70.226616"
       style="fill:#c75454;stroke:#c75454;stroke-width:5" />
    <g
       stroke="null"
       id="svg_4" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="Text"
     style="display:inline"
     transform="translate(-67.726616,-31.804405)">
    <text
       xml:space="preserve"
       font-size="40"
       id="svg_2"
       y="81.40818"
       x="95.478424"
       style="font-size:45.95826721px;font-family:Helvetica, Arial, sans-serif;text-anchor:start;fill:#ffffff;stroke:#ffffff;stroke-width:0">CLASS</text>
    <text
       xml:space="preserve"
       font-size="25"
       id="svg_3"
       y="61.774185"
       x="245.45314"
       style="font-size:28.72391701px;font-family:Helvetica, Arial, sans-serif;text-anchor:start;fill:#ffffff;stroke:#ffffff;stroke-width:0">3</text>
  </g>
</svg>
