/**
 * Bootstrap 5 Custom Styles for Class Application
 * 
 * This file overrides Bootstrap 5 default colors and styles
 * to match the Class application's branding and design.
 */

:root {
  /* Class Brand Colors */
  --class-red: #C75454;
  --class-blue: #6A6E8F;
  --class-dark-blue: #425668;
  --class-green: #4C9B5E;
  --class-yellow: #EDA707;
  --class-bg-blue: #6A6E8F;
  --class-light-blue: #66B6C3;
  --class-light: #f8f9fa;
  --class-dark: #343a40;
  
  /* Override Bootstrap 5 Colors */
  --bs-primary: var(--class-blue);
  --bs-primary-rgb: 106, 110, 143;
  --bs-success: var(--class-green);
  --bs-success-rgb: 76, 155, 94;
  --bs-danger: var(--class-red);
  --bs-danger-rgb: 199, 84, 84;
  --bs-warning: var(--class-yellow);
  --bs-warning-rgb: 237, 167, 7;
  
  /* Additional Custom Colors */
  --bs-light-blue: var(--class-light-blue);
  --bs-dark-blue: var(--class-dark-blue);
}

/* Primary Button */
.btn-primary {
  background-color: var(--class-blue);
  border-color: var(--class-blue);
}

.btn-primary:hover, 
.btn-primary:focus, 
.btn-primary:active {
  background-color: var(--class-dark-blue) !important;
  border-color: var(--class-dark-blue) !important;
}

/* Success Button */
.btn-success {
  background-color: var(--class-green);
  border-color: var(--class-green);
}

.btn-success:hover, 
.btn-success:focus, 
.btn-success:active {
  background-color: #3d7d4c !important;
  border-color: #3d7d4c !important;
}

/* Danger Button */
.btn-danger {
  background-color: var(--class-red);
  border-color: var(--class-red);
}

.btn-danger:hover, 
.btn-danger:focus, 
.btn-danger:active {
  background-color: #a64545 !important;
  border-color: #a64545 !important;
}

/* Warning Button */
.btn-warning {
  background-color: var(--class-yellow);
  border-color: var(--class-yellow);
  color: #212529;
}

.btn-warning:hover, 
.btn-warning:focus, 
.btn-warning:active {
  background-color: #c98c06 !important;
  border-color: #c98c06 !important;
  color: #212529 !important;
}

/* Alert Colors */
.alert-primary {
  background-color: rgba(var(--bs-primary-rgb), 0.15);
  border-color: rgba(var(--bs-primary-rgb), 0.3);
  color: var(--class-blue);
}

.alert-success {
  background-color: rgba(var(--bs-success-rgb), 0.15);
  border-color: rgba(var(--bs-success-rgb), 0.3);
  color: var(--class-green);
}

.alert-danger {
  background-color: rgba(var(--bs-danger-rgb), 0.15);
  border-color: rgba(var(--bs-danger-rgb), 0.3);
  color: var(--class-red);
}

.alert-warning {
  background-color: rgba(var(--bs-warning-rgb), 0.15);
  border-color: rgba(var(--bs-warning-rgb), 0.3);
  color: #856404;
}

/* Card Header */
.card-header {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--class-dark-blue);
  border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.2);
}

/* Links */
a {
  color: var(--class-blue);
}

a:hover {
  color: var(--class-dark-blue);
}

/* Form Focus */
.form-control:focus,
.form-select:focus {
  border-color: rgba(var(--bs-primary-rgb), 0.5);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Custom Navbar Styling */
.navbar-dark {
  background-color: var(--class-dark-blue) !important;
}

.navbar-light {
  background-color: var(--class-light) !important;
}

/* Badge Colors */
.badge.bg-primary {
  background-color: var(--class-blue) !important;
}

.badge.bg-success {
  background-color: var(--class-green) !important;
}

.badge.bg-danger {
  background-color: var(--class-red) !important;
}

.badge.bg-warning {
  background-color: var(--class-yellow) !important;
  color: #212529;
}

/* Progress Bar */
.progress-bar.bg-primary {
  background-color: var(--class-blue) !important;
}

.progress-bar.bg-success {
  background-color: var(--class-green) !important;
}

.progress-bar.bg-danger {
  background-color: var(--class-red) !important;
}

.progress-bar.bg-warning {
  background-color: var(--class-yellow) !important;
}

/* List Group */
.list-group-item.active {
  background-color: var(--class-blue);
  border-color: var(--class-blue);
}

/* Pagination */
.page-item.active .page-link {
  background-color: var(--class-blue);
  border-color: var(--class-blue);
}

.page-link {
  color: var(--class-blue);
}

.page-link:hover {
  color: var(--class-dark-blue);
}

/* Custom Utility Classes */
.bg-light-blue {
  background-color: var(--class-light-blue) !important;
}

.bg-dark-blue {
  background-color: var(--class-dark-blue) !important;
}

.text-light-blue {
  color: var(--class-light-blue) !important;
}

.text-dark-blue {
  color: var(--class-dark-blue) !important;
}

/* Toast Styling */
.toast {
  background-color: var(--bs-light);
  border-color: rgba(var(--bs-primary-rgb), 0.2);
}

.toast-header.bg-success {
  background-color: var(--class-green) !important;
}

.toast-header.bg-danger {
  background-color: var(--class-red) !important;
}

.toast-header.bg-warning {
  background-color: var(--class-yellow) !important;
  color: #212529 !important;
}

.toast-header.bg-primary {
  background-color: var(--class-blue) !important;
}

.toast-container {
  z-index: 1090;
}
