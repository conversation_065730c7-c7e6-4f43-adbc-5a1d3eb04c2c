name: Run Cypress Tests on CLASS

on:
  push:
    tags:
      - 'v*.*.*' # only run on version tags like v1.0.0
  workflow_dispatch:

jobs:
  cypress:
    runs-on: ubuntu-22.04

    services:
      mysql:
        image: mysql:8.0
        ports:
          - 3306:3306
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: class_test
        options: >-
          --health-cmd="mysqladmin ping --silent"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      # Checkout code
      - name: Check out code
        uses: actions/checkout@v3

      # Install PHP
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
          extensions: pdo_mysql, gd, xml, mbstring, curl, gettext, zip

      # Disable PHP JIT: PHP Warning: JIT is incompatible with third party extensions that override zend_execute_ex().
      # we don't need JIT
      - name: Disable PHP JIT
        run: echo "opcache.jit=0" >> $(php --ini | grep "Loaded Configuration File" | awk '{print $4}')

      # Install Node.js
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      # Copy .env file
      - name: Set up .env file
        run: cp .env.ci .env

      # Setup SSH credentials for Composer
      - name: Set up SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          ssh-keyscan github.com >> ~/.ssh/known_hosts

      # Install Composer dependencies
      - name: Install Composer dependencies
        run: composer install --no-scripts

      # Prepare storage and cache directories
      - name: Prepare storage and cache directories
        run: |
          mkdir -p storage/framework/cache
          mkdir -p storage/framework/sessions
          mkdir -p storage/framework/views
          mkdir -p bootstrap/cache
          chmod -R 775 storage bootstrap/cache

      # php artisan package:discover
      - name: Discover packages
        run: php artisan package:discover

      # Run database migrations
      - name: Run migrations and seed the database
        run: php artisan db:wipe --force && php artisan migrate --seed --force

      # Installeer Node-dependencies
      - name: Install Node dependencies
        run: npm install

      # After npm install, add:
      - name: Build frontend assets
        run: |
          npm run prod
          echo "Verifying build output:"
          ls -la public/js/
        env:
          NODE_ENV: production
          APP_ENV: testing
          APP_DEBUG: false
          APP_URL: http://localhost:8000
          APP_KEY: base64:1234567890
          APP_NAME: CLASS
          APP_VERSION: 3.0.0
          APP_LOCALE: nl

      # After preparing storage directories, add:
      - name: Set proper permissions and verify storage structure
        run: |
          sudo chown -R $USER:$USER .
          ls -la storage/
          ls -la public/js/tmpl3/ || echo "tmpl3 directory missing!"
          php artisan route:list  # Debug: verify routes are registered

      # Start de class server
      - name: Start CLASS
        run: |
          # Enable detailed error logging
          echo "APP_DEBUG=true" >> .env
          # Clear cache to ensure new env is picked up
          php artisan config:clear
          php artisan cache:clear
          # Generate OAuth keys for Passport
          php artisan passport:keys
          # Start server with error logging
          php artisan serve --host=0.0.0.0 --port=8000 2>&1 | tee storage/logs/laravel.log &
        env:
          APP_ENV: testing
          DB_CONNECTION: mysql
          DB_HOST: 127.0.0.1
          DB_PORT: 3306
          DB_DATABASE: class_test
          DB_USERNAME: root
          DB_PASSWORD: root
          SESSION_DRIVER: cookie
          SESSION_SECURE_COOKIE: true
          ALLOW_CORS_DOMAINS: "http://localhost,http://localhost:8000"

      # Add health check for Laravel server
      - name: Wait for Laravel server
        run: |
          timeout=30
          while ! curl -s http://localhost:8000 > /dev/null; do
            if [ $timeout -le 0 ]; then
              echo "Timeout waiting for Laravel server"
              exit 1
            fi
            echo "Waiting for Laravel server... ($timeout seconds remaining)"
            sleep 1
            timeout=$((timeout - 1))
          done
        
      # Run Cypress tests
      - name: Run Cypress Tests
        run: npx cypress run --browser chrome
        env:
          CYPRESS_baseUrl: http://localhost:8000
          CYPRESS_defaultCommandTimeout: 10000
          CYPRESS_requestTimeout: 10000

      # Improve artifact collection
      - name: Upload Cypress screenshots and Laravel logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: cypress-debug
          path: |
            tests/e2e/screenshots/**
            tests/e2e/videos/**
            storage/logs/*.log
            storage/logs/laravel*.log
            cypress/logs/**
            chrome.log
