<?php

$servername = "localhost";
$username = "scolar";
$password = "tochawru";
$dbname = "classprod";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
	die("Connection failed: " . $conn->connect_error . "\n");
}

// get all student records - we need to check if the students are not deleted
$existingStudents = [];
$q = "SELECT id FROM students";
$result = $conn->query($q);
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $existingStudents[] = $row['id'];
    }
}

// First, get all events that have no attendance notes
$q2 = "SELECT ev.id, ev.timetable_id, ev.datetime " .
    "FROM events ev " .
    "LEFT JOIN attendancenotes an on an.event_id = ev.id " .
    "WHERE an.id IS NULL " .
    "AND datetime < now()";
$result2 = $conn->query($q2);

$attendedQueries = [];
// loop through all events
if ($result2->num_rows > 0) {
    while ($row = $result2->fetch_assoc()) {
        // check if this is an individual session or a group session
        $tt = getTimetable($row['timetable_id'], $conn);
        $cs = getCourseStudent($tt['course_student_id'], $conn);
        $student = getStudent($cs['student_id'], $conn);
        if (isStudentGroup($student)) {
            // get all students participating the group at the same time as the event takes place
            $students = getStudentsInGroup($student['id'], $row['datetime'], $conn);
        } else {
            $students = [$student];
        }
        // now we have all students that need to be marked as 'attended'
        foreach ($students as $student) {
            if (!in_array($student['id'], $existingStudents)) {
                echo "-- skipping student " . $student['id'] . " because student is deleted\n";
            } else {
                $attendedQueries[] = "INSERT INTO attendancenotes (student_id, event_id, attendanceoption_id, notes, created_at, updated_at) " .
                    "VALUES ('" . $student['id'] . "', '" . $row['id'] . "', '1', 'setting initial attended by init script', now(), now());\n";
            }
        }
    }
}


// now select all attendance notes with type attendanceoption_id=2 (tijdig afgemeld)
// that have no corresponding document
$q3 = "SELECT an.id, an.student_id, an.event_id, an.attendanceoption_id, an.notes " .
    "FROM attendancenotes an " .
    "LEFT JOIN documents d on d.event_id = an.event_id " .
    "WHERE an.attendanceoption_id = 2 " .
    "AND d.id IS NULL";
$result2 = $conn->query($q3);
if ($result2->num_rows > 0) {
    while ($row = $result2->fetch_assoc()) {
        // create a document for this attendance note
        $attendedQueries[]  = "INSERT INTO documents (domain_id, event_id, type, content_type, label, file_location, url, crc, created_at, updated_at) " .
            "VALUES (1, " . $row['event_id'] . ", 'url', null, 'init attendance', null, 'https://www.muziekfabriek.nl', null, now(), now());\n";
    }
}

// now we have all queries to set the attendance to 'attended'
foreach ($attendedQueries as $q4) {
    echo $q4;
    // $conn->query($q);
}

$conn->close();

////////////////////////////////////////
// helper functions
////////////////////////////////////////
function getTimetable($id, $conn): ?array
{
    $q = "SELECT * FROM timetables WHERE id = $id";
    $result = $conn->query($q);
    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    return null;
}

function getCourseStudent($id, $conn): ?array
{
    $q = "SELECT * FROM course_student WHERE id = $id";
    $result = $conn->query($q);
    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    return null;
}

function getStudent($id, $conn): ?array
{
    $q = "SELECT * FROM students WHERE id = $id";
    $result = $conn->query($q);
    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    return null;
}

function isStudentGroup($student): bool
{
    return (str_starts_with($student['name'], "-") && $student['date_of_birth'] === '1800-01-01');
}

function getStudentsInGroup($studentId, $attendingOnDateTime, $conn): array
{
$q = "SELECT * FROM students s " .
        "LEFT JOIN student_studentgroup ssg ON s.id = ssg.student_id " .
        "WHERE ssg.studentgroup_id = $studentId " .
        "AND ssg.start_date <= '$attendingOnDateTime' " .
        "AND (ssg.end_date IS NULL OR ssg.end_date >= '$attendingOnDateTime')";
    $result = $conn->query($q);
    $students = [];
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
    }
    return $students;
}
