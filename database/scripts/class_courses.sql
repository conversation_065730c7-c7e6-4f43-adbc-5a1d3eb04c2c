-- update existing recurrence options to be in minutes instead of hours
update recurrenceoptions set nr_of_times = 90, timeunit='minutes' where id=1;
update recurrenceoptions set description='90 minuten per twee weken tot uitschrijven (doorlopend)' where id=1;
update recurrenceoptions set nr_of_times = 90, timeunit='minutes' where id=2;
update recurrenceoptions set description='90 minuten per week tot uitschrijven (doorlopend)' where id=2;
update recurrenceoptions set nr_of_times = 60, timeunit='minutes' where id=3;
update recurrenceoptions set description='60 minuten per week tot uitschrijven (doorlopend)' where id=3;
update recurrenceoptions set nr_of_times = 60, timeunit='minutes' where id=4;
update recurrenceoptions set description='60 minuten per twee weken tot uitschrijven (doorlopend)' where id=4;
update recurrenceoptions set nr_of_times = 60, timeunit='minutes', per_interval='notapply' where id=8;
update recurrenceoptions set description='60 minuten en eindigt na 1 herhaling' where id=8;
update recurrenceoptions set nr_of_times = 90, timeunit='minutes' where id=9;
update recurrenceoptions set description='90 minuten per week en eindigt na 4 herhalingen' where id=9;
update recurrenceoptions set nr_of_times = 150, timeunit='minutes' where id=12;
update recurrenceoptions set description='150 minuten per maand tot uitschrijven (doorlopend)' where id=12;
update recurrenceoptions set nr_of_times = 180, timeunit='minutes' where id=13;
update recurrenceoptions set description='180 minuten per maand tot uitschrijven (doorlopend)' where id=13;
update recurrenceoptions set nr_of_times = 180, timeunit='minutes' where id=15;
update recurrenceoptions set description='180 minuten per twee weken tot uitschrijven (doorlopend)' where id=15;
update recurrenceoptions set nr_of_times = 60, timeunit='minutes' where id=16;
update recurrenceoptions set description='60 minuten per maand tot uitschrijven (doorlopend)' where id=16;
update recurrenceoptions set nr_of_times = 90, timeunit='minutes' where id=18;
update recurrenceoptions set description='90 minuten per maand tot uitschrijven (doorlopend)' where id=18;
update recurrenceoptions set nr_of_times = 120, timeunit='minutes' where id=76;
update recurrenceoptions set description='120 minuten per week tot uitschrijven (doorlopend)' where id=76;
update recurrenceoptions set nr_of_times = 240, timeunit='minutes' where id=77;
update recurrenceoptions set description='240 minuten en eindigt na 1 herhaling' where id=77;
update recurrenceoptions set nr_of_times = 60, timeunit='minutes' where id=78;
update recurrenceoptions set description='60 minuten per week en eindigt na 10 herhalingen' where id=78;
update recurrenceoptions set nr_of_times = 240, timeunit='minutes' where id=79;
update recurrenceoptions set description='240 minuten per week tot uitschrijven (doorlopend)' where id=79;
update recurrenceoptions set nr_of_times = 60, timeunit='minutes' where id=98;
update recurrenceoptions set description='60 minuten per week tot uitschrijven (doorlopend)' where id=98;
update recurrenceoptions set nr_of_times = 240, timeunit='minutes' where id=108;
update recurrenceoptions set description='240 minuten per week tot uitschrijven (doorlopend)' where id=108;
update recurrenceoptions set nr_of_times = 240, timeunit='minutes', per_interval='notapply' where id=112;
update recurrenceoptions set description='240 minuten en eindigt na 1 herhaling' where id=112;
update recurrenceoptions set nr_of_times = 150, timeunit='minutes', per_interval='notapply' where id=114;
update recurrenceoptions set description='150 minuten en eindigt na 1 herhaling' where id=114;

-- 98 is dubbel met 3 -> nog oplossen
-- 108 is dubbel met 79
-- 112 is dubbel met 77
update courses set recurrenceoption_id=3 where recurrenceoption_id=98;

-- add missing recurrence options
insert into recurrenceoptions
(id, domain_id, description, nr_of_times, timeunit, per_interval, ends_after_nr_of_occurrences, created_at, updated_at)
VALUES
    (115, 1,'210 minuten en eindigt na 3 herhalingen', 210, 'minutes', 'notapply', 3, now(), now());
insert into recurrenceoptions
(id, domain_id, description, nr_of_times, timeunit, per_interval, ends_after_nr_of_occurrences, created_at, updated_at)
VALUES
    (116, 1,'90 minuten en eindigt na 1 herhaling', 90, 'minutes', 'notapply', 1, now(), now());

-- archiveer oude cursussen
UPDATE courses set archive=1 where id=	2;
UPDATE courses set archive=1 where id=	9;
UPDATE courses set archive=1 where id=	15;
UPDATE courses set archive=1 where id=	17;
UPDATE courses set archive=1 where id=	23;
UPDATE courses set archive=1 where id=	24;
UPDATE courses set archive=1 where id=	40;
UPDATE courses set archive=1 where id=	41;
UPDATE courses set archive=1 where id=	45;
UPDATE courses set archive=1 where id=	47;
UPDATE courses set archive=1 where id=	57;
UPDATE courses set archive=1 where id=	58;
UPDATE courses set archive=1 where id=	73;
UPDATE courses set archive=1 where id=	80;
UPDATE courses set archive=1 where id=	83;
UPDATE courses set archive=1 where id=	85;
UPDATE courses set archive=1 where id=	87;
UPDATE courses set archive=1 where id=	88;
UPDATE courses set archive=1 where id=	90;
UPDATE courses set archive=1 where id=	91;
UPDATE courses set archive=1 where id=	111;
UPDATE courses set archive=1 where id=	124;
UPDATE courses set archive=1 where id=	125;
UPDATE courses set archive=1 where id=	129;
UPDATE courses set archive=1 where id=	131;
UPDATE courses set archive=1 where id=	133;
UPDATE courses set archive=1 where id=	141;
UPDATE courses set archive=1 where id=	144;
UPDATE courses set archive=1 where id=	145;
UPDATE courses set archive=1 where id=	146;
UPDATE courses set archive=1 where id=	149;
UPDATE courses set archive=1 where id=	152;
UPDATE courses set archive=1 where id=	153;
UPDATE courses set archive=1 where id=	154;
UPDATE courses set archive=1 where id=	155;
UPDATE courses set archive=1 where id=	156;
UPDATE courses set archive=1 where id=	157;
UPDATE courses set archive=1 where id=	59;
UPDATE courses set archive=1 where id=	60;
UPDATE courses set archive=1 where id=	25;
UPDATE courses set archive=1 where id=	112;
UPDATE courses set archive=1 where id=	81;
UPDATE courses set archive=1 where id=	113;
UPDATE courses set archive=1 where id=	115;

-- groep workshops aanmaken
insert into coursegroups
(id, domain_id, name, webdescription, is_trial_group, created_at, updated_at)
VALUES
    (19, 1,'Workshops', '', 0, now(), now());
insert into coursegroup_tutor
    (coursegroup_id, tutor_id, age_group_adult, age_group_adolescent, age_group_child, created_at, updated_at)
VALUES
    (19,3,1,1,1,now(),now());

-- rename coursegroups
update coursegroups set name = 'bands' where id =9;
update coursegroups set name = 'drumlessen' where id =13;
update coursegroups set name = 'gitaarlessen' where id =6;
update coursegroups set name = 'pianolessen' where id =3;
update coursegroups set name = 'workshops' where id =19;
update coursegroups set name = 'zanglessen' where id =2;
update coursegroups set name = 'verhuur' where id =11;
update coursegroups set name = 'proeflessen' where id =10;

-- update foutieve recurrences
update courses set recurrenceoption_id=8 where id = 29;
update courses set recurrenceoption_id=116 where id = 42;
update courses set recurrenceoption_id=109 where id = 101;
update courses set recurrenceoption_id=109 where id = 102;
update courses set recurrenceoption_id=8 where id = 103;
update courses set recurrenceoption_id=109 where id = 104;
update courses set recurrenceoption_id=109 where id = 167;
update courses set recurrenceoption_id=8 where id = 130;
update courses set recurrenceoption_id=115, coursegroup_id=19 where id = 165;
update courses set recurrenceoption_id=8 where id = 166;

-- update namen van cursussen
update courses set name='zang II' where id =8;
update courses set name='zang II' where id =18;
update courses set name='zang II Popkoor' where id =19;
update courses set name='piano' where id =20;
update courses set name='piano' where id =22;
update courses set name='zang II' where id =27;
update courses set name='zang II' where id =29;
update courses set name='gitaar' where id =32;
update courses set name='gitaar' where id =34;
update courses set name='gitaar' where id =36;
update courses set name='piano' where id =37;
update courses set name='gitaar' where id =39;
update courses set name='zang II Popkoor' where id =42;
update courses set name='zang II' where id =43;
update courses set name='piano' where id =44;
update courses set name='gitaar' where id =46;
update courses set name='zang II' where id =48;
update courses set name='piano' where id =64;
update courses set name='zang II' where id =65;
update courses set name='zang II' where id =66;
update courses set name='gitaar' where id =68;
update courses set name='piano' where id =70;
update courses set name='gitaar' where id =72;
update courses set name='gitaar' where id =74;
update courses set name='piano' where id =75;
update courses set name='zang II Close Harmony Class' where id =78;
update courses set name='zang II' where id =87;
update courses set name='gitaar' where id =89;
update courses set name='huur oefenruimte' where id =92;
update courses set name='basgitaar' where id =94;
update courses set name='basgitaar' where id =95;
update courses set name='basgitaar' where id =96;
update courses set name='drums' where id =97;
update courses set name='drums' where id =98;
update courses set name='drums' where id =99;
update courses set name='drums' where id =100;
update courses set name="Do-Re-Mini's" where id =101;
update courses set name="SuperMaestro's" where id =102;
update courses set name='zang I Vocal Group Beesdse Bende' where id =103;
update courses set name='Bandstand' where id =104; -- proefles
update courses set name='basgitaar' where id =105;
update courses set name='gitaar' where id =109;
update courses set name='Bandstand' where id =113; -- reguliere les
update courses set name='huur oefenruimte' where id =114;
update courses set name='zang I' where id =117;
update courses set name='zang I' where id =118;
update courses set name='zang I' where id =119;
update courses set name='zang I' where id =120;
update courses set name='zang I' where id =121;
update courses set name='zang I' where id =122;
update courses set name='zang I' where id =123;
update courses set name='zang II' where id =127;
update courses set name='zang II' where id =128;
update courses set name='zang I' where id =130;
update courses set name='zang I' where id =132;
update courses set name='piano' where id =138;
update courses set name='zang I' where id =142;
update courses set name='basgitaar' where id =143;
update courses set name='zang II' where id =148;
update courses set name='zang II Close Harmony Class' where id =150;
update courses set name='Bandstand' where id =151;
update courses set name="Do-Re-Mini's" where id =158;
update courses set name="SuperMaestro's" where id =159;
update courses set name='zang I Vocal Group Beesdse Bende' where id =160;
update courses set name='ukelele ' where id =161;
update courses set name='ukelele ' where id =162;
update courses set name='ukelele ' where id =163;
update courses set name='ukelele ' where id =164;
update courses set name='workshop podium performance basics' where id =165;
update courses set name='piano' where id =166;

-- verhuizen cursussen naar de juiste cursusgroep
update courses set coursegroup_id=9 where id= 151;
update courses set coursegroup_id=9 where id= 113;
update courses set coursegroup_id=10 where id= 104;
update courses set coursegroup_id=13 where id= 99;
update courses set coursegroup_id=13 where id= 98;
update courses set coursegroup_id=13 where id= 97;
update courses set coursegroup_id=10 where id= 100;
update courses set coursegroup_id=6 where id= 96;
update courses set coursegroup_id=6 where id= 95;
update courses set coursegroup_id=6 where id= 94;
update courses set coursegroup_id=6 where id= 143;
update courses set coursegroup_id=10 where id= 105;
update courses set coursegroup_id=6 where id= 74;
update courses set coursegroup_id=6 where id= 32;
update courses set coursegroup_id=6 where id= 34;
update courses set coursegroup_id=6 where id= 39;
update courses set coursegroup_id=6 where id= 36;
update courses set coursegroup_id=6 where id= 109;
update courses set coursegroup_id=6 where id= 72;
update courses set coursegroup_id=6 where id= 68;
update courses set coursegroup_id=6 where id= 89;
update courses set coursegroup_id=10 where id= 46;
update courses set coursegroup_id=6 where id= 163;
update courses set coursegroup_id=6 where id= 164;
update courses set coursegroup_id=6 where id= 161;
update courses set coursegroup_id=10 where id= 162;
update courses set coursegroup_id=3 where id= 75;
update courses set coursegroup_id=3 where id= 22;
update courses set coursegroup_id=3 where id= 138;
update courses set coursegroup_id=3 where id= 37;
update courses set coursegroup_id=3 where id= 20;
update courses set coursegroup_id=3 where id= 70;
update courses set coursegroup_id=3 where id= 64;
update courses set coursegroup_id=3 where id= 166;
update courses set coursegroup_id=10 where id= 44;
update courses set coursegroup_id=19 where id= 165;
update courses set coursegroup_id=2 where id= 118;
update courses set coursegroup_id=2 where id= 117;
update courses set coursegroup_id=2 where id= 142;
update courses set coursegroup_id=2 where id= 119;
update courses set coursegroup_id=2 where id= 120;
update courses set coursegroup_id=2 where id= 123;
update courses set coursegroup_id=2 where id= 121;
update courses set coursegroup_id=2 where id= 122;
update courses set coursegroup_id=2 where id= 160;
update courses set coursegroup_id=15 where id= 158; -- AMV
update courses set coursegroup_id=15 where id= 159; -- AMV
update courses set coursegroup_id=2 where id= 132;
update courses set coursegroup_id=2 where id= 130;
update courses set coursegroup_id=10 where id= 103;
update courses set coursegroup_id=10 where id= 101;
update courses set coursegroup_id=10 where id= 102;
update courses set coursegroup_id=2 where id= 27;
update courses set coursegroup_id=2 where id= 8;
update courses set coursegroup_id=2 where id= 148;
update courses set coursegroup_id=2 where id= 65;
update courses set coursegroup_id=2 where id= 66;
update courses set coursegroup_id=2 where id= 87;
update courses set coursegroup_id=2 where id= 18;
update courses set coursegroup_id=2 where id= 48;
update courses set coursegroup_id=2 where id= 19;
update courses set coursegroup_id=2 where id= 78;
update courses set coursegroup_id=2 where id= 150;
update courses set coursegroup_id=2 where id= 128;
update courses set coursegroup_id=2 where id= 127;
update courses set coursegroup_id=2 where id= 29;
update courses set coursegroup_id=10 where id= 43;
update courses set coursegroup_id=10 where id= 42;
update courses set coursegroup_id=11 where id= 114;
update courses set coursegroup_id=11 where id= 92;


-- na verhuizen van de cursussen zijn de volgende cursusgroepen niet meer in gebruik:
-- songwriting (8) alleen nog historisch
-- zangles 4 (jongeren)
-- zangles 1 (koor)
UPDATE coursegroups set name = concat('ARCHIEF ', name) WHERE id IN (4,1,8);

-- koppel de proefles ukelele (162) aan de les ukelele (161)
INSERT INTO trialcourse_courses (trialcourse_id, course_id, created_at, updated_at) VALUES (162, 161,now(),now());

-- missende cursussen invoeren
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,10,8,1,'bandstand','0.00','0.00','0.00','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,13,4,0,'drums','75.00','74.38','90.00','month',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,13,19,0,'drums','22.50','22.73','27.50','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,13,8,0,'drums','45.00','45.46','55.00','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,6,6,0,'basgitaar','40.00','41.33','50.00','month', 2,2, now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,6,11,0,'basgitaar','60.00','61.99','75.00','month',2,2, now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,6,3,0,'basgitaar','80.00','82.65','100.00','month',2,2,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,6,19,0,'basgitaar','22.50','22.73','27.50','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,6,8,0,'basgitaar','45.00','45.46','55.00','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,6,8,0,'gitaar','45.00','45.46','55.00','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,6,3,0,'ukelele ','149.00','148.76','180.00','month',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,6,4,0,'ukelele ','75.00','74.38','90.00','month',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,6,6,0,'ukelele ','40.00','41.33','50.00','month',2,2,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,6,11,0,'ukelele ','60.00','61.99','75.00','month',2,2,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,6,3,0,'ukelele ','80.00','82.65','100.00','month',2,2,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,6,8,0,'ukelele ','45.00','45.46','55.00','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,3,6,0,'piano','40.00','41.33','50.00','month',2,2,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,3,19,0,'piano','22.50','22.73','27.50','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,19,115,0,'workshop podium performance advanced','250.00','250.00','272.50','time',2,35,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,2,3,0,'zang I','149.00','148.76','180.00','month',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,2,6,0,'zang I','40.00','41.33','50.00','month',2,2,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,10,19,1,'zang I','0.00','0.00','0.00','time',now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, group_size_min, group_size_max, created_at, updated_at) VALUES (1,2,6,0,'zang II','50.00','49.59','60.00','month',2,2,now(),now());
INSERT INTO courses (domain_id, coursegroup_id, recurrenceoption_id, is_trial_course, name, price_ex_tax_sub_adult, price_ex_tax, price_invoice,price_is_per, created_at, updated_at) VALUES (1,10,8,1,'zang II Close Harmony Class','0.00','0.00','0.00','time',now(),now());-- update prijzen in bestaande cursussen

-- update prijzen in bestaande cursussen
UPDATE courses SET price_ex_tax_sub_adult='40.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=151;
UPDATE courses SET price_ex_tax_sub_adult='55.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=113;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=104;
UPDATE courses SET price_ex_tax_sub_adult='49.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=99;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=98;
UPDATE courses SET price_ex_tax_sub_adult='149.00', price_ex_tax='148.76', price_invoice='180.00', price_is_per='month', updated_at=now() where id=97;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=100;
UPDATE courses SET price_ex_tax_sub_adult='49.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=96;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=95;
UPDATE courses SET price_ex_tax_sub_adult='149.00', price_ex_tax='148.76', price_invoice='180.00', price_is_per='month', updated_at=now() where id=94;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=143;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=105;
UPDATE courses SET price_ex_tax_sub_adult='49.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=74;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=32;
UPDATE courses SET price_ex_tax_sub_adult='112.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=34;
UPDATE courses SET price_ex_tax_sub_adult='149.00', price_ex_tax='148.76', price_invoice='180.00', price_is_per='month', updated_at=now() where id=39;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=36;
UPDATE courses SET price_ex_tax_sub_adult='40.00', price_ex_tax='41.33', price_invoice='50.00', price_is_per='month', updated_at=now() where id=109;
UPDATE courses SET price_ex_tax_sub_adult='60.00', price_ex_tax='61.99', price_invoice='75.00', price_is_per='month', updated_at=now() where id=72;
UPDATE courses SET price_ex_tax_sub_adult='80.00', price_ex_tax='82.65', price_invoice='100.00', price_is_per='month', updated_at=now() where id=68;
UPDATE courses SET price_ex_tax_sub_adult='22.50', price_ex_tax='22.73', price_invoice='27.50', price_is_per='time', updated_at=now() where id=89;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=46;
UPDATE courses SET price_ex_tax_sub_adult='49.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=163;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=164;
UPDATE courses SET price_ex_tax_sub_adult='22.50', price_ex_tax='22.73', price_invoice='27.50', price_is_per='time', updated_at=now() where id=161;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=162;
UPDATE courses SET price_ex_tax_sub_adult='49.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=75;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=22;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='55.78', price_invoice='67.50', price_is_per='month', updated_at=now() where id=138;
UPDATE courses SET price_ex_tax_sub_adult='149.00', price_ex_tax='148.76', price_invoice='180.00', price_is_per='month', updated_at=now() where id=37;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=20;
UPDATE courses SET price_ex_tax_sub_adult='60.00', price_ex_tax='61.99', price_invoice='75.00', price_is_per='month', updated_at=now() where id=70;
UPDATE courses SET price_ex_tax_sub_adult='80.00', price_ex_tax='82.65', price_invoice='100.00', price_is_per='month', updated_at=now() where id=64;
UPDATE courses SET price_ex_tax_sub_adult='45.00', price_ex_tax='45.46', price_invoice='55.00', price_is_per='time', updated_at=now() where id=166;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=44;
UPDATE courses SET price_ex_tax_sub_adult='250.00', price_ex_tax='250.00', price_invoice='272.50', price_is_per='time', updated_at=now() where id=165;
UPDATE courses SET price_ex_tax_sub_adult='49.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=118;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=117;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=142;
UPDATE courses SET price_ex_tax_sub_adult='60.00', price_ex_tax='61.99', price_invoice='75.00', price_is_per='month', updated_at=now() where id=119;
UPDATE courses SET price_ex_tax_sub_adult='80.00', price_ex_tax='82.65', price_invoice='100.00', price_is_per='month', updated_at=now() where id=120;
UPDATE courses SET price_ex_tax_sub_adult='37.50', price_ex_tax='37.19', price_invoice='45.00', price_is_per='month', updated_at=now() where id=123;
UPDATE courses SET price_ex_tax_sub_adult='50.00', price_ex_tax='49.59', price_invoice='60.00', price_is_per='month', updated_at=now() where id=121;
UPDATE courses SET price_ex_tax_sub_adult='25.00', price_ex_tax='24.79', price_invoice='30.00', price_is_per='month', updated_at=now() where id=122;
UPDATE courses SET price_ex_tax_sub_adult='29.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=160;
UPDATE courses SET price_ex_tax_sub_adult='29.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=158;
UPDATE courses SET price_ex_tax_sub_adult='29.50', price_ex_tax='0.00', price_invoice='0.00', price_is_per='month', updated_at=now() where id=159;
UPDATE courses SET price_ex_tax_sub_adult='22.50', price_ex_tax='22.73', price_invoice='27.50', price_is_per='time', updated_at=now() where id=132;
UPDATE courses SET price_ex_tax_sub_adult='45.00', price_ex_tax='45.46', price_invoice='55.00', price_is_per='time', updated_at=now() where id=130;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=103;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=101;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=102;
UPDATE courses SET price_ex_tax_sub_adult='90.00', price_ex_tax='90.91', price_invoice='110.00', price_is_per='month', updated_at=now() where id=27;
UPDATE courses SET price_ex_tax_sub_adult='180.00', price_ex_tax='181.82', price_invoice='220.00', price_is_per='month', updated_at=now() where id=8;
UPDATE courses SET price_ex_tax_sub_adult='90.00', price_ex_tax='90.91', price_invoice='110.00', price_is_per='month', updated_at=now() where id=148;
UPDATE courses SET price_ex_tax_sub_adult='75.00', price_ex_tax='74.38', price_invoice='90.00', price_is_per='month', updated_at=now() where id=65;
UPDATE courses SET price_ex_tax_sub_adult='100.00', price_ex_tax='99.18', price_invoice='120.00', price_is_per='month', updated_at=now() where id=66;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='45.46', price_invoice='55.00', price_is_per='month', updated_at=now() where id=87;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='59.92', price_invoice='72.50', price_is_per='month', updated_at=now() where id=18;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='29.96', price_invoice='36.25', price_is_per='month', updated_at=now() where id=48;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='27.48', price_invoice='29.95', price_is_per='month', updated_at=now() where id=19;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='73.40', price_invoice='80.00', price_is_per='month', updated_at=now() where id=78;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='36.70', price_invoice='40.00', price_is_per='month', updated_at=now() where id=150;
UPDATE courses SET price_ex_tax_sub_adult='27.50', price_ex_tax='27.27', price_invoice='33.00', price_is_per='time', updated_at=now() where id=128;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='40.91', price_invoice='49.50', price_is_per='time', updated_at=now() where id=127;
UPDATE courses SET price_ex_tax_sub_adult='55.00', price_ex_tax='54.55', price_invoice='66.00', price_is_per='time', updated_at=now() where id=29;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=43;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='0.00', price_invoice='0.00', price_is_per='time', updated_at=now() where id=42;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='130.16', price_invoice='157.50', price_is_per='month', updated_at=now() where id=114;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='59.92', price_invoice='72.50', price_is_per='time', updated_at=now() where id=92;
UPDATE courses SET price_ex_tax_sub_adult='0.00', price_ex_tax='24.79', price_invoice='30', price_is_per='time', updated_at=now() where id=116;


-- group sizes
update courses set group_size_min=2, group_size_max=35 where id =151;
update courses set group_size_min=2, group_size_max=35 where id =113;
update courses set group_size_min=2, group_size_max=2 where id =109;
update courses set group_size_min=2, group_size_max=2 where id =72;
update courses set group_size_min=2, group_size_max=2 where id =68;
update courses set group_size_min=2, group_size_max=2 where id =70;
update courses set group_size_min=2, group_size_max=2 where id =64;
update courses set group_size_min=2, group_size_max=35 where id =165;
update courses set group_size_min=2, group_size_max=2 where id =119;
update courses set group_size_min=2, group_size_max=2 where id =120;
update courses set group_size_min=2, group_size_max=35 where id =123;
update courses set group_size_min=2, group_size_max=35 where id =121;
update courses set group_size_min=2, group_size_max=35 where id =122;
update courses set group_size_min=2, group_size_max=35 where id =160;
update courses set group_size_min=2, group_size_max=35 where id =158;
update courses set group_size_min=2, group_size_max=35 where id =159;
update courses set group_size_min=2, group_size_max=2 where id =65;
update courses set group_size_min=2, group_size_max=2 where id =66;
update courses set group_size_min=2, group_size_max=35 where id =87;
update courses set group_size_min=2, group_size_max=35 where id =18;
update courses set group_size_min=2, group_size_max=35 where id =48;
update courses set group_size_min=2, group_size_max=35 where id =19;
update courses set group_size_min=2, group_size_max=35 where id =78;
update courses set group_size_min=2, group_size_max=35 where id =150;