<?php
// get connection
$servername = "localhost";  // let op: voor mamp: poort 8889
$username   = "scolar";
$password   = "tochawru";
$dbname     = "class";      // please note: this should be class_testing in test!

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// find events grouped by timetable-id
$q = "SELECT * FROM courses";

$result = $conn->query($q);
// output data of each row
while ($row = $result->fetch_assoc() ) {
    // bij elkaar op basis van recurrence option
    $coursesAsRec[$row["recurrenceoption_id"]." ".$row["name"]][] = $row;
}

$updateRegistrations = [];
$updateTrialstudents = [];
$updateTasks = [];
$cleanupCourses = [];
$cleanupPriceoptions = [];

foreach ($coursesAsRec as $recAndName => $items) {
    if (count($items) > 1) {
        // count($items) moet 2 zijn! (volwassenen / jongeren)
        if(count($items) === 2) {
            // found a duplicate course
            echo "-- duplicate found: $recAndName\n";
            // update registrations
            $updateRegistrations[] = "UPDATE course_student SET course_id = '" . $items[1]["id"] . "' WHERE course_id = '" . $items[0]["id"] . "';";
            $updateTrialstudents[] = "UPDATE trialstudents SET course_id = '" . $items[1]["id"] . "' WHERE course_id = '" . $items[0]["id"] . "';";
            $updateTasks[] = "UPDATE tasks SET course_id = '" . $items[1]["id"] . "' WHERE course_id = '" . $items[0]["id"] . "';";
            // cleanup
            $cleanupCourses[] = "DELETE FROM courses WHERE id = '" . $items[0]["id"] . "';";
            $cleanupPriceoptions[] = "DELETE FROM priceoptions WHERE course_id = '" . $items[0]["id"] . "';";
        }
    }
}

$conn->close();

echo "-- Deze cursus registraties worden verplaatst:\n";
foreach ($updateRegistrations as $update) {
    echo "$update\n";
}

echo "-- Deze trialstudents worden verplaatst:\n";
foreach ($updateTrialstudents as $update) {
    echo "$update\n";
}

echo "-- Deze tasks worden verplaatst:\n";
foreach ($updateTasks as $update) {
    echo "$update\n";
}
// tenslotte

echo "-- Deze priceoptions worden opgeruimd:\n";
foreach ($cleanupPriceoptions as $priceoption) {
    echo "$priceoption\n";
}
echo "-- Deze cursussen worden opgeruimd:\n";
foreach ($cleanupCourses as $course) {
    echo "$course\n";
}
