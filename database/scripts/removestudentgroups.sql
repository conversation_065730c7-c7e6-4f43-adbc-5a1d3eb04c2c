student id's
(256, 400)

DELETE 
FROM student_studentgroup
WHERE studentgroup_id IN (256, 400);

SELECT id 
FROM course_student 
WHERE student_id IN (256, 400);
-- deze moeten weg uit de timetables
(698, 1363);

SELECT id 
FROM timetables
WHERE course_student_id IN (698, 1363);
-- deze moeten weg uit de events
(539 ,  540 ,  541 ,  753 , 1121 , 1289 , 2567 , 2568 , 2569 , 2570 , 2571 , 2572 , 2573 , 3330 , 5258)

SELECT id 
FROM events
WHERE timetable_id IN (539 ,  540 ,  541 ,  753 , 1121 , 1289 , 2567 , 2568 , 2569 , 2570 , 2571 , 2572 , 2573 , 3330 , 5258);
-- deze moeten weg uit attendancenotes
(4162 ,  4163 ,  4164 ,  4165 ,  4166 ,  4167 ,  4168 ,  4169 ,  4170 ,  4171 ,  4172 ,  4173 ,  4174 ,  4175 ,  4176 ,  4177 ,  4178 ,  4179 ,  4180 ,  4181 ,  4182 ,  4183 ,  4184 ,  4185 ,  4186 ,  4187 ,  4188 ,  4189 ,  4190 ,  9490 ,  9491 ,  9492 ,  9493 ,  9494 ,  9495 ,  9496 ,  9497 ,  9498 ,  9499 ,  9500 ,  9501 ,  9502 ,  9503 ,  9504 ,  9505 ,  9506 ,  9507 ,  9508 ,  9509 ,  9510 ,  9511 ,  9512 ,  9513 ,  9514 ,  9515 ,  9516 ,  9517 ,  9518 ,  9519 ,  9520 ,  9521 ,  9522 ,  9523 ,  9524 , 12224 , 12225 , 12226 , 12227 , 12228 , 12229 , 12230 , 12231 , 12232 , 12233 , 12234 , 12235 , 12236 , 12237 , 12238 , 12239 , 12240 , 12241 , 12242 , 12243 , 12244 , 12245 , 12246 , 12247 , 12248 , 12249 , 12250 , 12251 , 12252 , 12253 , 12254 , 12255 , 12256 , 12257 , 12258 , 12259 , 12260 , 12261 , 12262 , 12263 , 12264 , 12265 , 12266 , 12267 , 12268 , 12269 , 13445 , 13446 , 13447 , 13448 , 13449 , 13450 , 13452 , 13453 , 13454 , 13455 , 13456 , 13457 , 13458 , 13459 , 13460 , 13463 , 13464 , 13465 , 13466 , 13467 , 13468 , 13469 , 13471 , 13472 , 13473 , 13474 , 19330 , 19331 , 19338 , 19339 , 19340 , 19341 , 19342 , 19343 , 19344 , 19345 , 19346 , 19347 , 19348 , 19349 , 19350 , 19351 , 19352 , 19353 , 19354 , 19355 , 19356 , 20752 , 20753 , 20754 , 20755 , 20756 , 20757 , 20758 , 20759 , 20760 , 20761 , 20762 , 20763 , 20764 , 20765 , 20766 , 20767 , 23629 , 23630 , 23631 , 23632 , 23633 , 23634 , 23635 , 23636 , 23637 , 23638 , 23639 , 23640 , 23641 , 23642 , 23643 , 23644 , 23645 , 23646 , 23647 , 23648 , 23649 , 23650 , 24355)

-------------------------------------
-------------------------------------
-------------------------------------

DELETE 
FROM student_studentgroup
WHERE studentgroup_id IN (256, 400);

DELETE 
FROM attendancenotes
WHERE event_id IN (4162 ,  4163 ,  4164 ,  4165 ,  4166 ,  4167 ,  4168 ,  4169 ,  4170 ,  4171 ,  4172 ,  4173 ,  4174 ,  4175 ,  4176 ,  4177 ,  4178 ,  4179 ,  4180 ,  4181 ,  4182 ,  4183 ,  4184 ,  4185 ,  4186 ,  4187 ,  4188 ,  4189 ,  4190 ,  9490 ,  9491 ,  9492 ,  9493 ,  9494 ,  9495 ,  9496 ,  9497 ,  9498 ,  9499 ,  9500 ,  9501 ,  9502 ,  9503 ,  9504 ,  9505 ,  9506 ,  9507 ,  9508 ,  9509 ,  9510 ,  9511 ,  9512 ,  9513 ,  9514 ,  9515 ,  9516 ,  9517 ,  9518 ,  9519 ,  9520 ,  9521 ,  9522 ,  9523 ,  9524 , 12224 , 12225 , 12226 , 12227 , 12228 , 12229 , 12230 , 12231 , 12232 , 12233 , 12234 , 12235 , 12236 , 12237 , 12238 , 12239 , 12240 , 12241 , 12242 , 12243 , 12244 , 12245 , 12246 , 12247 , 12248 , 12249 , 12250 , 12251 , 12252 , 12253 , 12254 , 12255 , 12256 , 12257 , 12258 , 12259 , 12260 , 12261 , 12262 , 12263 , 12264 , 12265 , 12266 , 12267 , 12268 , 12269 , 13445 , 13446 , 13447 , 13448 , 13449 , 13450 , 13452 , 13453 , 13454 , 13455 , 13456 , 13457 , 13458 , 13459 , 13460 , 13463 , 13464 , 13465 , 13466 , 13467 , 13468 , 13469 , 13471 , 13472 , 13473 , 13474 , 19330 , 19331 , 19338 , 19339 , 19340 , 19341 , 19342 , 19343 , 19344 , 19345 , 19346 , 19347 , 19348 , 19349 , 19350 , 19351 , 19352 , 19353 , 19354 , 19355 , 19356 , 20752 , 20753 , 20754 , 20755 , 20756 , 20757 , 20758 , 20759 , 20760 , 20761 , 20762 , 20763 , 20764 , 20765 , 20766 , 20767 , 23629 , 23630 , 23631 , 23632 , 23633 , 23634 , 23635 , 23636 , 23637 , 23638 , 23639 , 23640 , 23641 , 23642 , 23643 , 23644 , 23645 , 23646 , 23647 , 23648 , 23649 , 23650 , 24355);


DELETE 
FROM tasks 
WHERE event_id IN (4162 ,  4163 ,  4164 ,  4165 ,  4166 ,  4167 ,  4168 ,  4169 ,  4170 ,  4171 ,  4172 ,  4173 ,  4174 ,  4175 ,  4176 ,  4177 ,  4178 ,  4179 ,  4180 ,  4181 ,  4182 ,  4183 ,  4184 ,  4185 ,  4186 ,  4187 ,  4188 ,  4189 ,  4190 ,  9490 ,  9491 ,  9492 ,  9493 ,  9494 ,  9495 ,  9496 ,  9497 ,  9498 ,  9499 ,  9500 ,  9501 ,  9502 ,  9503 ,  9504 ,  9505 ,  9506 ,  9507 ,  9508 ,  9509 ,  9510 ,  9511 ,  9512 ,  9513 ,  9514 ,  9515 ,  9516 ,  9517 ,  9518 ,  9519 ,  9520 ,  9521 ,  9522 ,  9523 ,  9524 , 12224 , 12225 , 12226 , 12227 , 12228 , 12229 , 12230 , 12231 , 12232 , 12233 , 12234 , 12235 , 12236 , 12237 , 12238 , 12239 , 12240 , 12241 , 12242 , 12243 , 12244 , 12245 , 12246 , 12247 , 12248 , 12249 , 12250 , 12251 , 12252 , 12253 , 12254 , 12255 , 12256 , 12257 , 12258 , 12259 , 12260 , 12261 , 12262 , 12263 , 12264 , 12265 , 12266 , 12267 , 12268 , 12269 , 13445 , 13446 , 13447 , 13448 , 13449 , 13450 , 13452 , 13453 , 13454 , 13455 , 13456 , 13457 , 13458 , 13459 , 13460 , 13463 , 13464 , 13465 , 13466 , 13467 , 13468 , 13469 , 13471 , 13472 , 13473 , 13474 , 19330 , 19331 , 19338 , 19339 , 19340 , 19341 , 19342 , 19343 , 19344 , 19345 , 19346 , 19347 , 19348 , 19349 , 19350 , 19351 , 19352 , 19353 , 19354 , 19355 , 19356 , 20752 , 20753 , 20754 , 20755 , 20756 , 20757 , 20758 , 20759 , 20760 , 20761 , 20762 , 20763 , 20764 , 20765 , 20766 , 20767 , 23629 , 23630 , 23631 , 23632 , 23633 , 23634 , 23635 , 23636 , 23637 , 23638 , 23639 , 23640 , 23641 , 23642 , 23643 , 23644 , 23645 , 23646 , 23647 , 23648 , 23649 , 23650 , 24355);

DELETE 
FROM events 
WHERE timetable_id IN (539 ,  540 ,  541 ,  753 , 1121 , 1289 , 2567 , 2568 , 2569 , 2570 , 2571 , 2572 , 2573 , 3330 , 5258);

-- dan de timetables opruimen
DELETE 
FROM timetables 
WHERE id IN (539 ,  540 ,  541 ,  753 , 1121 , 1289 , 2567 , 2568 , 2569 , 2570 , 2571 , 2572 , 2573 , 3330 , 5258);

-- dan planningentries
DELETE
FROM planningentries
WHERE registration_id IN (698, 1363);

-- dan de registrations
DELETE 
FROM course_student
WHERE student_id IN (256, 400);

-- dan STUDENTS uit attendance notes
DELETE 
FROM attendancenotes
WHERE student_id IN (256, 400);

-- tenslotte de students zelf
DELETE 
FROM students
WHERE id IN (256, 400);

