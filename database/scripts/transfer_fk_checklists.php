<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "class"; // please note: this should be class_test in test!
$updates = [];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
	die("Connection failed: " . $conn->connect_error);
}

// find the correct registrations
$q = "SELECT * " .
     "FROM `course_student` ";

$result = $conn->query($q);
if ($result->num_rows > 0) {
	// output data of each row
	while ( $row = $result->fetch_assoc() ) {
		// generate a new code
		if(!empty($row["checklist_id"])) {
			$updates[] = "UPDATE `checklists` SET registration_id = " . $row['id'] . " WHERE id = " . $row["checklist_id"] . ";";
		}
	}
}

$conn->close();

if(count($updates) > 0) {
	echo "\nPlease run this on the database:\n\n";

	foreach ( $updates as $update ) {
		echo $update . "\n";
	}
} else {
	echo "\nno updates needed\n\n";
}
