<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "class"; // please note: this should be class_test in test!
$deletes = [];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
	die("Connection failed: " . $conn->connect_error);
}

// find the registrations that are orphaned
$q = "SELECT * " .
     "FROM `checklists` WHERE registration_id IS NULL ";

$result = $conn->query($q);
if ($result->num_rows > 0) {
	// output data of each row
	while ( $row = $result->fetch_assoc() ) {
		// generate a new code
		$deletes[] = "DELETE FROM `checklists` WHERE id = " . $row["id"] . ";";
	}
}

$conn->close();

if(count($deletes) > 0) {
	echo "\nPlease run this on the database:\n\n";

	foreach ( $deletes as $delete) {
		echo $delete . "\n";
	}
} else {
	echo "\nno deletes needed\n\n";
}
