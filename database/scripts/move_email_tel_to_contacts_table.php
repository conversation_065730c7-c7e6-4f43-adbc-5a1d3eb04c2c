<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "class";
$inserts = [];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
	die("Connection failed: " . $conn->connect_error);
}

// find the correct registrations
$q = "SELECT * " .
     "FROM `students`";
$result = $conn->query($q);
if ($result->num_rows > 0) {
	// output data of each row
	while ( $row = $result->fetch_assoc() ) {
		$email = $row["email"];
		if (!empty($email)) {
			$inserts[] = "INSERT INTO studentcontacts (`student_id`, `contacttype`, `value`) VALUES ('" . $row['id'] . "', 'email', '$email');";
		}
		$telephone = $row["telephone"];
		if (!empty($telephone)) {
			$inserts[] = "INSERT INTO studentcontacts (`student_id`, `contacttype`, `value`) VALUES ('" . $row['id'] . "', 'telephone', '$telephone');";
		}
	}
}


$conn->close();

if(count($inserts) > 0) {
	echo "\nPlease run this on the database:\n\n";

	foreach ( $inserts as $update ) {
		echo $update . "\n";
	}
} else {
	echo "\nno updates needed\n\n";
}
