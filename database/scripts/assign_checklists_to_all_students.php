<?php

$servername = "localhost";
$username = "root";
$password = "tochawru";
$dbname = "class";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
	die("Connection failed: " . $conn->connect_error . "\n");
}

// zoek uit welke students we bedoelen
// dit zijn studenten met actieve inschrijving
// maar tevens: er mag nog geen checklist aan gekoppeld zijn.
$q = "SELECT * FROM course_student " .
	"LEFT JOIN students ON course_student.student_id = students.id " .
	"WHERE (end_date IS NULL OR end_date = '') " .
	"AND (course_student.checklist_id IS NULL OR course_student.checklist_id = '')";

$result = $conn->query($q);
echo $q . '\n';

var_dump($result);

// voor elke student die hierin zit moet een checklist aangemaakt
// worden en gekoppeld worden aan de inschrijving
if ($result->num_rows > 0) {
	// output data of each row
	while($row = $result->fetch_assoc()) {
		// create checklist
		$newChecklistId = createNewChecklist(1, $conn);
		// update het inschrijfrecord met deze checklist id
		$q = "UPDATE course_student SET checklist_id = $newChecklistId".
			" WHERE student_id = '" . $row['student_id'] . "'" .
			" AND course_id = '" . $row['course_id'] . "'" .
			" AND start_date = '" . $row['start_date'] . "';\n";
		echo $q;
	}
} else {
	echo "No results!\n";
}
$conn->close();

/**
 * Create a new checklist based on te template identified by $defaultChecklistId
 * @param int $defaultChecklistId
 * @param null $conn
 * @return mixed
 */
function createNewChecklist($defaultChecklistId=0, $conn=null) {

	$q = "INSERT INTO checklists " .
		"(name, item1, item2, item3, item4, item5, item6, item7, item8, item9, item10, item11, item12, created_at, updated_at) ".
		"SELECT name,item1,item2,item3,item4,item5,item6, item7, item8, item9, item10, item11, item12, now(), now() ".
		"FROM default_checklists ".
		"WHERE id = $defaultChecklistId";

	$conn->query($q);

	return $conn->insert_id;
}
