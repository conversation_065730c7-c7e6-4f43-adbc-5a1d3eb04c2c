<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "class_michael"; // please note: this should be class_test in test!
$deletes = [];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}


// find events grouped by timetable-id
$q = "SELECT * FROM events";
$q = "SELECT COUNT(id) as count, DAYOFWEEK(datetime) as dow, TIME(datetime) as time, timespan, timetable_id " .
    "FROM events " .
    "GROUP BY timespan, dow, time, timetable_id";

$result = $conn->query($q);
if ($result->num_rows > 0) {
    // output data of each row
    while ($row = $result->fetch_assoc() ) {
        // if count > 1 we have a group
        if($row["count"] > 1) {
            $groupId = uniqid(null, true);
            $updateQueries[] = "UPDATE events " .
                "SET eventgrp_uniqueid = '$groupId' " .
                "WHERE DAYOFWEEK(datetime) = " . $row['dow'] . " " .
                "AND TIME(datetime) = '" . $row["time"] . "' " .
                "AND timespan = '" . $row["timespan"] . "' " .
                "AND timetable_id = '" . $row["timetable_id"] . "'";
        }
    }
}

$conn->close();

foreach ($updateQueries as $updateQuery) {
    echo $updateQuery . ";\n";
}