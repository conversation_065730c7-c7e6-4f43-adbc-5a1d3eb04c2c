<?php
$output = [];
$servername = "localhost";
$username = "scolar";
$password = "tochawru";
$dbname = "class_michael";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error . "\n");
}

// SELECTEER alle groepen
$q = "SELECT * FROM students WHERE firstname = '-' AND date_of_birth = '1800-01-01'";
$result = $conn->query($q);

if ($result->num_rows > 0) {
    // output data of each row
    while ($row = $result->fetch_assoc()) {
        echo "\n" . $row["lastname"] . "\n"; // == de coursegroup name

        // welke registraties horen bij deze cursusgroep (we pakken de laatste
        $q3 = "SELECT id, course_id FROM course_student WHERE student_id = '" . $row['id'] . "'";
        $result3 = $conn->query($q3);
        if ($result3->num_rows > 0) {
            while ($row3 = $result3->fetch_assoc()) {
                echo "Registration: " . $row3["id"] . " for course: " . $row3["course_id"] . "\n";
                $courseId = $row3["course_id"];
            }
        }

        // welke leerlingen horen bij deze cursusgroup?
        $q2 = "SELECT student_id FROM student_studentgroup WHERE studentgroup_id = '" . $row['id'] . "'";
        $result2 = $conn->query($q2);
        if ($result2->num_rows > 0) {
            // output data of each row
            while ($row2 = $result2->fetch_assoc()) {
                echo "student: " . $row2["student_id"] . "\n";

                // deze leerlingen mogen geen events meer hebben die dezelfde DT hebben
                // zoek de registratie en timetable erbij
                $q4 = "SELECT tt.id FROM timetables tt " .
                      "LEFT JOIN course_student cs ON tt.course_student_id = cs.id ".
                      "WHERE cs.student_id = '" . $row2['student_id'] . "' " .
                      "AND cs.course_id = '" . $courseId . "'";

                $result4 = $conn->query($q4);
                if ($result4->num_rows > 0) {
                    // output data of each row
                    while ($row4 = $result4->fetch_assoc()) {
                        echo "timetable: " . $row4["id"] . "\n";
                        // delete all events for these timetables
                        $output[] = "DELETE FROM events WHERE timetable_id = '" . $row4["id"] . "';";
                        // this timetable is no longer needed
                        $output[] = "DELETE FROM timetables WHERE id = '" . $row4["id"] . "';";
                    }
                }

            }
        }
    }
}

// show resulting queries
echo "\n*************************************************";
echo "\nexecute thes queries in the class database\n\n";
foreach ($output as $item) {
    echo $item . "\n";
}
echo "\n*************************************************\n";
