<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "class";
$updates = [];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
	die("Connection failed: " . $conn->connect_error);
}

// find the correct registrations
$q = "SELECT * " .
     "FROM `students`";
$result = $conn->query($q);
if ($result->num_rows > 0) {
	// output data of each row
	while ( $row = $result->fetch_assoc() ) {
		$name = $row["name"];
		$nameparts = explode(' ', $name);
		$firstname = array_shift($nameparts);
		$lastname = array_pop($nameparts);
		$preposition = implode(' ',$nameparts);
		// generate a new code
//		echo "$name: $firstname - $preposition - $lastname \n";
		$updates[] = "UPDATE `students` SET firstname = '$firstname', preposition = '$preposition', lastname = '$lastname' WHERE id = " . $row["id"] . ";";
	}
}

$conn->close();

if(count($updates) > 0) {
	echo "\nPlease run this on the database:\n\n";

	foreach ( $updates as $update ) {
		echo $update . "\n";
	}
} else {
	echo "\nno updates needed\n\n";
}
