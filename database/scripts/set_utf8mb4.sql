SET NAMES utf8mb4;
ALTER DATABASE class CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

# for each table that has a paste-able field
ALTER TABLE students CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE coursegroups CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# kan pas als tasks er is...
# ALTER TABLE coursegroups CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 

ALTER TABLE students CHANGE remarks remarks text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE coursegroups CHANGE webdescription webdescription text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# kan pas als tasks er is...
# ALTER TABLE tasks CHAN<PERSON> remarks remarks text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

REPAIR TABLE students;
OPTIMIZE TABLE students;
REPAIR TABLE coursegroups;
OPTIMIZE TABLE coursegroups;

# kan pas als tasks er is...
# REPAIR TABLE tasks;
# OPTIMIZE TABLE tasks;
