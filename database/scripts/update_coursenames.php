<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "classprod";
$upserts = [];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// find the correct registrations
$q = "SELECT * FROM `courses` WHERE `domain_id` = 1";
$result = $conn->query($q);

// update names to start with capital
if ($result->num_rows > 0) {
    // output data of each row
    while ( $row = $result->fetch_assoc() ) {
        $name = $row["name"];
        $name = ucfirst($name);
        $upserts[] = 'UPDATE courses SET name = "' . $name . '" WHERE id = ' . $row['id'] . ";\n";

        // if name contains Zang II, update to Zang (gevorderd)
        if (strpos($name, 'Zang II') !== false) {
            $newName = str_replace('Zang II', 'Zang (gevorderd)', $name);
            $upserts[] = 'UPDATE courses SET name = "' . $newName . '" WHERE id = ' . $row['id'] . ";\n";
        }
        // if name contains Zang I, update to Zang (basis)
        elseif (strpos($name, 'Zang I') !== false) {
            $newName = str_replace('Zang I', 'Zang (basis)', $name);
            $upserts[] = 'UPDATE courses SET name = "' . $newName . '" WHERE id = ' . $row['id'] . ";\n";

        }
        // if name contains Zang&Piano, update to Zang & Piano
        elseif (strpos($name, 'Zang&Piano') !== false) {
            $newName = str_replace('Zang&Piano', 'Zang & Piano', $name);
            $upserts[] = 'UPDATE courses SET name = "' . $newName . '" WHERE id = ' . $row['id'] . ";\n";
        }
    }
}

foreach ($upserts as $upsert) {
    echo $upsert;
}

