-- Set the domain ID
SET @domainid = 2;

-- Delete the domain from all related tables
DELETE FROM documents WHERE domain_id = @domainid;
DELETE FROM mailtemplates WHERE domain_id = @domainid;
DELETE FROM tasks WHERE domain_id = @domainid;
DELETE FROM events WHERE tutor_id in (
  SELECT id
  FROM users
  WHERE domain_id = @domainid
);
DELETE FROM availabilities WHERE tutor_id in (
  SELECT id
  FROM users
  WHERE domain_id = @domainid
);
DELETE FROM role_user WHERE user_id in (
  SELECT id
  FROM users
  WHERE domain_id = @domainid
);
DELETE FROM users WHERE domain_id = @domainid;
DELETE FROM locations WHERE domain_id = @domainid;
DELETE FROM date_exceptions WHERE domain_id = @domainid;
DELETE FROM timetables WHERE schoolyear_id in (
  SELECT id
  FROM schoolyears
  WHERE domain_id = @domainid
);
DELETE FROM schoolyears WHERE domain_id = @domainid;
DELETE FROM courses WHERE domain_id = @domainid;
DELETE FROM recurrenceoptions WHERE domain_id = @domainid;
DELETE FROM coursegroups WHERE domain_id = @domainid;
DELETE FROM default_checklists WHERE domain_id = @domainid;
DELETE FROM studentcontacts WHERE student_id IN (
  SELECT id
  FROM students
  WHERE domain_id = @domainid
);
DELETE FROM schedulepreferences WHERE student_id IN (
  SELECT id
  FROM students
  WHERE domain_id = @domainid
);
DELETE FROM logentries WHERE student_id IN (
  SELECT id
  FROM students
  WHERE domain_id = @domainid
);
DELETE FROM student_studentlist WHERE student_id IN (
  SELECT id
  FROM students
  WHERE domain_id = @domainid
);
DELETE FROM student_studentgroup WHERE student_id IN (
  SELECT id
  FROM students
  WHERE domain_id = @domainid
);

DELETE FROM students WHERE domain_id = @domainid;
DELETE FROM studentlists WHERE domain_id = @domainid;
DELETE FROM trialstudents WHERE domain_id = @domainid;

DELETE FROM domains WHERE id = @domainid;

-- hoe zit het met priceoptions?