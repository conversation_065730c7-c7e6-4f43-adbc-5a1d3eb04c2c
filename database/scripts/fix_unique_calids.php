<?php

$servername = "localhost";
$username = "scolar";
$password = "tochawru";
$dbname = "class_michael";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error . "\n");
}

$q = "select distinct DATE_FORMAT(datetime, '%H:%i') as timestring, tutor_id, timetable_id, timespan, location_id from events";
$result = $conn->query($q);

if ($result->num_rows > 0) {
    // output data of each row
    while ($row = $result->fetch_assoc()) {
        // all in this series should have the same calunique id
        $qInner = "SELECT caluniqueid " .
            "FROM `events` " .
            "WHERE timetable_id = " . $row["timetable_id"] . " " .
            "AND DATE_FORMAT(datetime, '%H:%i') = '" . $row["timestring"] . "' " .
            "AND timespan = '" . $row["timespan"] . "' " .
            "AND tutor_id = " . $row["tutor_id"] . " " .
            "AND location_id = " . $row["location_id"];
        $resultInner = $conn->query($qInner);

        // if its already only 1 row, don't change it
        if ($resultInner->num_rows > 1) {
            // get the first caluniqueid
            $rowInner = $resultInner->fetch_assoc();
            // create update query
            $updates[] = "UPDATE events SET caluniqueid = '" . $rowInner["caluniqueid"] . "' " .
                "WHERE timetable_id = " . $row["timetable_id"] . " " .
                "AND DATE_FORMAT(datetime, '%H:%i') = '" . $row["timestring"] . "' " .
                "AND timespan = '" . $row["timespan"] . "' " .
                "AND tutor_id = " . $row["tutor_id"] . " " .
                "AND location_id = " . $row["location_id"];
        }
    }
}


foreach ($updates as $update) {
    print $update . ";\n";
}