INSERT into `recurrenceoptions` VALUES ('1', '1.5 uur per twee weken tot uitschrijven (doorlopend)','1.5','hour','two weeks',null, NULL, NULL);
INSERT into `recurrenceoptions` VALUES ('2', '1.5 uur per week tot uitschrijven (doorlopend)','1.5','hour','week',null,NULL, NULL);
INSERT into `recurrenceoptions` VALUES ('3', '1 uur per week tot uitschrijven (doorlopend)','1','hour','week',null,NULL, NULL);
INSERT into `recurrenceoptions` VALUES ('4', '1 uur per twee weken tot uitschrijven (doorlopend)','1','hour','two weeks',null,NULL, NULL);
INSERT into `recurrenceoptions` VALUES ('5', '45 minuten per week tot uitschrijven (doorlopend)','45','minutes','week',null,NULL, NULL);
INSERT into `recurrenceoptions` VALUES ('6', '30 minuten per week tot uitschrijven (doorlopend)','30','minutes','week',null,NULL, NULL);
INSERT into `recurrenceoptions` VALUES ('7', '40 minuten per week tot uitschrijven (doorlopend)','40','minutes','week',null,NULL, NULL);

UPDATE courses SET recurrenceoption_id = '2' WHERE id = '2';
UPDATE courses SET recurrenceoption_id = '2' WHERE id = '3';
UPDATE courses SET recurrenceoption_id = '3' WHERE id = '6';
UPDATE courses SET recurrenceoption_id = '4' WHERE id = '7';
UPDATE courses SET recurrenceoption_id = '3' WHERE id = '8';
UPDATE courses SET recurrenceoption_id = '4' WHERE id = '9';
UPDATE courses SET recurrenceoption_id = '5' WHERE id = '10';
UPDATE courses SET recurrenceoption_id = '6' WHERE id = '11';
UPDATE courses SET recurrenceoption_id = '4' WHERE id = '12';
UPDATE courses SET recurrenceoption_id = '6' WHERE id = '13';
UPDATE courses SET recurrenceoption_id = '5' WHERE id = '14';
UPDATE courses SET recurrenceoption_id = '3' WHERE id = '15';
UPDATE courses SET recurrenceoption_id = '7' WHERE id = '17';
UPDATE courses SET recurrenceoption_id = '4' WHERE id = '18';
UPDATE courses SET recurrenceoption_id = '2' WHERE id = '19';
UPDATE courses SET recurrenceoption_id = '4' WHERE id = '20';
UPDATE courses SET recurrenceoption_id = '3' WHERE id = '21';
UPDATE courses SET recurrenceoption_id = '6' WHERE id = '22';
UPDATE courses SET recurrenceoption_id = '5' WHERE id = '23';
UPDATE courses SET recurrenceoption_id = '5' WHERE id = '24';
UPDATE courses SET recurrenceoption_id = '1' WHERE id = '25';

SELECT c.id, c.type, r.description, r.nr_of_times, r.timeunit, r.per_interval from courses c
  LEFT JOIN recurrenceoptions r ON c.recurrenceoption_id = r.id;
