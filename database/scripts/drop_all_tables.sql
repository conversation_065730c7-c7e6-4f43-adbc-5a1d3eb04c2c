SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `availabilities`;
DROP TABLE IF EXISTS `checklists`;
DROP TABLE IF EXISTS `course_student`;
DROP TABLE IF EXISTS `coursegroups`;
DROP TABLE IF EXISTS `courses`;
DROP TABLE IF EXISTS `date_exceptions`;
DROP TABLE IF EXISTS `default_checklists`;
DROP TABLE IF EXISTS `events`;
DROP TABLE IF EXISTS `locations`;
DROP TABLE IF EXISTS `logentries`;
DROP TABLE IF EXISTS `mailtemplates`;
DROP TABLE IF EXISTS `migrations`;
DROP TABLE IF EXISTS `oauth_access_tokens`;
DROP TABLE IF EXISTS `oauth_auth_codes`;
DROP TABLE IF EXISTS `oauth_clients`;
DROP TABLE IF EXISTS `oauth_personal_access_clients`;
DROP TABLE IF EXISTS `oauth_refresh_tokens`;
DROP TABLE IF EXISTS `password_resets`;
DROP TABLE IF EXISTS `recurrenceoptions`;
DROP TABLE IF EXISTS `role_user`;
DROP TABLE IF EXISTS `roles`;
DROP TABLE IF EXISTS `schedulepreferences`;
DROP TABLE IF EXISTS `schoolyears`;
DROP TABLE IF EXISTS `studentcontacts`;
DROP TABLE IF EXISTS `students`;
DROP TABLE IF EXISTS `tasks`;
DROP TABLE IF EXISTS `tasktypes`;
DROP TABLE IF EXISTS `timetables`;
DROP TABLE IF EXISTS `trialstudents`;
DROP TABLE IF EXISTS `users`;

SET FOREIGN_KEY_CHECKS = 1;
