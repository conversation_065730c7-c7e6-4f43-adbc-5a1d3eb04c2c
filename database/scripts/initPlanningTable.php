<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "classprod";
$queries    = [];
$domainId   = 9;

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// get recurrenceoption -> nrofminutes/oddeven table
$q = "select id, timeunit, nr_of_times, per_interval from recurrenceoptions";
$result = $conn->query($q);
while ($row = $result->fetch_assoc() ) {
    $recId = $row["id"];
    $nrOfMinuts = ($row["timeunit"] === "hour")  ?
        intval($row["nr_of_times"]) * 60 :
        $row["nr_of_times"];
    switch ($row["per_interval"]) {
        case "two weeks":
            $oddeven = 'odd or even';
            break;
        case "week":
            $oddeven = 'oddeven';
            break;
        default:
            // i.e. day, month, notapply
            $oddeven = 'other';
            break;
    }
    $lookup[$recId] = ["minutes" => $nrOfMinuts, "oddeven" => $oddeven];
}

// find events grouped by timetable-id
$q = "
SELECT cs.id as registration_id, timetable_id, c.domain_id, c.recurrenceoption_id
FROM events
LEFT JOIN timetables t on events.timetable_id = t.id
LEFT JOIN course_student cs on cs.id = t.course_student_id
LEFT JOIN courses c on cs.course_id = c.id
WHERE datetime BETWEEN DATE_SUB(now(), INTERVAL 6 MONTH) AND NOW()
AND (cs.end_date IS NULL OR cs.end_date > now())
GROUP BY timetable_id, c.domain_id, c.recurrenceoption_id
";
$result = $conn->query($q);
// Get plan day and time for each timetable
// This assumes that the last known appointment is the correct plan event
while ($row = $result->fetch_assoc() ) {
    $domainId = $row["domain_id"];
    $registrationId = $row["registration_id"];
    $timeTableId = $row["timetable_id"];
    $recurrenceOptionId = $row["recurrenceoption_id"];
    $qInner = "
        SELECT datetime, DAYOFWEEK(datetime) as daynumber, TIME(datetime) as plantime, WEEK(datetime) as weeknumber, tutor_id, location_id
        FROM events
        WHERE timetable_id = $timeTableId
        AND datetime = (
            SELECT max(datetime)
            FROM events
            WHERE timetable_id = $timeTableId
        )
    ";
    $resultInner = $conn->query($qInner);
    $rowInner = $resultInner->fetch_assoc();
    $dow = $rowInner["daynumber"];
    $time = $rowInner["plantime"];
    $tutorId = $rowInner["tutor_id"];
    $locationId = $rowInner["location_id"];
    if ($lookup[$recurrenceOptionId]["oddeven"] === "odd or even") {
        $oddeven = (intval($rowInner["weeknumber"]) +1) % 2 === 0 ? 'even' : 'odd';
    } else {
        $oddeven = $lookup[$recurrenceOptionId]["oddeven"];
    }
    $queries[] = "-- based on datetime: " . $rowInner["datetime"] . " (sunday = 1) \n";
    $insertQ = "INSERT INTO planningentries (domain_id, tutor_id, registration_id, location_id, starttime, daynumber, duration, oddeven, created_at, updated_at) ";
    $insertQ .= "VALUES ('$domainId', '$tutorId', '$registrationId', '$locationId', '$time', '$dow', '". $lookup[$recurrenceOptionId]["minutes"] . "', '$oddeven', NOW(), NOW());\n";
    $queries[] = $insertQ;
}

foreach ($queries as $query) {
    echo $query;
}

