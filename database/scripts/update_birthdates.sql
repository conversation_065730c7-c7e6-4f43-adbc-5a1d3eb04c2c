update students set `date_of_birth` = '1984-03-27' where id = 	1	;
update students set `date_of_birth` = '1945-10-05' where id = 	2	;
update students set `date_of_birth` = '1963-10-31' where id = 	3	;
update students set `date_of_birth` = '1976-04-20' where id = 	4	;
update students set `date_of_birth` = '1980-01-07' where id = 	5	;
update students set `date_of_birth` = '1964-04-27' where id = 	6	;
update students set `date_of_birth` = '1951-11-04' where id = 	7	;
update students set `date_of_birth` = '1963-06-30' where id = 	8	;
update students set `date_of_birth` = '1960-11-10' where id = 	9	;
update students set `date_of_birth` = '2000-02-04' where id = 	10	;
update students set `date_of_birth` = '1998-10-26' where id = 	11	;
update students set `date_of_birth` = '1999-07-05' where id = 	12	;
update students set `date_of_birth` = '1992-10-05' where id = 	13	;
update students set `date_of_birth` = '1959-09-10' where id = 	14	;
update students set `date_of_birth` = '1963-04-09' where id = 	15	;
update students set `date_of_birth` = '1951-05-30' where id = 	16	;
update students set `date_of_birth` = '2000-05-23' where id = 	17	;
update students set `date_of_birth` = '2005-11-30' where id = 	19	;
update students set `date_of_birth` = '1984-02-14' where id = 	20	;
update students set `date_of_birth` = '1966-09-28' where id = 	21	;
update students set `date_of_birth` = '1950-01-14' where id = 	22	;
update students set `date_of_birth` = '2008-06-23' where id = 	23	;
update students set `date_of_birth` = '2011-08-24' where id = 	24	;
update students set `date_of_birth` = '2004-11-08' where id = 	25	;
update students set `date_of_birth` = '2006-11-02' where id = 	26	;
update students set `date_of_birth` = '1965-03-20' where id = 	27	;
update students set `date_of_birth` = '1966-09-04' where id = 	28	;
update students set `date_of_birth` = '1991-09-29' where id = 	29	;
update students set `date_of_birth` = '1967-07-06' where id = 	30	;
update students set `date_of_birth` = '1964-03-23' where id = 	31	;
update students set `date_of_birth` = '2006-06-02' where id = 	33	;
update students set `date_of_birth` = '1999-05-06' where id = 	34	;
update students set `date_of_birth` = '2003-04-02' where id = 	35	;
update students set `date_of_birth` = '1959-10-21' where id = 	36	;
update students set `date_of_birth` = '2005-08-02' where id = 	37	;
update students set `date_of_birth` = '1944-11-07' where id = 	38	;
update students set `date_of_birth` = '1968-08-25' where id = 	39	;
update students set `date_of_birth` = '2000-12-31' where id = 	41	;
update students set `date_of_birth` = '1960-12-05' where id = 	43	;
update students set `date_of_birth` = '1974-06-16' where id = 	44	;
update students set `date_of_birth` = '1977-10-15' where id = 	45	;
update students set `date_of_birth` = '2006-01-24' where id = 	46	;
update students set `date_of_birth` = '2003-12-12' where id = 	47	;
update students set `date_of_birth` = '1956-02-02' where id = 	48	;
update students set `date_of_birth` = '1964-01-30' where id = 	50	;
update students set `date_of_birth` = '1971-07-02' where id = 	51	;
update students set `date_of_birth` = '1962-05-08' where id = 	54	;
update students set `date_of_birth` = '1962-06-05' where id = 	55	;
update students set `date_of_birth` = '1970-06-28' where id = 	56	;
update students set `date_of_birth` = '2008-08-20' where id = 	57	;
update students set `date_of_birth` = '1958-12-03' where id = 	58	;
update students set `date_of_birth` = '2005-09-30' where id = 	59	;
update students set `date_of_birth` = '1972-04-25' where id = 	60	;
update students set `date_of_birth` = '1983-01-25' where id = 	62	;
update students set `date_of_birth` = '2009-06-18' where id = 	63	;
update students set `date_of_birth` = '1939-09-07' where id = 	64	;
update students set `date_of_birth` = '2000-12-28' where id = 	65	;
update students set `date_of_birth` = '1964-02-03' where id = 	66	;
update students set `date_of_birth` = '2005-06-27' where id = 	67	;
update students set `date_of_birth` = '2007-06-14' where id = 	68	;
update students set `date_of_birth` = '1973-08-04' where id = 	70	;
update students set `date_of_birth` = '1963-04-02' where id = 	71	;
update students set `date_of_birth` = '1957-11-26' where id = 	72	;
update students set `date_of_birth` = '2010-07-28' where id = 	73	;
update students set `date_of_birth` = '2005-01-13' where id = 	74	;
update students set `date_of_birth` = '1966-08-12' where id = 	75	;
update students set `date_of_birth` = '1973-06-26' where id = 	76	;
update students set `date_of_birth` = '1967-01-29' where id = 	77	;
update students set `date_of_birth` = '2005-12-24' where id = 	78	;
update students set `date_of_birth` = '2006-11-02' where id = 	79	;
update students set `date_of_birth` = '2004-05-15' where id = 	83	;
update students set `date_of_birth` = '1998-03-03' where id = 	84	;
update students set `date_of_birth` = '1977-02-03' where id = 	85	;
update students set `date_of_birth` = '1981-03-13' where id = 	86	;
update students set `date_of_birth` = '2004-01-12' where id = 	87	;
update students set `date_of_birth` = '1961-10-31' where id = 	88	;
update students set `date_of_birth` = '1967-08-25' where id = 	89	;
update students set `date_of_birth` = '2010-03-25' where id = 	90	;
update students set `date_of_birth` = '1985-02-01' where id = 	91	;
update students set `date_of_birth` = '2009-04-02' where id = 	92	;
update students set `date_of_birth` = '1992-11-13' where id = 	93	;
update students set `date_of_birth` = '1973-06-28' where id = 	94	;
update students set `date_of_birth` = '2009-11-12' where id = 	95	;
update students set `date_of_birth` = '2010-12-30' where id = 	96	;
update students set `date_of_birth` = '1957-01-01' where id = 	97	;
update students set `date_of_birth` = '1899-12-30' where id = 	98	;
update students set `date_of_birth` = '1899-12-30' where id = 	99	;
update students set `date_of_birth` = '2010-12-02' where id = 	100	;
update students set `date_of_birth` = '2005-04-07' where id = 	101	;
update students set `date_of_birth` = '2005-10-18' where id = 	102	;
update students set `date_of_birth` = '1899-12-30' where id = 	103	;
update students set `date_of_birth` = '1899-12-30' where id = 	104	;
