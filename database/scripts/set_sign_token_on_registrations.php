<?php
// get connection
$servername = "localhost";
$username   = "scolar";
$password   = "tochawru";
$dbname     = "class";
$updates = [];

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
	die("Connection failed: " . $conn->connect_error);
}

// find the correct registrations
$q = "SELECT * " .
     "FROM `course_student` ".
     "WHERE (signed = 0 OR signed IS NULL) ".
     "AND (end_date IS NULL OR end_date > NOW()) ".
     "AND (sign_code IS NULL OR sign_code = '')";
$result = $conn->query($q);
if ($result->num_rows > 0) {
	// output data of each row
	while ( $row = $result->fetch_assoc() ) {
		// generate a new code
		$signCode = uniqid("", true);
		$updates[] = "UPDATE `course_student` SET sign_code = '$signCode' WHERE id = " . $row["id"] . ";";
	}
}

$conn->close();

if(count($updates) > 0) {
	echo "\nPlease run this on the database:\n\n";

	foreach ( $updates as $update ) {
		echo $update . "\n";
	}
} else {
	echo "\nno updates needed\n\n";
}
