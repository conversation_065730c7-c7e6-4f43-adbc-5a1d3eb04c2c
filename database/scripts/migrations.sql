-- MySQL dump 10.13  Distrib 5.7.25, for Linux (x86_64)
--
-- Host: localhost    Database: class
-- ------------------------------------------------------
-- Server version	5.7.25-0ubuntu0.18.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=301 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (74,'2017_10_08_121808_create_checklists_table',0),(75,'2017_10_08_121808_create_course_student_table',0),(76,'2017_10_08_121808_create_coursegroups_table',0),(77,'2017_10_08_121808_create_courses_table',0),(78,'2017_10_08_121808_create_date_exceptions_table',0),(79,'2017_10_08_121808_create_default_checklists_table',0),(80,'2017_10_08_121808_create_events_table',0),(81,'2017_10_08_121808_create_locations_table',0),(82,'2017_10_08_121808_create_password_resets_table',0),(83,'2017_10_08_121808_create_recurrenceoptions_table',0),(84,'2017_10_08_121808_create_role_user_table',0),(85,'2017_10_08_121808_create_roles_table',0),(86,'2017_10_08_121808_create_schoolyears_table',0),(87,'2017_10_08_121808_create_studentcontacts_table',0),(88,'2017_10_08_121808_create_students_table',0),(89,'2017_10_08_121808_create_timetables_table',0),(90,'2017_10_08_121808_create_users_table',0),(91,'2017_10_08_121809_add_foreign_keys_to_checklists_table',0),(92,'2017_10_08_121809_add_foreign_keys_to_course_student_table',0),(93,'2017_10_08_121809_add_foreign_keys_to_courses_table',0),(94,'2017_10_08_121809_add_foreign_keys_to_date_exceptions_table',0),(95,'2017_10_08_121809_add_foreign_keys_to_events_table',0),(96,'2017_10_08_121809_add_foreign_keys_to_role_user_table',0),(97,'2017_10_08_121809_add_foreign_keys_to_studentcontacts_table',0),(98,'2017_10_08_121809_add_foreign_keys_to_timetables_table',0),(99,'2017_10_13_140232_add_telephone_field_to_users_table',1),(100,'2017_10_13_175859_add_telephone_extra_to_users_table',1),(101,'2017_10_29_122321_add_caluniqueid_to_events_table',1),(102,'2016_06_01_000001_create_oauth_auth_codes_table',2),(103,'2016_06_01_000002_create_oauth_access_tokens_table',2),(104,'2016_06_01_000003_create_oauth_refresh_tokens_table',2),(105,'2016_06_01_000004_create_oauth_clients_table',2),(106,'2016_06_01_000005_create_oauth_personal_access_clients_table',2),(107,'2017_11_03_204051_delete_email_from_students_table',2),(108,'2017_12_03_101707_create_trialstudents_table',2),(109,'2017_12_03_103709_add_foreign_keys_to_trialstudents_table',2),(110,'2017_12_03_113129_add_is_trial_group_to_coursegroups_table',2),(111,'2017_12_08_182455_make_startdate_field_string_on_trialstudents_table',2),(112,'2017_12_09_124359_make_course_id_optional_for_trialstudents',2),(113,'2017_12_09_204235_add_appointment_field_to_trialstudents_table',2),(114,'2017_12_10_184155_add_student_id_to_trialstudents_table',2),(115,'2017_12_12_182417_add_registration_id_to_trialstudentstable',2),(116,'2017_12_15_154329_add_is_trial_to_courses_table',2),(117,'2017_12_22_200836_create_tasks_table',3),(118,'2017_12_22_201030_create_tasktypes_table',3),(119,'2017_12_22_201959_add_foreign_keys_to_tasks_table',3),(120,'2018_02_09_102524_add_apply_fields_to_studentcontacts',4),(121,'2018_02_27_203615_create_studententries_table',4),(122,'2018_02_27_204311_migrate_student_log_entries',4),(123,'2018_05_14_182019_add_color_field_to_user',5),(124,'2018_05_16_110145_create_studentgroups_table',5),(125,'2018_05_16_110534_create_student_studentgroups_table',5),(126,'2018_07_19_182051_create_studentlists_table',6),(127,'2018_07_19_182505_create_student_studentlist_table',6),(128,'2018_03_22_190802_add_calcolor_to_tutor',7),(129,'2018_06_09_133929_create_availabilitytypes_table',7),(130,'2018_06_09_133944_create_availabilities_table',7),(131,'2018_06_15_172833_drop_availability_type',7),(132,'2018_06_17_181616_create_schedulepreferences_table',7),(133,'2018_06_17_183550_add_accesstoken_to_students_table',7),(134,'2018_06_23_073618_add_block_date_to_schedulepreferences',7),(135,'2018_06_23_073702_add_keep_scheduled_time_to_course_student_table',7),(136,'2018_07_08_144429_create_mailtemplates_table',7),(137,'2018_07_08_145357_frefill_mailtemplates_table',7),(138,'2018_07_11_180340_create_emaillogentries_table',7),(139,'2018_07_11_191146_create_jobs_table',7),(140,'2018_07_11_192103_create_failed_jobs_table',7),(141,'2018_07_13_141212_add_is_prefered_field_to_schedulepreferences_table',7),(142,'2018_07_17_172720_add_has_access_field_to_students_table',7),(143,'2018_07_17_173105_create_repositories_table',7),(144,'2018_07_17_173622_create_messages_table',7),(145,'2018_07_17_174037_create_documents_table',7),(146,'2018_07_17_180106_create_document_repository_table',7),(147,'2018_07_17_180121_create_repository_student_table',7),(148,'2018_07_18_173801_fill_accesstoken_for_all_students',7),(149,'2018_08_02_141439_create_planingsections_table',7),(150,'2018_08_06_180813_create_planninggroups_table',7),(151,'2018_08_06_180919_ceate_planninggroup_student_table',7),(152,'2018_08_06_190507_add_planninggroup_id_to_timetables_table',7),(153,'2018_08_07_175358_change_planninggroups_table',7),(154,'2018_08_09_195033_add_confirm_ignore_current_schedule_to_registration_table',7),(155,'2018_08_18_121711_add_mail_template',7),(156,'2018_09_21_124358_remove_future_events_for_stopped_registrations',8),(157,'2018_11_03_190304_create_studentgroups_table',8),(158,'2018_11_03_194302_rename_planninggroupid_to_studentgroup',8),(159,'2018_11_03_200304_add_course_relation_to_studentgroups',8),(160,'2018_11_03_201438_reset_planninggroups',8),(161,'2018_11_08_193813_add_group_course_tag',8),(162,'2018_11_10_202113_create_priceoptions_table',8),(163,'2018_11_10_203054_fill_priceoptions_table_from_courses',8),(164,'2018_11_10_204138_delete_price_columns_from_courses_table',8),(165,'2018_11_11_200451_cleanup_model_after_group_changes',8),(166,'2018_11_13_192133_create_student_studentgroup_table',8),(167,'2018_12_06_190844_drop_studentgroup_id_from_timetables',8),(168,'2018_12_11_201918_rename_attachments_column_emaillog',8),(169,'2018_12_27_123454_add_original_dt_to_event',8),(170,'2018_12_27_200340_add_sequence_to_events_table',8),(171,'2019_01_29_185844_create_availabilities_table',0),(172,'2019_01_29_185844_create_checklists_table',0),(173,'2019_01_29_185844_create_course_student_table',0),(174,'2019_01_29_185844_create_coursegroups_table',0),(175,'2019_01_29_185844_create_courses_table',0),(176,'2019_01_29_185844_create_date_exceptions_table',0),(177,'2019_01_29_185844_create_default_checklists_table',0),(178,'2019_01_29_185844_create_document_repository_table',0),(179,'2019_01_29_185844_create_documents_table',0),(180,'2019_01_29_185844_create_emaillogentries_table',0),(181,'2019_01_29_185844_create_events_table',0),(182,'2019_01_29_185844_create_failed_jobs_table',0),(183,'2019_01_29_185844_create_jobs_table',0),(184,'2019_01_29_185844_create_locations_table',0),(185,'2019_01_29_185844_create_logentries_table',0),(186,'2019_01_29_185844_create_mailtemplates_table',0),(187,'2019_01_29_185844_create_messages_table',0),(188,'2019_01_29_185844_create_oauth_access_tokens_table',0),(189,'2019_01_29_185844_create_oauth_auth_codes_table',0),(190,'2019_01_29_185844_create_oauth_clients_table',0),(191,'2019_01_29_185844_create_oauth_personal_access_clients_table',0),(192,'2019_01_29_185844_create_oauth_refresh_tokens_table',0),(193,'2019_01_29_185844_create_password_resets_table',0),(194,'2019_01_29_185844_create_planninggroups_table',0),(195,'2019_01_29_185844_create_planningsections_table',0),(196,'2019_01_29_185844_create_priceoptions_table',0),(197,'2019_01_29_185844_create_priceoptions_old_table',0),(198,'2019_01_29_185844_create_recurrenceoptions_table',0),(199,'2019_01_29_185844_create_repositories_table',0),(200,'2019_01_29_185844_create_repository_student_table',0),(201,'2019_01_29_185844_create_role_user_table',0),(202,'2019_01_29_185844_create_roles_table',0),(203,'2019_01_29_185844_create_schedulepreferences_table',0),(204,'2019_01_29_185844_create_schoolyears_table',0),(205,'2019_01_29_185844_create_student_student_table',0),(206,'2019_01_29_185844_create_student_studentgroup_table',0),(207,'2019_01_29_185844_create_student_studentlist_table',0),(208,'2019_01_29_185844_create_studentcontacts_table',0),(209,'2019_01_29_185844_create_studentlists_table',0),(210,'2019_01_29_185844_create_students_table',0),(211,'2019_01_29_185844_create_tasks_table',0),(212,'2019_01_29_185844_create_tasktypes_table',0),(213,'2019_01_29_185844_create_timetables_table',0),(214,'2019_01_29_185844_create_trialstudents_table',0),(215,'2019_01_29_185844_create_users_table',0),(216,'2019_01_29_185845_add_foreign_keys_to_availabilities_table',0),(217,'2019_01_29_185845_add_foreign_keys_to_checklists_table',0),(218,'2019_01_29_185845_add_foreign_keys_to_course_student_table',0),(219,'2019_01_29_185845_add_foreign_keys_to_courses_table',0),(220,'2019_01_29_185845_add_foreign_keys_to_date_exceptions_table',0),(221,'2019_01_29_185845_add_foreign_keys_to_document_repository_table',0),(222,'2019_01_29_185845_add_foreign_keys_to_events_table',0),(223,'2019_01_29_185845_add_foreign_keys_to_logentries_table',0),(224,'2019_01_29_185845_add_foreign_keys_to_messages_table',0),(225,'2019_01_29_185845_add_foreign_keys_to_priceoptions_table',0),(226,'2019_01_29_185845_add_foreign_keys_to_repositories_table',0),(227,'2019_01_29_185845_add_foreign_keys_to_repository_student_table',0),(228,'2019_01_29_185845_add_foreign_keys_to_role_user_table',0),(229,'2019_01_29_185845_add_foreign_keys_to_schedulepreferences_table',0),(230,'2019_01_29_185845_add_foreign_keys_to_student_student_table',0),(231,'2019_01_29_185845_add_foreign_keys_to_student_studentgroup_table',0),(232,'2019_01_29_185845_add_foreign_keys_to_studentcontacts_table',0),(233,'2019_01_29_185845_add_foreign_keys_to_tasks_table',0),(234,'2019_01_29_185845_add_foreign_keys_to_timetables_table',0),(235,'2019_01_29_185845_add_foreign_keys_to_trialstudents_table',0),(236,'2019_01_29_190024_create_availabilities_table',0),(237,'2019_01_29_190024_create_checklists_table',0),(238,'2019_01_29_190024_create_course_student_table',0),(239,'2019_01_29_190024_create_coursegroups_table',0),(240,'2019_01_29_190024_create_courses_table',0),(241,'2019_01_29_190024_create_date_exceptions_table',0),(242,'2019_01_29_190024_create_default_checklists_table',0),(243,'2019_01_29_190024_create_document_repository_table',0),(244,'2019_01_29_190024_create_documents_table',0),(245,'2019_01_29_190024_create_emaillogentries_table',0),(246,'2019_01_29_190024_create_events_table',0),(247,'2019_01_29_190024_create_failed_jobs_table',0),(248,'2019_01_29_190024_create_jobs_table',0),(249,'2019_01_29_190024_create_locations_table',0),(250,'2019_01_29_190024_create_logentries_table',0),(251,'2019_01_29_190024_create_mailtemplates_table',0),(252,'2019_01_29_190024_create_messages_table',0),(253,'2019_01_29_190024_create_oauth_access_tokens_table',0),(254,'2019_01_29_190024_create_oauth_auth_codes_table',0),(255,'2019_01_29_190024_create_oauth_clients_table',0),(256,'2019_01_29_190024_create_oauth_personal_access_clients_table',0),(257,'2019_01_29_190024_create_oauth_refresh_tokens_table',0),(258,'2019_01_29_190024_create_password_resets_table',0),(259,'2019_01_29_190024_create_planninggroups_table',0),(260,'2019_01_29_190024_create_planningsections_table',0),(261,'2019_01_29_190024_create_priceoptions_table',0),(262,'2019_01_29_190024_create_priceoptions_old_table',0),(263,'2019_01_29_190024_create_recurrenceoptions_table',0),(264,'2019_01_29_190024_create_repositories_table',0),(265,'2019_01_29_190024_create_repository_student_table',0),(266,'2019_01_29_190024_create_role_user_table',0),(267,'2019_01_29_190024_create_roles_table',0),(268,'2019_01_29_190024_create_schedulepreferences_table',0),(269,'2019_01_29_190024_create_schoolyears_table',0),(270,'2019_01_29_190024_create_student_student_table',0),(271,'2019_01_29_190024_create_student_studentgroup_table',0),(272,'2019_01_29_190024_create_student_studentlist_table',0),(273,'2019_01_29_190024_create_studentcontacts_table',0),(274,'2019_01_29_190024_create_studentlists_table',0),(275,'2019_01_29_190024_create_students_table',0),(276,'2019_01_29_190024_create_tasks_table',0),(277,'2019_01_29_190024_create_tasktypes_table',0),(278,'2019_01_29_190024_create_timetables_table',0),(279,'2019_01_29_190024_create_trialstudents_table',0),(280,'2019_01_29_190024_create_users_table',0),(281,'2019_01_29_190026_add_foreign_keys_to_availabilities_table',0),(282,'2019_01_29_190026_add_foreign_keys_to_checklists_table',0),(283,'2019_01_29_190026_add_foreign_keys_to_course_student_table',0),(284,'2019_01_29_190026_add_foreign_keys_to_courses_table',0),(285,'2019_01_29_190026_add_foreign_keys_to_date_exceptions_table',0),(286,'2019_01_29_190026_add_foreign_keys_to_document_repository_table',0),(287,'2019_01_29_190026_add_foreign_keys_to_events_table',0),(288,'2019_01_29_190026_add_foreign_keys_to_logentries_table',0),(289,'2019_01_29_190026_add_foreign_keys_to_messages_table',0),(290,'2019_01_29_190026_add_foreign_keys_to_priceoptions_table',0),(291,'2019_01_29_190026_add_foreign_keys_to_repositories_table',0),(292,'2019_01_29_190026_add_foreign_keys_to_repository_student_table',0),(293,'2019_01_29_190026_add_foreign_keys_to_role_user_table',0),(294,'2019_01_29_190026_add_foreign_keys_to_schedulepreferences_table',0),(295,'2019_01_29_190026_add_foreign_keys_to_student_student_table',0),(296,'2019_01_29_190026_add_foreign_keys_to_student_studentgroup_table',0),(297,'2019_01_29_190026_add_foreign_keys_to_studentcontacts_table',0),(298,'2019_01_29_190026_add_foreign_keys_to_tasks_table',0),(299,'2019_01_29_190026_add_foreign_keys_to_timetables_table',0),(300,'2019_01_29_190026_add_foreign_keys_to_trialstudents_table',0);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2019-01-29 20:24:26
