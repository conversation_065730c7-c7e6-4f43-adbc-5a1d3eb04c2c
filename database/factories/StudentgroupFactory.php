<?php

namespace Database\Factories;

use App\Models\Studentgroup;
use Illuminate\Database\Eloquent\Factories\Factory;

class StudentgroupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Studentgroup::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $name = $this->faker->lastname;
        return [
            'id'            => $this->faker->unique()->randomNumber(),
            'domain_id'     => 9,
            'firstname'     => '-',
            'lastname'      => $name,
            'name'          => "- $name",
            'date_of_birth' => '1800-01-01',
            'status'        => 'active'
        ];
    }

    public function id($reqNr)
    {
        return $this->state([
            'id'                    => $reqNr
        ]);
    }
}
