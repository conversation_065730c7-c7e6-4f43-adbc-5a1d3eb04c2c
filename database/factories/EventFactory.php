<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Faker\Generator as Faker;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'location_id' => 1,
            'tutor_id' => 1,
            'timetable_id' => 1,
            'caluniqueid' => $this->faker->uuid(),
            'timespan' => "60 minuten",
            'datetime' => $this->faker->date(),
            'original_datetime' => $this->faker->date(),
            'sequence' => 1,
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime(),
        ];
    }
}