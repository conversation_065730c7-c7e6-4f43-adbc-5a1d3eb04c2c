<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'domain_id' => 9,
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'hexcolor' => $this->faker->hexcolor(),
            'password' => bcrypt('password'),
            'remember_token' => Str::random(10),
        ];
    }
}
