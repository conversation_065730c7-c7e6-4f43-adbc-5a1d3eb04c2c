<?php

namespace Database\Factories;

use App\Models\RecurrenceOption;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class RecurrenceOptionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RecurrenceOption::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'id'                            => $this->faker->unique()->randomNumber(),
            'domain_id'                     => 9,
            'description'                   => $this->faker->domainWord, // just about anything
            'nr_of_times'                   => '1',
            'timeunit'                      => 'hour',
            'per_interval'                  => 'day',
            'ends_after_nr_of_occurrences'  => '1'
        ];
    }

    public function id($reqNr)
    {
        return $this->state([
            'id' => $reqNr
        ]);
    }

}
