<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Schoolyear>
 */
class SchoolyearFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->faker->randomNumber(),
            'domain_id' => $this->faker->randomNumber(),
            'label' => $this->faker->name,
            'start_date' => $this->faker->dateTime(),
            'end_date' => $this->faker->dateTime(),
            "start_year" => $this->faker->year(),
            "end_year" => $this->faker->year(),
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime(),
        ];
    }
}
