<?php

namespace Database\Factories;

use App\Models\Course;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CourseFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Course::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'id'                    => $this->faker->unique()->randomNumber(),
            'coursegroup_id'        => $this->faker->unique()->randomNumber(),
            'recurrenceoption_id'   => 1,
            'domain_id'             => 9,
            'name'                  => $this->faker->name,
        ];
    }

    public function id($reqNr)
    {
        return $this->state([
            'id'                    => $reqNr
        ]);
    }

    public function courseGroupId($reqNr) {
        return $this->state([
            'coursegroup_id'        => $reqNr
        ]);
    }

    public function recurrenceOptionId($reqNr) {
        return $this->state([
            'recurrenceoption_id'   => $reqNr
        ]);

    }
}
