<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class RecurrenceoptionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->faker->randomNumber(),
            'domain_id' => $this->faker->randomNumber(),
            'description' => "90 minuten per twee weken tot uitschrijven (doorlopend)",
            'nr_of_times' => 90,
            'timeunit' => "minutes",
            'per_interval' => "two weeks",
            'ends_after_nr_of_occurrences' => null,
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime(),
        ];
    }
}
