<?php

namespace Database\Factories;

use App\Models\Coursegroup;
use Illuminate\Database\Eloquent\Factories\Factory;

class CoursegroupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Coursegroup::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'id'                => $this->faker->unique()->randomNumber(),
            'domain_id'         => 9,
            'name'              => $this->faker->domainWord, // whatever
            'is_trial_group'    => 0
        ];
    }

    public function id($reqNr)
    {
        return $this->state([
            'id'                    => $reqNr
        ]);
    }

    public function isTrialGroup()
    {
        return $this->state([
            'is_trial_group'    => 1
        ]);
    }
}
