<?php

namespace Database\Factories;

use App\Models\Registration;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class RegistrationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Registration::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'id'            => $this->faker->randomNumber(),
            'student_id'    => $this->faker->randomNumber(),
            'course_id'     => $this->faker->randomNumber(),
            'start_date'    => date('Y-m-d'),
            'status'        => 1
        ];
    }

    public function id($reqNr)
    {
        return $this->state([
            'id'            => $reqNr
        ]);
    }
    public function studentId($reqNr)
    {
        return $this->state([
            'student_id'     => $reqNr
        ]);
    }
    public function courseId($reqNr)
    {
        return $this->state([
            'course_id'     => $reqNr
        ]);
    }
}
