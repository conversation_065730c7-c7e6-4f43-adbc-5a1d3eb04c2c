<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Domain>
 */
class DomainFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->faker->randomNumber(),
            'domain_name' => 'scolavisa.eu',
            'language' => 'nl', // (or en)
            'lookup_url' => 'not set',
            'website_url' => $this->faker->url,
            'logo_url' => $this->faker->url,
            'rates_conditions_url' => $this->faker->url,
            'privacy_url' => $this->faker->url,
            'name' => $this->faker->name,
            'address1' => $this->faker->address,
            'address2' => $this->faker->address,
            'zip' => $this->faker->postcode,
            'city' => $this->faker->city,
            'telephone' => $this->faker->phoneNumber,
            'email' => $this->faker->email,
            'adult_threshold' => 21,
            'contact_person_name' => $this->faker->name,
            'course_tax_rate' => 21,
            'default_password' => 'password',
            'schedule_threshold' => 60,
            'status' => 'pro',
            'trial_end_date' => $this->faker->dateTime(),
            'warn_before_birthday' => 7, //(default: 10)
            'warn_before_adult' => 7, //(default: 30)
            'domaintoken' => $this->faker->uuid,
            'allowed_ip_addresses' => '127.0.0.1, 123.45.67.890',
            'broadcast_colors' => '#098497,#625999,#000000',
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime(),
        ];
    }
}
