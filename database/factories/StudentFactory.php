<?php

namespace Database\Factories;

use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class StudentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Student::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $firstname = $this->faker->firstname;
        $lastname = $this->faker->lastname;
        $name = "$firstname $lastname";

        return [
            'id'            => $this->faker->unique()->randomNumber(),
            'domain_id'     => 9,
            'firstname'     => $firstname,
            'lastname'      => $lastname,
            'name'          => $name,
            'date_of_birth' => $this->faker->dateTimeBetween(
                $startDate='-60 years', $endDate='-6 years'
            )->format('Y-m-d'),
            'status'        => 'active'
        ];
    }
    public function id($reqNr)
    {
        return $this->state([
            'id'                    => $reqNr
        ]);
    }
}
