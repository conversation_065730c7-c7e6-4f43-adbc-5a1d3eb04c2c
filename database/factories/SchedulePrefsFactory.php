<?php

namespace Database\Factories;

use App\Models\SchedulePrefs;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class SchedulePrefsFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SchedulePrefs::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'id'            => $this->faker->unique()->randomNumber(),
            'student_id'    => $this->faker->unique()->randomNumber(),
            'monday'        => '09:00|09:30|10:00|10:30|11:00|11:30',
            'tuesday'       => '09:00|09:30|10:00|10:30|11:00|11:30',
        ];
    }

    public function id($reqId)
    {
        return $this->state([
            'id'    => $reqId
        ]);
    }
    public function studentId($reqId)
    {
        return $this->state([
            'student_id'    => $reqId
        ]);
    }
}
