<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DateException>
 */
class DateExceptionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startTime = $this->faker->dateTime();
        $endTime = $this->faker->dateTimeBetween(
            $startTime->format('Y-m-d H:i:s') .' +1 hour',
            $startTime->format('Y-m-d H:i:s').' +2 days'
        );
        return [
            'id' => $this->faker->randomNumber(),
            'domain_id' => $this->faker->randomNumber(),
            'schoolyear_id' => $this->faker->randomNumber(),
            'location_id' => $this->faker->randomNumber(),
            'datetime_start' => $startTime,
            'datetime_end' => $endTime,
            'reason' => $this->faker->text(),
            'plan_blocking' => $this->faker->boolean(),
            'exclude_form_alerts' => $this->faker->boolean(),
            'calendar_color' => $this->faker->hexColor(),
            'detail_url' => $this->faker->url(),
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime()
        ];
    }
}
