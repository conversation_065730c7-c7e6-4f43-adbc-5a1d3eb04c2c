<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RecurrenceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('recurrenceoptions')->insert([[
            'id'            => 1,
            'domain_id'     => 9,
            'description'   => '1 uur per dag en eindigt na 1 herhaling',
            'nr_of_times'   => '1',
            'timeunit'      => 'hour',
            'per_interval'  => 'day',
            'ends_after_nr_of_occurrences'  => '1'
        ], [
            'id'            => 2,
            'domain_id'     => 9,
            'description'   => '1.5 uur per twee weken tot uitschrijven (doorlopend)',
            'nr_of_times'   => '1.5',
            'timeunit'      => 'hour',
            'per_interval'  => 'two weeks',
            'ends_after_nr_of_occurrences'  => NULL
        ], [
            'id'            => 3,
            'domain_id'     => 9,
            'description'   => '1.5 uur per week tot uitschrijven (doorlopend)',
            'nr_of_times'   => '1.5',
            'timeunit'      => 'hour',
            'per_interval'  => 'week',
            'ends_after_nr_of_occurrences'  => NULL
        ], [
            'id'            => 4,
            'domain_id'     => 9,
            'description'   => '1 uur per week tot uitschrijven (doorlopend)',
            'nr_of_times'   => '1',
            'timeunit'      => 'hour',
            'per_interval'  => 'week',
            'ends_after_nr_of_occurrences'  => NULL
        ], [
            'id'            => 5,
            'domain_id'     => 9,
            'description'   => '1 uur per twee weken tot uitschrijven (doorlopend)',
            'nr_of_times'   => '1',
            'timeunit'      => 'hour',
            'per_interval'  => 'two weeks',
            'ends_after_nr_of_occurrences'  => NULL
        ], [
            'id'            => 6,
            'domain_id'     => 9,
            'description'   => '1.5 uur per week en eindigt na 4 herhalingen',
            'nr_of_times'   => '1.5',
            'timeunit'      => 'hour',
            'per_interval'  => 'week',
            'ends_after_nr_of_occurrences'  => 4
        ], [
            'id'            => 7,
            'domain_id'     => 9,
            'description'   => '2.5 uur per twee weken tot uitschrijven (doorlopend)',
            'nr_of_times'   => '2.5',
            'timeunit'      => 'hour',
            'per_interval'  => 'two weeks',
            'ends_after_nr_of_occurrences'  => NULL
        ]]);
    }
}
