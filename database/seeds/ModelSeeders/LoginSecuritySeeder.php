<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LoginSecuritySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // insert into loginsecurities (id, user_id, google2fa_enable, google2fa_secret, created_at, updated_at)
        // values (1,1, 1, 'UX7E3PXBUKZE4VIB', now(), now());

        $data = [
            [
                'id' => 1,
                'user_id' => 1,
                'google2fa_enable' => 1,
                'google2fa_secret' => 'UX7E3PXBUKZE4VIB',
                'created_at' => now(),
                'updated_at' => now()
            ],[
                'id' => 2,
                'user_id' => 3,
                'google2fa_enable' => 1,
                'google2fa_secret' => '6F6D7WBQ5NWP423N',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];
        DB::table('loginsecurities')->insert($data);
    }
}
