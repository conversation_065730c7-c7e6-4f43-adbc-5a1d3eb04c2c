<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LibrarySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('libraries')->insert([
            [
                'id'                        => 1,
                'domain_id'                 => 9,
                'tutor_id'                  => 1,
                'label'                     => 'Kattenverzorging',
                'share_with_whole_school'   => true,
            ],
            [
                'id'                        => 2,
                'domain_id'                 => 9,
                'tutor_id'                  => 1,
                'label'                     => 'Medicatie',
                'share_with_whole_school'   => false
            ],
            [
                'id'                        => 3,
                'domain_id'                 => 9,
                'tutor_id'                  => 1,
                'label'                     => 'Katten paradijs',
                'share_with_whole_school'   => false
            ],
            [
                'id'                        => 4,
                'domain_id'                 => 9,
                'tutor_id'                  => 1,
                'label'                     => 'Bedrijfvoering',
                'share_with_whole_school'   => false
            ]
        ]);

        // create a document for each library
        DB::table('documents')->insert([
            [
                'id'            => 1,
                'domain_id'     => 9,
                'type'          => 'file',
                'content_type'  => 'application/pdf',
                'label'         => 'Kattenverzorging',
                'file_name'     => '9_55228899.pdf',
                'original_name' => 'kattenverzorging.pdf',
                'url'           => '',
                'description'   => 'Dit document bevat alle informatie over de verzorging van katten.',
                'crc'           => '1234567890'
            ],
            [
                'id'            => 2,
                'domain_id'     => 9,
                'type'          => 'file',
                'content_type'  => 'application/pdf',
                'label'         => 'Medicatie',
                'file_name'     => '9_8754698.pdf',
                'original_name' => 'medicatie.pdf',
                'url'           => '',
                'description'   => 'Dit document bevat alle informatie over het toedienen van medicatie.',
                'crc'           => '0987654321'
            ],
            [
                'id'            => 3,
                'domain_id'     => 9,
                'type'          => 'file',
                'content_type'  => 'application/pdf',
                'label'         => 'Parasieten',
                'file_name'     => '9_1234567.pdf',
                'original_name' => 'parasieten.pdf',
                'url'           => '',
                'description'   => 'Dit document bevat alle informatie over parasieten.',
                'crc'           => '1234567899'
            ],
            [
                'id'            => 4,
                'domain_id'     => 9,
                'type'          => 'file',
                'content_type'  => 'application/pdf',
                'label'         => 'Huisregels',
                'file_name'     => '9_9876543.pdf',
                'original_name' => 'huisregels.pdf',
                'url'           => '',
                'description'   => 'Dit document bevat de huisregels van Kattenparadijs.',
                'crc'           => '1234567898'
            ],
            [
                'id'            => 5,
                'domain_id'     => 9,
                'type'          => 'url',
                'content_type'  => 'text/html',
                'label'         => 'Algemene voorwaarden',
                'file_name'     => '',
                'original_name' => '',
                'url'           => 'https://www.kattenparadijs.nl/algemene-voorwaarden',
                'description'   => 'Dit document bevat de algemene voorwaarden van Kattenparadijs.',
                'crc'           => '1234567897'
            ]
        ]);

        // couple documents to libraries
        DB::table('library_document')->insert([
            [
                'library_id'    => 1,
                'document_id'   => 1
            ],
            [
                'library_id'    => 2,
                'document_id'   => 2
            ],
            [
                'library_id'    => 2,
                'document_id'   => 3
            ],
            [
                'library_id'    => 2,
                'document_id'   => 1  // reuse document 1 in two libraries
            ],
            [
                'library_id'    => 3,
                'document_id'   => 4
            ],
            [
                'library_id'    => 3,
                'document_id'   => 5
            ]
        ]);

        // share library 2 with studentgroup 10 (Spinners)
        DB::table('library_student')->insert([
            [
                'library_id'   => 2,
                'student_id'   => 10
            ]
        ]);
        // share lib 2 with student 8
        DB::table('library_student')->insert([
            [
                'library_id'   => 2,
                'student_id'   => 8
            ]
        ]);
        // share lib 4 with course 1
        DB::table('library_course')->insert([
            [
                'library_id'   => 4,
                'course_id'    => 1
            ]
        ]);
    }
}
