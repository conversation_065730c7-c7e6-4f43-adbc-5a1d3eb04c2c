<?php

namespace Database\Seeders\ModelSeeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DateExceptionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // School year 1 = previous - current year
        // School year 2 = current - next year

        // Add holidays in the first half of the year and on in the second half of the year
        $currentDate = Carbon::now();
        $currentYear = $currentDate->year;
        $nextYear = $currentYear + 1;
        $previousYear = $currentYear - 1;
        $schoolyear1 = 1;
        $schoolyear2 = 2;

        // This is fine if we are in the second semester (month 1-6)
        // but if we are in the first semester (month 9-12) or during summer break (month 7 and 8)
        // we need to adjust the years
        if ($currentDate->month > 6) {
            $currentYear = $currentYear + 1;
            $nextYear = $nextYear + 1;
            $previousYear = $previousYear + 1;
            $schoolyear1 = 2;
            $schoolyear2 = 3;
        }
        // time start: 00:00:00
        // time end: 23:59:59
        // makes it 'whole day'
        DB::table('date_exceptions')->insert([
            [
                'id' => 2,
                'schoolyear_id' => $schoolyear2,
                'domain_id' => 9,
                'datetime_start' => Carbon::parse($nextYear . '-12-25')->previous(Carbon::FRIDAY)->setTime(0, 0),
                'datetime_end' => Carbon::parse(($nextYear +1) . '-01-07')->next(Carbon::THURSDAY)->setTime(23, 59, 59),
                'reason' => 'Kerstvakantie 2',
                'plan_blocking' => 1,
            ],[
                'id' => 1,
                'schoolyear_id' => $schoolyear1,
                'domain_id' => 9,
                'datetime_start' => Carbon::parse($previousYear . '-12-25')->previous(Carbon::FRIDAY)->setTime(0, 0),
                'datetime_end' => Carbon::parse($currentYear . '-01-07')->next(Carbon::THURSDAY)->setTime(23, 59, 59),
                'reason' => 'Kerstvakantie 1',
                'plan_blocking' => 1,
            ],
        ]);
        // Add spring holidays in both school years in the week of 1st of may
        DB::table('date_exceptions')->insert([
            [
                'id' => 3,
                'schoolyear_id' => $schoolyear2,
                'domain_id' => 9,
                'datetime_start' => Carbon::parse(($nextYear +1) . '-05-01')->previous(Carbon::FRIDAY)->setTime(0, 0),
                'datetime_end' => Carbon::parse(($nextYear +1) . '-05-08')->next(Carbon::THURSDAY)->setTime(23, 59, 59),
                'reason' => 'Voorjaarsvakantie 2',
                'plan_blocking' => 1,
            ],[
                'id' => 4,
                'schoolyear_id' => $schoolyear1,
                'domain_id' => 9,
                'datetime_start' => Carbon::parse($currentYear . '-05-01')->previous(Carbon::FRIDAY)->setTime(0, 0),
                'datetime_end' => Carbon::parse($currentYear . '-05-08')->next(Carbon::THURSDAY)->setTime(23, 59, 59),
                'reason' => 'Voorjaarsvakantie 1',
                'plan_blocking' => 1,
            ],
        ]);
        // Add workshop, which is not blocking and not whole day
        DB::table('date_exceptions')->insert([
            [
                'id' => 5,
                'schoolyear_id' => $schoolyear2,
                'domain_id' => 9,
                'datetime_start' => Carbon::parse(($nextYear +1) . '-04-01')->previous(Carbon::FRIDAY)->setTime(10, 0),
                'datetime_end' => Carbon::parse(($nextYear +1) . '-04-01')->next(Carbon::THURSDAY)->setTime(11, 0),
                'reason' => 'Workshop 2',
                'plan_blocking' => 1,
            ],[
                'id' => 6,
                'schoolyear_id' => $schoolyear1,
                'domain_id' => 9,
                'datetime_start' => Carbon::parse($currentYear . '-04-01')->previous(Carbon::FRIDAY)->setTime(10, 0),
                'datetime_end' => Carbon::parse($currentYear . '-04-01')->next(Carbon::THURSDAY)->setTime(11, 0),
                'reason' => 'Workshop 1',
                'plan_blocking' => 1,
            ],
        ]);
    }
}
