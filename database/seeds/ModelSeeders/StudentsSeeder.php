<?php

namespace Database\Seeders\ModelSeeders;

use App\Models\SchedulePrefs;
use Faker\Factory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;


class StudentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Factory::create();
        for($s = 1; $s < 10; $s++) {
            $firstname = $faker->firstname;
            $lastname = $faker->lastname;
            $name = "$firstname $lastname";
            DB::table('students')->insert([
                'id'            => $s,
                'domain_id'     => 9,
                'firstname'     => $firstname,
                'lastname'      => $lastname,
                'name'          => $name,
                'date_of_birth' => $faker->dateTimeBetween(
                    $startDate='-60 years', $endDate='-6 years'
                )->format('Y-m-d'),
                'status'        => 'active',
                'accesstoken'   => str_replace("-", "", substr($faker->uuid, 0, 25)),
                'has_access'    => 0
            ]);
            DB::table('studentcontacts')->insert([[
                'student_id'    => $s,
                'contacttype'  => 'email',
                'label'         => '',
                'value'         => $faker->email
            ],[
                'student_id'    => $s,
                'contacttype'  => 'telephone',
                'label'         => '',
                'value'         => $faker->tollFreePhoneNumber
            ]]);
        }
        // add a log entry
        DB::table('logentries')->insert([
            'student_id'        => 5,
            'entry'             => $faker->sentence(25) . '<br>' . $faker->sentence(25),
            'created_at'        => date('Y-m-d H:i:s')
        ]);
        // add web access to student 1 (has_access = 1)
        DB::table('students')->where('id', 1)->update(['has_access' => 1]);

        // students no longer active
        for($s = 15; $s < 20; $s++) {
            $firstname = $faker->firstname;
            $lastname = $faker->lastname;
            $name = "$firstname $lastname";
            DB::table('students')->insert([
                'id'            => $s,
                'domain_id'     => 9,
                'firstname'     => $firstname,
                'lastname'      => $lastname,
                'name'          => $name,
                'date_of_birth' => $faker->dateTimeBetween($startDate='-60 years', $endDate='-6 years')->format('Y-m-d'),
                'status'        => 'active'
            ]);
            DB::table('studentcontacts')->insert([[
                'student_id'    => $s,
                'contacttype'  => 'email',
                'label'         => '',
                'value'         => $faker->email
            ],[
                'student_id'    => $s,
                'contacttype'  => 'telephone',
                'label'         => '',
                'value'         => $faker->tollFreePhoneNumber
            ]]);
        }

        // student 1 has filled in their schedulepref today
        $date_raw = date("r");
        $yesterday = date('Y-m-d H:i:s', strtotime('-1 day', strtotime($date_raw)));
        $todayMinus1month = date('Y-m-d H:i:s', strtotime('-1 month', strtotime($date_raw)));
        $todayMinus5month = date('Y-m-d H:i:s', strtotime('-5 month', strtotime($date_raw)));
        SchedulePrefs::create([
            'id'            => 1,
            'student_id'    => 1,
            'monday'        => '09:00|09:30|10:00|10:30|11:00|11:30',
            'tuesday'       => '09:00|09:30|10:00|10:30|11:00|11:30',
            'created_at'    => $yesterday,
            'updated_at'    => $yesterday
        ]);
        // student 6 has filled in their schedulepref a month ago
        SchedulePrefs::create([
            'id'            => 2,
            'student_id'    => 6,
            'monday'        => '11:00|11:30|12:00|12:30',
            'tuesday'       => '19:00|19:30|20:00|20:30|21:00|21:30',
            'created_at'    => $todayMinus1month,
            'updated_at'    => $todayMinus1month
        ]);
        // student 7 and 8 has filled in their schedulepref 5 months ago
        SchedulePrefs::create([
            'id'            => 3,
            'student_id'    => 7,
            'monday'        => '11:00|11:30|12:00|12:30',
            'tuesday'       => '19:00|19:30|20:00|20:30|21:00|21:30',
            'wednesday'     => '9:00|9:30|10:00|10:30|11:00|11:30',
            'created_at'    => $todayMinus5month,
            'updated_at'    => $todayMinus5month
        ]);
        SchedulePrefs::create([
            'id'            => 4,
            'student_id'    => 8,
            'monday'        => '11:00|11:30|12:00|12:30',
            'tuesday'       => '19:00|19:30|20:00|20:30|21:00|21:30',
            'friday'        => '9:00|9:30|10:00|10:30|11:00|11:30|12:00|12:30|13:00|13:30|14:00',
            'created_at'    => $todayMinus5month,
            'updated_at'    => $todayMinus5month
        ]);

    }
}
