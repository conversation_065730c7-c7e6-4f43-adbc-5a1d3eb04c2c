<?php

namespace Database\Seeders\ModelSeeders;

use Faker\Factory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RegistrationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Factory::create();

        DB::table('course_student')->insert([[
            'id'            => 1,
            'course_id'     => 6,       // trial course, recurrence: 1
            'student_id'    => 4,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 11,
            'course_id'     => 4,       // regular course, recurrence: n, once every two weeks
            'student_id'    => 7,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 12,
            'course_id'     => 5,       // regular course, recurrence: n, once every two weeks
            'student_id'    => 9,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 9,
            'course_id'     => 1,       // regular course, recurrence: n, every week
            'student_id'    => 1,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 2,
            'course_id'     => 1,       // regular course, recurrence: n, every week
            'student_id'    => 3,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 3,       // this is a student in the group
            'course_id'     => 1,       // regular course, recurrence: n, every week
            'student_id'    => 5,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 4,       // this is a second student in the group
            'course_id'     => 1,       // regular course, recurrence: n
            'student_id'    => 6,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 5,       // this the the reg for the group (10)
            'course_id'     => 1,       // regular course, recurrence: n, every week
            'student_id'    => 10,      // coursegroup: De Spinners, no sign_code
            'start_date'    => date('Y-m-d'),
            'sign_code'     => ''
        ], [
            'id'            => 6,       // events will be in the even weeks
            'course_id'     => 4,       // regular course, recurrence: n, once every two weeks
            'student_id'    => 2,       // has no other course registrations
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 7,       // this is individual
            'course_id'     => 2,       // regular course, recurrence: n, every week
            'student_id'    => 5,
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ], [
            'id'            => 8,       // events will be in the odd weeks
            'course_id'     => 4,       // two week interval course: medicijnen
            'student_id'    => 8,       // has no other course registrations
            'start_date'    => date('Y-m-d'),
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ]]);

        // student no longer active
        $date_raw = date("r");
        $todayMinus1Year = date('Y-m-d H:i:s', strtotime('-1 year', strtotime($date_raw)));
        $todayMinus6Month = date('Y-m-d H:i:s', strtotime('-6 month', strtotime($date_raw)));
        DB::table('course_student')->insert([[
            'id'            => 10,
            'course_id'     => 1,
            'student_id'    => 15,
            'start_date'    => $todayMinus1Year,
            'end_date'      => $todayMinus6Month,
            'sign_code'     => substr($faker->unique()->uuid, 0, 30),
        ]]);
    }
}
