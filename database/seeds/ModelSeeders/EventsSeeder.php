<?php

namespace Database\Seeders\ModelSeeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EventsSeeder extends Seeder
{
    /**
     * Create a planning for an individual student and a group of students
     *
     * @return void
     */
    public function run()
    {
        // Test events
        // First we need to know the current school year number, this depends on the current date.
        // School year 1 = previous - current year
        // School year 2 = current - next year
        // note: dates are not inmmutable, so we need to clone them
        $currentDate = Carbon::now();
        $currentYear = $currentDate->year;
        $nextYear = $currentYear + 1;
        $previousYear = $currentYear - 1;

        $previousYearStartDate = Carbon::parse($previousYear . '-09-01')->next(Carbon::FRIDAY);
        $currentYearEndDate = Carbon::parse($currentYear . '-06-30')->previous(Carbon::FRIDAY);
        $currentYearStartDate = Carbon::parse($currentYear . '-09-01')->next(Carbon::FRIDAY);
        $nextYearEndDate = Carbon::parse($nextYear . '-06-30')->previous(Carbon::FRIDAY);

        $this->addPlanDates($previousYearStartDate, $currentYearEndDate, 1, 2, "16:30:00");
        $this->addPlanDates($currentYearStartDate, $nextYearEndDate, 5, 2, "16:30:00");

        // Events for timetable 6: group "spinners", next year (useful if we are between school years)
        $currentYearStartDate = Carbon::parse($currentYear . '-09-01')->next(Carbon::FRIDAY);
        $currentYear5WeeksIn = Carbon::parse($currentYearStartDate)->addWeeks(5);
        $currentYearStartDate = Carbon::parse($currentYear . '-09-01')->next(Carbon::FRIDAY);
        $this->addPlanDates($currentYearStartDate, $currentYear5WeeksIn, 6, 2, "11:00:00");
    }

    function addPlanDates(Carbon $startDate, Carbon $endDate, $timetableId, $tutorId, $plantime) {
        Log::debug("Adding events for timetable $timetableId, tutor $tutorId, from $startDate to $endDate");
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $planDate = $currentDate->format('Y-m-d');
            DB::table('events')->insert([
                'location_id'       => 1,
                'tutor_id'          => $tutorId,
                'caluniqueid'       => '12345ABCD',
                'timetable_id'      => $timetableId,
                'datetime'          => $planDate . " " . $plantime,
                'original_datetime' => $planDate . " " . $plantime,
                'sequence'          => 0,
                'timespan'          => '60 minutes'
            ]);
            $currentDate = $currentDate->addWeek();
        }
    }
}
