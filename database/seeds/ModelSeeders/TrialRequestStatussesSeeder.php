<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TrialRequestStatussesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('trialrequeststatuses')->insert([
            ['id' => 1, 'domain_id' => 9, 'description' => 'Nieuw', 'needs_admin_action' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 2, 'domain_id' => 9, 'description' => 'Leerling aangemaakt, les nog inplannen', 'needs_admin_action' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 3, 'domain_id' => 9, 'description' => 'On hold: geen docent/locatie beschikbaar', 'needs_admin_action' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 4, 'domain_id' => 9, 'description' => 'On hold: geen leerlinggroep beschikbaar', 'needs_admin_action' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 5, 'domain_id' => 9, 'description' => 'On hold: leerling geeft GO in toekomst', 'needs_admin_action' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 6, 'domain_id' => 9, 'description' => 'On hold: uitnodigen voor communicatiekanalen', 'needs_admin_action' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 7, 'domain_id' => 9, 'description' => 'Succesvol afgerond, leerling ingevoerd, les ingepland, communicatie geregeld', 'needs_admin_action' => 0, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 8, 'domain_id' => 9, 'description' => 'Geannuleerd, leerling gegeven wel opslaan', 'needs_admin_action' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['id' => 9, 'domain_id' => 9, 'description' => 'Geannuleerd', 'needs_admin_action' => 0, 'created_at' => now(), 'updated_at' => now()],
        ]);
    }
}
