<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MailTemplatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('mailtemplates')->insert([
            'id'        => 1,
            'domain_id' => 9,
            'label'     => 'Standaard ondertekenverzoek',
            'targets'   => 'b',
            'content'   => "<p>Hello %studentfirstname%</p><p>Please go here: %registrationsignlink% to sign your registration!</p><p>All the best,</p><p>%schoolcontactperson%<br><br>%schoolname%<br>T: %schooltelephone%<br>W: %schoolwebsite%</p><p>%schoollogo|width='100'%</p>",
            'created_at' => now(),
            'updated_at' => now()
        ]);
        DB::table('mailtemplates')->insert([
            'id'        => 2,
            'domain_id' => 9,
            'label'     => 'Leerling uitnodiging plan voorkeuren',
            'targets'   => 'c',
            'content'   => "<h4>Beste %studentfirstname%,</h4><br/>Graag nodig ik je uit om in te vullen op welke dagen je beschikbaar bent voor je les bij %schoolname%.<br/>Als je vorig jaar ook al les had, dan kun je aangeven of je de huidige lestijd graag wilt houden.<br/>Volg deze link om je beschikbaarheid in te vullen: <h3>%studentaccesslink%</h3>Met vriendelijke groet,<br/><br/>%schoolcontactperson%<br/>%schoollogo|width='100px'%<br/>tel: %schooltelephone%<br/>web: %schoolwebsite%",
            'created_at' => now(),
            'updated_at' => now()
        ]);
        DB::table('mailtemplates')->insert([
            'id'        => 3,
            'domain_id' => 9,
            'label'     => 'Request student schedule preferences',
            'targets'   => 'c',
            'content'   => "<h4>Dear %studentfirstname%,</h4><p><br>Please let us know which days and times would suite you best for your lessons at %schoolname%.<br>If you had lessons previous year, you can request to keep your currently scheduled time. &nbsp;</p><p>Please follow this link to enter your availabillity:</p><h3>%studentaccesslink%</h3><p>All the best from our team!<br><br>%schoolcontactperson%<br>%schoollogo|width='100px'%<br>tel: %schooltelephone%<br>web: %schoolwebsite%</p>",
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
