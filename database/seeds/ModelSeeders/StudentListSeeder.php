<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StudentListSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('studentlists')->insert([
            [
                'id'            => 1,
                'domain_id'     => 9,
                'name'          => 'Leerlingenuitvoering',
                'hexcolor'      => 'f7ff1d',
                'remarks'       => 'Leerlingen die hebben aangegeven dat ze deel willen nemen aan de komende leerlingenuitvoering.',
                'created_at'    => now(),
                'updated_at'    => now()
            ],
            [
                'id'            => 2,
                'domain_id'     => 9,
                'name'          => 'Inhaalweek',
                'hexcolor'      => '1dffff',
                'remarks'       => 'Leerlingen die een les in moeten halen in de inhaalweek.',
                'created_at'    => now(),
                'updated_at'    => now()
            ]
        ]);

        // put some students on the lists
        DB::table('student_studentlist')->insert([
            [
                'studentlist_id'    => 1,
                'student_id'        => 2,
                'created_at'        => now(),
                'updated_at'        => now()
            ],
            [
                'studentlist_id'    => 1,
                'student_id'        => 5,
                'created_at'        => now(),
                'updated_at'        => now()
            ],
            [
                'studentlist_id'    => 2,
                'student_id'        => 5,
                'created_at'        => now(),
                'updated_at'        => now()
            ]
        ]);
    }
}
