<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AttendanceOptionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [[
            "domain_id" => 9,
            "label" => "reguliere les",
            "action_tutor" => "",
            "created_at" => Carbon::now(),
            "updated_at" => Carbon::now()
        ], [
            "domain_id" => 9,
            "label" => "absent met tijdig afmelden",
            "action_tutor" => "videoles maken",
            "created_at" => Carbon::now(),
            "updated_at" => Carbon::now()
        ], [
            "domain_id" => 9,
            "label" => "te laat afgemeld",
            "action_tutor" => null,
            "created_at" => Carbon::now(),
            "updated_at" => Carbon::now()
        ], [
            "domain_id" => 9,
            "label" => "niet op komen dagen",
            "action_tutor" => "leerling bellen",
            "created_at" => Carbon::now(),
            "updated_at" => Carbon::now()
        ]];
        DB::table("attendanceoptions")->insert($data);
    }
}
