<?php

namespace Database\Seeders\ModelSeeders;

use Faker\Factory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StudentgroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Factory::create();
        // create group 1
        DB::table('students')->insert([
            'id'        => 10,
            'domain_id'     => 9,
            'firstname'     => '-',
            'lastname'      => "De Spinners",
            'name'          => "- <PERSON>",
            'date_of_birth' => '1800-01-01',
            'status'        => 'active'
        ]);
        // create group 2
        DB::table('students')->insert([
            'id'        => 11,
            'domain_id'     => 9,
            'firstname'     => '-',
            'lastname'      => "<PERSON>",
            'name'          => "- <PERSON>",
            'date_of_birth' => '1800-01-01',
            'status'        => 'active'
        ]);

        // subscribe students to populate the group
        // these students need to be subscripbed to the same course or a course leading to the same course
        // see registration Seeder
        DB::table('student_studentgroup')->insert([[
            'student_id'        => 5,
            'studentgroup_id'   => 10,
            'start_date'        => date('Y-m-d')
        ],[
            'student_id'        => 6,
            'studentgroup_id'   => 10,
            'start_date'        => date('Y-m-d')
        ],[
            'student_id'        => 4,   // trial course participant
            'studentgroup_id'   => 10,
            'start_date'        => date('Y-m-d')
        ]]);
        // add a log entry
        DB::table('logentries')->insert([
            'student_id'        => 10,
            'entry'             => $faker->sentence(25),
            'created_at'        => date('Y-m-d H:i:s')
        ]);


    }
}
