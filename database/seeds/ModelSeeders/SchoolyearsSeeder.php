<?php
namespace Database\Seeders\ModelSeeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;


class SchoolyearsSeeder extends Seeder {
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run() {

        $currentYear = date('Y');
        $nextYear = intval(date('Y')) + 1;
        $previousYear = intval(date('Y')) - 1;

        // whichever is the date, we always create a 'current' schoolyear
	    DB::table('schoolyears')->insert([[
            "id"			=> '1',
            "domain_id"     => '9',
            "label" 		=> $previousYear . '-' . $currentYear,
            "start_year" 	=> $previousYear,
            "end_year"		=> $currentYear,
            "start_date"    => $previousYear . '-09-01',
            "end_date"      => $currentYear . '-06-30'
        ],[
            "id"			=> '2',
            "domain_id"     => '9',
            "label" 		=> $currentYear . '-' . $nextYear,
            "start_year" 	=> $currentYear,
            "end_year"		=> $nextYear,
            "start_date"    => $currentYear . '-09-01',
            "end_date"      => $nextYear . '-06-30'
        ],[
            "id"			=> '3',
            "domain_id"     => '9',
            "label" 		=> $nextYear . '-' . ($nextYear + 1),
            "start_year" 	=> $nextYear,
            "end_year"		=> ($nextYear + 1),
            "start_date"    => $nextYear . '-09-01',
            "end_date"      => ($nextYear + 1) . '-06-30'
        ]]);
	}
}
