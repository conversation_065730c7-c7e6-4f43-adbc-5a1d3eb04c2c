<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CoursesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('courses')->insert([[
            'id'                    => 1,
            'domain_id'             => 9,
            'coursegroup_id'        => 1,   // Verzorging
            'recurrenceoption_id'   => 4,   // 1 uur per week tot uitschrijven (doorlopend)
            'is_trial_course'       => null,
            'name'                  => 'Voeding',
            'price_invoice'         => '35.00',
            'price_ex_tax'          => '28.93',
            'price_ex_tax_sub_adult'=> '29.00',
            'price_is_per'          => 'week',
            'tax_rate'              => '21',
            'group_size_min'        => 2,
            'group_size_max'        => 10
        ],[
            'id'                    => 2,
            'domain_id'             => 9,
            'coursegroup_id'        => 1,   // Verzorging
            'recurrenceoption_id'   => 4,   // 1 uur per week tot uitschrijven (doorlopend)
            'is_trial_course'       => null,
            'name'                  => 'Vachtverzorging',
            'price_invoice'         => '35.00',
            'price_ex_tax'          => '28.93',
            'price_ex_tax_sub_adult'=> '29.00',
            'price_is_per'          => 'week',
            'tax_rate'              => '21',
            'group_size_min'        => 1,
            'group_size_max'        => 1
        ],[
            'id'                    => 3,
            'domain_id'             => 9,
            'coursegroup_id'        => 2,   // Proefles
            'recurrenceoption_id'   => 1,   // 1 uur per dag en eindigt na 1 herhaling
            'is_trial_course'       => 1,
            'name'                  => 'Vachtverzorging',
            'price_invoice'         => '0',
            'price_ex_tax'          => '0',
            'price_ex_tax_sub_adult'=> '0',
            'price_is_per'          => 'week',
            'tax_rate'              => '21',
            'group_size_min'        => 1,
            'group_size_max'        => 1
        ],[
            'id'                    => 6,
            'domain_id'             => 9,
            'coursegroup_id'        => 2,   // Proefles
            'recurrenceoption_id'   => 1,   // 1 uur per dag en eindigt na 1 herhaling
            'is_trial_course'       => 1,
            'name'                  => 'Voeding',
            'price_invoice'         => '0',
            'price_ex_tax'          => '0',
            'price_ex_tax_sub_adult'=> '0',
            'price_is_per'          => 'week',
            'tax_rate'              => '21',
            'group_size_min'        => 1,
            'group_size_max'        => 1
        ],[
            'id'                    => 4,
            'domain_id'             => 9,
            'coursegroup_id'        => 1,   // Verzorging
            'recurrenceoption_id'   => 5,   // 1 uur per twee weken doorlopend
            'is_trial_course'       => null,
            'name'                  => 'Medicijnen',
            'price_invoice'         => '28.00',
            'price_ex_tax'          => '23.14',
            'price_ex_tax_sub_adult'=> '23',
            'price_is_per'          => 'month',
            'tax_rate'              => '21',
            'group_size_min'        => 1,
            'group_size_max'        => 1
        ],[
            'id'                    => 5,
            'domain_id'             => 9,
            'coursegroup_id'        => 1,   // Verzorging
            'recurrenceoption_id'   => 7,   // 2.5 uur per twee weken doorlopend
            'is_trial_course'       => null,
            'name'                  => 'Kattencafe',
            'price_invoice'         => '28.00',
            'price_ex_tax'          => '23.14',
            'price_ex_tax_sub_adult'=> '23',
            'price_is_per'          => 'month',
            'tax_rate'              => '21',
            'group_size_min'        => 1,
            'group_size_max'        => 1
        ]]);
    }
}
