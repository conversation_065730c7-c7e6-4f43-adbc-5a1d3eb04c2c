<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TimetableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // reg id 7 is individual course
        // reg id 5 is the group
        // loc 1 (individual) and loc 2 (group)
        DB::table('timetables')->insert([[
            'id'                => 1,
            'schoolyear_id'     => 1,
            'course_student_id' => 7    // individual course reg (1 uur per week tot uitschrijven (doorlopend))
        ], [
            'id'                => 2,
            'schoolyear_id'     => 1,
            'course_student_id' => 5    // group course reg (1 uur per week tot uitschrijven (doorlopend))
        ], [
            'id'                => 3,
            'schoolyear_id'     => 1,
            'course_student_id' => 6    // individual course reg (1 uur per twee weken tot uitschrijven (doorlopend))
        ], [
            'id'                => 4,
            'schoolyear_id'     => 1,
            'course_student_id' => 8    // individual course reg (1 uur per twee weken tot uitschrijven (doorlopend))
        ],[
            'id'                => 5,
            'schoolyear_id'     => 2,
            'course_student_id' => 7    // individual course reg (1 uur per week tot uitschrijven (doorlopend))
        ], [
            'id'                => 6,
            'schoolyear_id'     => 2,
            'course_student_id' => 5    // group course reg (1 uur per week tot uitschrijven (doorlopend))
        ], [
            'id'                => 7,
            'schoolyear_id'     => 2,
            'course_student_id' => 6    // individual course reg (1 uur per twee weken tot uitschrijven (doorlopend))
        ], [
            'id'                => 8,
            'schoolyear_id'     => 2,
            'course_student_id' => 8    // individual course reg (1 uur per twee weken tot uitschrijven (doorlopend))
        ], [
            'id'                => 9,
            'schoolyear_id'     => 1,
            'course_student_id' => 10    // individual course reg (1 uur per week tot uitschrijven (doorlopend))
        ], [
            'id'                => 10,
            'schoolyear_id'     => 2,
            'course_student_id' => 10    // individual course reg (1 uur per week tot uitschrijven (doorlopend))
        ],[
            'id'                => 11,
            'schoolyear_id'     => 3,
            'course_student_id' => 5    // group course reg (1 uur per week tot uitschrijven (doorlopend))
        ],[
            'id'                => 12,
            'schoolyear_id'     => 1,
            'course_student_id' => 3    // group course reg (1 uur per week tot uitschrijven (doorlopend))
        ],[
            'id'                => 13,
            'schoolyear_id'     => 2,
            'course_student_id' => 3    // group course reg (1 uur per week tot uitschrijven (doorlopend))
        ],[
            'id'                => 14,
            'schoolyear_id'     => 3,
            'course_student_id' => 3    // group course reg (1 uur per week tot uitschrijven (doorlopend))
        ]]);
    }
}
