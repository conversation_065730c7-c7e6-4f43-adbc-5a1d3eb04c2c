<?php
namespace Database\Seeders;
use Database\Seeders\ModelSeeders\AttendanceOptionsSeeder;
use Database\Seeders\ModelSeeders\CoursegroupSeeder;
use Database\Seeders\ModelSeeders\CoursesSeeder;
use Database\Seeders\ModelSeeders\DateExceptionsSeeder;
use Database\Seeders\ModelSeeders\DefaultChecklistSeeder;
use Database\Seeders\ModelSeeders\DomainSeeder;
use Database\Seeders\ModelSeeders\EventsSeeder;
use Database\Seeders\ModelSeeders\LibrarySeeder;
use Database\Seeders\ModelSeeders\LocationsSeeder;
use Database\Seeders\ModelSeeders\MailTemplatesSeeder;
use Database\Seeders\ModelSeeders\LoginSecuritySeeder;
use Database\Seeders\ModelSeeders\RecurrenceSeeder;
use Database\Seeders\ModelSeeders\RegistrationsSeeder;
use Database\Seeders\ModelSeeders\RolesSeeder;
use Database\Seeders\ModelSeeders\SchoolyearsSeeder;
use Database\Seeders\ModelSeeders\StudentgroupSeeder;
use Database\Seeders\ModelSeeders\StudentListSeeder;
use Database\Seeders\ModelSeeders\StudentsSeeder;
use Database\Seeders\ModelSeeders\TasktypesSeeder;
use Database\Seeders\ModelSeeders\TimetableSeeder;
use Database\Seeders\ModelSeeders\TrialcourseCoursesSeeder;
use Database\Seeders\ModelSeeders\TrialRequestStatussesSeeder;
use Database\Seeders\ModelSeeders\UserSeeder;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DatabaseSeeder extends Seeder
{
	/**
	 * Run the database seeds.
	 *
	 * @return void
	 */
	public function run()
    {
        // first, purge all tables, disregarding foreign key constraints
        $this->truncateAllTables();

        $this->call([
            DomainSeeder::class,
            RolesSeeder::class,
            UserSeeder::class,
            LoginSecuritySeeder::class,
            TasktypesSeeder::class,
            SchoolyearsSeeder::class,
            RecurrenceSeeder::class,
            CoursegroupSeeder::class,
            LocationsSeeder::class,
            MailTemplatesSeeder::class,
            StudentsSeeder::class,
            CoursesSeeder::class,
            StudentgroupSeeder::class,
            StudentlistSeeder::class,
            RegistrationsSeeder::class,
            TimetableSeeder::class,
            EventsSeeder::class,
            DateExceptionsSeeder::class,
            AttendanceOptionsSeeder::class,
            TrialRequestStatussesSeeder::class,
            TrialcourseCoursesSeeder::class,
            DefaultChecklistSeeder::class,
            LibrarySeeder::class
        ]);
	}

    /**
     * Truncate all tables in the database.
     */
    public function truncateAllTables()
    {
        $tableNames = Schema::getConnection()->getDoctrineSchemaManager()->listTableNames();

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        foreach ($tableNames as $name) {
            //if you don't want to truncate migrations
            if ($name == 'migrations') {
                continue;
            }
            DB::table($name)->truncate();
        }

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
}
