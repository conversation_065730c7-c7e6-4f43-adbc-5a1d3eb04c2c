<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: 3c3398dd-d577-4a5b-9b05-3266cd088d15
 * Migration local datetime: 2025-01-27 16:32:34.159012
 * Migration UTC datetime: 2025-01-27 15:32:34.159012
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2025012716323415 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->string('file_name', 255)->after('event_id')->change();
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->string('original_name', 155)->nullable(true)->after('file_name');
        });
        /* Reordering of existing columns is not supported by Laravel Eloquent.
        Schema::table('documents', function (Blueprint $table) {
            $table->moveColumnAfter('description', 'original_name');
        });
        */
        
        /* But you can create your own migrations with following SQL query based on your target SQL engine.
        DB::transaction(function () {
            DB::statement('ALTER TABLE `documents` MODIFY `description` [COLUMN_DEF] AFTER `original_name`');
        });
        */
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        /* Reordering of existing columns is not supported by Laravel Eloquent.
        Schema::table('documents', function (Blueprint $table) {
            $table->moveColumnAfter('description', 'file_name');
        });
        */
        
        /* But you can create your own migrations with following SQL query based on your target SQL engine.
        DB::transaction(function () {
            DB::statement('ALTER TABLE `documents` MODIFY `description` [COLUMN_DEF] AFTER `file_name`');
        });
        */
        Schema::table('documents', function (Blueprint $table) {
            $table->dropColumn('original_name');
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->string('file_name', 255)->after('event_id')->change();
        });
    }
}
