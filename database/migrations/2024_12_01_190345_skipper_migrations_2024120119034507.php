<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: ae00cd83-2f1c-4ff4-9197-627ad6868dd0
 * Migration local datetime: 2024-12-01 19:03:45.078295
 * Migration UTC datetime: 2024-12-01 18:03:45.078295
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2024120119034507 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('alerts', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->string('entity_name', 50);
            $table->integer('entity_id');
            $table->boolean('disabled')->nullable(true)->default(0);
            $table->date('valid_until')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('attendancenotes', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('student_id')->nullable(true)->unsigned();
            $table->integer('event_id')->unsigned();
            $table->bigInteger('attendanceoption_id')->nullable(true)->unsigned();
            $table->text('notes')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->string('updated_by', 255)->nullable(true);
        });
        Schema::create('attendanceoptions', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->string('label', 255);
            $table->string('action_tutor', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('availabilities', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('tutor_id')->unsigned();
            $table->tinyInteger('day_number');
            $table->time('from_time')->nullable(true);
            $table->time('to_time')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('books', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->string('name', 255);
            $table->string('author', 255);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('checklists', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('registration_id')->nullable(true)->unsigned();
            $table->string('name', 255);
            $table->string('item1', 255)->nullable(true);
            $table->tinyInteger('item1_checked')->nullable(true);
            $table->string('item2', 255)->nullable(true);
            $table->tinyInteger('item2_checked')->nullable(true);
            $table->string('item3', 255)->nullable(true);
            $table->tinyInteger('item3_checked')->nullable(true);
            $table->string('item4', 255)->nullable(true);
            $table->tinyInteger('item4_checked')->nullable(true);
            $table->string('item5', 255)->nullable(true);
            $table->tinyInteger('item5_checked')->nullable(true);
            $table->string('item6', 255)->nullable(true);
            $table->tinyInteger('item6_checked')->nullable(true);
            $table->string('item7', 255)->nullable(true);
            $table->tinyInteger('item7_checked')->nullable(true);
            $table->string('item8', 255)->nullable(true);
            $table->tinyInteger('item8_checked')->nullable(true);
            $table->string('item9', 255)->nullable(true);
            $table->tinyInteger('item9_checked')->nullable(true);
            $table->string('item10', 255)->nullable(true);
            $table->tinyInteger('item10_checked')->nullable(true);
            $table->string('item11', 255)->nullable(true);
            $table->tinyInteger('item11_checked')->nullable(true);
            $table->string('item12', 255)->nullable(true);
            $table->tinyInteger('item12_checked')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('course_student', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('course_id')->default(0)->unsigned();
            $table->integer('student_id')->default(0)->unsigned();
            $table->integer('checklist_id')->nullable(true)->unsigned();
            $table->date('start_date')->default('2016-01-01');
            $table->date('end_date')->nullable(true);
            $table->tinyInteger('signed')->nullable(true);
            $table->tinyInteger('status')->nullable(true)->default(1);
            $table->string('sign_code', 30)->nullable(true);
            $table->tinyInteger('sign_request_send')->nullable(true);
            $table->timestamp('sign_requested_at')->nullable(true);
            $table->timestamp('signed_at')->nullable(true);
            $table->string('signed_user_agent', 255)->nullable(true);
            $table->boolean('please_keep_scheduled_time')->nullable(true);
            $table->tinyInteger('ignore_current_schedule')->nullable(true);
            $table->decimal('incidental_price_ex_tax', 8, 2)->nullable(true);
            $table->double('incidental_tax_rate')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('coursegroup_tutor', function (Blueprint $table) {
            $table->integer('coursegroup_id')->unsigned();
            $table->integer('tutor_id')->unsigned();
            $table->boolean('age_group_adult')->default(1);
            $table->boolean('age_group_adolescent')->default(1);
            $table->boolean('age_group_child')->default(1);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('coursegroups', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('name', 255);
            $table->text('webdescription')->nullable(true);
            $table->boolean('is_trial_group')->default(0);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_coursegroup_domain_id_foreign');
        });
        Schema::create('courses', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('variant_code', 255)->nullable(true);
            $table->integer('coursegroup_id')->nullable(true)->unsigned();
            $table->integer('recurrenceoption_id')->nullable(true)->unsigned();
            $table->boolean('is_trial_course')->nullable(true);
            $table->string('name', 255);
            $table->decimal('price_ex_tax', 8, 2)->default(0.00);
            $table->decimal('price_invoice', 8, 2)->default(0.00);
            $table->decimal('price_ex_tax_sub_adult', 8, 2)->default(0.00);
            $table->string('price_is_per', 15)->default('month');
            $table->integer('tax_rate')->default(0);
            $table->boolean('archive')->default(0);
            $table->integer('group_size_min')->default(1);
            $table->integer('group_size_max')->default(1);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->unique(['variant_code'], 'courses_variant_code_unique');
            $table->index(['domain_id'], 'domain_course_domain_id_foreign');
            $table->unique(['domain_id','variant_code'], 'Unique variantcode');
        });
        Schema::create('coursevariances', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->integer('course_id')->unsigned();
            $table->integer('recurrenceoption_id')->unsigned();
        });
        Schema::create('date_exceptions', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->integer('schoolyear_id')->nullable(true)->unsigned();
            $table->integer('location_id')->nullable(true)->unsigned();
            $table->dateTime('datetime_start');
            $table->dateTime('datetime_end');
            $table->string('reason', 255);
            $table->boolean('plan_blocking')->default(1);
            $table->boolean('exclude_from_alerts')->default(0);
            $table->string('calendar_color', 15)->default('#836EC3');
            $table->string('detail_url', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'date_exception_domain_domain_id_foreign');
        });
        Schema::create('dateexception_tutor', function (Blueprint $table) {
            $table->integer('date_exception_id')->unsigned();
            $table->integer('user_id')->unsigned();
            $table->boolean('mandatory')->default(0);
            $table->boolean('confirmed')->default(0);
            $table->primary(['date_exception_id','user_id']);
        });
        Schema::create('default_checklists', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('name', 255);
            $table->tinyInteger('auto_add')->nullable(true);
            $table->string('item1', 255)->nullable(true);
            $table->string('item2', 255)->nullable(true);
            $table->string('item3', 255)->nullable(true);
            $table->string('item4', 255)->nullable(true);
            $table->string('item5', 255)->nullable(true);
            $table->string('item6', 255)->nullable(true);
            $table->string('item7', 255)->nullable(true);
            $table->string('item8', 255)->nullable(true);
            $table->string('item9', 255)->nullable(true);
            $table->string('item10', 255)->nullable(true);
            $table->string('item11', 255)->nullable(true);
            $table->string('item12', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_default_checklist_domain_id_foreign');
        });
        Schema::create('documents', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->integer('event_id')->nullable(true)->unsigned();
            $table->enum('type', ['file','url']);
            $table->string('content_type', 50)->nullable(true);
            $table->string('label', 50);
            $table->string('file_location', 255)->nullable(true);
            $table->string('url', 255)->nullable(true);
            $table->string('crc', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_document_domain_id_foreign');
        });
        Schema::create('domains', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->string('domain_name', 255);
            $table->string('language', 2)->default('en');
            $table->string('lookup_url', 255);
            $table->string('website_url', 255)->nullable(true);
            $table->string('logo_url', 255)->nullable(true);
            $table->string('rates_conditions_url', 255)->nullable(true);
            $table->string('privacy_url', 255)->nullable(true);
            $table->string('name', 50);
            $table->string('address1', 85);
            $table->string('address2', 85);
            $table->string('zip', 10);
            $table->string('city', 85);
            $table->string('telephone', 15)->nullable(true);
            $table->string('email', 35);
            $table->integer('adult_threshold')->nullable(true);
            $table->string('contact_person_name', 50)->nullable(true);
            $table->double('course_tax_rate', 3, 1)->default(21.0);
            $table->string('default_password', 15);
            $table->integer('schedule_threshold')->nullable(true);
            $table->enum('status', ['trial','pro']);
            $table->date('trial_end_date')->nullable(true);
            $table->integer('warn_before_birthday')->default(10);
            $table->integer('warn_before_adult')->default(30);
            $table->text('domaintoken')->nullable(true);
            $table->text('allowed_ip_addresses')->nullable(true);
            $table->string('broadcast_colors', 50)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('emaillogentries', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->text('to');
            $table->text('cc')->nullable(true);
            $table->text('bcc')->nullable(true);
            $table->string('from', 150);
            $table->string('subject', 150);
            $table->text('body');
            $table->text('attachments')->nullable(true);
            $table->string('studentids', 255)->nullable(true);
            $table->string('unique_token', 50)->default('unknown');
            $table->string('status', 10)->default('unknown');
            $table->text('log')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_emaillogentry_domain_id_foreign');
        });
        Schema::create('events', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('location_id')->nullable(true)->unsigned();
            $table->integer('tutor_id')->nullable(true)->unsigned();
            $table->string('caluniqueid', 32);
            $table->integer('timetable_id')->nullable(true)->unsigned();
            $table->dateTime('datetime');
            $table->dateTime('original_datetime')->default('2000-01-01 23:59:59');
            $table->integer('sequence')->default(0);
            $table->text('timespan');
            $table->string('remarks', 150)->nullable(true);
            $table->boolean('flag_sticky')->default(0);
            $table->boolean('flag_publish')->nullable(true)->default(0);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });
        Schema::create('jobs', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->string('queue', 255);
            $table->longText('payload');
            $table->tinyInteger('attempts')->unsigned();
            $table->integer('reserved_at')->nullable(true)->unsigned();
            $table->integer('available_at')->unsigned();
            $table->integer('created_at')->unsigned();
            $table->index(['queue','reserved_at'], 'jobs_queue_reserved_at_index');
        });
        Schema::create('libraries', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->integer('tutor_id')->unsigned();
            $table->string('label', 50);
            $table->boolean('share_with_whole_school')->default(0);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('library_course', function (Blueprint $table) {
            $table->integer('course_id')->unsigned();
            $table->integer('library_id')->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('library_coursegroup', function (Blueprint $table) {
            $table->integer('coursegroup_id')->unsigned();
            $table->integer('library_id')->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('library_document', function (Blueprint $table) {
            $table->integer('document_id')->unsigned();
            $table->integer('library_id')->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('library_student', function (Blueprint $table) {
            $table->integer('student_id')->unsigned();
            $table->integer('library_id')->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('locations', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('name', 255);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_location_domain_id_foreign');
        });
        Schema::create('logentries', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('student_id')->unsigned();
            $table->text('entry')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('loginsecurities', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('user_id')->unsigned();
            $table->boolean('google2fa_enable')->default(0);
            $table->string('google2fa_secret', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('mailtemplates', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('label', 50);
            $table->string('targets', 50)->default('a');
            $table->text('content');
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_mailtemplate_domain_id_foreign');
        });
        Schema::create('messages', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->integer('from_id')->unsigned();
            $table->string('from_type', 15);
            $table->string('from_label', 50);
            $table->integer('to_id')->unsigned();
            $table->string('to_type', 15);
            $table->string('to_label', 50);
            $table->string('subject', 50);
            $table->text('body');
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->timestamp('read_at')->nullable(true);
        });

//        Schema::create('oauth_access_tokens', function (Blueprint $table) {
//            $table->string('id', 100);
//            $table->bigInteger('user_id')->nullable(true)->unsigned();
//            $table->bigInteger('client_id')->unsigned();
//            $table->string('name', 255)->nullable(true);
//            $table->text('scopes')->nullable(true);
//            $table->boolean('revoked');
//            $table->timestamp('created_at')->nullable(true);
//            $table->timestamp('updated_at')->nullable(true);
//            $table->dateTime('expires_at')->nullable(true);
//            $table->index(['user_id'], 'oauth_access_tokens_user_id_index');
//            $table->primary(['id']);
//        });
//        Schema::create('oauth_auth_codes', function (Blueprint $table) {
//            $table->string('id', 100);
//            $table->bigInteger('user_id')->unsigned();
//            $table->bigInteger('client_id')->unsigned();
//            $table->text('scopes')->nullable(true);
//            $table->boolean('revoked');
//            $table->dateTime('expires_at')->nullable(true);
//            $table->index(['user_id'], 'oauth_auth_codes_user_id_index');
//            $table->primary(['id']);
//        });
//        Schema::create('oauth_clients', function (Blueprint $table) {
//            $table->bigInteger('id')->autoIncrement()->unsigned();
//            $table->bigInteger('user_id')->nullable(true)->unsigned();
//            $table->string('name', 255);
//            $table->string('secret', 100)->nullable(true);
//            $table->string('provider', 255)->nullable(true);
//            $table->text('redirect');
//            $table->boolean('personal_access_client');
//            $table->boolean('password_client');
//            $table->boolean('revoked');
//            $table->timestamp('created_at')->nullable(true);
//            $table->timestamp('updated_at')->nullable(true);
//            $table->index(['user_id'], 'oauth_clients_user_id_index');
//        });
//        Schema::create('oauth_personal_access_clients', function (Blueprint $table) {
//            $table->bigInteger('id')->autoIncrement()->unsigned();
//            $table->bigInteger('client_id')->unsigned();
//            $table->timestamp('created_at')->nullable(true);
//            $table->timestamp('updated_at')->nullable(true);
//        });
//        Schema::create('oauth_refresh_tokens', function (Blueprint $table) {
//            $table->string('id', 100);
//            $table->string('access_token_id', 100);
//            $table->boolean('revoked');
//            $table->dateTime('expires_at')->nullable(true);
//            $table->index(['access_token_id'], 'oauth_refresh_tokens_access_token_id_index');
//            $table->primary(['id']);
//        });
//        Schema::create('password_resets', function (Blueprint $table) {
//            $table->string('email', 255);
//            $table->string('token', 255);
//            $table->timestamp('created_at')->nullable(true);
//            $table->index(['email'], 'password_resets_email_index');
//            $table->index(['token'], 'password_resets_token_index');
//        });
//        Schema::create('personal_access_tokens', function (Blueprint $table) {
//            $table->bigInteger('id')->autoIncrement()->unsigned();
//            $table->string('tokenable_type', 255);
//            $table->bigInteger('tokenable_id')->unsigned();
//            $table->string('name', 255);
//            $table->string('token', 64);
//            $table->text('abilities')->nullable(true);
//            $table->timestamp('last_used_at')->nullable(true);
//            $table->timestamp('expires_at')->nullable(true);
//            $table->timestamp('created_at')->nullable(true);
//            $table->timestamp('updated_at')->nullable(true);
//            $table->unique(['token'], 'personal_access_tokens_token_unique');
//            $table->index(['tokenable_type','tokenable_id'], 'personal_access_tokens_tokenable_type_tokenable_id_index');
//        });
        Schema::create('planning_entries_changes', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('domain_id')->unsigned();
            $table->enum('change', ['insert','update','delete']);
            $table->integer('planningentry_id')->nullable(true)->unsigned();
            $table->integer('new_tutor_id')->nullable(true)->unsigned();
            $table->integer('new_location_id')->nullable(true)->unsigned();
            $table->time('new_starttime')->nullable(true);
            $table->integer('new_daynumber')->nullable(true);
            $table->enum('new_oddeven', ['odd','even','oddeven','other'])->nullable(true);
            $table->enum('status', ['initial','handled','failed'])->default('initial');
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('planningentries', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->integer('registration_id')->unsigned();
            $table->integer('tutor_id')->nullable(true)->unsigned();
            $table->integer('location_id')->nullable(true)->unsigned();
            $table->time('starttime');
            $table->integer('duration')->default(1);
            $table->integer('daynumber')->default(1);
            $table->enum('oddeven', ['odd','even','oddeven','other'])->default('other');
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('priceoptions', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('course_id')->unsigned();
            $table->integer('from_nr_of_students');
            $table->integer('to_nr_of_students');
            $table->decimal('price_ex_tax', 6, 2)->default(0.00);
            $table->string('price_is_per', 15)->default('month');
            $table->integer('tax_rate')->default(0);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('pulse_aggregates', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('bucket')->unsigned();
            $table->mediumInteger('period')->unsigned();
            $table->string('type', 255);
            $table->mediumText('key');
            $table->binary('key_hash')->nullable(true);
            $table->string('aggregate', 255);
            $table->decimal('value', 20, 2);
            $table->integer('count')->nullable(true)->unsigned();
            //$table->unique(['bucket','period','type','key_hash','aggregate'], 'pulse_aggregates_bucket_period_type_aggregate_key_hash_unique');
            $table->unique([
                'bucket',
                'period',
                'type',
                'aggregate',
                DB::raw('key_hash(255)')
            ], 'pulse_aggregates_bucket_period_type_aggregate_key_hash_unique');
            $table->index(['bucket','period'], 'pulse_aggregates_period_bucket_index');
            $table->index(['bucket','period','type','aggregate'], 'pulse_aggregates_period_type_aggregate_bucket_index');
            $table->index(['type'], 'pulse_aggregates_type_index');
        });
        Schema::create('pulse_entries', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('timestamp')->unsigned();
            $table->string('type', 255);
            $table->mediumText('key');
            $table->binary('key_hash')->nullable(true);
            $table->bigInteger('value')->nullable(true);
            // $table->index(['key_hash'], 'pulse_entries_key_hash_index');
            $table->unique([
                DB::raw('key_hash(255)')
            ], 'pulse_entries_key_hash_index');

            $table->index(['timestamp'], 'pulse_entries_timestamp_index');
            //$table->index(['timestamp','type','key_hash','value'], 'pulse_entries_timestamp_type_key_hash_value_index');

            $table->unique([
                'timestamp',
                'type',
                DB::raw('key_hash(255)'),
                'value'
            ], 'pulse_entries_timestamp_type_key_hash_value_index');

            $table->index(['type'], 'pulse_entries_type_index');
        });
        Schema::create('pulse_values', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('timestamp')->unsigned();
            $table->string('type', 255);
            $table->mediumText('key');
            $table->binary('key_hash')->nullable(true);
            $table->mediumText('value');
            $table->index(['timestamp'], 'pulse_values_timestamp_index');
            $table->index(['type'], 'pulse_values_type_index');
            //$table->unique(['type','key_hash'], 'pulse_values_type_key_hash_unique');
            $table->unique([
                'type',
                DB::raw('key_hash(255)')
            ], 'pulse_values_type_key_hash_unique');

        });
        Schema::create('recurrenceoptions', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->boolean('is_trial_lesson')->nullable(true)->default(0);
            $table->string('description', 80);
            $table->double('nr_of_times', 8, 2);
            $table->string('timeunit', 15);
            $table->string('per_interval', 15);
            $table->boolean('ends_after_nr_of_occurrences')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_recurrenceoption_domain_id_foreign');
        });
        Schema::create('role_user', function (Blueprint $table) {
            $table->integer('user_id')->nullable(true)->unsigned();
            $table->integer('role_id')->nullable(true)->unsigned();
            $table->string('calcolor', 6)->default('5f6f7e');
            $table->date('start_date')->default('2015-01-01');
            $table->date('end_date')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('roles', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->string('rolename', 15);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('schedule_prefs', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('student_id')->unsigned();
            $table->string('monday', 255)->nullable(true);
            $table->string('tuesday', 255)->nullable(true);
            $table->string('wednesday', 255)->nullable(true);
            $table->string('thursday', 255)->nullable(true);
            $table->string('friday', 255)->nullable(true);
            $table->string('saturday', 255)->nullable(true);
            $table->string('sunday', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('scheduleproposals', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->integer('registration_id')->unsigned();
            $table->integer('location_id')->unsigned();
            $table->integer('tutor_id')->unsigned();
            $table->string('schedule_dt', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->unique(['registration_id'], 'scheduleproposals_registration_id_unique');
        });
        Schema::create('schoolyears', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('label', 25);
            $table->integer('start_year');
            $table->date('start_date')->default('1970-01-01');
            $table->date('end_date')->default('1970-01-01');
            $table->integer('end_year');
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_schoolyear_domain_id_foreign');
        });
        Schema::create('student_studentgroup', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('student_id')->default(0)->unsigned();
            $table->integer('studentgroup_id')->default(0)->unsigned();
            $table->integer('as_trial_student')->default(0);
            $table->date('start_date')->default('2016-01-01');
            $table->date('end_date')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('student_studentlist', function (Blueprint $table) {
            $table->integer('student_id')->unsigned();
            $table->integer('studentlist_id')->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->primary(['student_id','studentlist_id']);
        });
        Schema::create('studentcontacts', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('student_id')->unsigned();
            $table->enum('contacttype', ['telephone','email']);
            $table->string('label', 255)->nullable(true);
            $table->string('value', 100);
            $table->boolean('apply_for_planning')->nullable(true);
            $table->boolean('apply_for_finance')->nullable(true);
            $table->boolean('apply_for_promotions')->nullable(true);
            $table->string('use_salutation', 255)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('studentlists', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('name', 50);
            $table->string('hexcolor', 6);
            $table->text('remarks')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_studentlist_domain_id_foreign');
        });
        Schema::create('students', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('name', 255);
            $table->string('firstname', 45)->nullable(true);
            $table->string('preposition', 15)->nullable(true);
            $table->string('lastname', 75);
            $table->string('address', 255)->nullable(true);
            $table->string('zipcode', 255)->nullable(true);
            $table->string('city', 255)->nullable(true);
            $table->date('date_of_birth');
            $table->string('permission_auto_banktransfer', 255)->nullable(true);
            $table->string('bankaccount_name', 255)->nullable(true);
            $table->string('bankaccount_number', 255)->nullable(true);
            $table->string('mandate_number', 255)->nullable(true);
            $table->text('remarks')->nullable(true);
            $table->string('status', 255);
            $table->string('accesstoken', 32)->nullable(true);
            $table->integer('apipin')->nullable(true);
            $table->tinyInteger('has_access')->nullable(true);
            $table->tinyInteger('agreeSocialShare')->default(0);
            $table->integer('min_participants')->nullable(true)->default(2);
            $table->integer('max_participants')->nullable(true)->default(99);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_student_domain_id_foreign');
        });
        Schema::create('tasks', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->integer('tasktype_id')->unsigned();
            $table->integer('student_id')->nullable(true)->unsigned();
            $table->integer('course_id')->nullable(true)->unsigned();
            $table->integer('tutor_id')->nullable(true)->unsigned();
            $table->integer('registration_id')->nullable(true)->unsigned();
            $table->integer('event_id')->nullable(true)->unsigned();
            $table->integer('assigned_user_id')->nullable(true)->unsigned();
            $table->date('date_opened');
            $table->date('date_due')->nullable(true);
            $table->date('date_closed')->nullable(true);
            $table->text('remarks')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_task_domain_id_foreign');
        });
        Schema::create('tasktypes', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->string('description', 25);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('timetables', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('schoolyear_id')->nullable(true)->unsigned();
            $table->integer('course_student_id')->nullable(true)->unsigned();
            $table->text('description')->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('trialcourse_courses', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('trialcourse_id')->unsigned();
            $table->integer('course_id')->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('trialrequeststatuses', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->string('description', 100);
            $table->boolean('needs_admin_action')->default(1);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::create('trialstudents', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->bigInteger('trialrequeststatus_id')->default(1)->unsigned();
            $table->string('firstname', 45);
            $table->string('preposition', 15)->nullable(true);
            $table->string('lastname', 45);
            $table->date('date_of_birth')->nullable(true);
            $table->string('telephone', 255)->nullable(true);
            $table->string('email', 255)->nullable(true);
            $table->integer('course_id')->nullable(true)->unsigned();
            $table->string('requested_startdate', 255)->nullable(true);
            $table->text('remarks')->nullable(true);
            $table->integer('generated_registration_id')->nullable(true)->unsigned();
            $table->integer('generated_student_id')->nullable(true)->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->index(['domain_id'], 'domain_trialstudent_domain_id_foreign');
        });
        Schema::create('users', function (Blueprint $table) {
            $table->integer('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->default(0)->unsigned();
            $table->string('name', 255);
            $table->string('avatar', 255)->nullable(true);
            $table->string('hexcolor', 30)->nullable(true);
            $table->string('email', 255);
            $table->string('password', 255);
            $table->string('telephone', 15)->nullable(true);
            $table->string('telephone_extra', 15)->nullable(true);
            $table->string('remember_token', 100)->nullable(true);
            $table->string('preferred_language', 2)->nullable(true);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
            $table->timestamp('last_active_at')->nullable(true);
            $table->index(['domain_id'], 'domain_user_domain_id_foreign');
            $table->unique(['email'], 'users_email_unique');
        });
        Schema::table('alerts', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('CASCADE')->onUpdate('CASCADE');
        });
        Schema::table('attendancenotes', function (Blueprint $table) {
            $table->foreign('attendanceoption_id')->references('id')->on('attendanceoptions')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('attendancenotes', function (Blueprint $table) {
            $table->foreign('event_id')->references('id')->on('events')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('attendancenotes', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('attendanceoptions', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('availabilities', function (Blueprint $table) {
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('checklists', function (Blueprint $table) {
            $table->foreign('registration_id')->references('id')->on('course_student')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('course_student', function (Blueprint $table) {
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('course_student', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('coursegroup_tutor', function (Blueprint $table) {
            $table->foreign('coursegroup_id')->references('id')->on('coursegroups')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('coursegroup_tutor', function (Blueprint $table) {
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('coursegroups', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('courses', function (Blueprint $table) {
            $table->foreign('coursegroup_id')->references('id')->on('coursegroups')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('courses', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('courses', function (Blueprint $table) {
            $table->foreign('recurrenceoption_id')->references('id')->on('recurrenceoptions')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('coursevariances', function (Blueprint $table) {
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('coursevariances', function (Blueprint $table) {
            $table->foreign('recurrenceoption_id')->references('id')->on('recurrenceoptions')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('date_exceptions', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('date_exceptions', function (Blueprint $table) {
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('date_exceptions', function (Blueprint $table) {
            $table->foreign('schoolyear_id')->references('id')->on('schoolyears')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('dateexception_tutor', function (Blueprint $table) {
            $table->foreign('date_exception_id')->references('id')->on('date_exceptions')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('dateexception_tutor', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('default_checklists', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->foreign('event_id')->references('id')->on('events')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('emaillogentries', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('events', function (Blueprint $table) {
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('events', function (Blueprint $table) {
            $table->foreign('timetable_id')->references('id')->on('timetables')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('events', function (Blueprint $table) {
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('libraries', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('libraries', function (Blueprint $table) {
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_course', function (Blueprint $table) {
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_course', function (Blueprint $table) {
            $table->foreign('library_id')->references('id')->on('libraries')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_coursegroup', function (Blueprint $table) {
            $table->foreign('coursegroup_id')->references('id')->on('coursegroups')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_coursegroup', function (Blueprint $table) {
            $table->foreign('library_id')->references('id')->on('libraries')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_document', function (Blueprint $table) {
            $table->foreign('document_id')->references('id')->on('documents')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_document', function (Blueprint $table) {
            $table->foreign('library_id')->references('id')->on('libraries')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_student', function (Blueprint $table) {
            $table->foreign('library_id')->references('id')->on('libraries')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('library_student', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('locations', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('logentries', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('loginsecurities', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('mailtemplates', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('messages', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planning_entries_changes', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planning_entries_changes', function (Blueprint $table) {
            $table->foreign('new_location_id')->references('id')->on('locations')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planning_entries_changes', function (Blueprint $table) {
            $table->foreign('new_tutor_id')->references('id')->on('users')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planning_entries_changes', function (Blueprint $table) {
            $table->foreign('planningentry_id')->references('id')->on('planningentries')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planningentries', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planningentries', function (Blueprint $table) {
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planningentries', function (Blueprint $table) {
            $table->foreign('registration_id')->references('id')->on('course_student')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('planningentries', function (Blueprint $table) {
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('priceoptions', function (Blueprint $table) {
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('recurrenceoptions', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('role_user', function (Blueprint $table) {
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('role_user', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('schedule_prefs', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('CASCADE')->onUpdate('CASCADE');
        });
        Schema::table('scheduleproposals', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('scheduleproposals', function (Blueprint $table) {
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('scheduleproposals', function (Blueprint $table) {
            $table->foreign('registration_id')->references('id')->on('course_student')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('scheduleproposals', function (Blueprint $table) {
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('schoolyears', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('student_studentgroup', function (Blueprint $table) {
            $table->foreign('studentgroup_id')->references('id')->on('students')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('student_studentgroup', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('student_studentlist', function (Blueprint $table) {
            $table->foreign('studentlist_id')->references('id')->on('studentlists')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('student_studentlist', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('studentcontacts', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('studentlists', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('students', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('assigned_user_id')->references('id')->on('users')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('event_id')->references('id')->on('events')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('registration_id')->references('id')->on('course_student')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('student_id')->references('id')->on('students')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('tasktype_id')->references('id')->on('tasktypes')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('tasks', function (Blueprint $table) {
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('timetables', function (Blueprint $table) {
            $table->foreign('course_student_id')->references('id')->on('course_student')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('timetables', function (Blueprint $table) {
            $table->foreign('schoolyear_id')->references('id')->on('schoolyears')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('trialcourse_courses', function (Blueprint $table) {
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('trialcourse_courses', function (Blueprint $table) {
            $table->foreign('trialcourse_id')->references('id')->on('courses')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('trialrequeststatuses', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('trialstudents', function (Blueprint $table) {
            $table->foreign('course_id')->references('id')->on('courses')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('trialstudents', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('trialstudents', function (Blueprint $table) {
            $table->foreign('generated_registration_id')->references('id')->on('course_student')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('trialstudents', function (Blueprint $table) {
            $table->foreign('generated_student_id')->references('id')->on('students')->onDelete('CASCADE')->onUpdate('NO ACTION');
        });
        Schema::table('trialstudents', function (Blueprint $table) {
            $table->foreign('trialrequeststatus_id')->references('id')->on('trialrequeststatuses')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onDelete('NO ACTION')->onUpdate('NO ACTION');
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('trialstudents')) {
            Schema::table('trialstudents', function (Blueprint $table) {
                $table->dropForeign(['trialrequeststatus_id']);
            });
        }
        if (Schema::hasTable('trialstudents')) {
            Schema::table('trialstudents', function (Blueprint $table) {
                $table->dropForeign(['generated_student_id']);
            });
        }
        if (Schema::hasTable('trialstudents')) {
            Schema::table('trialstudents', function (Blueprint $table) {
                $table->dropForeign(['generated_registration_id']);
            });
        }
        if (Schema::hasTable('trialstudents')) {
            Schema::table('trialstudents', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('trialstudents')) {
            Schema::table('trialstudents', function (Blueprint $table) {
                $table->dropForeign(['course_id']);
            });
        }
        if (Schema::hasTable('trialrequeststatuses')) {
            Schema::table('trialrequeststatuses', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('trialcourse_courses')) {
            Schema::table('trialcourse_courses', function (Blueprint $table) {
                $table->dropForeign(['trialcourse_id']);
            });
        }
        if (Schema::hasTable('trialcourse_courses')) {
            Schema::table('trialcourse_courses', function (Blueprint $table) {
                $table->dropForeign(['course_id']);
            });
        }
        if (Schema::hasTable('timetables')) {
            Schema::table('timetables', function (Blueprint $table) {
                $table->dropForeign(['schoolyear_id']);
            });
        }
        if (Schema::hasTable('timetables')) {
            Schema::table('timetables', function (Blueprint $table) {
                $table->dropForeign(['course_student_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['tutor_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['tasktype_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['registration_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['event_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['course_id']);
            });
        }
        if (Schema::hasTable('tasks')) {
            Schema::table('tasks', function (Blueprint $table) {
                $table->dropForeign(['assigned_user_id']);
            });
        }
        if (Schema::hasTable('students')) {
            Schema::table('students', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('studentlists')) {
            Schema::table('studentlists', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('studentcontacts')) {
            Schema::table('studentcontacts', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('student_studentlist')) {
            Schema::table('student_studentlist', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('student_studentlist')) {
            Schema::table('student_studentlist', function (Blueprint $table) {
                $table->dropForeign(['studentlist_id']);
            });
        }
        if (Schema::hasTable('student_studentgroup')) {
            Schema::table('student_studentgroup', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('student_studentgroup')) {
            Schema::table('student_studentgroup', function (Blueprint $table) {
                $table->dropForeign(['studentgroup_id']);
            });
        }
        if (Schema::hasTable('schoolyears')) {
            Schema::table('schoolyears', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('scheduleproposals')) {
            Schema::table('scheduleproposals', function (Blueprint $table) {
                $table->dropForeign(['tutor_id']);
            });
        }
        if (Schema::hasTable('scheduleproposals')) {
            Schema::table('scheduleproposals', function (Blueprint $table) {
                $table->dropForeign(['registration_id']);
            });
        }
        if (Schema::hasTable('scheduleproposals')) {
            Schema::table('scheduleproposals', function (Blueprint $table) {
                $table->dropForeign(['location_id']);
            });
        }
        if (Schema::hasTable('scheduleproposals')) {
            Schema::table('scheduleproposals', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('schedule_prefs')) {
            Schema::table('schedule_prefs', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('role_user')) {
            Schema::table('role_user', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
            });
        }
        if (Schema::hasTable('role_user')) {
            Schema::table('role_user', function (Blueprint $table) {
                $table->dropForeign(['role_id']);
            });
        }
        if (Schema::hasTable('recurrenceoptions')) {
            Schema::table('recurrenceoptions', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('priceoptions')) {
            Schema::table('priceoptions', function (Blueprint $table) {
                $table->dropForeign(['course_id']);
            });
        }
        if (Schema::hasTable('planningentries')) {
            Schema::table('planningentries', function (Blueprint $table) {
                $table->dropForeign(['tutor_id']);
            });
        }
        if (Schema::hasTable('planningentries')) {
            Schema::table('planningentries', function (Blueprint $table) {
                $table->dropForeign(['registration_id']);
            });
        }
        if (Schema::hasTable('planningentries')) {
            Schema::table('planningentries', function (Blueprint $table) {
                $table->dropForeign(['location_id']);
            });
        }
        if (Schema::hasTable('planningentries')) {
            Schema::table('planningentries', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('planning_entries_changes')) {
            Schema::table('planning_entries_changes', function (Blueprint $table) {
                $table->dropForeign(['planningentry_id']);
            });
        }
        if (Schema::hasTable('planning_entries_changes')) {
            Schema::table('planning_entries_changes', function (Blueprint $table) {
                $table->dropForeign(['new_tutor_id']);
            });
        }
        if (Schema::hasTable('planning_entries_changes')) {
            Schema::table('planning_entries_changes', function (Blueprint $table) {
                $table->dropForeign(['new_location_id']);
            });
        }
        if (Schema::hasTable('planning_entries_changes')) {
            Schema::table('planning_entries_changes', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('messages')) {
            Schema::table('messages', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('mailtemplates')) {
            Schema::table('mailtemplates', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('loginsecurities')) {
            Schema::table('loginsecurities', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
            });
        }
        if (Schema::hasTable('logentries')) {
            Schema::table('logentries', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('locations')) {
            Schema::table('locations', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('library_student')) {
            Schema::table('library_student', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('library_student')) {
            Schema::table('library_student', function (Blueprint $table) {
                $table->dropForeign(['library_id']);
            });
        }
        if (Schema::hasTable('library_document')) {
            Schema::table('library_document', function (Blueprint $table) {
                $table->dropForeign(['library_id']);
            });
        }
        if (Schema::hasTable('library_document')) {
            Schema::table('library_document', function (Blueprint $table) {
                $table->dropForeign(['document_id']);
            });
        }
        if (Schema::hasTable('library_coursegroup')) {
            Schema::table('library_coursegroup', function (Blueprint $table) {
                $table->dropForeign(['library_id']);
            });
        }
        if (Schema::hasTable('library_coursegroup')) {
            Schema::table('library_coursegroup', function (Blueprint $table) {
                $table->dropForeign(['coursegroup_id']);
            });
        }
        if (Schema::hasTable('library_course')) {
            Schema::table('library_course', function (Blueprint $table) {
                $table->dropForeign(['library_id']);
            });
        }
        if (Schema::hasTable('library_course')) {
            Schema::table('library_course', function (Blueprint $table) {
                $table->dropForeign(['course_id']);
            });
        }
        if (Schema::hasTable('libraries')) {
            Schema::table('libraries', function (Blueprint $table) {
                $table->dropForeign(['tutor_id']);
            });
        }
        if (Schema::hasTable('libraries')) {
            Schema::table('libraries', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('events')) {
            Schema::table('events', function (Blueprint $table) {
                $table->dropForeign(['tutor_id']);
            });
        }
        if (Schema::hasTable('events')) {
            Schema::table('events', function (Blueprint $table) {
                $table->dropForeign(['timetable_id']);
            });
        }
        if (Schema::hasTable('events')) {
            Schema::table('events', function (Blueprint $table) {
                $table->dropForeign(['location_id']);
            });
        }
        if (Schema::hasTable('emaillogentries')) {
            Schema::table('emaillogentries', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('documents')) {
            Schema::table('documents', function (Blueprint $table) {
                $table->dropForeign(['event_id']);
            });
        }
        if (Schema::hasTable('documents')) {
            Schema::table('documents', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('default_checklists')) {
            Schema::table('default_checklists', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('dateexception_tutor')) {
            Schema::table('dateexception_tutor', function (Blueprint $table) {
                $table->dropForeign(['user_id']);
            });
        }
        if (Schema::hasTable('dateexception_tutor')) {
            Schema::table('dateexception_tutor', function (Blueprint $table) {
                $table->dropForeign(['date_exception_id']);
            });
        }
        if (Schema::hasTable('date_exceptions')) {
            Schema::table('date_exceptions', function (Blueprint $table) {
                $table->dropForeign(['schoolyear_id']);
            });
        }
        if (Schema::hasTable('date_exceptions')) {
            Schema::table('date_exceptions', function (Blueprint $table) {
                $table->dropForeign(['location_id']);
            });
        }
        if (Schema::hasTable('date_exceptions')) {
            Schema::table('date_exceptions', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('coursevariances')) {
            Schema::table('coursevariances', function (Blueprint $table) {
                $table->dropForeign(['recurrenceoption_id']);
            });
        }
        if (Schema::hasTable('coursevariances')) {
            Schema::table('coursevariances', function (Blueprint $table) {
                $table->dropForeign(['course_id']);
            });
        }
        if (Schema::hasTable('courses')) {
            Schema::table('courses', function (Blueprint $table) {
                $table->dropForeign(['recurrenceoption_id']);
            });
        }
        if (Schema::hasTable('courses')) {
            Schema::table('courses', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('courses')) {
            Schema::table('courses', function (Blueprint $table) {
                $table->dropForeign(['coursegroup_id']);
            });
        }
        if (Schema::hasTable('coursegroups')) {
            Schema::table('coursegroups', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('coursegroup_tutor')) {
            Schema::table('coursegroup_tutor', function (Blueprint $table) {
                $table->dropForeign(['tutor_id']);
            });
        }
        if (Schema::hasTable('coursegroup_tutor')) {
            Schema::table('coursegroup_tutor', function (Blueprint $table) {
                $table->dropForeign(['coursegroup_id']);
            });
        }
        if (Schema::hasTable('course_student')) {
            Schema::table('course_student', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('course_student')) {
            Schema::table('course_student', function (Blueprint $table) {
                $table->dropForeign(['course_id']);
            });
        }
        if (Schema::hasTable('checklists')) {
            Schema::table('checklists', function (Blueprint $table) {
                $table->dropForeign(['registration_id']);
            });
        }
        if (Schema::hasTable('availabilities')) {
            Schema::table('availabilities', function (Blueprint $table) {
                $table->dropForeign(['tutor_id']);
            });
        }
        if (Schema::hasTable('attendanceoptions')) {
            Schema::table('attendanceoptions', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        if (Schema::hasTable('attendancenotes')) {
            Schema::table('attendancenotes', function (Blueprint $table) {
                $table->dropForeign(['student_id']);
            });
        }
        if (Schema::hasTable('attendancenotes')) {
            Schema::table('attendancenotes', function (Blueprint $table) {
                $table->dropForeign(['event_id']);
            });
        }
        if (Schema::hasTable('attendancenotes')) {
            Schema::table('attendancenotes', function (Blueprint $table) {
                $table->dropForeign(['attendanceoption_id']);
            });
        }
        if (Schema::hasTable('alerts')) {
            Schema::table('alerts', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        Schema::dropIfExists('users');
        Schema::dropIfExists('trialstudents');
        Schema::dropIfExists('trialrequeststatuses');
        Schema::dropIfExists('trialcourse_courses');
        Schema::dropIfExists('timetables');
        Schema::dropIfExists('tasktypes');
        Schema::dropIfExists('tasks');
        Schema::dropIfExists('students');
        Schema::dropIfExists('studentlists');
        Schema::dropIfExists('studentcontacts');
        Schema::dropIfExists('student_studentlist');
        Schema::dropIfExists('student_studentgroup');
        Schema::dropIfExists('schoolyears');
        Schema::dropIfExists('scheduleproposals');
        Schema::dropIfExists('schedule_prefs');
        Schema::dropIfExists('roles');
        Schema::dropIfExists('role_user');
        Schema::dropIfExists('recurrenceoptions');
        Schema::dropIfExists('pulse_values');
        Schema::dropIfExists('pulse_entries');
        Schema::dropIfExists('pulse_aggregates');
        Schema::dropIfExists('priceoptions');
        Schema::dropIfExists('planningentries');
        Schema::dropIfExists('planning_entries_changes');
        Schema::dropIfExists('personal_access_tokens');
        Schema::dropIfExists('password_resets');
        Schema::dropIfExists('oauth_refresh_tokens');
        Schema::dropIfExists('oauth_personal_access_clients');
        Schema::dropIfExists('oauth_clients');
        Schema::dropIfExists('oauth_auth_codes');
        Schema::dropIfExists('oauth_access_tokens');
        Schema::dropIfExists('messages');
        Schema::dropIfExists('mailtemplates');
        Schema::dropIfExists('loginsecurities');
        Schema::dropIfExists('logentries');
        Schema::dropIfExists('locations');
        Schema::dropIfExists('library_student');
        Schema::dropIfExists('library_document');
        Schema::dropIfExists('library_coursegroup');
        Schema::dropIfExists('library_course');
        Schema::dropIfExists('libraries');
        Schema::dropIfExists('jobs');
        Schema::dropIfExists('failed_jobs');
        Schema::dropIfExists('events');
        Schema::dropIfExists('emaillogentries');
        Schema::dropIfExists('domains');
        Schema::dropIfExists('documents');
        Schema::dropIfExists('default_checklists');
        Schema::dropIfExists('dateexception_tutor');
        Schema::dropIfExists('date_exceptions');
        Schema::dropIfExists('coursevariances');
        Schema::dropIfExists('courses');
        Schema::dropIfExists('coursegroups');
        Schema::dropIfExists('coursegroup_tutor');
        Schema::dropIfExists('course_student');
        Schema::dropIfExists('checklists');
        Schema::dropIfExists('books');
        Schema::dropIfExists('availabilities');
        Schema::dropIfExists('attendanceoptions');
        Schema::dropIfExists('attendancenotes');
        Schema::dropIfExists('alerts');
    }
}
