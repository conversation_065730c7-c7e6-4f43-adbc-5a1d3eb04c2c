<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: 729bfbc1-1b7c-4ef5-8706-1c0edb65c4c5
 * Migration local datetime: 2025-01-26 08:57:15.360957
 * Migration UTC datetime: 2025-01-26 07:57:15.360957
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2025012608571536 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->enum('type', ['file','url','audio','video'])->change();
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->renameColumn('file_location', 'file_name');
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->string('file_name', 255)->after('event_id')->change();
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->text('description')->nullable(true)->after('file_name');
        });
        /* Reordering of existing columns is not supported by Laravel Eloquent.
        Schema::table('documents', function (Blueprint $table) {
            $table->moveColumnAfter('url', 'label');
        });
        */
        
        /* But you can create your own migrations with following SQL query based on your target SQL engine.
        DB::transaction(function () {
            DB::statement('ALTER TABLE `documents` MODIFY `url` [COLUMN_DEF] AFTER `label`');
        });
        */
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        /* Reordering of existing columns is not supported by Laravel Eloquent.
        Schema::table('documents', function (Blueprint $table) {
            $table->moveColumnAfter('url', 'file_name');
        });
        */
        
        /* But you can create your own migrations with following SQL query based on your target SQL engine.
        DB::transaction(function () {
            DB::statement('ALTER TABLE `documents` MODIFY `url` [COLUMN_DEF] AFTER `file_name`');
        });
        */
        Schema::table('documents', function (Blueprint $table) {
            $table->dropColumn('description');
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->string('file_name', 255)->nullable(true)->after('event_id')->change();
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->renameColumn('file_name', 'file_location');
        });
        Schema::table('documents', function (Blueprint $table) {
            $table->enum('type', ['file','url','audio','video'])->change();
        });
    }
}
