<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: eaef22de-0cc9-4d2f-855c-92c586e3f76d
 * Migration local datetime: 2024-12-02 20:14:14.767059
 * Migration UTC datetime: 2024-12-02 19:14:14.767059
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2024120220141476 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('books');
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('books', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->string('name', 255);
            $table->string('author', 255);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
    }
}
