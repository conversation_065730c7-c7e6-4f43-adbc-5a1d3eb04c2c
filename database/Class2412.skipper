<?xml version="1.0"?>
<skipper version="3.3.7.1828" mvc="Laravel" orm="Laravel" name="Class2412" uuid="7db39806-493a-4dab-b847-b0b4237f689f">
  <module name="\Class" local-name="Class" namespace="\App\Models" local-namespace="App\Models" export-format="Laravel" export-path="." uuid="27f3d342-15d2-4073-a51c-cd2da5b30548">
    <entity name="\App\Models\CoursegroupTutor" local-name="CoursegroupTutor" namespace="\App\Models" uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d">
      <field name="coursegroup_id" type="integer" required="true" unsigned="true" uuid="12c88c34-1b59-4723-81ed-29cc45f0e405"/>
      <field name="tutor_id" type="integer" required="true" unsigned="true" uuid="4c6d1ba6-ac3b-4b1d-9fbf-787b6f858537"/>
      <field name="age_group_adult" type="boolean" default="1" required="true" uuid="180a241f-cec8-4d4a-979d-c6542f085cc0"/>
      <field name="age_group_adolescent" type="boolean" default="1" required="true" uuid="55d00245-3e64-42e1-ad8d-544db99a042b"/>
      <field name="age_group_child" type="boolean" default="1" required="true" uuid="ee3dd08c-65c7-44db-bc7f-a8fd2ac58349"/>
      <field name="created_at" type="timestamp" uuid="fd6ac3cd-d148-4911-b3a7-aa918264dc1a"/>
      <field name="updated_at" type="timestamp" uuid="0b63d686-7010-4a02-98bc-d99bd656a59d"/>
      <orm-attributes>
        <attribute name="table">coursegroup_tutor</attribute>
      </orm-attributes>
    </entity>
    <association from="\App\Models\CoursegroupTutor" to="\App\Models\Coursegroup" owner-alias="coursegroupTutors" inverse-alias="coursegroups" many-owner="true" many-inverse="false" parent-required="true" uuid="e80160e7-ad70-4b80-9fa1-0ee076ed378a">
      <association-field from="coursegroup_id" to="id" uuid="8b49d77c-**************-f56c62320874"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\CoursegroupTutor" to="\App\Models\User" owner-alias="coursegroupTutors" inverse-alias="users" many-owner="true" many-inverse="false" parent-required="true" uuid="1cca546b-189b-4c68-af0b-0939ee635e6f">
      <association-field from="tutor_id" to="id" uuid="61d511bc-31fe-4e58-85d6-57b125d8a039"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <entity name="\App\Models\DateexceptionTutor" local-name="DateexceptionTutor" namespace="\App\Models" uuid="e5e8860a-f818-4e0c-9819-7a7ad9255c7f">
      <field name="date_exception_id" type="integer" required="true" unique="true" primary="true" unsigned="true" uuid="88775d15-3b91-4b0b-adb2-9df60deef31e"/>
      <field name="user_id" type="integer" required="true" unique="true" primary="true" unsigned="true" uuid="905af1b7-e30e-4f80-b296-02af0b907951"/>
      <field name="mandatory" type="boolean" default="0" required="true" uuid="19073e65-b13d-459a-b9d9-d1512e332c1a"/>
      <field name="confirmed" type="boolean" default="0" required="true" uuid="19dc5573-5711-4431-a75f-4a3f21af43bc"/>
      <orm-attributes>
        <attribute name="table">dateexception_tutor</attribute>
      </orm-attributes>
    </entity>
    <association from="\App\Models\DateexceptionTutor" to="\App\Models\DateException" owner-alias="dateexceptionTutors" inverse-alias="dateExceptions" many-owner="false" many-inverse="false" parent-required="true" uuid="f1727a3a-9845-4fde-b400-f62f029741ad">
      <association-field from="date_exception_id" to="id" uuid="64e3535b-9a21-4461-8327-6acfc7dfb690"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\DateexceptionTutor" to="\App\Models\User" owner-alias="dateexceptionTutors" inverse-alias="users" many-owner="false" many-inverse="false" parent-required="true" uuid="3731e6d8-80b7-4073-85ee-2e01a848ae98">
      <association-field from="user_id" to="id" uuid="786d6cb6-06d0-4320-a9d1-775b5fbe9a67"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <entity name="\App\Models\Domain" local-name="Domain" namespace="\App\Models" uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b">
      <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="1c0dbc85-22c2-4e16-bad0-80a345b62d78"/>
      <field name="domain_name" type="string" size="255" required="true" uuid="0b942573-0c2e-4c84-85a8-907d3c7939df"/>
      <field name="language" type="string" size="2" default="en" required="true" uuid="4abb013d-bac7-484e-8f7c-616c064885bd"/>
      <field name="lookup_url" type="string" size="255" required="true" uuid="29e6016a-4447-42dc-a893-f50a5d3175ed"/>
      <field name="website_url" type="string" size="255" uuid="45c48d9c-6c47-48bc-ac58-ea69a0d4d135"/>
      <field name="logo_url" type="string" size="255" uuid="7bf4897b-2d6b-430f-a592-45acf7f4f719"/>
      <field name="rates_conditions_url" type="string" size="255" uuid="84d48ecc-4e98-4d0b-a10e-4c5187a19095"/>
      <field name="privacy_url" type="string" size="255" uuid="************************************"/>
      <field name="name" type="string" size="50" required="true" uuid="7357f4e4-e4fb-4aa3-9dd5-1fb20d03a57a"/>
      <field name="address1" type="string" size="85" required="true" uuid="224d02db-8612-42ea-8d62-84631d3a7ab1"/>
      <field name="address2" type="string" size="85" required="true" uuid="482f53e4-8598-4829-bc94-eb5d967cbaf5"/>
      <field name="zip" type="string" size="10" required="true" uuid="828e3e21-364d-4fc1-8e86-807b6bee5736"/>
      <field name="city" type="string" size="85" required="true" uuid="98319d40-5395-4bb0-be99-12f88f3a3f56"/>
      <field name="telephone" type="string" size="15" uuid="27bb27ca-1b35-4ce5-82ef-98222f77b235"/>
      <field name="email" type="string" size="35" required="true" uuid="9d4f9650-37d4-4fe9-87bb-aaffd42c6830"/>
      <field name="adult_threshold" type="integer" uuid="2b4ab67b-7c5e-431a-8109-9f2d7069ccc7"/>
      <field name="contact_person_name" type="string" size="50" uuid="db67c9ec-3d5f-418d-8700-21259d11d438"/>
      <field name="course_tax_rate" type="double" size="3" default="21.0" required="true" uuid="870b723e-6c7c-44cc-b926-109cc778035d">
        <orm-attributes>
          <attribute name="decimal">1</attribute>
        </orm-attributes>
      </field>
      <field name="default_password" type="string" size="15" required="true" uuid="7005a7fd-dbf7-4fbf-a2a3-10bd475d307e"/>
      <field name="schedule_threshold" type="integer" uuid="c33dba2d-6017-483d-baaf-49b93f9011d4"/>
      <field name="status" type="enum" size="5" enum-values="'trial','pro'" required="true" uuid="dc882893-724d-4155-a809-d9d130212909"/>
      <field name="trial_end_date" type="date" uuid="accb0671-cda0-4c4b-a0ad-c4a601a7b7ee"/>
      <field name="warn_before_birthday" type="integer" default="10" required="true" uuid="2bd4d89b-f739-436e-acac-6c9d1b44e247"/>
      <field name="warn_before_adult" type="integer" default="30" required="true" uuid="5f1ecf71-f60f-4265-96e1-e4000db11f5c"/>
      <field name="domaintoken" type="text" uuid="6012f778-4163-4499-ac09-de0422d96990"/>
      <field name="allowed_ip_addresses" type="text" uuid="313e233e-cdc8-4d71-a33d-7a9a93ff342e"/>
      <field name="broadcast_colors" type="string" size="50" uuid="b9c9e15a-e818-402b-b11a-505020e885e2"/>
      <field name="created_at" type="timestamp" uuid="31f1ee8d-b684-49d5-aa3f-2d322a8325c4"/>
      <field name="updated_at" type="timestamp" uuid="f36aa44a-7255-42a1-b133-55c64dfdcad5"/>
      <orm-attributes>
        <attribute name="table">domains</attribute>
      </orm-attributes>
    </entity>
    <entity name="\App\Models\Task" local-name="Task" namespace="\App\Models" uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d">
      <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="b2a56956-0c9b-46bd-b816-ee296b5b4ad2"/>
      <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="3cd43c90-7c8f-4159-9d57-05c9629a8d9d"/>
      <field name="tasktype_id" type="integer" required="true" unsigned="true" uuid="5520291a-9ac4-49fe-8cb5-fc95228f13a7"/>
      <field name="student_id" type="integer" unsigned="true" uuid="b8f97249-bdb0-40c8-8b74-a81576967d87"/>
      <field name="course_id" type="integer" unsigned="true" uuid="bd87ab92-ccbe-44f6-97a9-24c4d402b933"/>
      <field name="tutor_id" type="integer" unsigned="true" uuid="f3e6b3f0-d827-4cbf-b9f4-d0793a2f1c6a"/>
      <field name="registration_id" type="integer" unsigned="true" uuid="18f6da4f-ee17-4056-8de7-42684d76e7f6"/>
      <field name="event_id" type="integer" unsigned="true" uuid="81782c87-2c5b-4f21-943a-3eda48d66dd2"/>
      <field name="assigned_user_id" type="integer" unsigned="true" uuid="83428949-47b0-48fa-8c3f-8b9502e77549"/>
      <field name="date_opened" type="date" required="true" uuid="6550080f-85e7-4917-b3db-8e844a691638"/>
      <field name="date_due" type="date" uuid="ff0cafe4-f12c-46be-b52f-d91a006564d5"/>
      <field name="date_closed" type="date" uuid="5681101c-a184-44c3-9310-3203e01cc0c2"/>
      <field name="remarks" type="text" uuid="cf4f62a8-1543-46fd-8464-292de387d4a3"/>
      <field name="created_at" type="timestamp" uuid="d8e98af2-7fbd-4048-adc3-d3dedd2cc80a"/>
      <field name="updated_at" type="timestamp" uuid="1cb642a9-6fec-4187-8266-f06a61030f22"/>
      <index name="domain_task_domain_id_foreign" uuid="bd79395f-b3f7-4172-a7be-0998b978277d">
        <index-field name="domain_id" uuid="544e5c30-f403-4333-b2f8-9e67bcacaf45"/>
      </index>
      <orm-attributes>
        <attribute name="table">tasks</attribute>
      </orm-attributes>
    </entity>
    <association from="\App\Models\Task" to="\App\Models\User" owner-alias="tasksViaAssignedUserId" inverse-alias="usersViaAssignedUserId" many-owner="true" many-inverse="false" uuid="4b65b521-e809-44a2-8df2-3801fb791855">
      <association-field from="assigned_user_id" to="id" uuid="5741fb1c-e983-4740-a1f1-22f45008669e"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Task" to="\App\Models\Course" owner-alias="tasks" inverse-alias="courses" many-owner="true" many-inverse="false" uuid="22c2bcd1-4112-42e9-9649-f66fd4c1cbee">
      <association-field from="course_id" to="id" uuid="4eb37f10-ec53-4337-953a-3ec7f58f3485"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Task" to="\App\Models\Domain" owner-alias="tasks" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="e8b1d77f-613a-4170-b4e7-6ab5e59d2ec8">
      <association-field from="domain_id" to="id" uuid="7e26efa3-5ff6-4d6a-b6e4-96cea4e97e30"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Task" to="\App\Models\Event" owner-alias="tasks" inverse-alias="events" many-owner="true" many-inverse="false" uuid="269e9223-3b7c-4c7e-a165-0324f30533a3">
      <association-field from="event_id" to="id" uuid="19bc2e40-3ef7-4ca7-815e-2dee01f6760e"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Task" to="\App\Models\CourseStudent" owner-alias="tasks" inverse-alias="courseStudent" many-owner="true" many-inverse="false" uuid="f7d2bc4c-e95e-4739-b28a-bedf7f52873c">
      <association-field from="registration_id" to="id" uuid="45c93e80-9b5e-489f-9939-7846f0fc47d6"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Task" to="\App\Models\Student" owner-alias="tasks" inverse-alias="students" many-owner="true" many-inverse="false" uuid="e1c528fe-4cf3-4353-a93f-bf475fb5578e">
      <association-field from="student_id" to="id" uuid="bfa86b45-8c10-46e9-84c3-9c6f504c0dac"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Task" to="\App\Models\Tasktype" owner-alias="tasks" inverse-alias="tasktypes" many-owner="true" many-inverse="false" parent-required="true" uuid="456272d0-2430-4b0c-9ea7-0386bf04c937">
      <association-field from="tasktype_id" to="id" uuid="6d564468-063f-4da0-8060-7be00663b84d"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Task" to="\App\Models\User" owner-alias="tasksViaTutorId" inverse-alias="usersViaTutorId" many-owner="true" many-inverse="false" uuid="bf8ffb2a-c900-43e1-8d57-dc8bce94959e">
      <association-field from="tutor_id" to="id" uuid="4db1d496-**************-b14986aef13b"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <entity name="\App\Models\Tasktype" local-name="Tasktype" namespace="\App\Models" uuid="f69b0190-690b-46ad-9697-cf47b35a3bdb">
      <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="b22d604a-312c-4255-879d-1a319b7611f2"/>
      <field name="description" type="string" size="25" required="true" uuid="a90195e7-2906-46fc-8641-e19769fabf36"/>
      <field name="created_at" type="timestamp" uuid="0fee319d-394a-4e7c-9bb0-5f90ec953c4f"/>
      <field name="updated_at" type="timestamp" uuid="2e8c1798-f985-4696-8694-c641d6279f79"/>
      <orm-attributes>
        <attribute name="table">tasktypes</attribute>
      </orm-attributes>
    </entity>
    <region namespace="\App\Models" caption="Planning" uuid="7d796ff8-b92a-4489-b903-a045ff907135">
      <entity name="\App\Models\Attendancenote" local-name="Attendancenote" namespace="\App\Models" uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="4959d66a-7f38-41fa-8fea-4aef19de1aef"/>
        <field name="student_id" type="integer" unsigned="true" uuid="aaa2b675-f590-48ce-b28b-c0f854d98a6b"/>
        <field name="event_id" type="integer" required="true" unsigned="true" uuid="b7a512e5-cab5-494b-801a-5f13b94333f7"/>
        <field name="attendanceoption_id" type="bigInteger" unsigned="true" uuid="c4d738ec-f227-4316-88f1-942cb777d036"/>
        <field name="notes" type="text" uuid="5718af63-c4ed-4a9c-920e-bfa1e4010848"/>
        <field name="created_at" type="timestamp" uuid="7125895d-4eb1-480c-8573-92cd68605fc8"/>
        <field name="updated_at" type="timestamp" uuid="6318fa7b-84a1-46b4-821d-3edffa47d218"/>
        <field name="updated_by" type="string" size="255" uuid="87cb4b73-f8d6-4eff-9320-afae89124060"/>
        <orm-attributes>
          <attribute name="table">attendancenotes</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Attendanceoption" local-name="Attendanceoption" namespace="\App\Models" uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="49c8a19a-78ac-40ec-80e6-91fad566bc44"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="215d7f10-9f78-49f0-8ee4-6f8114157de3"/>
        <field name="label" type="string" size="255" required="true" uuid="856ef411-c9fd-458b-a967-1e23045d2ed1"/>
        <field name="action_tutor" type="string" size="255" uuid="02235e70-**************-f9c9be1b5485"/>
        <field name="created_at" type="timestamp" uuid="fcf83ddc-9b55-4358-a150-080fae20ee30"/>
        <field name="updated_at" type="timestamp" uuid="a1bdf226-82c4-4f26-88de-a1ddeb79ead0"/>
        <orm-attributes>
          <attribute name="table">attendanceoptions</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\DateException" local-name="DateException" namespace="\App\Models" uuid="6ba88ab3-4143-4362-864a-fac809bc5b14">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="1027ed24-57fc-49b6-a0b7-5b8225d60413"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="f8c96715-d22d-4f5f-954b-baf10c96c709"/>
        <field name="schoolyear_id" type="integer" unsigned="true" uuid="ee9eb5e6-0d84-4584-acd8-cbf37033f1d7"/>
        <field name="location_id" type="integer" unsigned="true" uuid="cfb49d56-1e2b-46dc-bf3d-b38d38a5a9ac"/>
        <field name="datetime_start" type="dateTime" required="true" uuid="31fde653-479e-4a90-b6ec-1d809bfe9030"/>
        <field name="datetime_end" type="dateTime" required="true" uuid="75d2d1f3-c90a-4bdb-99b9-c67fd3aa21cb"/>
        <field name="reason" type="string" size="255" required="true" uuid="29862cc1-a3d0-461a-9f11-7bc66408df8a"/>
        <field name="plan_blocking" type="boolean" default="1" required="true" uuid="e765eb4a-fd36-461b-a89f-0384fa2e94f6"/>
        <field name="exclude_from_alerts" type="boolean" default="0" required="true" uuid="9844e03c-7314-496a-ad0d-30c6cecc202e"/>
        <field name="calendar_color" type="string" size="15" default="#836EC3" required="true" uuid="b2ae55d7-5eb6-453b-bff2-cf835fc6d616"/>
        <field name="detail_url" type="string" size="255" uuid="4d34ee6f-762b-4c40-b159-c9484129f219"/>
        <field name="created_at" type="timestamp" uuid="4206a6ad-da95-4838-b931-a6bf69602cd4"/>
        <field name="updated_at" type="timestamp" uuid="5b35786a-9172-48e2-a30a-2b908619e633"/>
        <index name="date_exception_domain_domain_id_foreign" uuid="917e372e-2808-4fed-8b3e-e6ae9c8c60bf">
          <index-field name="domain_id" uuid="d9ccd6fe-28fe-466c-a071-5ea331cec466"/>
        </index>
        <orm-attributes>
          <attribute name="table">date_exceptions</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Event" local-name="Event" namespace="\App\Models" uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="dd07d277-e1be-4372-9765-29b42a55dab6"/>
        <field name="location_id" type="integer" unsigned="true" uuid="b83eafbb-3d9d-4432-84d9-edb0b4b74c8d"/>
        <field name="tutor_id" type="integer" unsigned="true" uuid="b8d24715-3f86-4b97-84a7-5ebbe4470e7d"/>
        <field name="caluniqueid" type="string" size="32" required="true" uuid="06c3a449-515d-4cd1-8cd3-e92104c2dab6"/>
        <field name="timetable_id" type="integer" unsigned="true" uuid="b738fcbc-f591-462d-b4b4-c7dac35b0783"/>
        <field name="datetime" type="dateTime" required="true" uuid="5d68f58a-d578-4983-a9af-10c94c870640"/>
        <field name="original_datetime" type="dateTime" default="2000-01-01 23:59:59" required="true" uuid="41ba7ed6-dc1d-404e-8df4-2a221c30c62f"/>
        <field name="sequence" type="integer" default="0" required="true" uuid="a7d10b80-e828-4bf6-a2d2-55e7442a5edb"/>
        <field name="timespan" type="text" required="true" uuid="bf1fd5d2-0fe0-4377-88f3-f4c927795247"/>
        <field name="remarks" type="string" size="150" uuid="7e90ef2b-1b69-4a71-96d4-24337435e5c5"/>
        <field name="flag_sticky" type="boolean" default="0" required="true" uuid="ead7b98f-5c6e-4a8a-bf02-8894bf5dbd9c"/>
        <field name="flag_publish" type="boolean" default="0" uuid="324d369f-482e-4fde-bc3e-360ccedb4a1b"/>
        <field name="created_at" type="timestamp" uuid="9c95850b-6438-4f13-ad33-b1f57261f570"/>
        <field name="updated_at" type="timestamp" uuid="90126ad1-c4d8-41d2-8380-6adf132a81d0"/>
        <orm-attributes>
          <attribute name="table">events</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Location" local-name="Location" namespace="\App\Models" uuid="ef423929-3822-431c-a4bb-36e67d80a964">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="0e4fae86-f8b6-4019-9e4e-5695ee03220c"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="cd6ca238-240d-44e1-bd4f-5b9f469e2516"/>
        <field name="name" type="string" size="255" required="true" uuid="68bd620d-6776-4373-8a32-60866db8ec71"/>
        <field name="created_at" type="timestamp" uuid="ec4abb96-1bf1-4e80-ada9-84bd2075ebd7"/>
        <field name="updated_at" type="timestamp" uuid="320377db-9f1d-4667-8e3a-964b39dd19be"/>
        <index name="domain_location_domain_id_foreign" uuid="01f6d54d-02ee-4813-a307-debb08d88074">
          <index-field name="domain_id" uuid="edafa91f-89e7-49d3-8710-53a6a6390852"/>
        </index>
        <orm-attributes>
          <attribute name="table">locations</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\PlanningEntriesChange" local-name="PlanningEntriesChange" namespace="\App\Models" uuid="2a98b125-f664-422e-9097-b8896608cafa">
        <field name="id" type="integer" required="true" unique="true" primary="true" auto-increment="true" uuid="c15b2e00-301e-48ce-9c2c-38655d70ff33"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="003c0421-8f7b-40d5-89bf-3d21c4430dc6"/>
        <field name="change" type="enum" size="6" enum-values="'insert','update','delete'" required="true" uuid="c15f852f-9e48-4af6-bcbc-76dc322c9442"/>
        <field name="planningentry_id" type="integer" unsigned="true" uuid="6d736061-46fa-4270-afe4-78a6fce64f74"/>
        <field name="new_tutor_id" type="integer" unsigned="true" uuid="fe7e331d-7da2-4a31-b65f-469de5c5f961"/>
        <field name="new_location_id" type="integer" unsigned="true" uuid="24a9ed41-d222-4075-890b-aab0ff0d2f90"/>
        <field name="new_starttime" type="time" uuid="1f14bd6e-9fd9-460e-b2cd-0fce235e0314"/>
        <field name="new_daynumber" type="integer" uuid="f806ae9e-f2c3-4448-bf8b-1e28738640e8"/>
        <field name="new_oddeven" type="enum" size="7" enum-values="'odd','even','oddeven','other'" uuid="aea18727-055b-4767-9cd6-740cac9adebb"/>
        <field name="status" type="enum" size="7" default="initial" enum-values="'initial','handled','failed'" required="true" uuid="674eae94-9f5b-4fc5-95fa-d4f3ed519235"/>
        <field name="created_at" type="timestamp" uuid="9d661525-29cb-4f15-80da-d80585b6d1f9"/>
        <field name="updated_at" type="timestamp" uuid="c669af82-419f-450b-a7be-b4e3e9a4233e"/>
        <orm-attributes>
          <attribute name="table">planning_entries_changes</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Planningentry" local-name="Planningentry" namespace="\App\Models" uuid="b7435152-5821-4b19-9e50-701520fe7704">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="93c3cf69-b5bf-48ce-8a4b-da2448b8da56"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="3e431bb1-10ea-487c-a144-f3b179516f33"/>
        <field name="registration_id" type="integer" required="true" unsigned="true" uuid="c6a52e92-5b39-4de8-8945-2772caed5190"/>
        <field name="tutor_id" type="integer" unsigned="true" uuid="670a2898-e934-4335-b623-f1cb0fd49dfd"/>
        <field name="location_id" type="integer" unsigned="true" uuid="b7699c57-1610-4e14-8f3c-253ef7770c7f"/>
        <field name="starttime" type="time" required="true" uuid="dd154762-f0c8-430f-9c64-f94912b072bd"/>
        <field name="duration" type="integer" default="1" required="true" uuid="f55d843d-16f7-41b3-b307-7cf98b7914a6"/>
        <field name="daynumber" type="integer" default="1" required="true" uuid="49e39d4b-dc23-4cac-b81e-b74a1720198d"/>
        <field name="oddeven" type="enum" size="7" default="other" enum-values="'odd','even','oddeven','other'" required="true" uuid="acf77095-07d1-4c1c-94f5-0ba1515d44c3"/>
        <field name="created_at" type="timestamp" uuid="e459fa0e-aeae-4656-9ad3-1840056709e8"/>
        <field name="updated_at" type="timestamp" uuid="963efe5c-d1e8-416c-9785-25326be12147"/>
        <orm-attributes>
          <attribute name="table">planningentries</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Scheduleproposal" local-name="Scheduleproposal" namespace="\App\Models" uuid="011269e1-cbaa-44f1-b534-fb730b2998cd">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="2aeb2d89-0b28-401a-b9da-39e1c6911a5e"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="eb7e9a8b-b041-4634-b4d2-4fb5284024d0"/>
        <field name="registration_id" type="integer" required="true" unsigned="true" uuid="afbe1a9c-b441-4ae3-8cfd-333453b36da4"/>
        <field name="location_id" type="integer" required="true" unsigned="true" uuid="52f2b213-05a4-4cef-b0a7-9f587898f59f"/>
        <field name="tutor_id" type="integer" required="true" unsigned="true" uuid="00954e05-8843-44e8-9414-4896eb43ada1"/>
        <field name="schedule_dt" type="string" size="255" uuid="a7fff893-f821-40e9-a904-664603a38e7f"/>
        <field name="created_at" type="timestamp" uuid="a62bc0ed-9416-45bd-87b0-48e5d039278c"/>
        <field name="updated_at" type="timestamp" uuid="0b0916b5-8e32-449c-af22-c1c7c7ff6cb3"/>
        <index name="scheduleproposals_registration_id_unique" unique="true" uuid="06d3ce4e-fd0a-4368-8c25-3527019d415b">
          <index-field name="registration_id" uuid="dcbae3cf-0b5f-43c8-b63e-7bcbe5708e43"/>
        </index>
        <orm-attributes>
          <attribute name="table">scheduleproposals</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Schoolyear" local-name="Schoolyear" namespace="\App\Models" uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="365aef2a-d72c-4c35-a3d0-7d2ab4fc4612"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="bdeab4fe-af41-493b-816d-f5c05f37682e"/>
        <field name="label" type="string" size="25" required="true" uuid="ac0bad23-2a56-40c8-8272-aae7fca5be74"/>
        <field name="start_year" type="integer" required="true" uuid="0c2b83f9-6931-4ea8-8645-04fa7873e6a1"/>
        <field name="start_date" type="date" default="1970-01-01" required="true" uuid="2f7438a6-9ba1-4e21-baf9-4820b7cf9fdc"/>
        <field name="end_date" type="date" default="1970-01-01" required="true" uuid="df9f4a68-eb40-4db2-8408-4e26c9d1db42"/>
        <field name="end_year" type="integer" required="true" uuid="1e09827f-3f87-4a02-9c3c-ca61928a15b1"/>
        <field name="created_at" type="timestamp" uuid="a3dc9e57-cdc7-4878-b240-8518fc47928f"/>
        <field name="updated_at" type="timestamp" uuid="685200d9-4a94-4798-a94f-9ca36d65f22a"/>
        <index name="domain_schoolyear_domain_id_foreign" uuid="230f471d-e76e-4c6d-b51d-d4c81573c274">
          <index-field name="domain_id" uuid="ead0fd57-6c43-496a-842f-74ca9e7c0617"/>
        </index>
        <orm-attributes>
          <attribute name="table">schoolyears</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Timetable" local-name="Timetable" namespace="\App\Models" uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="f3c20d50-867b-4582-b7c3-95476b8cffa7"/>
        <field name="schoolyear_id" type="integer" unsigned="true" uuid="c2c26d84-e4a8-4f7e-9fe5-63d861e716e6"/>
        <field name="course_student_id" type="integer" unsigned="true" uuid="c2a0bbcb-fb5a-48e3-927d-3e5310caee81"/>
        <field name="description" type="text" uuid="5b95c570-20d9-4f05-b322-c9abfc8caee4"/>
        <field name="created_at" type="timestamp" uuid="5a05ddcf-191a-41c5-8ab4-0b27f2e01631"/>
        <field name="updated_at" type="timestamp" uuid="fd1f21d5-8f6d-4333-ad3d-f70ea35c5e34"/>
        <orm-attributes>
          <attribute name="table">timetables</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\Attendancenote" to="\App\Models\Attendanceoption" owner-alias="attendancenotes" inverse-alias="attendanceoptions" many-owner="true" many-inverse="false" uuid="e88a69d6-065f-43bc-9a63-8c3c4fffa21c">
      <association-field from="attendanceoption_id" to="id" uuid="d5c99019-50a4-4701-8269-a69c41093359"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Attendancenote" to="\App\Models\Event" owner-alias="attendancenotes" inverse-alias="events" many-owner="true" many-inverse="false" parent-required="true" uuid="b12184b0-3fa4-41af-b99a-cb6686e346be">
      <association-field from="event_id" to="id" uuid="b82a0eb2-1364-4e8f-9996-0b13909f7ab7"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Attendancenote" to="\App\Models\Student" owner-alias="attendancenotes" inverse-alias="students" many-owner="true" many-inverse="false" uuid="5025ad30-5fd2-454e-9e6d-c61455d03601">
      <association-field from="student_id" to="id" uuid="49133189-a842-4f66-ae33-30474a024b85"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Attendanceoption" to="\App\Models\Domain" owner-alias="attendanceoptions" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="1d2f74fb-4152-4ae6-8d51-8585820bb9fc">
      <association-field from="domain_id" to="id" uuid="333548f8-8527-4336-a233-3f56226b9528"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\DateException" to="\App\Models\Domain" owner-alias="dateExceptions" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="9f54fb6d-abfd-43a0-a513-ea5459b6311a">
      <association-field from="domain_id" to="id" uuid="a616fd35-dbeb-4c0e-a19a-039c1dcdb983"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\DateException" to="\App\Models\Location" owner-alias="dateExceptions" inverse-alias="locations" many-owner="true" many-inverse="false" uuid="9cde0869-dbeb-408e-926d-c9c2e976aa0e">
      <association-field from="location_id" to="id" uuid="51517d07-c476-420d-8d69-15634ecbeb86"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\DateException" to="\App\Models\Schoolyear" owner-alias="dateExceptions" inverse-alias="schoolyears" many-owner="true" many-inverse="false" uuid="83c5b7e3-**************-c768731e81c4">
      <association-field from="schoolyear_id" to="id" uuid="30528ece-e5f8-408e-ac17-2c33408308f0"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Event" to="\App\Models\Location" owner-alias="events" inverse-alias="locations" many-owner="true" many-inverse="false" uuid="813ea696-1f40-47e8-9dc2-131d578944fe">
      <association-field from="location_id" to="id" uuid="903d7952-4c62-4de9-86cc-6bb6f31217f8"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Event" to="\App\Models\Timetable" owner-alias="events" inverse-alias="timetables" many-owner="true" many-inverse="false" uuid="11bca13d-9211-4098-ab82-31756edc13c7">
      <association-field from="timetable_id" to="id" uuid="f0057e4e-d967-4886-b5b4-5e7326f921f6"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Event" to="\App\Models\User" owner-alias="events" inverse-alias="users" many-owner="true" many-inverse="false" uuid="b2693963-f971-428a-92af-1e35240d339b">
      <association-field from="tutor_id" to="id" uuid="3a85307b-e91e-497e-b9de-a031a5422392"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Location" to="\App\Models\Domain" owner-alias="locations" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="314bffe1-fbf6-40c0-9644-62c1d581f016">
      <association-field from="domain_id" to="id" uuid="9d5b100d-ede5-444e-badf-065f37640bd6"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\PlanningEntriesChange" to="\App\Models\Domain" owner-alias="planningEntriesChanges" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="9d9b83fe-d7d6-4999-8ff4-688c4f0ec4a1">
      <association-field from="domain_id" to="id" uuid="7c02c618-5606-4928-ac29-b04a1c1cfe8c"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\PlanningEntriesChange" to="\App\Models\Location" owner-alias="planningEntriesChanges" inverse-alias="locations" many-owner="true" many-inverse="false" uuid="ca43fd42-322b-46f4-8c9c-f536601d9e83">
      <association-field from="new_location_id" to="id" uuid="8f57028e-9f6b-4164-9e18-50b6390f798c"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\PlanningEntriesChange" to="\App\Models\User" owner-alias="planningEntriesChanges" inverse-alias="users" many-owner="true" many-inverse="false" uuid="ff09776d-a26f-4ee4-bff3-1183b537b1b5">
      <association-field from="new_tutor_id" to="id" uuid="1fb53876-9acb-48d6-a98d-a712ae685333"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\PlanningEntriesChange" to="\App\Models\Planningentry" owner-alias="planningEntriesChanges" inverse-alias="planningentries" many-owner="true" many-inverse="false" uuid="805ab5e4-964a-4081-ab94-d2959e0c0b15">
      <association-field from="planningentry_id" to="id" uuid="eac25daa-49cd-4026-8240-d55947307f80"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Planningentry" to="\App\Models\Domain" owner-alias="planningentries" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="4bede789-0ad8-472f-865f-a6cc56494218">
      <association-field from="domain_id" to="id" uuid="003f3a4f-8459-4748-8f68-8d68a122c2b7"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Planningentry" to="\App\Models\Location" owner-alias="planningentries" inverse-alias="locations" many-owner="true" many-inverse="false" uuid="b9c597e6-a803-4a3e-85c8-6da346e0c31a">
      <association-field from="location_id" to="id" uuid="32c9b071-7582-4262-8a52-3242736a20f6"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Planningentry" to="\App\Models\CourseStudent" owner-alias="planningentries" inverse-alias="courseStudent" many-owner="true" many-inverse="false" parent-required="true" uuid="12050ccb-2af5-41ec-af76-ab793893484f">
      <association-field from="registration_id" to="id" uuid="58bc7d3e-39c8-45d5-b452-01fc897b3bc9"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Planningentry" to="\App\Models\User" owner-alias="planningentries" inverse-alias="users" many-owner="true" many-inverse="false" uuid="af30b4b2-a85b-495c-b30e-772238391dde">
      <association-field from="tutor_id" to="id" uuid="*************-4cc4-afe0-5277ecc86a3c"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Scheduleproposal" to="\App\Models\Domain" owner-alias="scheduleproposals" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="1086a0dc-2c3b-4431-9151-e02b634a1398">
      <association-field from="domain_id" to="id" uuid="cb551b14-8da8-4429-91e2-47ad152db8a6"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Scheduleproposal" to="\App\Models\Location" owner-alias="scheduleproposals" inverse-alias="locations" many-owner="true" many-inverse="false" parent-required="true" uuid="d7a46353-7aee-4e8f-b553-24f5ebaf3eb3">
      <association-field from="location_id" to="id" uuid="e26a42a4-db7e-42d3-8862-aa8f845c8ab4"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Scheduleproposal" to="\App\Models\CourseStudent" owner-alias="scheduleproposals" inverse-alias="courseStudent" many-owner="true" many-inverse="false" parent-required="true" uuid="07f092b7-95d0-43cb-bbcc-7ccb48f83ed8">
      <association-field from="registration_id" to="id" uuid="cb1f0036-7b6e-4534-a0ed-47eda4a62d89"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Scheduleproposal" to="\App\Models\User" owner-alias="scheduleproposals" inverse-alias="users" many-owner="true" many-inverse="false" parent-required="true" uuid="f6665d67-2798-454b-a32b-335b3615df02">
      <association-field from="tutor_id" to="id" uuid="a2c7ba8f-23c7-40bf-b16e-652c9283f9a7"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Schoolyear" to="\App\Models\Domain" owner-alias="schoolyears" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="41bd98a3-ddcc-4ca6-9acb-c05abcc5c5c4">
      <association-field from="domain_id" to="id" uuid="32608a8b-afe5-4b64-ae17-f8302aba5f82"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Timetable" to="\App\Models\CourseStudent" owner-alias="timetables" inverse-alias="courseStudent" many-owner="true" many-inverse="false" uuid="e215b9d2-c316-4e13-9d73-5667b7de1c5c">
      <association-field from="course_student_id" to="id" uuid="bf2f2741-cbc1-46ad-a4c1-1711e1d8ee0d"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Timetable" to="\App\Models\Schoolyear" owner-alias="timetables" inverse-alias="schoolyears" many-owner="true" many-inverse="false" uuid="a4263efa-b60d-4ba4-aa7b-c6f29800179d">
      <association-field from="schoolyear_id" to="id" uuid="926e8130-6a52-4501-8dcd-36fa63ffbf72"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="ClassY" uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc">
      <entity name="\App\Models\Document" local-name="Document" namespace="\App\Models" uuid="8cd208d8-de83-4695-9468-02073ea1f00f">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="b069947c-c3d8-4663-82b7-ebfacad00c29"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="36677065-445a-46c7-9f4d-5a2373df4f3e"/>
        <field name="event_id" type="integer" unsigned="true" uuid="28cd60f4-969a-4c15-a377-ec01b2bb80ee"/>
        <field name="file_name" description="name as saved on the stack server" type="string" size="255" required="true" uuid="127a1101-f2b5-4bab-8c08-e5597c27a814"/>
        <field name="original_name" description="name of the original file during upload" type="string" size="155" uuid="ac157ee4-9f79-4a66-ac1b-d9f2ca24c3b9"/>
        <field name="description" type="text" uuid="6f655fed-6fb8-4250-99a2-05841bb55cc7"/>
        <field name="type" type="enum" size="4" enum-values="'file','url','audio','video'" required="true" uuid="6f437310-b073-465f-bedd-5f8a217b53fb"/>
        <field name="content_type" type="string" size="50" uuid="0d9ce1da-addd-4921-b514-9838c3f398ce"/>
        <field name="label" type="string" size="50" required="true" uuid="96dc2784-1399-4a28-8e5e-80709adc0fe1"/>
        <field name="url" type="string" size="255" uuid="8a0e2691-9a0e-414b-a351-18a7dc42d1ea"/>
        <field name="crc" type="string" size="255" uuid="5bedab7f-9f11-4020-aa30-cfe7c51083d3"/>
        <field name="created_at" type="timestamp" uuid="b894a703-bee8-4fef-af93-b979516ba268"/>
        <field name="updated_at" type="timestamp" uuid="ccf4e52c-20e3-4101-8a72-93b26d521ec1"/>
        <index name="domain_document_domain_id_foreign" uuid="44407047-01b2-4e2f-8201-3a3fad9f31c1">
          <index-field name="domain_id" uuid="8065b284-11d6-4371-b8e8-a1bb5cc6a9df"/>
        </index>
        <orm-attributes>
          <attribute name="table">documents</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Library" local-name="Library" namespace="\App\Models" uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="40746c69-04fb-48ce-a721-2016e406ec80"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="526e1761-ea8f-43d5-8b1c-fbba3f92051d"/>
        <field name="tutor_id" type="integer" required="true" unsigned="true" uuid="4b9f3e25-eecf-4755-8336-f1b3e62ddd8f"/>
        <field name="label" type="string" size="50" required="true" uuid="216d7023-8b3f-472a-b822-5568c8bc9b2a"/>
        <field name="share_with_whole_school" type="boolean" default="0" required="true" uuid="6f0dc744-fc0c-4fd9-8794-92bc14a0ee2d"/>
        <field name="created_at" type="timestamp" uuid="d6fa110a-c7af-4b1f-92c8-c059beede88b"/>
        <field name="updated_at" type="timestamp" uuid="bfdb7b36-7383-4aa8-bdce-b6fe3de16dff"/>
        <orm-attributes>
          <attribute name="table">libraries</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\LibraryCourse" local-name="LibraryCourse" namespace="\App\Models" uuid="b8404cea-9e03-48c5-b2e8-d2014a185356">
        <field name="course_id" type="integer" required="true" unsigned="true" uuid="e2ba3db5-f8f3-4eac-a903-7cebf8c9c788"/>
        <field name="library_id" type="integer" required="true" unsigned="true" uuid="6a1505b2-ced1-4f70-85a8-b1794aa259d9"/>
        <field name="created_at" type="timestamp" uuid="f7c05055-be04-4291-81b0-b58435074e3c"/>
        <field name="updated_at" type="timestamp" uuid="8b675610-629e-4705-a9cf-3433654d9c9c"/>
        <orm-attributes>
          <attribute name="table">library_course</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\LibraryCoursegroup" local-name="LibraryCoursegroup" namespace="\App\Models" uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf">
        <field name="coursegroup_id" type="integer" required="true" unsigned="true" uuid="a6f7ae14-fd0d-4287-96ed-19408883ab55"/>
        <field name="library_id" type="integer" required="true" unsigned="true" uuid="6fb85621-**************-9b1c14a9c8b8"/>
        <field name="created_at" type="timestamp" uuid="a0642570-40a2-46d9-b0a0-58ecfa1a85e0"/>
        <field name="updated_at" type="timestamp" uuid="7fbb198d-fb32-49ca-a87b-fcedbc6c8516"/>
        <orm-attributes>
          <attribute name="table">library_coursegroup</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\LibraryDocument" local-name="LibraryDocument" namespace="\App\Models" uuid="ceb6e565-00ec-43e2-8146-5e052d702270">
        <field name="document_id" type="integer" required="true" unsigned="true" uuid="e4e88a59-e2bc-45a8-be70-d07ee74308e2"/>
        <field name="library_id" type="integer" required="true" unsigned="true" uuid="d6918bdc-f9d6-4218-b821-cf2e162c107b"/>
        <field name="created_at" type="timestamp" uuid="fc962001-19bc-4ab6-a655-6ba0e5025432"/>
        <field name="updated_at" type="timestamp" uuid="e41a3e66-2a02-4018-8758-5f582314bc89"/>
        <orm-attributes>
          <attribute name="table">library_document</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\LibraryStudent" local-name="LibraryStudent" namespace="\App\Models" uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc">
        <field name="student_id" type="integer" required="true" unsigned="true" uuid="a9251b9f-eb8d-4ae4-aa9f-c52224686bc8"/>
        <field name="library_id" type="integer" required="true" unsigned="true" uuid="41ec4db7-2125-47a2-b167-df5d0f0946f4"/>
        <field name="created_at" type="timestamp" uuid="51c171e1-10d8-4ad7-ab4f-91629d8780c3"/>
        <field name="updated_at" type="timestamp" uuid="fa532052-b832-4f21-bf21-77e847ff9d37"/>
        <orm-attributes>
          <attribute name="table">library_student</attribute>
        </orm-attributes>
      </entity>
      <comment caption="Classy" description="These entities are mainly used for Classy, comunicating docs and instructions between tutor and student." uuid="ed70e4c9-3ee4-483a-b44d-04628047d830"/>
      <comment caption="including student group" uuid="08d97bf2-4057-4e15-912f-4b7149a0a509"/>
    </region>
    <association from="\App\Models\Document" to="\App\Models\Domain" owner-alias="documents" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="ab290cce-b8d0-4eb6-b310-765bdb4ad543">
      <association-field from="domain_id" to="id" uuid="ea5671b7-af80-41f1-bc36-61dcddc2db3c"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Document" to="\App\Models\Event" owner-alias="documents" inverse-alias="events" many-owner="true" many-inverse="false" uuid="b4a07b82-dac7-4ca6-ad74-10e3520f2d00">
      <association-field from="event_id" to="id" uuid="7de8156e-6ae8-4646-a521-e76a90954597"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Library" to="\App\Models\Domain" owner-alias="libraries" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="c13051a6-cc30-4f84-9826-a2bdd69a105d">
      <association-field from="domain_id" to="id" uuid="b18bcbbb-987b-44c2-a4b2-158d61afef00"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Library" to="\App\Models\User" owner-alias="libraries" inverse-alias="users" many-owner="true" many-inverse="false" parent-required="true" uuid="dd8d1d23-8267-44f0-b184-b64baaec8740">
      <association-field from="tutor_id" to="id" uuid="69e9beed-4f7b-4228-aa92-982543693a33"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryCourse" to="\App\Models\Course" owner-alias="libraryCourses" inverse-alias="courses" many-owner="true" many-inverse="false" parent-required="true" uuid="deb7a666-a98c-4fe7-86bd-bed6fec8e554">
      <association-field from="course_id" to="id" uuid="e87e596c-ea49-4972-a88e-8e4695ac7d2a"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryCourse" to="\App\Models\Library" owner-alias="libraryCourses" inverse-alias="libraries" many-owner="true" many-inverse="false" parent-required="true" uuid="5eaac0de-fe0c-4628-bed1-a39b7bab1daf">
      <association-field from="library_id" to="id" uuid="da15748d-151a-4ba5-88e4-3ce0a27fe6db"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryCoursegroup" to="\App\Models\Coursegroup" owner-alias="libraryCoursegroups" inverse-alias="coursegroups" many-owner="true" many-inverse="false" parent-required="true" uuid="8cda6768-4413-46a0-9068-9cf8da9d9270">
      <association-field from="coursegroup_id" to="id" uuid="48bf1dfd-6222-4b67-8678-043f50867879"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryCoursegroup" to="\App\Models\Library" owner-alias="libraryCoursegroups" inverse-alias="libraries" many-owner="true" many-inverse="false" parent-required="true" uuid="99fec556-aeb7-4e8e-8044-7b4caefbf39d">
      <association-field from="library_id" to="id" uuid="0c82e87a-0b47-401f-a0ce-6e803eadbfaa"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryDocument" to="\App\Models\Document" owner-alias="libraryDocuments" inverse-alias="documents" many-owner="true" many-inverse="false" parent-required="true" uuid="6d447a7e-ff56-4a41-97e4-e6c8a75b5ac4">
      <association-field from="document_id" to="id" uuid="03b74e67-b6f4-486c-b7a8-65a3e6f1caad"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryDocument" to="\App\Models\Library" owner-alias="libraryDocuments" inverse-alias="libraries" many-owner="true" many-inverse="false" parent-required="true" uuid="f0ac4ad6-1989-4e6e-a700-ac1788868f83">
      <association-field from="library_id" to="id" uuid="99977f23-022e-4f25-a3d7-81fddbd07e85"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryStudent" to="\App\Models\Library" owner-alias="libraryStudents" inverse-alias="libraries" many-owner="true" many-inverse="false" parent-required="true" uuid="d377efb6-3c55-4402-bd41-eb1a53b4a97c">
      <association-field from="library_id" to="id" uuid="dda6f17a-2fdd-422e-b311-b61433ade3ab"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\LibraryStudent" to="\App\Models\Student" owner-alias="libraryStudents" inverse-alias="students" many-owner="true" many-inverse="false" parent-required="true" uuid="635a8982-006b-4df9-97f8-126d5d07305d">
      <association-field from="student_id" to="id" uuid="16de081c-87ae-480d-b26e-f960e58dbdce"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="Tutor/User" uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac">
      <entity name="\App\Models\Availability" local-name="Availability" namespace="\App\Models" uuid="24be36a3-768b-4bde-bc50-2239b5545201">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="05125437-f699-4e22-847d-7d4286f3fadc"/>
        <field name="tutor_id" type="integer" required="true" unsigned="true" uuid="dc61b14b-3256-4e46-aee5-b45092f7d517"/>
        <field name="day_number" type="tinyInteger" required="true" uuid="8a266596-dedf-4461-a2ed-e9cbdce99298"/>
        <field name="from_time" type="time" uuid="9364c908-5fb5-4604-9b18-1540f8e0a75d"/>
        <field name="to_time" type="time" uuid="d29f4563-c9df-4ab3-8637-f86f8a5ceeef"/>
        <field name="created_at" type="timestamp" uuid="e963091f-7648-4957-88f7-322f5bfa24bd"/>
        <field name="updated_at" type="timestamp" uuid="a627419a-8185-495a-afee-1f12cd68a695"/>
        <orm-attributes>
          <attribute name="table">availabilities</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Loginsecurity" local-name="Loginsecurity" namespace="\App\Models" uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="5dae1d1a-4645-4e33-bcf7-9ce47946157f"/>
        <field name="user_id" type="integer" required="true" unsigned="true" uuid="fae7d048-b54e-41d2-95e9-d413c6e92dbc"/>
        <field name="google2fa_enable" type="boolean" default="0" required="true" uuid="9cd829c0-2394-4d44-b6a3-188e4deb4bdb"/>
        <field name="google2fa_secret" type="string" size="255" uuid="690bafde-2d90-4d21-8213-09f41ca4b67f"/>
        <field name="created_at" type="timestamp" uuid="1046a4dc-0e32-415c-948b-fb96aba31123"/>
        <field name="updated_at" type="timestamp" uuid="03179f7d-1608-4050-8a13-98dc65c19e33"/>
        <orm-attributes>
          <attribute name="table">loginsecurities</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\RoleUser" local-name="RoleUser" namespace="\App\Models" uuid="bf934382-8829-4844-b190-4b8c4bdafe2d">
        <field name="user_id" type="integer" unsigned="true" uuid="ac99ce09-6edc-4c1f-9ab1-4c3153ae0c6d"/>
        <field name="role_id" type="integer" unsigned="true" uuid="d61cf553-2576-4fdd-8f03-6f3143f4de65"/>
        <field name="calcolor" type="string" size="6" default="5f6f7e" required="true" uuid="0b084fee-7fbd-471a-9f02-5c1cac783386"/>
        <field name="start_date" type="date" default="2015-01-01" required="true" uuid="3c48da3f-4c4e-463c-b24a-accbe568260d"/>
        <field name="end_date" type="date" uuid="0d0d0b6c-4f71-499f-83d2-cb2d6b680c18"/>
        <field name="created_at" type="timestamp" uuid="71e19a15-978f-4d68-9761-5fd11a8470a1"/>
        <field name="updated_at" type="timestamp" uuid="bcbc0e45-632b-4e4f-9331-cefcc328212c"/>
        <orm-attributes>
          <attribute name="table">role_user</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Role" local-name="Role" namespace="\App\Models" uuid="c10c1531-15c0-4e26-8860-4d6068585afa">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="527c0d31-079c-4dec-8bf4-ec5a0b89e488"/>
        <field name="rolename" type="string" size="15" required="true" uuid="f7cb2429-77c3-4fc3-8344-0c1fa4f47232"/>
        <field name="created_at" type="timestamp" uuid="f969baf7-35ab-49d7-acb8-bb9f3cd8a409"/>
        <field name="updated_at" type="timestamp" uuid="8e134f05-9012-466a-bf81-b069fb37e158"/>
        <orm-attributes>
          <attribute name="table">roles</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\User" local-name="User" namespace="\App\Models" uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="2f26a517-9bb5-4f8c-9d98-022d95764af4"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="451ce281-1dee-4273-853a-20ae25e53346"/>
        <field name="name" type="string" size="255" required="true" uuid="97fbcba6-22c3-453a-954b-c32b4ad97f25"/>
        <field name="avatar" type="string" size="255" uuid="918200b1-8e33-4327-a0a7-2e04ca7a1dfa"/>
        <field name="hexcolor" type="string" size="30" uuid="7fd19a18-73a0-424e-bd09-5dc4ce121412"/>
        <field name="email" type="string" size="255" required="true" uuid="99c16f69-e128-4df0-819d-47df458c1900"/>
        <field name="password" type="string" size="255" required="true" uuid="c71554a2-4286-472c-a1a4-80cb3789507d"/>
        <field name="telephone" type="string" size="15" uuid="6d7cd4d8-a84b-411c-8e9b-1951ca2598b7"/>
        <field name="telephone_extra" type="string" size="15" uuid="afa6893b-d5fa-4ac5-bb9f-8b4a22e7e377"/>
        <field name="remember_token" type="string" size="100" uuid="485e8ba9-41bb-45ab-861d-d8cf6c28da7a"/>
        <field name="preferred_language" type="string" size="2" uuid="65fefcef-a7e9-4941-8137-574b56d78e2a"/>
        <field name="created_at" type="timestamp" uuid="576716fd-6baf-4229-a456-9bc870e71dd6"/>
        <field name="updated_at" type="timestamp" uuid="78f1d441-9ca5-42b8-b423-51d71e86f328"/>
        <field name="last_active_at" type="timestamp" uuid="62af128d-e8b0-4ff2-be58-7a51b1088d8f"/>
        <index name="domain_user_domain_id_foreign" uuid="7dd9b4ee-94fc-4d96-863a-9eb9ae1a0c71">
          <index-field name="domain_id" uuid="3e7a1fc3-5b42-4170-9714-5272e593a4c6"/>
        </index>
        <index name="users_email_unique" unique="true" uuid="9b4ed301-d149-42f5-bac7-f69ed436df2b">
          <index-field name="email" uuid="92cdb21d-7ff9-41f1-965b-3222b0d77b34"/>
        </index>
        <orm-attributes>
          <attribute name="table">users</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\Availability" to="\App\Models\User" owner-alias="availabilities" inverse-alias="users" many-owner="true" many-inverse="false" parent-required="true" uuid="9c5f96bf-16a7-4df3-a146-903c2c81ec1c">
      <association-field from="tutor_id" to="id" uuid="0c949da5-875b-4d4b-b115-22282d1e545f"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Loginsecurity" to="\App\Models\User" owner-alias="loginsecurities" inverse-alias="users" many-owner="true" many-inverse="false" parent-required="true" uuid="6e25f026-53cc-4fc9-8adf-726cc1924026">
      <association-field from="user_id" to="id" uuid="f8837813-9fc2-4251-a4b1-3246eea422cc"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\RoleUser" to="\App\Models\Role" owner-alias="roleUsers" inverse-alias="roles" many-owner="true" many-inverse="false" uuid="09209b58-f2ab-4dbc-af18-ffba95fa3c11">
      <association-field from="role_id" to="id" uuid="20652d29-3444-4b43-bf45-4c611d2031ec"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\RoleUser" to="\App\Models\User" owner-alias="roleUsers" inverse-alias="users" many-owner="true" many-inverse="false" uuid="0c4df872-ce8d-451d-a75b-e4fca4cbe66d">
      <association-field from="user_id" to="id" uuid="36a38280-d359-4e44-85cb-eff756fa3466"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\User" to="\App\Models\Domain" owner-alias="users" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="84256974-8cd9-4ac7-b144-451905cb6e66">
      <association-field from="domain_id" to="id" uuid="e49a0411-b754-46f4-89ff-978f16886e6a"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="Student" uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa">
      <entity name="\App\Models\Logentry" local-name="Logentry" namespace="\App\Models" uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="717a7eae-b0c3-4807-96d6-b30dc731b7c0"/>
        <field name="student_id" type="integer" required="true" unsigned="true" uuid="9577a00a-5f6f-4373-bde7-ebd87dc83e9d"/>
        <field name="entry" type="text" uuid="0eee24cc-616a-4dd9-828d-a3554936cf33"/>
        <field name="created_at" type="timestamp" uuid="b1b4981d-478b-494b-a2f8-8120fed0227c"/>
        <field name="updated_at" type="timestamp" uuid="7bff5a8b-9faa-444c-86f8-7ec626f9a44e"/>
        <orm-attributes>
          <attribute name="table">logentries</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\SchedulePref" local-name="SchedulePref" namespace="\App\Models" uuid="c9e0f153-ba0b-4843-9837-1f982d71537d">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="565a955d-094e-49c6-bbc1-9da9db32ccb1"/>
        <field name="student_id" type="integer" required="true" unsigned="true" uuid="2c87cb1a-43db-46b4-8ef2-49889230b99a"/>
        <field name="monday" type="string" size="255" uuid="cf93296c-2949-49ce-86b9-e4ef6fc5682f"/>
        <field name="tuesday" type="string" size="255" uuid="680113a3-57ab-40d7-9b45-7595168a7f4f"/>
        <field name="wednesday" type="string" size="255" uuid="0a38b358-b15d-4cfe-a5a8-e45a7fc9b314"/>
        <field name="thursday" type="string" size="255" uuid="2c1cfdf5-4607-4be0-b01a-a5c967c308d4"/>
        <field name="friday" type="string" size="255" uuid="4e463394-fef9-4729-89a0-90e1f30ef896"/>
        <field name="saturday" type="string" size="255" uuid="8b287f29-b792-4be3-9467-91fbd7358627"/>
        <field name="sunday" type="string" size="255" uuid="0217e49b-d97c-4025-ac05-6b7599710da7"/>
        <field name="created_at" type="timestamp" uuid="84472db6-e05f-402a-937d-83ab2768e0ff"/>
        <field name="updated_at" type="timestamp" uuid="832e5a51-8c2d-420f-b4da-3ad3bd8a0e69"/>
        <orm-attributes>
          <attribute name="table">schedule_prefs</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\StudentStudentgroup" local-name="StudentStudentgroup" namespace="\App\Models" uuid="72f556d7-609b-405b-88a4-8f6d4587b032">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="286196d9-d502-4b9c-9e6b-9ff2b1c6734a"/>
        <field name="student_id" type="integer" default="0" required="true" unsigned="true" uuid="a0ea1f1c-c708-4f7f-93a5-b754ccd70361"/>
        <field name="studentgroup_id" type="integer" default="0" required="true" unsigned="true" uuid="88200540-74b5-44df-8ccf-50f7174b7473"/>
        <field name="as_trial_student" type="integer" default="0" required="true" uuid="6d1eba1c-6a29-43e6-a639-095cee1a30e8"/>
        <field name="start_date" type="date" default="2016-01-01" required="true" uuid="542a7639-1c9c-423b-ba5b-b41ba2074694"/>
        <field name="end_date" type="date" uuid="ab02a776-a05a-4385-943c-725ed9ed4d32"/>
        <field name="created_at" type="timestamp" uuid="8cdc11ec-7031-46d0-b167-5b5787ec4e23"/>
        <field name="updated_at" type="timestamp" uuid="31288fa7-cd95-42ef-b1b0-359dcc5d7b5e"/>
        <orm-attributes>
          <attribute name="table">student_studentgroup</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\StudentStudentlist" local-name="StudentStudentlist" namespace="\App\Models" uuid="e2056103-3473-42f2-a865-4a63f378d655">
        <field name="student_id" type="integer" required="true" unique="true" primary="true" unsigned="true" uuid="8defe66a-4f0f-4297-b0a2-fd1980ee74a1"/>
        <field name="studentlist_id" type="integer" required="true" unique="true" primary="true" unsigned="true" uuid="*************-4f88-8e1d-c7b131b8844e"/>
        <field name="created_at" type="timestamp" uuid="cca6af82-cf9e-4927-a63d-8b9b49acdf73"/>
        <field name="updated_at" type="timestamp" uuid="a345e116-b894-4e35-ac13-64847e45b102"/>
        <orm-attributes>
          <attribute name="table">student_studentlist</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Studentcontact" local-name="Studentcontact" namespace="\App\Models" uuid="709b0eec-1818-4251-856b-44fd280b3812">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="d62a8bce-f4ce-442a-83b6-94c714adb86e"/>
        <field name="student_id" type="integer" required="true" unsigned="true" uuid="dd1a5992-b975-46a3-a411-01c51a4d18b7"/>
        <field name="contacttype" type="enum" size="9" enum-values="'telephone','email'" required="true" uuid="82e3ece1-072f-4ac9-9e12-b7efddc24d72"/>
        <field name="label" type="string" size="255" uuid="1caa7430-3c12-43db-af0c-c08378c4a6f0"/>
        <field name="value" type="string" size="100" required="true" uuid="b52c2a4e-84a7-496d-820c-0e6f06d08c42"/>
        <field name="apply_for_planning" type="boolean" uuid="89bc8b04-70d0-46c1-a5fc-00f9c82234f3"/>
        <field name="apply_for_finance" type="boolean" uuid="154c26d4-028d-47ce-8434-53d806803d00"/>
        <field name="apply_for_promotions" type="boolean" uuid="810b405c-4e7a-4bb8-8bae-08559234b14d"/>
        <field name="use_salutation" type="string" size="255" uuid="a5710f43-f4aa-4a2a-b736-129cd280877f"/>
        <field name="created_at" type="timestamp" uuid="3dad6b74-98a9-4a0d-b361-5504594de16c"/>
        <field name="updated_at" type="timestamp" uuid="97cbc9df-3d23-480d-9083-3bda3ce0c79b"/>
        <orm-attributes>
          <attribute name="table">studentcontacts</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Studentlist" local-name="Studentlist" namespace="\App\Models" uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="7161224c-47d5-4aa3-b85e-67e099f71f86"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="6851d581-1bac-45bd-a9d9-b2296d9d8943"/>
        <field name="name" type="string" size="50" required="true" uuid="c679bc49-d76a-4302-9d38-b4a7ff133a6f"/>
        <field name="hexcolor" type="string" size="6" required="true" uuid="fd548684-668d-4c60-b16d-2d6d9f8f1d5b"/>
        <field name="remarks" type="text" uuid="32abdb69-4c45-4f41-96f8-7e947e1fe95e"/>
        <field name="created_at" type="timestamp" uuid="8081c521-d4c9-45a7-8885-dbad51f5368e"/>
        <field name="updated_at" type="timestamp" uuid="dfb1964c-1868-44ea-9562-0daca79ae6f8"/>
        <index name="domain_studentlist_domain_id_foreign" uuid="5e3bbd99-86bd-444e-a4c5-7b3fbcb13859">
          <index-field name="domain_id" uuid="0f4bbef0-2667-4d5a-b4be-4b6bc47335da"/>
        </index>
        <orm-attributes>
          <attribute name="table">studentlists</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Student" local-name="Student" namespace="\App\Models" uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="113edf63-d0c2-45f2-92ec-bb20794cb5b1"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="f6c49026-a67e-4f22-af7c-e329ecd12571"/>
        <field name="name" type="string" size="255" required="true" uuid="60dd1189-be6c-4199-9a75-3a89e6fe23a1"/>
        <field name="firstname" type="string" size="45" uuid="dbc0e15f-3c9a-493a-9bbb-f37c80da60ed"/>
        <field name="preposition" type="string" size="15" uuid="b9b3836f-ef54-4969-af1b-4084609d209c"/>
        <field name="lastname" type="string" size="75" required="true" uuid="6f6411f2-1244-4923-a3f2-277998cfe93b"/>
        <field name="address" type="string" size="255" uuid="338b3b40-f965-45cc-b802-5cf6ce2c357c"/>
        <field name="zipcode" type="string" size="255" uuid="********-fcd9-4dba-8ca3-588ac392f5ee"/>
        <field name="city" type="string" size="255" uuid="aaae03e7-b023-4b59-8c6f-b223b3cf98ea"/>
        <field name="date_of_birth" type="date" required="true" uuid="ee6a25e5-f05e-44a8-ae65-6576bf78e282"/>
        <field name="permission_auto_banktransfer" type="string" size="255" uuid="ee300ab5-cd2e-4023-a278-fad45763cf73"/>
        <field name="bankaccount_name" type="string" size="255" uuid="f64d1890-db96-48ea-8ae2-1732f20f70b5"/>
        <field name="bankaccount_number" type="string" size="255" uuid="fd168a2d-a20f-4650-bf11-c40a4707e6c6"/>
        <field name="mandate_number" type="string" size="255" uuid="0795fb82-5952-4f38-9455-d8997f62cc02"/>
        <field name="remarks" type="text" uuid="e0cc9641-6aa0-4a14-ac9b-5902bdeb5b37"/>
        <field name="status" type="string" size="255" required="true" uuid="a4b5639f-05e9-4ccf-ad05-88b223504622"/>
        <field name="accesstoken" type="string" size="32" uuid="ebf15672-1b48-4b66-8e78-3bd97f67c82e"/>
        <field name="apipin" type="integer" uuid="6ab47dd4-bb42-436a-825a-e65a41e5277f"/>
        <field name="has_access" type="tinyInteger" uuid="dd5f3d3a-cf8d-4f49-9828-db378718a8cc"/>
        <field name="agreeSocialShare" type="tinyInteger" default="0" required="true" uuid="d509d106-f2ee-4f37-85de-1483447a9370"/>
        <field name="min_participants" type="integer" default="2" uuid="3c7a9dab-3699-4f69-ae5f-577149ee18ee"/>
        <field name="max_participants" type="integer" default="99" uuid="e80596c8-064e-4c44-b3e4-e7c7ab84cf12"/>
        <field name="created_at" type="timestamp" uuid="8531861b-a2a5-47f7-aa5e-0bff98a180b3"/>
        <field name="updated_at" type="timestamp" uuid="0fbb9032-3058-42f8-9131-799555193dc3"/>
        <index name="domain_student_domain_id_foreign" uuid="6ec7281f-d131-4ebd-9685-97ee7e51432f">
          <index-field name="domain_id" uuid="a9e2b358-011d-40c8-b666-32d603b81a6f"/>
        </index>
        <orm-attributes>
          <attribute name="table">students</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Trialrequeststatus" local-name="Trialrequeststatus" namespace="\App\Models" uuid="68a489f5-2ff4-4359-9710-085720368ca6">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="bd735aa4-060b-4d14-8cdb-8db5c49b67a7"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="054d0b46-db73-4f1e-948e-2c05384e6976"/>
        <field name="description" type="string" size="100" required="true" uuid="a53e9a18-744a-4f20-a93c-bfff4a8aa144"/>
        <field name="needs_admin_action" type="boolean" default="1" required="true" uuid="4e212625-48fa-4c16-810b-b2d41c38bdfb"/>
        <field name="created_at" type="timestamp" uuid="95be7402-bfa2-474d-b781-972a76ca3805"/>
        <field name="updated_at" type="timestamp" uuid="d77f6a3f-ee3c-4f1e-bf9c-d60c4378683d"/>
        <orm-attributes>
          <attribute name="table">trialrequeststatuses</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Trialstudent" local-name="Trialstudent" namespace="\App\Models" uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="9420aa9c-fbbd-43b3-b827-eb4ec56bb7d5"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="675cbd4d-a761-4539-81cf-3f1df8c2d65a"/>
        <field name="trialrequeststatus_id" type="bigInteger" default="1" required="true" unsigned="true" uuid="7ab5d2a9-d344-466b-a4f4-7ec98f64e29a"/>
        <field name="firstname" type="string" size="45" required="true" uuid="72d00514-7f09-48ca-b213-719fd7d6cb14"/>
        <field name="preposition" type="string" size="15" uuid="b1fec482-3995-4c01-bab6-b79515257cc0"/>
        <field name="lastname" type="string" size="45" required="true" uuid="90e3fceb-58e2-4ea5-b6f5-df8837a8ff41"/>
        <field name="date_of_birth" type="date" uuid="ea6f5555-4a92-4cc0-9551-04e352e970c5"/>
        <field name="telephone" type="string" size="255" uuid="2ca59906-c8c5-4858-818e-40269693922d"/>
        <field name="email" type="string" size="255" uuid="38865f87-13c3-4885-b2cf-94e4af177296"/>
        <field name="course_id" type="integer" unsigned="true" uuid="94f0f7b6-be22-45f1-9b43-7b251a287cf0"/>
        <field name="requested_startdate" type="string" size="255" uuid="1cbdce48-24c7-4387-b049-f7f7e56c944b"/>
        <field name="remarks" type="text" uuid="f0b976be-c0c1-42d0-bd1f-b25af1483d9a"/>
        <field name="generated_registration_id" type="integer" unsigned="true" uuid="4fc534cb-3636-4f6d-be69-060469d62ef6"/>
        <field name="generated_student_id" type="integer" unsigned="true" uuid="f03ad90e-7bba-4767-8c8a-98085822dc62"/>
        <field name="created_at" type="timestamp" uuid="ac4f3c15-70ec-4906-a874-806246687fd1"/>
        <field name="updated_at" type="timestamp" uuid="0d981367-91da-44c7-8642-438bdb3cd1bd"/>
        <index name="domain_trialstudent_domain_id_foreign" uuid="e66b67dc-cef5-481d-9e8c-d26c375c62c7">
          <index-field name="domain_id" uuid="340cca15-ec34-48f5-a31a-88d73bf553da"/>
        </index>
        <orm-attributes>
          <attribute name="table">trialstudents</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\Logentry" to="\App\Models\Student" owner-alias="logentries" inverse-alias="students" many-owner="true" many-inverse="false" parent-required="true" uuid="f1340b43-1b0d-44bc-ad76-3537cfc9f409">
      <association-field from="student_id" to="id" uuid="95f9715e-263b-4a48-9232-ad54d1391895"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\SchedulePref" to="\App\Models\Student" owner-alias="schedulePrefs" inverse-alias="students" many-owner="true" many-inverse="false" parent-required="true" uuid="81334048-52d0-44c3-992a-a47f0f1c2741">
      <association-field from="student_id" to="id" uuid="5850d881-aec0-4167-bc0b-0491a398effa"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">CASCADE</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\StudentStudentgroup" to="\App\Models\Student" owner-alias="studentStudentgroupViaStudentgroupId" inverse-alias="studentsViaStudentgroupId" many-owner="true" many-inverse="false" parent-required="true" uuid="4f888a6c-0349-427f-bbc8-f01b95094805">
      <association-field from="studentgroup_id" to="id" uuid="04f29184-a843-4a4e-aee9-b5a527afdbc1"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\StudentStudentgroup" to="\App\Models\Student" owner-alias="studentStudentgroupViaStudentId" inverse-alias="studentsViaStudentId" many-owner="true" many-inverse="false" parent-required="true" uuid="3368badc-f7cb-48f0-be21-c3671b02eb91">
      <association-field from="student_id" to="id" uuid="d483dfcd-df6a-4a59-8e75-fad27f4e3f64"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\StudentStudentlist" to="\App\Models\Studentlist" owner-alias="studentStudentlists" inverse-alias="studentlists" many-owner="false" many-inverse="false" parent-required="true" uuid="9a7f4a49-de23-4b26-88a6-9e5d659290d2">
      <association-field from="studentlist_id" to="id" uuid="8381c4ab-de2c-4591-b4ba-3c4235b1756a"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\StudentStudentlist" to="\App\Models\Student" owner-alias="studentStudentlists" inverse-alias="students" many-owner="false" many-inverse="false" parent-required="true" uuid="00d196dd-bb81-4b00-92e7-4444d68c0ea4">
      <association-field from="student_id" to="id" uuid="c0ca33d4-99b9-433c-9531-057cf967ee54"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Studentcontact" to="\App\Models\Student" owner-alias="studentcontacts" inverse-alias="students" many-owner="true" many-inverse="false" parent-required="true" uuid="bce5d578-2190-4d87-b8b2-586d794f8276">
      <association-field from="student_id" to="id" uuid="0509bb49-2231-4ba0-98c7-34fc375a4a1d"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Studentlist" to="\App\Models\Domain" owner-alias="studentlists" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="fdda2275-54e9-4fb5-8464-9cf1b25f2da3">
      <association-field from="domain_id" to="id" uuid="1775f967-da7a-44d6-87dc-0ee2fbb0ab93"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Student" to="\App\Models\Domain" owner-alias="students" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="5263e8f0-4194-4ef6-88ac-ddb52061089d">
      <association-field from="domain_id" to="id" uuid="2235f5b4-5c42-474b-bc3e-3eaf231daec0"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Trialrequeststatus" to="\App\Models\Domain" owner-alias="trialrequeststatuses" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="4a667653-9b4a-4cc1-9c49-bb8b2d17426a">
      <association-field from="domain_id" to="id" uuid="d50cf3ad-a057-483b-84ec-23cf54fe2391"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Trialstudent" to="\App\Models\Course" owner-alias="trialstudents" inverse-alias="courses" many-owner="true" many-inverse="false" uuid="1464b9e0-4852-4abc-8c0d-8548f8a010ef">
      <association-field from="course_id" to="id" uuid="57ec9fad-b35a-4a77-b13e-803f4fda53ee"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Trialstudent" to="\App\Models\Domain" owner-alias="trialstudents" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="630e42d9-eab3-42ea-8fb6-639af59ae2b7">
      <association-field from="domain_id" to="id" uuid="34e7800c-6d8c-4666-af47-38eee3f74eeb"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Trialstudent" to="\App\Models\CourseStudent" owner-alias="trialstudents" inverse-alias="courseStudent" many-owner="true" many-inverse="false" uuid="3e8e337e-1dd3-4c1d-a885-56e69658e00a">
      <association-field from="generated_registration_id" to="id" uuid="f180f810-a92d-4628-9e86-781654165e4a"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Trialstudent" to="\App\Models\Student" owner-alias="trialstudents" inverse-alias="students" many-owner="true" many-inverse="false" uuid="7e899f8c-8143-46ed-b6a9-d9917e8e8b6c">
      <association-field from="generated_student_id" to="id" uuid="292ad35d-5aa6-4faf-9b32-49342bdf7175"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Trialstudent" to="\App\Models\Trialrequeststatus" owner-alias="trialstudents" inverse-alias="trialrequeststatuses" many-owner="true" many-inverse="false" parent-required="true" uuid="8b3de001-85f4-410a-ad59-8cdc750f6a2b">
      <association-field from="trialrequeststatus_id" to="id" uuid="1283543d-7a8e-4336-a8aa-ad0d72cd27c6"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="Registration" uuid="8f2fdea8-8ba7-469b-991e-9610187d4a3a">
      <entity name="\App\Models\Checklist" local-name="Checklist" namespace="\App\Models" uuid="87e30c38-9117-4c2e-83b1-38930f88de0e">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="32737be7-9dbd-4842-abd6-2738c98e9500"/>
        <field name="registration_id" type="integer" unsigned="true" uuid="cd9db68e-0d86-4ecd-93b6-a442d99af92e"/>
        <field name="name" type="string" size="255" required="true" uuid="47768724-c13f-4354-94b3-b5f1a2b5a71b"/>
        <field name="item1" type="string" size="255" uuid="1db1bb0b-13a2-41d5-a57e-f893ae27fd85"/>
        <field name="item1_checked" type="tinyInteger" uuid="089c59fa-a8d4-42d2-9bce-20d80af44f3a"/>
        <field name="item2" type="string" size="255" uuid="938dcd98-f7ee-43e2-a74e-ae4b2adc255f"/>
        <field name="item2_checked" type="tinyInteger" uuid="b9c9e2a7-12a0-422d-9fe6-641f49e12fcb"/>
        <field name="item3" type="string" size="255" uuid="0a7709b1-9f92-46a3-a2ca-4f15ccbe5f75"/>
        <field name="item3_checked" type="tinyInteger" uuid="9c9485d9-86a8-4e62-a0e2-94ab8bfc7ef9"/>
        <field name="item4" type="string" size="255" uuid="213589ae-b1dc-45de-8d80-c08471a99ffc"/>
        <field name="item4_checked" type="tinyInteger" uuid="81670bb1-6c74-4f60-b87d-726d3b518529"/>
        <field name="item5" type="string" size="255" uuid="734b80db-0cd1-4989-bdb8-aaeb59cf036c"/>
        <field name="item5_checked" type="tinyInteger" uuid="94d82080-bd51-4caa-a690-61750a3c7263"/>
        <field name="item6" type="string" size="255" uuid="05769423-ab63-4bf6-a53b-167bb0b589a2"/>
        <field name="item6_checked" type="tinyInteger" uuid="2ba937b5-fcea-4377-9494-79027a92d0a7"/>
        <field name="item7" type="string" size="255" uuid="16ec0376-f40f-487a-8ccf-93d1389c9df5"/>
        <field name="item7_checked" type="tinyInteger" uuid="87e2dfad-883e-4290-bc74-3e2375ac6183"/>
        <field name="item8" type="string" size="255" uuid="841c72ce-1914-4248-bc3f-a23cd10eed04"/>
        <field name="item8_checked" type="tinyInteger" uuid="16fdaa96-c8b9-42b3-aee0-29724135809c"/>
        <field name="item9" type="string" size="255" uuid="3d3f1507-aa0a-407b-99e9-a07e86162c01"/>
        <field name="item9_checked" type="tinyInteger" uuid="c9ae5dd8-41b1-4668-a88b-23ae56375e67"/>
        <field name="item10" type="string" size="255" uuid="25c57b49-28c7-4a96-8771-9514eec37764"/>
        <field name="item10_checked" type="tinyInteger" uuid="82c4926c-3f4e-4846-8502-735738d2bb2a"/>
        <field name="item11" type="string" size="255" uuid="7ccbaabb-a88b-47ba-a54b-9ce09f65c048"/>
        <field name="item11_checked" type="tinyInteger" uuid="412fab43-3d65-44f3-a682-c28f3dc4abe0"/>
        <field name="item12" type="string" size="255" uuid="c99427b8-ccc5-4dd0-88af-fea7f0f96556"/>
        <field name="item12_checked" type="tinyInteger" uuid="a4cacc8b-57f6-4ecc-8fe6-fb18bb294934"/>
        <field name="created_at" type="timestamp" uuid="2f0ab28f-74b8-4f61-a371-63912c44f39b"/>
        <field name="updated_at" type="timestamp" uuid="9ad6d4b3-f0d1-489e-80c4-88d676f23094"/>
        <orm-attributes>
          <attribute name="table">checklists</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\CourseStudent" local-name="CourseStudent" namespace="\App\Models" uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="3e53b69c-d2e3-4024-9f9b-5334d7d1a0b6"/>
        <field name="course_id" type="integer" default="0" required="true" unsigned="true" uuid="27e52482-17ed-47a2-93e3-a83803860607"/>
        <field name="student_id" type="integer" default="0" required="true" unsigned="true" uuid="da4c2900-7f75-438d-8eb4-22988a7ac013"/>
        <field name="checklist_id" type="integer" unsigned="true" uuid="6f411441-b2d3-4c28-999f-2033b339d023"/>
        <field name="start_date" type="date" default="2016-01-01" required="true" uuid="2ae5d7f8-15f8-4caf-9f63-5dc5eb24ad18"/>
        <field name="end_date" type="date" uuid="1f9f35b8-f655-4115-992a-a1616979af21"/>
        <field name="signed" type="tinyInteger" uuid="6ce39388-f32f-4431-a1eb-f62888bc2e10"/>
        <field name="status" type="tinyInteger" default="1" uuid="205c8cde-42fc-4c5d-a93f-b14428c7e2fe"/>
        <field name="sign_code" type="string" size="30" uuid="9f16cdc0-5746-435d-85e3-2bfaae71ae5a"/>
        <field name="sign_request_send" type="tinyInteger" uuid="c62af4f3-989e-4b50-8ff9-61b1e5bf647c"/>
        <field name="sign_requested_at" type="timestamp" uuid="d2e03155-445a-4b12-96c6-bcc18aab884d"/>
        <field name="signed_at" type="timestamp" uuid="70ef228b-fce2-4880-83ce-5d209bf9e5ed"/>
        <field name="signed_user_agent" type="string" size="255" uuid="e1502a55-7203-44ff-b703-677228aa73cf"/>
        <field name="please_keep_scheduled_time" type="boolean" uuid="7224400a-e701-48e4-8960-27027a2a6f10"/>
        <field name="ignore_current_schedule" type="tinyInteger" uuid="9e4ac133-3c9c-477c-9087-32911dccff91"/>
        <field name="incidental_price_ex_tax" type="decimal" size="8" uuid="0b5495a5-b70d-4add-abec-c4255e6b5a5e">
          <orm-attributes>
            <attribute name="decimal">2</attribute>
          </orm-attributes>
        </field>
        <field name="incidental_tax_rate" type="double" uuid="76770556-102f-41d7-92a8-9fd8a5fb08db"/>
        <field name="created_at" type="timestamp" uuid="e083a9e9-feeb-451a-bc5a-a1610a04f134"/>
        <field name="updated_at" type="timestamp" uuid="5df446a7-a230-44aa-ba6f-aceaed054acd"/>
        <orm-attributes>
          <attribute name="table">course_student</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\DefaultChecklist" local-name="DefaultChecklist" namespace="\App\Models" uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="5c379f10-2ecc-4c28-ae1a-f684f9e1b362"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="23c1b95e-ac8e-43cb-9a2f-c54613336a56"/>
        <field name="name" type="string" size="255" required="true" uuid="bea2241e-d92f-4a3b-9624-f117fb7a7043"/>
        <field name="auto_add" type="tinyInteger" uuid="d5bd59d2-326e-490e-87ec-b1fce03c5099"/>
        <field name="item1" type="string" size="255" uuid="81a5adbe-d1e5-4cc3-9d95-1e5957bdd0c7"/>
        <field name="item2" type="string" size="255" uuid="6963c6c2-adfa-4a11-8d61-8bfb9cff0e85"/>
        <field name="item3" type="string" size="255" uuid="4d8461e7-dbd3-4a17-8442-4a480654331b"/>
        <field name="item4" type="string" size="255" uuid="d5128cb9-2fd5-4e2d-802d-3d5a3c696acc"/>
        <field name="item5" type="string" size="255" uuid="d6ba2d75-07a6-401d-9552-ffac34109c84"/>
        <field name="item6" type="string" size="255" uuid="a0cb886f-d3a7-4543-9ef1-088387a044c1"/>
        <field name="item7" type="string" size="255" uuid="7fa5bbd9-3427-44c8-ac68-0c2e089301aa"/>
        <field name="item8" type="string" size="255" uuid="1461c2d4-c17c-4a40-a5b5-1dbd394a9995"/>
        <field name="item9" type="string" size="255" uuid="831e635f-571f-4b3a-9dcf-499c8c771d50"/>
        <field name="item10" type="string" size="255" uuid="34f3933f-7b7a-486d-ab82-a01077718523"/>
        <field name="item11" type="string" size="255" uuid="d589f299-a823-4644-80a1-6552c14698b7"/>
        <field name="item12" type="string" size="255" uuid="be79e7f2-da21-4e3d-ab05-ae5f4bb36570"/>
        <field name="created_at" type="timestamp" uuid="d5cca1c5-3aeb-445d-887b-d1911a8af25b"/>
        <field name="updated_at" type="timestamp" uuid="cfaa5a7f-e48b-43c3-afdb-70120fd003b3"/>
        <index name="domain_default_checklist_domain_id_foreign" uuid="3778d88e-8edb-4a5a-aba0-c42e1d4eb27c">
          <index-field name="domain_id" uuid="23801e5a-82b0-45ab-b1ab-3058d3e09dcf"/>
        </index>
        <orm-attributes>
          <attribute name="table">default_checklists</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\Checklist" to="\App\Models\CourseStudent" owner-alias="checklists" inverse-alias="courseStudent" many-owner="true" many-inverse="false" uuid="8aa25083-889c-44ee-818c-bb7037f660b4">
      <association-field from="registration_id" to="id" uuid="b5f24000-6415-4da2-8f3f-fd135e06f3c9"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\CourseStudent" to="\App\Models\Course" owner-alias="courseStudents" inverse-alias="courses" many-owner="true" many-inverse="false" parent-required="true" uuid="b75fee78-71b7-4ff1-97c9-732ca1caa938">
      <association-field from="course_id" to="id" uuid="845a93b2-77a4-4598-9151-017cd50ac661"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\CourseStudent" to="\App\Models\Student" owner-alias="courseStudents" inverse-alias="students" many-owner="true" many-inverse="false" parent-required="true" uuid="59bc29b0-b5c3-4fb1-9e74-030b4806a69b">
      <association-field from="student_id" to="id" uuid="85af0068-4bb5-47fe-bd3b-10e06d4aa919"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\DefaultChecklist" to="\App\Models\Domain" owner-alias="defaultChecklists" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="a3d4a434-abe4-4264-bfc7-621c02e0c379">
      <association-field from="domain_id" to="id" uuid="b5a02862-b5c4-4805-bad4-0eeda93ee316"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="Course" uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0">
      <entity name="\App\Models\Coursegroup" local-name="Coursegroup" namespace="\App\Models" uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="e67298f1-0509-498e-ae99-7f46cf1a4c2b"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="3b0e1309-f456-4e38-bc7f-8bd6c403971c"/>
        <field name="name" type="string" size="255" required="true" uuid="dbbd2860-e037-4bc6-808c-29fda4c6e8df"/>
        <field name="webdescription" type="text" uuid="0279f4d0-8a56-468f-bfab-d2dd749a3478"/>
        <field name="is_trial_group" type="boolean" default="0" required="true" uuid="9d4b7824-e071-4b2a-909f-ceca878dbfb2"/>
        <field name="created_at" type="timestamp" uuid="e835b47d-de7a-4794-a733-5cf04c573c10"/>
        <field name="updated_at" type="timestamp" uuid="c67799af-4390-4b4f-b0d2-9a263112174d"/>
        <index name="domain_coursegroup_domain_id_foreign" uuid="e5f38fe4-747c-4eef-ab1b-863cf2a7e5fc">
          <index-field name="domain_id" uuid="0077a265-d0c6-4403-be90-da17f33a8add"/>
        </index>
        <orm-attributes>
          <attribute name="table">coursegroups</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Course" local-name="Course" namespace="\App\Models" uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="d6cd54e9-21fd-4fa7-bf47-53c24b566462"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="519716b8-06c2-4aee-8de2-ee96e74d2f6b"/>
        <field name="variant_code" type="string" size="255" uuid="49f9e9d5-744d-4b77-8b78-0103242b7e6c"/>
        <field name="coursegroup_id" type="integer" unsigned="true" uuid="3a36ced6-bc3e-47e5-a446-d5a63f10964f"/>
        <field name="recurrenceoption_id" type="integer" unsigned="true" uuid="5ed440ac-a958-417b-ab05-a60a6141945f"/>
        <field name="is_trial_course" type="boolean" uuid="62ad328b-54b3-43d7-aa7f-07c8b0fca089"/>
        <field name="name" type="string" size="255" required="true" uuid="7c7f9332-a058-405f-9cdd-32952ea69541"/>
        <field name="price_ex_tax" type="decimal" size="8" default="0.00" required="true" uuid="a6f9c4be-77b4-4689-a5e0-d395ce47c357">
          <orm-attributes>
            <attribute name="decimal">2</attribute>
          </orm-attributes>
        </field>
        <field name="price_invoice" type="decimal" size="8" default="0.00" required="true" uuid="6b72c505-5c81-4b36-b96d-75b834094aa3">
          <orm-attributes>
            <attribute name="decimal">2</attribute>
          </orm-attributes>
        </field>
        <field name="price_ex_tax_sub_adult" type="decimal" size="8" default="0.00" required="true" uuid="cc3addf1-b64b-43c8-b5c3-43077557e3f8">
          <orm-attributes>
            <attribute name="decimal">2</attribute>
          </orm-attributes>
        </field>
        <field name="price_is_per" type="string" size="15" default="month" required="true" uuid="dc054b00-a1f4-4802-b346-b657115dfc81"/>
        <field name="tax_rate" type="integer" default="0" required="true" uuid="bf7b9b28-3de0-4a12-a7a8-fd734e939393"/>
        <field name="archive" type="boolean" default="0" required="true" uuid="3a8ccc61-f8ff-43fd-86b0-967cd07cf8aa"/>
        <field name="group_size_min" type="integer" default="1" required="true" uuid="a93a5341-1cf5-42e1-b228-2d8fcca8028c"/>
        <field name="group_size_max" type="integer" default="1" required="true" uuid="e3ead94c-4699-4d5a-95df-35ce0382ae30"/>
        <field name="created_at" type="timestamp" uuid="2094c1e4-c449-4b8d-a394-d55945da567a"/>
        <field name="updated_at" type="timestamp" uuid="288051f5-99b3-4bff-9b99-c1a4e22d4920"/>
        <index name="courses_variant_code_unique" unique="true" uuid="bf7b0071-8932-49be-a77a-6b548126c95c">
          <index-field name="variant_code" uuid="0c651856-5f33-40d7-845f-f54ec6c91a6a"/>
        </index>
        <index name="domain_course_domain_id_foreign" uuid="32c5c14a-94d2-4c51-9808-c77e5d6a6ee4">
          <index-field name="domain_id" uuid="a4b57c4e-a7b3-467d-adef-01d752226b72"/>
        </index>
        <index name="Unique variantcode" unique="true" uuid="f55b01b1-2add-4fc7-b169-6ea602eaae01">
          <index-field name="domain_id" uuid="0ff8d544-eda4-4240-beae-98ec57d5ea5b"/>
          <index-field name="variant_code" uuid="a48684f2-f827-4fb6-bcb5-76dd28ec5293"/>
        </index>
        <orm-attributes>
          <attribute name="table">courses</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Coursevariance" local-name="Coursevariance" namespace="\App\Models" uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="1a7ba6a9-fe14-4b0a-b862-f22217b333d1"/>
        <field name="created_at" type="timestamp" uuid="c477db8e-bd34-4432-9732-8d25f549147b"/>
        <field name="updated_at" type="timestamp" uuid="a1015202-ab60-434d-8ea3-a45376fb6efe"/>
        <field name="course_id" type="integer" required="true" unsigned="true" uuid="31e9d86d-dfba-4cb7-afd9-b0cfa281017c"/>
        <field name="recurrenceoption_id" type="integer" required="true" unsigned="true" uuid="d9368677-4097-45c4-9670-323dc97b52ff"/>
        <orm-attributes>
          <attribute name="table">coursevariances</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Priceoption" local-name="Priceoption" namespace="\App\Models" uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="ae9bbe9a-aac1-4abd-9ec6-9680ca4519aa"/>
        <field name="course_id" type="integer" required="true" unsigned="true" uuid="6459de8d-374e-4835-b061-23753c902ed6"/>
        <field name="from_nr_of_students" type="integer" required="true" uuid="fd52aa24-9d45-46d5-b245-35d466bbecfc"/>
        <field name="to_nr_of_students" type="integer" required="true" uuid="c78d621b-5e9f-41e5-8767-510b0ec8fb60"/>
        <field name="price_ex_tax" type="decimal" size="6" default="0.00" required="true" uuid="447e27a3-f259-4c6c-83b2-c9d7cb8e4a0c">
          <orm-attributes>
            <attribute name="decimal">2</attribute>
          </orm-attributes>
        </field>
        <field name="price_is_per" type="string" size="15" default="month" required="true" uuid="d90d784a-af31-4a3d-acda-0f7a8fe8f189"/>
        <field name="tax_rate" type="integer" default="0" required="true" uuid="04954199-ef64-416a-8cd0-ff5aadbba9eb"/>
        <field name="created_at" type="timestamp" uuid="44a791f6-7c9b-454d-a5d0-fa4e2f3bf20d"/>
        <field name="updated_at" type="timestamp" uuid="1973fa8c-f5b6-4225-867a-852d3665000a"/>
        <orm-attributes>
          <attribute name="table">priceoptions</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Recurrenceoption" local-name="Recurrenceoption" namespace="\App\Models" uuid="253849ed-5411-4cec-bd9d-0053c321f423">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="464fcf97-e931-471e-963a-cfa05d2fdd41"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="079e0266-a34d-49b1-89f3-03303c751f4a"/>
        <field name="is_trial_lesson" type="boolean" default="0" uuid="6f5368ea-5c44-491f-a296-81769946fd7b"/>
        <field name="description" type="string" size="80" required="true" uuid="f0f7e0ab-8aa2-4c2b-aa2b-d2904f9c8811"/>
        <field name="nr_of_times" type="double" size="8" required="true" uuid="6dd76fc2-dce1-4c19-8cd0-2fe411e19146">
          <orm-attributes>
            <attribute name="decimal">2</attribute>
          </orm-attributes>
        </field>
        <field name="timeunit" type="string" size="15" required="true" uuid="3b8e1785-8122-4419-ab30-c1f9e7212a79"/>
        <field name="per_interval" type="string" size="15" required="true" uuid="3a1d6a60-af14-4502-96c3-b5b2f4d0687d"/>
        <field name="ends_after_nr_of_occurrences" type="boolean" uuid="2b311125-2000-43a2-b867-433e79cd48ab"/>
        <field name="created_at" type="timestamp" uuid="1a9ab171-d225-4bf9-905a-85c00f3c1838"/>
        <field name="updated_at" type="timestamp" uuid="fafdc948-7037-4a35-bff4-603cc1e6aec8"/>
        <index name="domain_recurrenceoption_domain_id_foreign" uuid="b8eae8dd-b516-4b68-bb5a-abd1525309e6">
          <index-field name="domain_id" uuid="e477c8e7-7a8a-4ca4-b49a-e8c4a86b246d"/>
        </index>
        <orm-attributes>
          <attribute name="table">recurrenceoptions</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\TrialcourseCourse" local-name="TrialcourseCourse" namespace="\App\Models" uuid="05cc3b23-f921-457d-9fac-434a5ebcc928">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="410686c2-fbc5-486c-acf0-3e8662666647"/>
        <field name="trialcourse_id" type="integer" required="true" unsigned="true" uuid="a6bbe5d7-de4d-4820-983b-e6e25f61f919"/>
        <field name="course_id" type="integer" required="true" unsigned="true" uuid="e8af8c95-9d02-4de2-baa1-7f1ab7abcce0"/>
        <field name="created_at" type="timestamp" uuid="2b44a90d-405d-420f-883f-8710fda69913"/>
        <field name="updated_at" type="timestamp" uuid="d2bc8271-a831-4af3-9a88-305bf98206fa"/>
        <orm-attributes>
          <attribute name="table">trialcourse_courses</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\Coursegroup" to="\App\Models\Domain" owner-alias="coursegroups" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="f1d4fd54-d460-48b6-8990-3024207e3663">
      <association-field from="domain_id" to="id" uuid="c8f2570f-221b-48c4-9f8d-710e38bd7cb2"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Course" to="\App\Models\Coursegroup" owner-alias="courses" inverse-alias="coursegroups" many-owner="true" many-inverse="false" uuid="fd72c4e9-ff5f-45e3-9fea-2b12877a0326">
      <association-field from="coursegroup_id" to="id" uuid="98ccf6fc-238b-4c81-a171-262f6aac38da"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Course" to="\App\Models\Domain" owner-alias="courses" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="650a406d-9890-4ca6-9bc0-0adc03fbe746">
      <association-field from="domain_id" to="id" uuid="f9488815-5607-4ac8-9bb6-3ee1535485fc"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Course" to="\App\Models\Recurrenceoption" owner-alias="courses" inverse-alias="recurrenceoptions" many-owner="true" many-inverse="false" uuid="2dc9846a-15c1-46ad-a896-7e4d8bad600d">
      <association-field from="recurrenceoption_id" to="id" uuid="2f12ea00-fc73-46d0-a969-4f2e850f1558"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Coursevariance" to="\App\Models\Course" owner-alias="coursevariances" inverse-alias="courses" many-owner="true" many-inverse="false" parent-required="true" uuid="d7a47c8e-7067-4fb5-9eac-d5380f4977b7">
      <association-field from="course_id" to="id" uuid="f49f4107-b0ea-4674-b603-829f83636c17"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Coursevariance" to="\App\Models\Recurrenceoption" owner-alias="coursevariances" inverse-alias="recurrenceoptions" many-owner="true" many-inverse="false" parent-required="true" uuid="690a229d-955e-4c3e-8e0a-9c489ede5366">
      <association-field from="recurrenceoption_id" to="id" uuid="aa23da80-7d86-4571-938f-5c9059b9510e"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Priceoption" to="\App\Models\Course" owner-alias="priceoptions" inverse-alias="courses" many-owner="true" many-inverse="false" parent-required="true" uuid="f56ce73a-aaab-46c1-bbc3-63734aec26ee">
      <association-field from="course_id" to="id" uuid="f2f96948-1eb0-43c2-b79e-9e9d53b1930d"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Recurrenceoption" to="\App\Models\Domain" owner-alias="recurrenceoptions" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="6024dcfd-f538-46b7-8059-0f62051ac637">
      <association-field from="domain_id" to="id" uuid="7845bb3b-68fb-4944-bc82-5861485d7bc9"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\TrialcourseCourse" to="\App\Models\Course" owner-alias="trialcourseCoursesViaCourseId" inverse-alias="coursesViaCourseId" many-owner="true" many-inverse="false" parent-required="true" uuid="fff36ee4-4b8d-4890-8b80-b23326a4677f">
      <association-field from="course_id" to="id" uuid="f3635fe9-7ec3-44b3-8e11-75a64755bee5"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\TrialcourseCourse" to="\App\Models\Course" owner-alias="trialcourseCoursesViaTrialcourseId" inverse-alias="coursesViaTrialcourseId" many-owner="true" many-inverse="false" parent-required="true" uuid="11cbcc23-5253-4b15-b3e6-e3ad22542445">
      <association-field from="trialcourse_id" to="id" uuid="bf410293-792f-4dd9-bdee-4cf2138a287a"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="Extra tables" uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb">
      <entity name="\App\Models\FailedJob" local-name="FailedJob" namespace="\App\Models" uuid="dd612244-e406-4048-a3d9-5001719eaf66">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="c3057afa-e47b-4c4a-b867-cdcf09a3d496"/>
        <field name="connection" type="text" required="true" uuid="f0d4b654-6782-44db-b097-8e2811455556"/>
        <field name="queue" type="text" required="true" uuid="b5f3eac2-a31b-475a-a492-da3b3403bfd7"/>
        <field name="payload" type="longText" required="true" uuid="7f1e0d64-892e-4d7f-a65b-c42f0f96d077"/>
        <field name="exception" type="longText" required="true" uuid="bed656e8-9254-47ce-ac26-e53e83489a38"/>
        <field name="failed_at" type="timestamp" default="NOW()" required="true" uuid="ded7dbbd-cab4-413f-9236-6c99ffeb868e"/>
        <orm-attributes>
          <attribute name="table">failed_jobs</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Job" local-name="Job" namespace="\App\Models" uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="2489985e-9bf3-44e7-9293-edde06e216ad"/>
        <field name="queue" type="string" size="255" required="true" uuid="b575b7d1-c810-4931-b0b4-69301d064b5c"/>
        <field name="payload" type="longText" required="true" uuid="774d8f92-9657-49a9-b877-d3a35a203abd"/>
        <field name="attempts" type="tinyInteger" required="true" unsigned="true" uuid="675d0926-9bf9-4f79-9bcf-6703b35cb037"/>
        <field name="reserved_at" type="integer" unsigned="true" uuid="0718327b-9244-4354-80c9-541d9ee7a7f9"/>
        <field name="available_at" type="integer" required="true" unsigned="true" uuid="ae66f1f2-d2b3-44cb-8582-a1ec6e0927d4"/>
        <field name="created_at" type="integer" required="true" unsigned="true" uuid="f136ed12-ead3-4979-a8e9-a2fe00df9433"/>
        <index name="jobs_queue_reserved_at_index" uuid="4fe9675a-11ae-4151-9289-f16933d55d43">
          <index-field name="queue" uuid="b85aa21e-e981-4086-a2cb-baa80435458e"/>
          <index-field name="reserved_at" uuid="337ad00c-d9ed-4e4b-ad1d-ba9757d8587b"/>
        </index>
        <orm-attributes>
          <attribute name="table">jobs</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthAccessToken" local-name="OauthAccessToken" namespace="\App\Models" uuid="34879c08-1079-4cee-b770-e42debb31c86">
        <field name="id" type="string" size="100" required="true" unique="true" primary="true" uuid="d23fb2c3-5c2a-4b15-900b-e55b23d504df"/>
        <field name="user_id" type="bigInteger" unsigned="true" uuid="d50a66b1-033c-44cc-81c9-0c8c5b7dbe71"/>
        <field name="client_id" type="bigInteger" required="true" unsigned="true" uuid="7ea84620-4e35-406c-820c-cdc2a4d28663"/>
        <field name="name" type="string" size="255" uuid="106ef390-8efa-495c-bc82-e79e60155c5c"/>
        <field name="scopes" type="text" uuid="67464128-bff4-41d4-88bf-b4635daad936"/>
        <field name="revoked" type="boolean" required="true" uuid="d3e298d7-7ed8-4119-ac88-0c41e93f4c7d"/>
        <field name="created_at" type="timestamp" uuid="fa9741dc-6d5f-4e20-9d46-23442b633619"/>
        <field name="updated_at" type="timestamp" uuid="d8090b68-4990-4f16-ac85-e6614ddff8ef"/>
        <field name="expires_at" type="dateTime" uuid="2296c9ef-cc0e-45af-be46-86407e950cf8"/>
        <index name="oauth_access_tokens_user_id_index" uuid="cfd14618-8295-4219-b964-ff00b07d0f94">
          <index-field name="user_id" uuid="2bd4f22a-eee0-483d-9363-951d172694fb"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_access_tokens</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthAuthCode" local-name="OauthAuthCode" namespace="\App\Models" uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06">
        <field name="id" type="string" size="100" required="true" unique="true" primary="true" uuid="a4830069-a3e3-43e6-b0cc-388b2eccfa15"/>
        <field name="user_id" type="bigInteger" required="true" unsigned="true" uuid="6194356a-2a44-475e-bd91-fa2ce0d449ef"/>
        <field name="client_id" type="bigInteger" required="true" unsigned="true" uuid="5c6e77eb-669f-4064-b81e-0a1d9f5a2c4c"/>
        <field name="scopes" type="text" uuid="65695e74-8a66-426e-83fc-5e3702acbaa0"/>
        <field name="revoked" type="boolean" required="true" uuid="61f78b9e-c6d5-44b9-9d88-7c18431785e9"/>
        <field name="expires_at" type="dateTime" uuid="43e1a466-5ffc-41d5-b9f0-71e88ca780f8"/>
        <index name="oauth_auth_codes_user_id_index" uuid="1556615d-b8aa-4035-b682-e9327a88d7d2">
          <index-field name="user_id" uuid="95f5493c-d7ce-421f-9a36-f57bb723134b"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_auth_codes</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthClient" local-name="OauthClient" namespace="\App\Models" uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="1c54a139-0539-437d-9414-f07435833a22"/>
        <field name="user_id" type="bigInteger" unsigned="true" uuid="75244cbd-b37d-4eba-b332-ea2bfb7ea48d"/>
        <field name="name" type="string" size="255" required="true" uuid="860fa5cf-72ac-43c3-9ab0-df41aa96de42"/>
        <field name="secret" type="string" size="100" uuid="ccb9ae0d-3aea-4b7b-ac3d-7951da091f4a"/>
        <field name="provider" type="string" size="255" uuid="1a78b317-9d41-4558-a426-0e03b3222557"/>
        <field name="redirect" type="text" required="true" uuid="ab1c5690-6d2a-4765-ba42-a1efb6b45055"/>
        <field name="personal_access_client" type="boolean" required="true" uuid="9f2d81c9-b678-4a1e-a7b0-a9afe7e96340"/>
        <field name="password_client" type="boolean" required="true" uuid="793d6176-480a-4fc9-8c11-a585072b3ee3"/>
        <field name="revoked" type="boolean" required="true" uuid="bfbaacc8-b1f3-4c48-a24a-487ed6881a86"/>
        <field name="created_at" type="timestamp" uuid="67dda06d-6317-4a45-ba19-2d2cda49d652"/>
        <field name="updated_at" type="timestamp" uuid="cec5df34-e9da-4bc5-8c91-00626f3215ac"/>
        <index name="oauth_clients_user_id_index" uuid="dd349e11-e52f-4852-b5b4-************">
          <index-field name="user_id" uuid="75986d36-96c2-48c3-9a0b-17b2b7a9ac2f"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_clients</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthPersonalAccessClient" local-name="OauthPersonalAccessClient" namespace="\App\Models" uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="662ea3f9-0171-42ee-94ca-a03a4b3c6887"/>
        <field name="client_id" type="bigInteger" required="true" unsigned="true" uuid="8055801d-3045-418b-bb6b-c9a372a17d79"/>
        <field name="created_at" type="timestamp" uuid="eaa1ecb5-cac7-4ff0-bea1-afb1524da5c6"/>
        <field name="updated_at" type="timestamp" uuid="e363c7a1-116d-482e-963f-************"/>
        <orm-attributes>
          <attribute name="table">oauth_personal_access_clients</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthRefreshToken" local-name="OauthRefreshToken" namespace="\App\Models" uuid="e799a893-7c3e-4ae7-b214-946c90225fca">
        <field name="id" type="string" size="100" required="true" unique="true" primary="true" uuid="7ee69630-fc66-4243-85d6-2864cc083df6"/>
        <field name="access_token_id" type="string" size="100" required="true" uuid="07ddb140-e18a-4584-99c2-a87dcc9d5beb"/>
        <field name="revoked" type="boolean" required="true" uuid="c79c3090-4983-4c65-97c9-ebe24864d513"/>
        <field name="expires_at" type="dateTime" uuid="42e118e3-cbf9-4951-b505-5673ca2a0e74"/>
        <index name="oauth_refresh_tokens_access_token_id_index" uuid="30e0ad70-839c-4c84-a960-2ad7447fa433">
          <index-field name="access_token_id" uuid="f48f6a5f-a3a4-4086-befb-964bf568a1d4"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_refresh_tokens</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\PasswordReset" local-name="PasswordReset" namespace="\App\Models" uuid="38250f77-2e1a-496f-b5ed-449d426d2e32">
        <field name="email" type="string" size="255" required="true" uuid="7041d879-296d-4344-aec7-8599a3ef2cc3"/>
        <field name="token" type="string" size="255" required="true" uuid="e031b6db-4649-4cb2-9b0d-a846b4151142"/>
        <field name="created_at" type="timestamp" uuid="fa50c28c-31e3-468e-b577-b1d539442c4c"/>
        <index name="password_resets_email_index" uuid="2aaf6228-86cd-40df-9951-a3102bac5a19">
          <index-field name="email" uuid="fb1a10fb-ab3d-44e8-bf28-4f083ff1d080"/>
        </index>
        <index name="password_resets_token_index" uuid="b040c330-0ec8-494c-bb81-47143470ef13">
          <index-field name="token" uuid="7c7ee4a2-ed67-43c8-ae4f-980d1775c1a2"/>
        </index>
        <orm-attributes>
          <attribute name="table">password_resets</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\PersonalAccessToken" local-name="PersonalAccessToken" namespace="\App\Models" uuid="ed9a1554-55ad-494e-a84a-2217e636774a">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="da490eda-eb0c-4aa3-aef1-a2da01701f2a"/>
        <field name="tokenable_type" type="string" size="255" required="true" uuid="3f5e1452-8c01-4c39-afe9-2e08150af66f"/>
        <field name="tokenable_id" type="bigInteger" required="true" unsigned="true" uuid="0e8bfb0a-488b-4370-bc43-b980fdc1dd35"/>
        <field name="name" type="string" size="255" required="true" uuid="2d155006-2cc6-43fc-8823-38e3aa8d18be"/>
        <field name="token" type="string" size="64" required="true" uuid="6e0c0179-206a-480a-9d58-054577b8df87"/>
        <field name="abilities" type="text" uuid="7a0be331-7207-4896-adca-c2b1a769b56c"/>
        <field name="last_used_at" type="timestamp" uuid="fe199e6b-05f2-4516-882c-ac398aa476d9"/>
        <field name="expires_at" type="timestamp" uuid="83256cb9-33ef-4d71-bbb8-66ef3d2995cd"/>
        <field name="created_at" type="timestamp" uuid="1b5a321b-bf0c-4090-9fcc-637be0a027b6"/>
        <field name="updated_at" type="timestamp" uuid="3d4296ac-e0e2-489e-bf36-a0a7ee0c691f"/>
        <index name="personal_access_tokens_token_unique" unique="true" uuid="e22ea9cb-892d-4937-b925-0a125b629b59">
          <index-field name="token" uuid="986c841c-8431-4da0-8cb8-b8e9907d21e4"/>
        </index>
        <index name="personal_access_tokens_tokenable_type_tokenable_id_index" uuid="13010421-06ae-4678-b9e8-9853a4d1bd0f">
          <index-field name="tokenable_type" uuid="41d8c114-01f2-4760-966e-cb0fc9d22b2f"/>
          <index-field name="tokenable_id" uuid="aab69aa2-5b6a-4a34-951d-5cefcdf8b43d"/>
        </index>
        <orm-attributes>
          <attribute name="table">personal_access_tokens</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\PulseAggregate" local-name="PulseAggregate" namespace="\App\Models" uuid="5b414e55-7f53-424c-a425-79eb086472b8">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="38fe07dd-de76-4282-8176-e9f03667d725"/>
        <field name="bucket" type="integer" required="true" unsigned="true" uuid="05dce5c5-accf-4b91-a777-ebf3ac302668"/>
        <field name="period" type="mediumInteger" required="true" unsigned="true" uuid="393ebccc-d117-488d-9891-dbc5adcaba8e"/>
        <field name="type" type="string" size="255" required="true" uuid="329900a1-d371-4521-99b0-8ee82ae529d9"/>
        <field name="key" type="mediumText" required="true" uuid="ecf6e18d-978e-4785-b03d-fc4c368bb0f4"/>
        <field name="key_hash" type="binary" size="16" uuid="cad6f013-b04a-480e-a291-abea5731820c"/>
        <field name="aggregate" type="string" size="255" required="true" uuid="2a3a4bee-d088-4070-a451-8df54885ecf8"/>
        <field name="value" type="decimal" size="20" required="true" uuid="a2a64dd8-546a-486a-9f46-7e586542adc7">
          <orm-attributes>
            <attribute name="decimal">2</attribute>
          </orm-attributes>
        </field>
        <field name="count" type="integer" unsigned="true" uuid="de11156c-ff65-43b4-adc5-2226542a5b74"/>
        <index name="pulse_aggregates_bucket_period_type_aggregate_key_hash_unique" unique="true" uuid="12197dea-96d4-42be-85bc-bb2ef8dccb40">
          <index-field name="bucket" uuid="ddd5f005-1537-4821-8138-0366f192ff54"/>
          <index-field name="period" uuid="ce7c756c-dbae-4e74-a426-8f06642b4f85"/>
          <index-field name="type" uuid="f0cd2481-2a0a-4edf-ac05-************"/>
          <index-field name="key_hash" uuid="83268f9e-b835-4b36-bd00-13062cd89ec3"/>
          <index-field name="aggregate" uuid="339a6486-7486-432d-a773-3fb41f47ce8a"/>
        </index>
        <index name="pulse_aggregates_period_bucket_index" uuid="08247cda-18f5-4b94-bf17-29516d8b9ab1">
          <index-field name="bucket" uuid="e0eaf3d7-40d6-4b21-8d62-2678d5a981a1"/>
          <index-field name="period" uuid="b1fdf7e9-47ff-469c-86fd-ead43731aebc"/>
        </index>
        <index name="pulse_aggregates_period_type_aggregate_bucket_index" uuid="e7cf5c93-0ff3-4c18-9a83-97e780f1e8b7">
          <index-field name="bucket" uuid="7429a2bd-c283-4ef6-a865-1e0e6106984b"/>
          <index-field name="period" uuid="70baf195-3d68-4f87-a1e1-40454000ab46"/>
          <index-field name="type" uuid="3640fa3e-497c-46b3-8827-9e163dd11f63"/>
          <index-field name="aggregate" uuid="9cbd73ef-1ccc-4b8c-af4b-77f9d60b0ecc"/>
        </index>
        <index name="pulse_aggregates_type_index" uuid="cafc15d8-5598-47ed-8ad6-ae03d0450814">
          <index-field name="type" uuid="3312a77e-c1c1-41ab-88fe-30aa0147423b"/>
        </index>
        <orm-attributes>
          <attribute name="table">pulse_aggregates</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\PulseEntry" local-name="PulseEntry" namespace="\App\Models" uuid="fafee967-9109-4d9c-861f-bdf269339540">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="2b61e224-cf73-4800-aecf-b556ccb86862"/>
        <field name="timestamp" type="integer" required="true" unsigned="true" uuid="d88ae2ac-319c-4e28-9d49-161f1dffb75d"/>
        <field name="type" type="string" size="255" required="true" uuid="92b15d9f-eacf-49e7-9d50-b35d9322f8f9"/>
        <field name="key" type="mediumText" required="true" uuid="e370c3ba-a738-4c85-a7cf-0b9173d88f8c"/>
        <field name="key_hash" type="binary" size="16" uuid="81d3b8a4-7dfe-4b79-a7c1-192b3d860397"/>
        <field name="value" type="bigInteger" uuid="d51794f0-00cc-44d6-980d-8315894eb9dd"/>
        <index name="pulse_entries_key_hash_index" uuid="31aace01-c046-4a1c-9fd0-9872ecbb259a">
          <index-field name="key_hash" uuid="1708e985-67a8-44f1-8479-7f738dff1f1e"/>
        </index>
        <index name="pulse_entries_timestamp_index" uuid="3cc95419-a8cd-4b65-8d2e-0a5b0341764d">
          <index-field name="timestamp" uuid="3d2b2894-f7b9-4549-a2da-075543da8988"/>
        </index>
        <index name="pulse_entries_timestamp_type_key_hash_value_index" uuid="e5284f32-9012-4b26-b5ef-a3d2595e4e60">
          <index-field name="timestamp" uuid="da399954-a0ec-462e-b39b-d32f21ce5ccb"/>
          <index-field name="type" uuid="02c8493c-fa1b-4165-93d4-6531cdfc2e86"/>
          <index-field name="key_hash" uuid="a6bc94f2-0fe3-4c24-b24c-803da72a38fa"/>
          <index-field name="value" uuid="51b3aa19-492d-49f2-a885-75c3a19d2365"/>
        </index>
        <index name="pulse_entries_type_index" uuid="d7552e77-8a9f-44b6-938e-6a4c5bbf676e">
          <index-field name="type" uuid="0f8435e1-2459-4ab6-8948-3cd977109399"/>
        </index>
        <orm-attributes>
          <attribute name="table">pulse_entries</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\PulseValue" local-name="PulseValue" namespace="\App\Models" uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="03acc5e3-5257-4883-afe3-c0fb259294ac"/>
        <field name="timestamp" type="integer" required="true" unsigned="true" uuid="6ec06755-1153-4ee5-9ff9-f11ecfdae9d9"/>
        <field name="type" type="string" size="255" required="true" uuid="22a0deb7-29a1-44c0-907b-1de200cdafc5"/>
        <field name="key" type="mediumText" required="true" uuid="7eed8531-1b95-4ff9-9a25-74727385f9f0"/>
        <field name="key_hash" type="binary" size="16" uuid="0c3284db-13ee-4bc8-8a34-f20682c1e2b9"/>
        <field name="value" type="mediumText" required="true" uuid="24e81dc3-75ec-402a-9b7c-153b27097e82"/>
        <index name="pulse_values_timestamp_index" uuid="e37e6296-576b-41de-a5e6-48698ea7584d">
          <index-field name="timestamp" uuid="72b71e2b-61fa-4f61-b714-32ce1e7849e3"/>
        </index>
        <index name="pulse_values_type_index" uuid="04dc175b-5290-44de-9e66-bf28057b4392">
          <index-field name="type" uuid="56084b25-9c34-4309-9505-c72eedf98097"/>
        </index>
        <index name="pulse_values_type_key_hash_unique" unique="true" uuid="11960663-8695-4904-98b1-a634169a7dd2">
          <index-field name="type" uuid="9bbe3ed2-5d67-4d5d-bf24-aca86b7fdcb2"/>
          <index-field name="key_hash" uuid="a57fa35e-f93d-4d25-ace8-f2fe352e771b"/>
        </index>
        <orm-attributes>
          <attribute name="table">pulse_values</attribute>
        </orm-attributes>
      </entity>
    </region>
    <region namespace="\App\Models" caption="Notification" uuid="eb68626c-e7e1-44a6-84d9-ecd13d7414be">
      <entity name="\App\Models\Alert" local-name="Alert" namespace="\App\Models" uuid="3f4f2663-6d56-449e-863e-635c607f6a6a">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="d650cf50-7037-4b36-8e6b-66e6ef56234b"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="2fa88aad-aab7-47c6-957e-284e3e991b9c"/>
        <field name="entity_name" type="string" size="50" required="true" uuid="9328219a-eb26-4e25-bc71-b627eb7b96a6"/>
        <field name="entity_id" type="integer" required="true" uuid="28468824-c887-4cbe-95e0-b5225da71cbb"/>
        <field name="disabled" type="boolean" default="0" uuid="2a88b6df-6d26-414b-9f09-76417483028e"/>
        <field name="valid_until" type="date" uuid="db30c538-1ea7-454a-9ecb-8dfc0dc91856"/>
        <field name="created_at" type="timestamp" uuid="d6c9c329-59cb-4f9e-b5ae-c0890a1ae2af"/>
        <field name="updated_at" type="timestamp" uuid="2ccc4c14-3684-4f4d-be1d-19636f90539c"/>
        <orm-attributes>
          <attribute name="table">alerts</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Emaillogentry" local-name="Emaillogentry" namespace="\App\Models" uuid="7b400813-2907-42f8-83e7-05ea7763af1c">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="*************-432c-9344-d182311c1f2d"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="fa4e89a7-e447-4817-b5f4-f9d1b4d346fd"/>
        <field name="to" type="text" required="true" uuid="84287dfc-d0f1-463a-928d-e9a95b960d8c"/>
        <field name="cc" type="text" uuid="a580cc6d-0f6e-45b6-b7aa-823e206dc3e0"/>
        <field name="bcc" type="text" uuid="f59e3644-b227-411d-9b5d-1ffae863ba9a"/>
        <field name="from" type="string" size="150" required="true" uuid="ba5f9172-4f85-462d-b575-a7c9d70d22b8"/>
        <field name="subject" type="string" size="150" required="true" uuid="f72b79bb-5196-4fdf-8593-60636e5629cd"/>
        <field name="body" type="text" required="true" uuid="a437f661-8209-438b-b85b-1dfb424d5812"/>
        <field name="attachments" type="text" uuid="2d48a269-9b58-40c4-a830-5cd8eddbd7f4"/>
        <field name="studentids" type="string" size="255" uuid="8c3d86fa-a46c-46d5-992a-40b43f66a2e6"/>
        <field name="unique_token" type="string" size="50" default="unknown" required="true" uuid="1c893d82-492b-415c-b69f-7cab63c21fd4"/>
        <field name="status" type="string" size="10" default="unknown" required="true" uuid="3e95e12c-21cf-44b8-bb80-a073e83738d4"/>
        <field name="log" type="text" uuid="289a0586-2b0b-4c4e-b41d-50e730b5a950"/>
        <field name="created_at" type="timestamp" uuid="9d53a336-d98b-4794-b627-abe6867234e5"/>
        <field name="updated_at" type="timestamp" uuid="c921e9f2-449d-4e9d-a9b8-96e0d93aff80"/>
        <index name="domain_emaillogentry_domain_id_foreign" uuid="529c6595-9fb5-4c8d-9105-6ac7e51e0376">
          <index-field name="domain_id" uuid="e08afd3d-d93d-4196-af5e-5ad79334a2d4"/>
        </index>
        <orm-attributes>
          <attribute name="table">emaillogentries</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Mailtemplate" local-name="Mailtemplate" namespace="\App\Models" uuid="2bfcb045-f8e8-4bef-b158-8187021066ee">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="27b47e8e-6fce-411a-bb30-72a8804f9e89"/>
        <field name="domain_id" type="integer" default="0" required="true" unsigned="true" uuid="55a03dfd-73ca-4c90-b263-b04632ff57d3"/>
        <field name="label" type="string" size="50" required="true" uuid="a1fe614e-182e-492c-bc08-d86c7c8a1d6d"/>
        <field name="targets" type="string" size="50" default="a" required="true" uuid="83044825-9d6a-4549-9690-49af6b33f937"/>
        <field name="content" type="text" required="true" uuid="75197df9-1017-46a2-bb88-a5dc9da80f97"/>
        <field name="created_at" type="timestamp" uuid="3f7fb64f-bfb2-44b0-aa9e-fbef88121556"/>
        <field name="updated_at" type="timestamp" uuid="4f98686f-965a-4feb-9b7b-8d45e02b8433"/>
        <index name="domain_mailtemplate_domain_id_foreign" uuid="1522e075-60bf-4374-ae87-9968bbb207de">
          <index-field name="domain_id" uuid="3b8f58de-84c2-415d-ba8d-cb04fbdb69a2"/>
        </index>
        <orm-attributes>
          <attribute name="table">mailtemplates</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\Alert" to="\App\Models\Domain" owner-alias="alerts" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="bdb2a6f9-7697-45ba-9382-83ae67fc4c4a">
      <association-field from="domain_id" to="id" uuid="09061919-beb8-4a3d-91b6-2b8ee359e42b"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">CASCADE</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Emaillogentry" to="\App\Models\Domain" owner-alias="emaillogentries" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="203e75e0-d79f-4f66-8ccd-01e16652ac61">
      <association-field from="domain_id" to="id" uuid="fdf9397b-db4a-419e-8d3e-371a7d093f63"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Mailtemplate" to="\App\Models\Domain" owner-alias="mailtemplates" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="6b488045-5239-4975-9fc0-7e2b121e998b">
      <association-field from="domain_id" to="id" uuid="a2f36b91-1f4c-4db2-ad95-fc8b0c98d370"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="ClassE" description="Communication between Class and tutors" uuid="a26a64a6-e2bc-4086-9d36-a2f414b5f290">
      <entity name="\App\Models\Message" local-name="Message" namespace="\App\Models" uuid="c94bd824-635d-40bb-9717-360ea46514e9">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="910c866c-051f-4ec0-ad9e-d9a68e1d754e"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="e9b544f1-5cbc-49ff-9174-3c29e17f2233"/>
        <field name="from_id" type="integer" required="true" unsigned="true" uuid="77d8be7e-fcec-4422-85d5-fa424e1e47c2"/>
        <field name="from_type" type="string" size="15" required="true" uuid="552e9f48-1d65-4a2e-bdcb-2b89cb4060f0"/>
        <field name="from_label" type="string" size="50" required="true" uuid="fbb9f02a-4603-486a-b862-c17800fae867"/>
        <field name="to_id" type="integer" required="true" unsigned="true" uuid="8df657ba-96cb-4310-b56a-1d746276eed5"/>
        <field name="to_type" type="string" size="15" required="true" uuid="90613c0e-faf9-467c-bb39-203370d91399"/>
        <field name="to_label" type="string" size="50" required="true" uuid="04949edf-025d-45fd-8493-e4ee1e00a51d"/>
        <field name="subject" type="string" size="50" required="true" uuid="84793f30-5a63-4faa-865c-66ee59f869ae"/>
        <field name="body" type="text" required="true" uuid="f625fbee-0512-45fc-96c1-36b8bdfe3c75"/>
        <field name="created_at" type="timestamp" uuid="072e5813-d177-40d2-9f59-d902cbb7a18f"/>
        <field name="updated_at" type="timestamp" uuid="977cafce-1d03-454e-a56d-9ff15401ea03"/>
        <field name="read_at" type="timestamp" uuid="f30976d2-7287-4333-ad5b-cbb12859ea5c"/>
        <orm-attributes>
          <attribute name="table">messages</attribute>
        </orm-attributes>
      </entity>
      <comment caption="notifications" description="to and from ClassE" uuid="14ec30d9-3ce1-4e6d-b78e-a147b3bda63c"/>
    </region>
    <association from="\App\Models\Message" to="\App\Models\Domain" owner-alias="messages" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="cb8e56ea-b5e6-4371-921b-f1a9c96bf1d9">
      <association-field from="domain_id" to="id" uuid="844c46b3-8f05-4750-bc95-bc6497ec5754"/>
      <orm-attributes>
        <attribute name="onDelete">NO ACTION</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <orm-attributes>
      <attribute name="migrations-path">migrations</attribute>
      <attribute name="models-disabled">true</attribute>
    </orm-attributes>
    <migrations version="1.0">
      <revision uuid="ae00cd83-2f1c-4ff4-9197-627ad6868dd0" date="2024-12-01 19:03:45.078295" exportable="ignored">
        <element action="add" uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="module">
          <property name="export-format" value="Laravel"/>
          <property name="export-path" value="."/>
          <property name="local-name" value="class_test"/>
          <property name="local-namespace" value="App\Models"/>
          <property name="migrations-path" value="database/migrations"/>
          <property name="name" value="\class_test"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Alert"/>
          <property name="name" value="\App\Models\Alert"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="alerts"/>
        </element>
        <element action="add" uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Attendancenote"/>
          <property name="name" value="\App\Models\Attendancenote"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="attendancenotes"/>
        </element>
        <element action="add" uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Attendanceoption"/>
          <property name="name" value="\App\Models\Attendanceoption"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="attendanceoptions"/>
        </element>
        <element action="add" uuid="24be36a3-768b-4bde-bc50-2239b5545201" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Availability"/>
          <property name="name" value="\App\Models\Availability"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="availabilities"/>
        </element>
        <element action="add" uuid="6721abcb-5865-4db8-98df-f6679e120741" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Book"/>
          <property name="name" value="\App\Models\Book"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="books"/>
        </element>
        <element action="add" uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Checklist"/>
          <property name="name" value="\App\Models\Checklist"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="checklists"/>
        </element>
        <element action="add" uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="CourseStudent"/>
          <property name="name" value="\App\Models\CourseStudent"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="course_student"/>
        </element>
        <element action="add" uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="CoursegroupTutor"/>
          <property name="name" value="\App\Models\CoursegroupTutor"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="coursegroup_tutor"/>
        </element>
        <element action="add" uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Coursegroup"/>
          <property name="name" value="\App\Models\Coursegroup"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="coursegroups"/>
        </element>
        <element action="add" uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Course"/>
          <property name="name" value="\App\Models\Course"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="courses"/>
        </element>
        <element action="add" uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Coursevariance"/>
          <property name="name" value="\App\Models\Coursevariance"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="coursevariances"/>
        </element>
        <element action="add" uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="DateException"/>
          <property name="name" value="\App\Models\DateException"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="date_exceptions"/>
        </element>
        <element action="add" uuid="e5e8860a-f818-4e0c-9819-7a7ad9255c7f" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="DateexceptionTutor"/>
          <property name="name" value="\App\Models\DateexceptionTutor"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="dateexception_tutor"/>
        </element>
        <element action="add" uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="DefaultChecklist"/>
          <property name="name" value="\App\Models\DefaultChecklist"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="default_checklists"/>
        </element>
        <element action="add" uuid="8cd208d8-de83-4695-9468-02073ea1f00f" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Document"/>
          <property name="name" value="\App\Models\Document"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="documents"/>
        </element>
        <element action="add" uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Domain"/>
          <property name="name" value="\App\Models\Domain"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="domains"/>
        </element>
        <element action="add" uuid="7b400813-2907-42f8-83e7-05ea7763af1c" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Emaillogentry"/>
          <property name="name" value="\App\Models\Emaillogentry"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="emaillogentries"/>
        </element>
        <element action="add" uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Event"/>
          <property name="name" value="\App\Models\Event"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="events"/>
        </element>
        <element action="add" uuid="dd612244-e406-4048-a3d9-5001719eaf66" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="FailedJob"/>
          <property name="name" value="\App\Models\FailedJob"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="failed_jobs"/>
        </element>
        <element action="add" uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Job"/>
          <property name="name" value="\App\Models\Job"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="jobs"/>
        </element>
        <element action="add" uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Library"/>
          <property name="name" value="\App\Models\Library"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="libraries"/>
        </element>
        <element action="add" uuid="b8404cea-9e03-48c5-b2e8-d2014a185356" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="LibraryCourse"/>
          <property name="name" value="\App\Models\LibraryCourse"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="library_course"/>
        </element>
        <element action="add" uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="LibraryCoursegroup"/>
          <property name="name" value="\App\Models\LibraryCoursegroup"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="library_coursegroup"/>
        </element>
        <element action="add" uuid="ceb6e565-00ec-43e2-8146-5e052d702270" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="LibraryDocument"/>
          <property name="name" value="\App\Models\LibraryDocument"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="library_document"/>
        </element>
        <element action="add" uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="LibraryStudent"/>
          <property name="name" value="\App\Models\LibraryStudent"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="library_student"/>
        </element>
        <element action="add" uuid="ef423929-3822-431c-a4bb-36e67d80a964" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Location"/>
          <property name="name" value="\App\Models\Location"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="locations"/>
        </element>
        <element action="add" uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Logentry"/>
          <property name="name" value="\App\Models\Logentry"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="logentries"/>
        </element>
        <element action="add" uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Loginsecurity"/>
          <property name="name" value="\App\Models\Loginsecurity"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="loginsecurities"/>
        </element>
        <element action="add" uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Mailtemplate"/>
          <property name="name" value="\App\Models\Mailtemplate"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="mailtemplates"/>
        </element>
        <element action="add" uuid="c94bd824-635d-40bb-9717-360ea46514e9" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Message"/>
          <property name="name" value="\App\Models\Message"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="messages"/>
        </element>
        <element action="add" uuid="34879c08-1079-4cee-b770-e42debb31c86" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="OauthAccessToken"/>
          <property name="name" value="\App\Models\OauthAccessToken"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_access_tokens"/>
        </element>
        <element action="add" uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="OauthAuthCode"/>
          <property name="name" value="\App\Models\OauthAuthCode"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_auth_codes"/>
        </element>
        <element action="add" uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="OauthClient"/>
          <property name="name" value="\App\Models\OauthClient"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_clients"/>
        </element>
        <element action="add" uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="OauthPersonalAccessClient"/>
          <property name="name" value="\App\Models\OauthPersonalAccessClient"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_personal_access_clients"/>
        </element>
        <element action="add" uuid="e799a893-7c3e-4ae7-b214-946c90225fca" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="OauthRefreshToken"/>
          <property name="name" value="\App\Models\OauthRefreshToken"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_refresh_tokens"/>
        </element>
        <element action="add" uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="PasswordReset"/>
          <property name="name" value="\App\Models\PasswordReset"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="password_resets"/>
        </element>
        <element action="add" uuid="ed9a1554-55ad-494e-a84a-2217e636774a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="PersonalAccessToken"/>
          <property name="name" value="\App\Models\PersonalAccessToken"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="personal_access_tokens"/>
        </element>
        <element action="add" uuid="2a98b125-f664-422e-9097-b8896608cafa" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="PlanningEntriesChange"/>
          <property name="name" value="\App\Models\PlanningEntriesChange"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="planning_entries_changes"/>
        </element>
        <element action="add" uuid="b7435152-5821-4b19-9e50-701520fe7704" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Planningentry"/>
          <property name="name" value="\App\Models\Planningentry"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="planningentries"/>
        </element>
        <element action="add" uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Priceoption"/>
          <property name="name" value="\App\Models\Priceoption"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="priceoptions"/>
        </element>
        <element action="add" uuid="5b414e55-7f53-424c-a425-79eb086472b8" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="PulseAggregate"/>
          <property name="name" value="\App\Models\PulseAggregate"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="pulse_aggregates"/>
        </element>
        <element action="add" uuid="fafee967-9109-4d9c-861f-bdf269339540" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="PulseEntry"/>
          <property name="name" value="\App\Models\PulseEntry"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="pulse_entries"/>
        </element>
        <element action="add" uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="PulseValue"/>
          <property name="name" value="\App\Models\PulseValue"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="pulse_values"/>
        </element>
        <element action="add" uuid="253849ed-5411-4cec-bd9d-0053c321f423" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Recurrenceoption"/>
          <property name="name" value="\App\Models\Recurrenceoption"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="recurrenceoptions"/>
        </element>
        <element action="add" uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="RoleUser"/>
          <property name="name" value="\App\Models\RoleUser"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="role_user"/>
        </element>
        <element action="add" uuid="c10c1531-15c0-4e26-8860-4d6068585afa" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Role"/>
          <property name="name" value="\App\Models\Role"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="roles"/>
        </element>
        <element action="add" uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="SchedulePref"/>
          <property name="name" value="\App\Models\SchedulePref"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="schedule_prefs"/>
        </element>
        <element action="add" uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Scheduleproposal"/>
          <property name="name" value="\App\Models\Scheduleproposal"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="scheduleproposals"/>
        </element>
        <element action="add" uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Schoolyear"/>
          <property name="name" value="\App\Models\Schoolyear"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="schoolyears"/>
        </element>
        <element action="add" uuid="72f556d7-609b-405b-88a4-8f6d4587b032" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="StudentStudentgroup"/>
          <property name="name" value="\App\Models\StudentStudentgroup"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="student_studentgroup"/>
        </element>
        <element action="add" uuid="e2056103-3473-42f2-a865-4a63f378d655" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="StudentStudentlist"/>
          <property name="name" value="\App\Models\StudentStudentlist"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="student_studentlist"/>
        </element>
        <element action="add" uuid="709b0eec-1818-4251-856b-44fd280b3812" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Studentcontact"/>
          <property name="name" value="\App\Models\Studentcontact"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="studentcontacts"/>
        </element>
        <element action="add" uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Studentlist"/>
          <property name="name" value="\App\Models\Studentlist"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="studentlists"/>
        </element>
        <element action="add" uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Student"/>
          <property name="name" value="\App\Models\Student"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="students"/>
        </element>
        <element action="add" uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Task"/>
          <property name="name" value="\App\Models\Task"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="tasks"/>
        </element>
        <element action="add" uuid="f69b0190-690b-46ad-9697-cf47b35a3bdb" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Tasktype"/>
          <property name="name" value="\App\Models\Tasktype"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="tasktypes"/>
        </element>
        <element action="add" uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Timetable"/>
          <property name="name" value="\App\Models\Timetable"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="timetables"/>
        </element>
        <element action="add" uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="TrialcourseCourse"/>
          <property name="name" value="\App\Models\TrialcourseCourse"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="trialcourse_courses"/>
        </element>
        <element action="add" uuid="68a489f5-2ff4-4359-9710-085720368ca6" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Trialrequeststatus"/>
          <property name="name" value="\App\Models\Trialrequeststatus"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="trialrequeststatuses"/>
        </element>
        <element action="add" uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="Trialstudent"/>
          <property name="name" value="\App\Models\Trialstudent"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="trialstudents"/>
        </element>
        <element action="add" uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity">
          <property name="local-name" value="User"/>
          <property name="name" value="\App\Models\User"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="users"/>
        </element>
        <element action="add" uuid="d650cf50-7037-4b36-8e6b-66e6ef56234b" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2fa88aad-aab7-47c6-957e-284e3e991b9c" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" sibling-uuid="d650cf50-7037-4b36-8e6b-66e6ef56234b" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="9328219a-eb26-4e25-bc71-b627eb7b96a6" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" sibling-uuid="2fa88aad-aab7-47c6-957e-284e3e991b9c" type="field">
          <property name="name" value="entity_name"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="28468824-c887-4cbe-95e0-b5225da71cbb" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" sibling-uuid="9328219a-eb26-4e25-bc71-b627eb7b96a6" type="field">
          <property name="name" value="entity_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="2a88b6df-6d26-414b-9f09-76417483028e" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" sibling-uuid="28468824-c887-4cbe-95e0-b5225da71cbb" type="field">
          <property name="default" value="0"/>
          <property name="name" value="disabled"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="db30c538-1ea7-454a-9ecb-8dfc0dc91856" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" sibling-uuid="2a88b6df-6d26-414b-9f09-76417483028e" type="field">
          <property name="name" value="valid_until"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="d6c9c329-59cb-4f9e-b5ae-c0890a1ae2af" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" sibling-uuid="db30c538-1ea7-454a-9ecb-8dfc0dc91856" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="2ccc4c14-3684-4f4d-be1d-19636f90539c" parent-uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" sibling-uuid="d6c9c329-59cb-4f9e-b5ae-c0890a1ae2af" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="4959d66a-7f38-41fa-8fea-4aef19de1aef" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="aaa2b675-f590-48ce-b28b-c0f854d98a6b" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" sibling-uuid="4959d66a-7f38-41fa-8fea-4aef19de1aef" type="field">
          <property name="name" value="student_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="b7a512e5-cab5-494b-801a-5f13b94333f7" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" sibling-uuid="aaa2b675-f590-48ce-b28b-c0f854d98a6b" type="field">
          <property name="name" value="event_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c4d738ec-f227-4316-88f1-942cb777d036" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" sibling-uuid="b7a512e5-cab5-494b-801a-5f13b94333f7" type="field">
          <property name="name" value="attendanceoption_id"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5718af63-c4ed-4a9c-920e-bfa1e4010848" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" sibling-uuid="c4d738ec-f227-4316-88f1-942cb777d036" type="field">
          <property name="name" value="notes"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="7125895d-4eb1-480c-8573-92cd68605fc8" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" sibling-uuid="5718af63-c4ed-4a9c-920e-bfa1e4010848" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="6318fa7b-84a1-46b4-821d-3edffa47d218" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" sibling-uuid="7125895d-4eb1-480c-8573-92cd68605fc8" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="87cb4b73-f8d6-4eff-9320-afae89124060" parent-uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" sibling-uuid="6318fa7b-84a1-46b4-821d-3edffa47d218" type="field">
          <property name="name" value="updated_by"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="49c8a19a-78ac-40ec-80e6-91fad566bc44" parent-uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="215d7f10-9f78-49f0-8ee4-6f8114157de3" parent-uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" sibling-uuid="49c8a19a-78ac-40ec-80e6-91fad566bc44" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="856ef411-c9fd-458b-a967-1e23045d2ed1" parent-uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" sibling-uuid="215d7f10-9f78-49f0-8ee4-6f8114157de3" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="02235e70-**************-f9c9be1b5485" parent-uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" sibling-uuid="856ef411-c9fd-458b-a967-1e23045d2ed1" type="field">
          <property name="name" value="action_tutor"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fcf83ddc-9b55-4358-a150-080fae20ee30" parent-uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" sibling-uuid="02235e70-**************-f9c9be1b5485" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a1bdf226-82c4-4f26-88de-a1ddeb79ead0" parent-uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" sibling-uuid="fcf83ddc-9b55-4358-a150-080fae20ee30" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="05125437-f699-4e22-847d-7d4286f3fadc" parent-uuid="24be36a3-768b-4bde-bc50-2239b5545201" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="dc61b14b-3256-4e46-aee5-b45092f7d517" parent-uuid="24be36a3-768b-4bde-bc50-2239b5545201" sibling-uuid="05125437-f699-4e22-847d-7d4286f3fadc" type="field">
          <property name="name" value="tutor_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="8a266596-dedf-4461-a2ed-e9cbdce99298" parent-uuid="24be36a3-768b-4bde-bc50-2239b5545201" sibling-uuid="dc61b14b-3256-4e46-aee5-b45092f7d517" type="field">
          <property name="name" value="day_number"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="9364c908-5fb5-4604-9b18-1540f8e0a75d" parent-uuid="24be36a3-768b-4bde-bc50-2239b5545201" sibling-uuid="8a266596-dedf-4461-a2ed-e9cbdce99298" type="field">
          <property name="name" value="from_time"/>
          <property name="type" value="time"/>
        </element>
        <element action="add" uuid="d29f4563-c9df-4ab3-8637-f86f8a5ceeef" parent-uuid="24be36a3-768b-4bde-bc50-2239b5545201" sibling-uuid="9364c908-5fb5-4604-9b18-1540f8e0a75d" type="field">
          <property name="name" value="to_time"/>
          <property name="type" value="time"/>
        </element>
        <element action="add" uuid="e963091f-7648-4957-88f7-322f5bfa24bd" parent-uuid="24be36a3-768b-4bde-bc50-2239b5545201" sibling-uuid="d29f4563-c9df-4ab3-8637-f86f8a5ceeef" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a627419a-8185-495a-afee-1f12cd68a695" parent-uuid="24be36a3-768b-4bde-bc50-2239b5545201" sibling-uuid="e963091f-7648-4957-88f7-322f5bfa24bd" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="f77b86e1-b998-45a5-834a-a16bfb442210" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5e7b018b-0fb8-40ca-8b81-2d110b9260a3" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="f77b86e1-b998-45a5-834a-a16bfb442210" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f328e5a0-d17e-440a-a21b-7806c6e86648" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="5e7b018b-0fb8-40ca-8b81-2d110b9260a3" type="field">
          <property name="name" value="author"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b04883b0-d287-4640-8db7-6dd58aafd2b1" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="f328e5a0-d17e-440a-a21b-7806c6e86648" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="ff75089a-1015-42b0-a66c-6985e8093df4" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="b04883b0-d287-4640-8db7-6dd58aafd2b1" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="32737be7-9dbd-4842-abd6-2738c98e9500" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="cd9db68e-0d86-4ecd-93b6-a442d99af92e" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="32737be7-9dbd-4842-abd6-2738c98e9500" type="field">
          <property name="name" value="registration_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="47768724-c13f-4354-94b3-b5f1a2b5a71b" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="cd9db68e-0d86-4ecd-93b6-a442d99af92e" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1db1bb0b-13a2-41d5-a57e-f893ae27fd85" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="47768724-c13f-4354-94b3-b5f1a2b5a71b" type="field">
          <property name="name" value="item1"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="089c59fa-a8d4-42d2-9bce-20d80af44f3a" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="1db1bb0b-13a2-41d5-a57e-f893ae27fd85" type="field">
          <property name="name" value="item1_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="938dcd98-f7ee-43e2-a74e-ae4b2adc255f" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="089c59fa-a8d4-42d2-9bce-20d80af44f3a" type="field">
          <property name="name" value="item2"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b9c9e2a7-12a0-422d-9fe6-641f49e12fcb" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="938dcd98-f7ee-43e2-a74e-ae4b2adc255f" type="field">
          <property name="name" value="item2_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="0a7709b1-9f92-46a3-a2ca-4f15ccbe5f75" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="b9c9e2a7-12a0-422d-9fe6-641f49e12fcb" type="field">
          <property name="name" value="item3"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="9c9485d9-86a8-4e62-a0e2-94ab8bfc7ef9" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="0a7709b1-9f92-46a3-a2ca-4f15ccbe5f75" type="field">
          <property name="name" value="item3_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="213589ae-b1dc-45de-8d80-c08471a99ffc" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="9c9485d9-86a8-4e62-a0e2-94ab8bfc7ef9" type="field">
          <property name="name" value="item4"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="81670bb1-6c74-4f60-b87d-726d3b518529" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="213589ae-b1dc-45de-8d80-c08471a99ffc" type="field">
          <property name="name" value="item4_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="734b80db-0cd1-4989-bdb8-aaeb59cf036c" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="81670bb1-6c74-4f60-b87d-726d3b518529" type="field">
          <property name="name" value="item5"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="94d82080-bd51-4caa-a690-61750a3c7263" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="734b80db-0cd1-4989-bdb8-aaeb59cf036c" type="field">
          <property name="name" value="item5_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="05769423-ab63-4bf6-a53b-167bb0b589a2" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="94d82080-bd51-4caa-a690-61750a3c7263" type="field">
          <property name="name" value="item6"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2ba937b5-fcea-4377-9494-79027a92d0a7" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="05769423-ab63-4bf6-a53b-167bb0b589a2" type="field">
          <property name="name" value="item6_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="16ec0376-f40f-487a-8ccf-93d1389c9df5" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="2ba937b5-fcea-4377-9494-79027a92d0a7" type="field">
          <property name="name" value="item7"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="87e2dfad-883e-4290-bc74-3e2375ac6183" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="16ec0376-f40f-487a-8ccf-93d1389c9df5" type="field">
          <property name="name" value="item7_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="841c72ce-1914-4248-bc3f-a23cd10eed04" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="87e2dfad-883e-4290-bc74-3e2375ac6183" type="field">
          <property name="name" value="item8"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="16fdaa96-c8b9-42b3-aee0-29724135809c" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="841c72ce-1914-4248-bc3f-a23cd10eed04" type="field">
          <property name="name" value="item8_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="3d3f1507-aa0a-407b-99e9-a07e86162c01" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="16fdaa96-c8b9-42b3-aee0-29724135809c" type="field">
          <property name="name" value="item9"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c9ae5dd8-41b1-4668-a88b-23ae56375e67" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="3d3f1507-aa0a-407b-99e9-a07e86162c01" type="field">
          <property name="name" value="item9_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="25c57b49-28c7-4a96-8771-9514eec37764" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="c9ae5dd8-41b1-4668-a88b-23ae56375e67" type="field">
          <property name="name" value="item10"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="82c4926c-3f4e-4846-8502-735738d2bb2a" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="25c57b49-28c7-4a96-8771-9514eec37764" type="field">
          <property name="name" value="item10_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="7ccbaabb-a88b-47ba-a54b-9ce09f65c048" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="82c4926c-3f4e-4846-8502-735738d2bb2a" type="field">
          <property name="name" value="item11"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="412fab43-3d65-44f3-a682-c28f3dc4abe0" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="7ccbaabb-a88b-47ba-a54b-9ce09f65c048" type="field">
          <property name="name" value="item11_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="c99427b8-ccc5-4dd0-88af-fea7f0f96556" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="412fab43-3d65-44f3-a682-c28f3dc4abe0" type="field">
          <property name="name" value="item12"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a4cacc8b-57f6-4ecc-8fe6-fb18bb294934" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="c99427b8-ccc5-4dd0-88af-fea7f0f96556" type="field">
          <property name="name" value="item12_checked"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="2f0ab28f-74b8-4f61-a371-63912c44f39b" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="a4cacc8b-57f6-4ecc-8fe6-fb18bb294934" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="9ad6d4b3-f0d1-489e-80c4-88d676f23094" parent-uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" sibling-uuid="2f0ab28f-74b8-4f61-a371-63912c44f39b" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="3e53b69c-d2e3-4024-9f9b-5334d7d1a0b6" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="27e52482-17ed-47a2-93e3-a83803860607" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="3e53b69c-d2e3-4024-9f9b-5334d7d1a0b6" type="field">
          <property name="default" value="0"/>
          <property name="name" value="course_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="da4c2900-7f75-438d-8eb4-22988a7ac013" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="27e52482-17ed-47a2-93e3-a83803860607" type="field">
          <property name="default" value="0"/>
          <property name="name" value="student_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6f411441-b2d3-4c28-999f-2033b339d023" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="da4c2900-7f75-438d-8eb4-22988a7ac013" type="field">
          <property name="name" value="checklist_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2ae5d7f8-15f8-4caf-9f63-5dc5eb24ad18" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="6f411441-b2d3-4c28-999f-2033b339d023" type="field">
          <property name="default" value="2016-01-01"/>
          <property name="name" value="start_date"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="1f9f35b8-f655-4115-992a-a1616979af21" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="2ae5d7f8-15f8-4caf-9f63-5dc5eb24ad18" type="field">
          <property name="name" value="end_date"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="6ce39388-f32f-4431-a1eb-f62888bc2e10" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="1f9f35b8-f655-4115-992a-a1616979af21" type="field">
          <property name="name" value="signed"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="205c8cde-42fc-4c5d-a93f-b14428c7e2fe" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="6ce39388-f32f-4431-a1eb-f62888bc2e10" type="field">
          <property name="default" value="1"/>
          <property name="name" value="status"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="9f16cdc0-5746-435d-85e3-2bfaae71ae5a" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="205c8cde-42fc-4c5d-a93f-b14428c7e2fe" type="field">
          <property name="name" value="sign_code"/>
          <property name="size" value="30"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c62af4f3-989e-4b50-8ff9-61b1e5bf647c" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="9f16cdc0-5746-435d-85e3-2bfaae71ae5a" type="field">
          <property name="name" value="sign_request_send"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="d2e03155-445a-4b12-96c6-bcc18aab884d" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="c62af4f3-989e-4b50-8ff9-61b1e5bf647c" type="field">
          <property name="name" value="sign_requested_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="70ef228b-fce2-4880-83ce-5d209bf9e5ed" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="d2e03155-445a-4b12-96c6-bcc18aab884d" type="field">
          <property name="name" value="signed_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e1502a55-7203-44ff-b703-677228aa73cf" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="70ef228b-fce2-4880-83ce-5d209bf9e5ed" type="field">
          <property name="name" value="signed_user_agent"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7224400a-e701-48e4-8960-27027a2a6f10" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="e1502a55-7203-44ff-b703-677228aa73cf" type="field">
          <property name="name" value="please_keep_scheduled_time"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="9e4ac133-3c9c-477c-9087-32911dccff91" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="7224400a-e701-48e4-8960-27027a2a6f10" type="field">
          <property name="name" value="ignore_current_schedule"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="0b5495a5-b70d-4add-abec-c4255e6b5a5e" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="9e4ac133-3c9c-477c-9087-32911dccff91" type="field">
          <property name="decimal" value="2"/>
          <property name="name" value="incidental_price_ex_tax"/>
          <property name="size" value="8"/>
          <property name="type" value="decimal"/>
        </element>
        <element action="add" uuid="76770556-102f-41d7-92a8-9fd8a5fb08db" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="0b5495a5-b70d-4add-abec-c4255e6b5a5e" type="field">
          <property name="name" value="incidental_tax_rate"/>
          <property name="type" value="double"/>
        </element>
        <element action="add" uuid="e083a9e9-feeb-451a-bc5a-a1610a04f134" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="76770556-102f-41d7-92a8-9fd8a5fb08db" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="5df446a7-a230-44aa-ba6f-aceaed054acd" parent-uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" sibling-uuid="e083a9e9-feeb-451a-bc5a-a1610a04f134" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="12c88c34-1b59-4723-81ed-29cc45f0e405" parent-uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" type="field">
          <property name="name" value="coursegroup_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="4c6d1ba6-ac3b-4b1d-9fbf-787b6f858537" parent-uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" sibling-uuid="12c88c34-1b59-4723-81ed-29cc45f0e405" type="field">
          <property name="name" value="tutor_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="180a241f-cec8-4d4a-979d-c6542f085cc0" parent-uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" sibling-uuid="4c6d1ba6-ac3b-4b1d-9fbf-787b6f858537" type="field">
          <property name="default" value="1"/>
          <property name="name" value="age_group_adult"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="55d00245-3e64-42e1-ad8d-544db99a042b" parent-uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" sibling-uuid="180a241f-cec8-4d4a-979d-c6542f085cc0" type="field">
          <property name="default" value="1"/>
          <property name="name" value="age_group_adolescent"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="ee3dd08c-65c7-44db-bc7f-a8fd2ac58349" parent-uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" sibling-uuid="55d00245-3e64-42e1-ad8d-544db99a042b" type="field">
          <property name="default" value="1"/>
          <property name="name" value="age_group_child"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="fd6ac3cd-d148-4911-b3a7-aa918264dc1a" parent-uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" sibling-uuid="ee3dd08c-65c7-44db-bc7f-a8fd2ac58349" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0b63d686-7010-4a02-98bc-d99bd656a59d" parent-uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" sibling-uuid="fd6ac3cd-d148-4911-b3a7-aa918264dc1a" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e67298f1-0509-498e-ae99-7f46cf1a4c2b" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="3b0e1309-f456-4e38-bc7f-8bd6c403971c" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" sibling-uuid="e67298f1-0509-498e-ae99-7f46cf1a4c2b" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="dbbd2860-e037-4bc6-808c-29fda4c6e8df" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" sibling-uuid="3b0e1309-f456-4e38-bc7f-8bd6c403971c" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0279f4d0-8a56-468f-bfab-d2dd749a3478" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" sibling-uuid="dbbd2860-e037-4bc6-808c-29fda4c6e8df" type="field">
          <property name="name" value="webdescription"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="9d4b7824-e071-4b2a-909f-ceca878dbfb2" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" sibling-uuid="0279f4d0-8a56-468f-bfab-d2dd749a3478" type="field">
          <property name="default" value="0"/>
          <property name="name" value="is_trial_group"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="e835b47d-de7a-4794-a733-5cf04c573c10" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" sibling-uuid="9d4b7824-e071-4b2a-909f-ceca878dbfb2" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="c67799af-4390-4b4f-b0d2-9a263112174d" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" sibling-uuid="e835b47d-de7a-4794-a733-5cf04c573c10" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="d6cd54e9-21fd-4fa7-bf47-53c24b566462" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="519716b8-06c2-4aee-8de2-ee96e74d2f6b" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="d6cd54e9-21fd-4fa7-bf47-53c24b566462" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="49f9e9d5-744d-4b77-8b78-0103242b7e6c" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="519716b8-06c2-4aee-8de2-ee96e74d2f6b" type="field">
          <property name="name" value="variant_code"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="3a36ced6-bc3e-47e5-a446-d5a63f10964f" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="49f9e9d5-744d-4b77-8b78-0103242b7e6c" type="field">
          <property name="name" value="coursegroup_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5ed440ac-a958-417b-ab05-a60a6141945f" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="3a36ced6-bc3e-47e5-a446-d5a63f10964f" type="field">
          <property name="name" value="recurrenceoption_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="62ad328b-54b3-43d7-aa7f-07c8b0fca089" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="5ed440ac-a958-417b-ab05-a60a6141945f" type="field">
          <property name="name" value="is_trial_course"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="7c7f9332-a058-405f-9cdd-32952ea69541" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="62ad328b-54b3-43d7-aa7f-07c8b0fca089" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a6f9c4be-77b4-4689-a5e0-d395ce47c357" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="7c7f9332-a058-405f-9cdd-32952ea69541" type="field">
          <property name="decimal" value="2"/>
          <property name="default" value="0.00"/>
          <property name="name" value="price_ex_tax"/>
          <property name="required" value="true"/>
          <property name="size" value="8"/>
          <property name="type" value="decimal"/>
        </element>
        <element action="add" uuid="6b72c505-5c81-4b36-b96d-75b834094aa3" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="a6f9c4be-77b4-4689-a5e0-d395ce47c357" type="field">
          <property name="decimal" value="2"/>
          <property name="default" value="0.00"/>
          <property name="name" value="price_invoice"/>
          <property name="required" value="true"/>
          <property name="size" value="8"/>
          <property name="type" value="decimal"/>
        </element>
        <element action="add" uuid="cc3addf1-b64b-43c8-b5c3-43077557e3f8" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="6b72c505-5c81-4b36-b96d-75b834094aa3" type="field">
          <property name="decimal" value="2"/>
          <property name="default" value="0.00"/>
          <property name="name" value="price_ex_tax_sub_adult"/>
          <property name="required" value="true"/>
          <property name="size" value="8"/>
          <property name="type" value="decimal"/>
        </element>
        <element action="add" uuid="dc054b00-a1f4-4802-b346-b657115dfc81" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="cc3addf1-b64b-43c8-b5c3-43077557e3f8" type="field">
          <property name="default" value="month"/>
          <property name="name" value="price_is_per"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="bf7b9b28-3de0-4a12-a7a8-fd734e939393" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="dc054b00-a1f4-4802-b346-b657115dfc81" type="field">
          <property name="default" value="0"/>
          <property name="name" value="tax_rate"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="3a8ccc61-f8ff-43fd-86b0-967cd07cf8aa" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="bf7b9b28-3de0-4a12-a7a8-fd734e939393" type="field">
          <property name="default" value="0"/>
          <property name="name" value="archive"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="a93a5341-1cf5-42e1-b228-2d8fcca8028c" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="3a8ccc61-f8ff-43fd-86b0-967cd07cf8aa" type="field">
          <property name="default" value="1"/>
          <property name="name" value="group_size_min"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e3ead94c-4699-4d5a-95df-35ce0382ae30" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="a93a5341-1cf5-42e1-b228-2d8fcca8028c" type="field">
          <property name="default" value="1"/>
          <property name="name" value="group_size_max"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="2094c1e4-c449-4b8d-a394-d55945da567a" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="e3ead94c-4699-4d5a-95df-35ce0382ae30" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="288051f5-99b3-4bff-9b99-c1a4e22d4920" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" sibling-uuid="2094c1e4-c449-4b8d-a394-d55945da567a" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="1a7ba6a9-fe14-4b0a-b862-f22217b333d1" parent-uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c477db8e-bd34-4432-9732-8d25f549147b" parent-uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" sibling-uuid="1a7ba6a9-fe14-4b0a-b862-f22217b333d1" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a1015202-ab60-434d-8ea3-a45376fb6efe" parent-uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" sibling-uuid="c477db8e-bd34-4432-9732-8d25f549147b" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="31e9d86d-dfba-4cb7-afd9-b0cfa281017c" parent-uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" sibling-uuid="a1015202-ab60-434d-8ea3-a45376fb6efe" type="field">
          <property name="name" value="course_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="d9368677-4097-45c4-9670-323dc97b52ff" parent-uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" sibling-uuid="31e9d86d-dfba-4cb7-afd9-b0cfa281017c" type="field">
          <property name="name" value="recurrenceoption_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="1027ed24-57fc-49b6-a0b7-5b8225d60413" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f8c96715-d22d-4f5f-954b-baf10c96c709" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="1027ed24-57fc-49b6-a0b7-5b8225d60413" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ee9eb5e6-0d84-4584-acd8-cbf37033f1d7" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="f8c96715-d22d-4f5f-954b-baf10c96c709" type="field">
          <property name="name" value="schoolyear_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="cfb49d56-1e2b-46dc-bf3d-b38d38a5a9ac" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="ee9eb5e6-0d84-4584-acd8-cbf37033f1d7" type="field">
          <property name="name" value="location_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="31fde653-479e-4a90-b6ec-1d809bfe9030" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="cfb49d56-1e2b-46dc-bf3d-b38d38a5a9ac" type="field">
          <property name="name" value="datetime_start"/>
          <property name="required" value="true"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="75d2d1f3-c90a-4bdb-99b9-c67fd3aa21cb" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="31fde653-479e-4a90-b6ec-1d809bfe9030" type="field">
          <property name="name" value="datetime_end"/>
          <property name="required" value="true"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="29862cc1-a3d0-461a-9f11-7bc66408df8a" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="75d2d1f3-c90a-4bdb-99b9-c67fd3aa21cb" type="field">
          <property name="name" value="reason"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e765eb4a-fd36-461b-a89f-0384fa2e94f6" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="29862cc1-a3d0-461a-9f11-7bc66408df8a" type="field">
          <property name="default" value="1"/>
          <property name="name" value="plan_blocking"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="9844e03c-7314-496a-ad0d-30c6cecc202e" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="e765eb4a-fd36-461b-a89f-0384fa2e94f6" type="field">
          <property name="default" value="0"/>
          <property name="name" value="exclude_from_alerts"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="b2ae55d7-5eb6-453b-bff2-cf835fc6d616" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="9844e03c-7314-496a-ad0d-30c6cecc202e" type="field">
          <property name="default" value="#836EC3"/>
          <property name="name" value="calendar_color"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4d34ee6f-762b-4c40-b159-c9484129f219" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="b2ae55d7-5eb6-453b-bff2-cf835fc6d616" type="field">
          <property name="name" value="detail_url"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4206a6ad-da95-4838-b931-a6bf69602cd4" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="4d34ee6f-762b-4c40-b159-c9484129f219" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="5b35786a-9172-48e2-a30a-2b908619e633" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" sibling-uuid="4206a6ad-da95-4838-b931-a6bf69602cd4" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="88775d15-3b91-4b0b-adb2-9df60deef31e" parent-uuid="e5e8860a-f818-4e0c-9819-7a7ad9255c7f" type="field">
          <property name="name" value="date_exception_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="905af1b7-e30e-4f80-b296-02af0b907951" parent-uuid="e5e8860a-f818-4e0c-9819-7a7ad9255c7f" sibling-uuid="88775d15-3b91-4b0b-adb2-9df60deef31e" type="field">
          <property name="name" value="user_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="19073e65-b13d-459a-b9d9-d1512e332c1a" parent-uuid="e5e8860a-f818-4e0c-9819-7a7ad9255c7f" sibling-uuid="905af1b7-e30e-4f80-b296-02af0b907951" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mandatory"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="19dc5573-5711-4431-a75f-4a3f21af43bc" parent-uuid="e5e8860a-f818-4e0c-9819-7a7ad9255c7f" sibling-uuid="19073e65-b13d-459a-b9d9-d1512e332c1a" type="field">
          <property name="default" value="0"/>
          <property name="name" value="confirmed"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="5c379f10-2ecc-4c28-ae1a-f684f9e1b362" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="23c1b95e-ac8e-43cb-9a2f-c54613336a56" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="5c379f10-2ecc-4c28-ae1a-f684f9e1b362" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="bea2241e-d92f-4a3b-9624-f117fb7a7043" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="23c1b95e-ac8e-43cb-9a2f-c54613336a56" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d5bd59d2-326e-490e-87ec-b1fce03c5099" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="bea2241e-d92f-4a3b-9624-f117fb7a7043" type="field">
          <property name="name" value="auto_add"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="81a5adbe-d1e5-4cc3-9d95-1e5957bdd0c7" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="d5bd59d2-326e-490e-87ec-b1fce03c5099" type="field">
          <property name="name" value="item1"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6963c6c2-adfa-4a11-8d61-8bfb9cff0e85" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="81a5adbe-d1e5-4cc3-9d95-1e5957bdd0c7" type="field">
          <property name="name" value="item2"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4d8461e7-dbd3-4a17-8442-4a480654331b" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="6963c6c2-adfa-4a11-8d61-8bfb9cff0e85" type="field">
          <property name="name" value="item3"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d5128cb9-2fd5-4e2d-802d-3d5a3c696acc" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="4d8461e7-dbd3-4a17-8442-4a480654331b" type="field">
          <property name="name" value="item4"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d6ba2d75-07a6-401d-9552-ffac34109c84" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="d5128cb9-2fd5-4e2d-802d-3d5a3c696acc" type="field">
          <property name="name" value="item5"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a0cb886f-d3a7-4543-9ef1-088387a044c1" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="d6ba2d75-07a6-401d-9552-ffac34109c84" type="field">
          <property name="name" value="item6"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7fa5bbd9-3427-44c8-ac68-0c2e089301aa" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="a0cb886f-d3a7-4543-9ef1-088387a044c1" type="field">
          <property name="name" value="item7"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1461c2d4-c17c-4a40-a5b5-1dbd394a9995" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="7fa5bbd9-3427-44c8-ac68-0c2e089301aa" type="field">
          <property name="name" value="item8"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="831e635f-571f-4b3a-9dcf-499c8c771d50" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="1461c2d4-c17c-4a40-a5b5-1dbd394a9995" type="field">
          <property name="name" value="item9"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="34f3933f-7b7a-486d-ab82-a01077718523" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="831e635f-571f-4b3a-9dcf-499c8c771d50" type="field">
          <property name="name" value="item10"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d589f299-a823-4644-80a1-6552c14698b7" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="34f3933f-7b7a-486d-ab82-a01077718523" type="field">
          <property name="name" value="item11"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="be79e7f2-da21-4e3d-ab05-ae5f4bb36570" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="d589f299-a823-4644-80a1-6552c14698b7" type="field">
          <property name="name" value="item12"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d5cca1c5-3aeb-445d-887b-d1911a8af25b" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="be79e7f2-da21-4e3d-ab05-ae5f4bb36570" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="cfaa5a7f-e48b-43c3-afdb-70120fd003b3" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" sibling-uuid="d5cca1c5-3aeb-445d-887b-d1911a8af25b" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="b069947c-c3d8-4663-82b7-ebfacad00c29" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="36677065-445a-46c7-9f4d-5a2373df4f3e" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="b069947c-c3d8-4663-82b7-ebfacad00c29" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="28cd60f4-969a-4c15-a377-ec01b2bb80ee" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="36677065-445a-46c7-9f4d-5a2373df4f3e" type="field">
          <property name="name" value="event_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6f437310-b073-465f-bedd-5f8a217b53fb" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="28cd60f4-969a-4c15-a377-ec01b2bb80ee" type="field">
          <property name="enum-values" value="'file','url'"/>
          <property name="name" value="type"/>
          <property name="required" value="true"/>
          <property name="size" value="4"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="0d9ce1da-addd-4921-b514-9838c3f398ce" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="6f437310-b073-465f-bedd-5f8a217b53fb" type="field">
          <property name="name" value="content_type"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="96dc2784-1399-4a28-8e5e-80709adc0fe1" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="0d9ce1da-addd-4921-b514-9838c3f398ce" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="96dc2784-1399-4a28-8e5e-80709adc0fe1" type="field">
          <property name="name" value="file_location"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="8a0e2691-9a0e-414b-a351-18a7dc42d1ea" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" type="field">
          <property name="name" value="url"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="5bedab7f-9f11-4020-aa30-cfe7c51083d3" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="8a0e2691-9a0e-414b-a351-18a7dc42d1ea" type="field">
          <property name="name" value="crc"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b894a703-bee8-4fef-af93-b979516ba268" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="5bedab7f-9f11-4020-aa30-cfe7c51083d3" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="ccf4e52c-20e3-4101-8a72-93b26d521ec1" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="b894a703-bee8-4fef-af93-b979516ba268" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="1c0dbc85-22c2-4e16-bad0-80a345b62d78" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="0b942573-0c2e-4c84-85a8-907d3c7939df" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="1c0dbc85-22c2-4e16-bad0-80a345b62d78" type="field">
          <property name="name" value="domain_name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4abb013d-bac7-484e-8f7c-616c064885bd" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="0b942573-0c2e-4c84-85a8-907d3c7939df" type="field">
          <property name="default" value="en"/>
          <property name="name" value="language"/>
          <property name="required" value="true"/>
          <property name="size" value="2"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="29e6016a-4447-42dc-a893-f50a5d3175ed" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="4abb013d-bac7-484e-8f7c-616c064885bd" type="field">
          <property name="name" value="lookup_url"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="45c48d9c-6c47-48bc-ac58-ea69a0d4d135" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="29e6016a-4447-42dc-a893-f50a5d3175ed" type="field">
          <property name="name" value="website_url"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7bf4897b-2d6b-430f-a592-45acf7f4f719" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="45c48d9c-6c47-48bc-ac58-ea69a0d4d135" type="field">
          <property name="name" value="logo_url"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="84d48ecc-4e98-4d0b-a10e-4c5187a19095" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="7bf4897b-2d6b-430f-a592-45acf7f4f719" type="field">
          <property name="name" value="rates_conditions_url"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="************************************" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="84d48ecc-4e98-4d0b-a10e-4c5187a19095" type="field">
          <property name="name" value="privacy_url"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7357f4e4-e4fb-4aa3-9dd5-1fb20d03a57a" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="************************************" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="224d02db-8612-42ea-8d62-84631d3a7ab1" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="7357f4e4-e4fb-4aa3-9dd5-1fb20d03a57a" type="field">
          <property name="name" value="address1"/>
          <property name="required" value="true"/>
          <property name="size" value="85"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="482f53e4-8598-4829-bc94-eb5d967cbaf5" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="224d02db-8612-42ea-8d62-84631d3a7ab1" type="field">
          <property name="name" value="address2"/>
          <property name="required" value="true"/>
          <property name="size" value="85"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="828e3e21-364d-4fc1-8e86-807b6bee5736" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="482f53e4-8598-4829-bc94-eb5d967cbaf5" type="field">
          <property name="name" value="zip"/>
          <property name="required" value="true"/>
          <property name="size" value="10"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="98319d40-5395-4bb0-be99-12f88f3a3f56" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="828e3e21-364d-4fc1-8e86-807b6bee5736" type="field">
          <property name="name" value="city"/>
          <property name="required" value="true"/>
          <property name="size" value="85"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="27bb27ca-1b35-4ce5-82ef-98222f77b235" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="98319d40-5395-4bb0-be99-12f88f3a3f56" type="field">
          <property name="name" value="telephone"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="9d4f9650-37d4-4fe9-87bb-aaffd42c6830" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="27bb27ca-1b35-4ce5-82ef-98222f77b235" type="field">
          <property name="name" value="email"/>
          <property name="required" value="true"/>
          <property name="size" value="35"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2b4ab67b-7c5e-431a-8109-9f2d7069ccc7" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="9d4f9650-37d4-4fe9-87bb-aaffd42c6830" type="field">
          <property name="name" value="adult_threshold"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="db67c9ec-3d5f-418d-8700-21259d11d438" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="2b4ab67b-7c5e-431a-8109-9f2d7069ccc7" type="field">
          <property name="name" value="contact_person_name"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="870b723e-6c7c-44cc-b926-109cc778035d" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="db67c9ec-3d5f-418d-8700-21259d11d438" type="field">
          <property name="decimal" value="1"/>
          <property name="default" value="21.0"/>
          <property name="name" value="course_tax_rate"/>
          <property name="required" value="true"/>
          <property name="size" value="3"/>
          <property name="type" value="double"/>
        </element>
        <element action="add" uuid="7005a7fd-dbf7-4fbf-a2a3-10bd475d307e" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="870b723e-6c7c-44cc-b926-109cc778035d" type="field">
          <property name="name" value="default_password"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c33dba2d-6017-483d-baaf-49b93f9011d4" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="7005a7fd-dbf7-4fbf-a2a3-10bd475d307e" type="field">
          <property name="name" value="schedule_threshold"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="dc882893-724d-4155-a809-d9d130212909" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="c33dba2d-6017-483d-baaf-49b93f9011d4" type="field">
          <property name="enum-values" value="'trial','pro'"/>
          <property name="name" value="status"/>
          <property name="required" value="true"/>
          <property name="size" value="5"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="accb0671-cda0-4c4b-a0ad-c4a601a7b7ee" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="dc882893-724d-4155-a809-d9d130212909" type="field">
          <property name="name" value="trial_end_date"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="2bd4d89b-f739-436e-acac-6c9d1b44e247" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="accb0671-cda0-4c4b-a0ad-c4a601a7b7ee" type="field">
          <property name="default" value="10"/>
          <property name="name" value="warn_before_birthday"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="5f1ecf71-f60f-4265-96e1-e4000db11f5c" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="2bd4d89b-f739-436e-acac-6c9d1b44e247" type="field">
          <property name="default" value="30"/>
          <property name="name" value="warn_before_adult"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="6012f778-4163-4499-ac09-de0422d96990" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="5f1ecf71-f60f-4265-96e1-e4000db11f5c" type="field">
          <property name="name" value="domaintoken"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="313e233e-cdc8-4d71-a33d-7a9a93ff342e" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="6012f778-4163-4499-ac09-de0422d96990" type="field">
          <property name="name" value="allowed_ip_addresses"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="b9c9e15a-e818-402b-b11a-505020e885e2" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="313e233e-cdc8-4d71-a33d-7a9a93ff342e" type="field">
          <property name="name" value="broadcast_colors"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="31f1ee8d-b684-49d5-aa3f-2d322a8325c4" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="b9c9e15a-e818-402b-b11a-505020e885e2" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="f36aa44a-7255-42a1-b133-55c64dfdcad5" parent-uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" sibling-uuid="31f1ee8d-b684-49d5-aa3f-2d322a8325c4" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="*************-432c-9344-d182311c1f2d" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="fa4e89a7-e447-4817-b5f4-f9d1b4d346fd" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="*************-432c-9344-d182311c1f2d" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="84287dfc-d0f1-463a-928d-e9a95b960d8c" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="fa4e89a7-e447-4817-b5f4-f9d1b4d346fd" type="field">
          <property name="name" value="to"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="a580cc6d-0f6e-45b6-b7aa-823e206dc3e0" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="84287dfc-d0f1-463a-928d-e9a95b960d8c" type="field">
          <property name="name" value="cc"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="f59e3644-b227-411d-9b5d-1ffae863ba9a" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="a580cc6d-0f6e-45b6-b7aa-823e206dc3e0" type="field">
          <property name="name" value="bcc"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="ba5f9172-4f85-462d-b575-a7c9d70d22b8" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="f59e3644-b227-411d-9b5d-1ffae863ba9a" type="field">
          <property name="name" value="from"/>
          <property name="required" value="true"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f72b79bb-5196-4fdf-8593-60636e5629cd" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="ba5f9172-4f85-462d-b575-a7c9d70d22b8" type="field">
          <property name="name" value="subject"/>
          <property name="required" value="true"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a437f661-8209-438b-b85b-1dfb424d5812" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="f72b79bb-5196-4fdf-8593-60636e5629cd" type="field">
          <property name="name" value="body"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="2d48a269-9b58-40c4-a830-5cd8eddbd7f4" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="a437f661-8209-438b-b85b-1dfb424d5812" type="field">
          <property name="name" value="attachments"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="8c3d86fa-a46c-46d5-992a-40b43f66a2e6" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="2d48a269-9b58-40c4-a830-5cd8eddbd7f4" type="field">
          <property name="name" value="studentids"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1c893d82-492b-415c-b69f-7cab63c21fd4" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="8c3d86fa-a46c-46d5-992a-40b43f66a2e6" type="field">
          <property name="default" value="unknown"/>
          <property name="name" value="unique_token"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="3e95e12c-21cf-44b8-bb80-a073e83738d4" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="1c893d82-492b-415c-b69f-7cab63c21fd4" type="field">
          <property name="default" value="unknown"/>
          <property name="name" value="status"/>
          <property name="required" value="true"/>
          <property name="size" value="10"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="289a0586-2b0b-4c4e-b41d-50e730b5a950" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="3e95e12c-21cf-44b8-bb80-a073e83738d4" type="field">
          <property name="name" value="log"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="9d53a336-d98b-4794-b627-abe6867234e5" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="289a0586-2b0b-4c4e-b41d-50e730b5a950" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="c921e9f2-449d-4e9d-a9b8-96e0d93aff80" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" sibling-uuid="9d53a336-d98b-4794-b627-abe6867234e5" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="dd07d277-e1be-4372-9765-29b42a55dab6" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="b83eafbb-3d9d-4432-84d9-edb0b4b74c8d" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="dd07d277-e1be-4372-9765-29b42a55dab6" type="field">
          <property name="name" value="location_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="b8d24715-3f86-4b97-84a7-5ebbe4470e7d" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="b83eafbb-3d9d-4432-84d9-edb0b4b74c8d" type="field">
          <property name="name" value="tutor_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="06c3a449-515d-4cd1-8cd3-e92104c2dab6" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="b8d24715-3f86-4b97-84a7-5ebbe4470e7d" type="field">
          <property name="name" value="caluniqueid"/>
          <property name="required" value="true"/>
          <property name="size" value="32"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b738fcbc-f591-462d-b4b4-c7dac35b0783" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="06c3a449-515d-4cd1-8cd3-e92104c2dab6" type="field">
          <property name="name" value="timetable_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5d68f58a-d578-4983-a9af-10c94c870640" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="b738fcbc-f591-462d-b4b4-c7dac35b0783" type="field">
          <property name="name" value="datetime"/>
          <property name="required" value="true"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="41ba7ed6-dc1d-404e-8df4-2a221c30c62f" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="5d68f58a-d578-4983-a9af-10c94c870640" type="field">
          <property name="default" value="2000-01-01 23:59:59"/>
          <property name="name" value="original_datetime"/>
          <property name="required" value="true"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="a7d10b80-e828-4bf6-a2d2-55e7442a5edb" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="41ba7ed6-dc1d-404e-8df4-2a221c30c62f" type="field">
          <property name="default" value="0"/>
          <property name="name" value="sequence"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="bf1fd5d2-0fe0-4377-88f3-f4c927795247" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="a7d10b80-e828-4bf6-a2d2-55e7442a5edb" type="field">
          <property name="name" value="timespan"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="7e90ef2b-1b69-4a71-96d4-24337435e5c5" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="bf1fd5d2-0fe0-4377-88f3-f4c927795247" type="field">
          <property name="name" value="remarks"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ead7b98f-5c6e-4a8a-bf02-8894bf5dbd9c" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="7e90ef2b-1b69-4a71-96d4-24337435e5c5" type="field">
          <property name="default" value="0"/>
          <property name="name" value="flag_sticky"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="324d369f-482e-4fde-bc3e-360ccedb4a1b" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="ead7b98f-5c6e-4a8a-bf02-8894bf5dbd9c" type="field">
          <property name="default" value="0"/>
          <property name="name" value="flag_publish"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="9c95850b-6438-4f13-ad33-b1f57261f570" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="324d369f-482e-4fde-bc3e-360ccedb4a1b" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="90126ad1-c4d8-41d2-8380-6adf132a81d0" parent-uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" sibling-uuid="9c95850b-6438-4f13-ad33-b1f57261f570" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="c3057afa-e47b-4c4a-b867-cdcf09a3d496" parent-uuid="dd612244-e406-4048-a3d9-5001719eaf66" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f0d4b654-6782-44db-b097-8e2811455556" parent-uuid="dd612244-e406-4048-a3d9-5001719eaf66" sibling-uuid="c3057afa-e47b-4c4a-b867-cdcf09a3d496" type="field">
          <property name="name" value="connection"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="b5f3eac2-a31b-475a-a492-da3b3403bfd7" parent-uuid="dd612244-e406-4048-a3d9-5001719eaf66" sibling-uuid="f0d4b654-6782-44db-b097-8e2811455556" type="field">
          <property name="name" value="queue"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="7f1e0d64-892e-4d7f-a65b-c42f0f96d077" parent-uuid="dd612244-e406-4048-a3d9-5001719eaf66" sibling-uuid="b5f3eac2-a31b-475a-a492-da3b3403bfd7" type="field">
          <property name="name" value="payload"/>
          <property name="required" value="true"/>
          <property name="type" value="longText"/>
        </element>
        <element action="add" uuid="bed656e8-9254-47ce-ac26-e53e83489a38" parent-uuid="dd612244-e406-4048-a3d9-5001719eaf66" sibling-uuid="7f1e0d64-892e-4d7f-a65b-c42f0f96d077" type="field">
          <property name="name" value="exception"/>
          <property name="required" value="true"/>
          <property name="type" value="longText"/>
        </element>
        <element action="add" uuid="ded7dbbd-cab4-413f-9236-6c99ffeb868e" parent-uuid="dd612244-e406-4048-a3d9-5001719eaf66" sibling-uuid="bed656e8-9254-47ce-ac26-e53e83489a38" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="failed_at"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="2489985e-9bf3-44e7-9293-edde06e216ad" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="b575b7d1-c810-4931-b0b4-69301d064b5c" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" sibling-uuid="2489985e-9bf3-44e7-9293-edde06e216ad" type="field">
          <property name="name" value="queue"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="774d8f92-9657-49a9-b877-d3a35a203abd" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" sibling-uuid="b575b7d1-c810-4931-b0b4-69301d064b5c" type="field">
          <property name="name" value="payload"/>
          <property name="required" value="true"/>
          <property name="type" value="longText"/>
        </element>
        <element action="add" uuid="675d0926-9bf9-4f79-9bcf-6703b35cb037" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" sibling-uuid="774d8f92-9657-49a9-b877-d3a35a203abd" type="field">
          <property name="name" value="attempts"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="0718327b-9244-4354-80c9-541d9ee7a7f9" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" sibling-uuid="675d0926-9bf9-4f79-9bcf-6703b35cb037" type="field">
          <property name="name" value="reserved_at"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ae66f1f2-d2b3-44cb-8582-a1ec6e0927d4" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" sibling-uuid="0718327b-9244-4354-80c9-541d9ee7a7f9" type="field">
          <property name="name" value="available_at"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f136ed12-ead3-4979-a8e9-a2fe00df9433" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" sibling-uuid="ae66f1f2-d2b3-44cb-8582-a1ec6e0927d4" type="field">
          <property name="name" value="created_at"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="40746c69-04fb-48ce-a721-2016e406ec80" parent-uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="526e1761-ea8f-43d5-8b1c-fbba3f92051d" parent-uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" sibling-uuid="40746c69-04fb-48ce-a721-2016e406ec80" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="4b9f3e25-eecf-4755-8336-f1b3e62ddd8f" parent-uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" sibling-uuid="526e1761-ea8f-43d5-8b1c-fbba3f92051d" type="field">
          <property name="name" value="tutor_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="216d7023-8b3f-472a-b822-5568c8bc9b2a" parent-uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" sibling-uuid="4b9f3e25-eecf-4755-8336-f1b3e62ddd8f" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6f0dc744-fc0c-4fd9-8794-92bc14a0ee2d" parent-uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" sibling-uuid="216d7023-8b3f-472a-b822-5568c8bc9b2a" type="field">
          <property name="default" value="0"/>
          <property name="name" value="share_with_whole_school"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="d6fa110a-c7af-4b1f-92c8-c059beede88b" parent-uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" sibling-uuid="6f0dc744-fc0c-4fd9-8794-92bc14a0ee2d" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="bfdb7b36-7383-4aa8-bdce-b6fe3de16dff" parent-uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" sibling-uuid="d6fa110a-c7af-4b1f-92c8-c059beede88b" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e2ba3db5-f8f3-4eac-a903-7cebf8c9c788" parent-uuid="b8404cea-9e03-48c5-b2e8-d2014a185356" type="field">
          <property name="name" value="course_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6a1505b2-ced1-4f70-85a8-b1794aa259d9" parent-uuid="b8404cea-9e03-48c5-b2e8-d2014a185356" sibling-uuid="e2ba3db5-f8f3-4eac-a903-7cebf8c9c788" type="field">
          <property name="name" value="library_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f7c05055-be04-4291-81b0-b58435074e3c" parent-uuid="b8404cea-9e03-48c5-b2e8-d2014a185356" sibling-uuid="6a1505b2-ced1-4f70-85a8-b1794aa259d9" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="8b675610-629e-4705-a9cf-3433654d9c9c" parent-uuid="b8404cea-9e03-48c5-b2e8-d2014a185356" sibling-uuid="f7c05055-be04-4291-81b0-b58435074e3c" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a6f7ae14-fd0d-4287-96ed-19408883ab55" parent-uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf" type="field">
          <property name="name" value="coursegroup_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6fb85621-**************-9b1c14a9c8b8" parent-uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf" sibling-uuid="a6f7ae14-fd0d-4287-96ed-19408883ab55" type="field">
          <property name="name" value="library_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a0642570-40a2-46d9-b0a0-58ecfa1a85e0" parent-uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf" sibling-uuid="6fb85621-**************-9b1c14a9c8b8" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="7fbb198d-fb32-49ca-a87b-fcedbc6c8516" parent-uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf" sibling-uuid="a0642570-40a2-46d9-b0a0-58ecfa1a85e0" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e4e88a59-e2bc-45a8-be70-d07ee74308e2" parent-uuid="ceb6e565-00ec-43e2-8146-5e052d702270" type="field">
          <property name="name" value="document_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="d6918bdc-f9d6-4218-b821-cf2e162c107b" parent-uuid="ceb6e565-00ec-43e2-8146-5e052d702270" sibling-uuid="e4e88a59-e2bc-45a8-be70-d07ee74308e2" type="field">
          <property name="name" value="library_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="fc962001-19bc-4ab6-a655-6ba0e5025432" parent-uuid="ceb6e565-00ec-43e2-8146-5e052d702270" sibling-uuid="d6918bdc-f9d6-4218-b821-cf2e162c107b" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e41a3e66-2a02-4018-8758-5f582314bc89" parent-uuid="ceb6e565-00ec-43e2-8146-5e052d702270" sibling-uuid="fc962001-19bc-4ab6-a655-6ba0e5025432" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a9251b9f-eb8d-4ae4-aa9f-c52224686bc8" parent-uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc" type="field">
          <property name="name" value="student_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="41ec4db7-2125-47a2-b167-df5d0f0946f4" parent-uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc" sibling-uuid="a9251b9f-eb8d-4ae4-aa9f-c52224686bc8" type="field">
          <property name="name" value="library_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="51c171e1-10d8-4ad7-ab4f-91629d8780c3" parent-uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc" sibling-uuid="41ec4db7-2125-47a2-b167-df5d0f0946f4" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="fa532052-b832-4f21-bf21-77e847ff9d37" parent-uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc" sibling-uuid="51c171e1-10d8-4ad7-ab4f-91629d8780c3" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0e4fae86-f8b6-4019-9e4e-5695ee03220c" parent-uuid="ef423929-3822-431c-a4bb-36e67d80a964" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="cd6ca238-240d-44e1-bd4f-5b9f469e2516" parent-uuid="ef423929-3822-431c-a4bb-36e67d80a964" sibling-uuid="0e4fae86-f8b6-4019-9e4e-5695ee03220c" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="68bd620d-6776-4373-8a32-60866db8ec71" parent-uuid="ef423929-3822-431c-a4bb-36e67d80a964" sibling-uuid="cd6ca238-240d-44e1-bd4f-5b9f469e2516" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ec4abb96-1bf1-4e80-ada9-84bd2075ebd7" parent-uuid="ef423929-3822-431c-a4bb-36e67d80a964" sibling-uuid="68bd620d-6776-4373-8a32-60866db8ec71" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="320377db-9f1d-4667-8e3a-964b39dd19be" parent-uuid="ef423929-3822-431c-a4bb-36e67d80a964" sibling-uuid="ec4abb96-1bf1-4e80-ada9-84bd2075ebd7" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="717a7eae-b0c3-4807-96d6-b30dc731b7c0" parent-uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="9577a00a-5f6f-4373-bde7-ebd87dc83e9d" parent-uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" sibling-uuid="717a7eae-b0c3-4807-96d6-b30dc731b7c0" type="field">
          <property name="name" value="student_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="0eee24cc-616a-4dd9-828d-a3554936cf33" parent-uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" sibling-uuid="9577a00a-5f6f-4373-bde7-ebd87dc83e9d" type="field">
          <property name="name" value="entry"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="b1b4981d-478b-494b-a2f8-8120fed0227c" parent-uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" sibling-uuid="0eee24cc-616a-4dd9-828d-a3554936cf33" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="7bff5a8b-9faa-444c-86f8-7ec626f9a44e" parent-uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" sibling-uuid="b1b4981d-478b-494b-a2f8-8120fed0227c" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="5dae1d1a-4645-4e33-bcf7-9ce47946157f" parent-uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="fae7d048-b54e-41d2-95e9-d413c6e92dbc" parent-uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" sibling-uuid="5dae1d1a-4645-4e33-bcf7-9ce47946157f" type="field">
          <property name="name" value="user_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="9cd829c0-2394-4d44-b6a3-188e4deb4bdb" parent-uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" sibling-uuid="fae7d048-b54e-41d2-95e9-d413c6e92dbc" type="field">
          <property name="default" value="0"/>
          <property name="name" value="google2fa_enable"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="690bafde-2d90-4d21-8213-09f41ca4b67f" parent-uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" sibling-uuid="9cd829c0-2394-4d44-b6a3-188e4deb4bdb" type="field">
          <property name="name" value="google2fa_secret"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1046a4dc-0e32-415c-948b-fb96aba31123" parent-uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" sibling-uuid="690bafde-2d90-4d21-8213-09f41ca4b67f" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="03179f7d-1608-4050-8a13-98dc65c19e33" parent-uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" sibling-uuid="1046a4dc-0e32-415c-948b-fb96aba31123" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="27b47e8e-6fce-411a-bb30-72a8804f9e89" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="55a03dfd-73ca-4c90-b263-b04632ff57d3" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" sibling-uuid="27b47e8e-6fce-411a-bb30-72a8804f9e89" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a1fe614e-182e-492c-bc08-d86c7c8a1d6d" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" sibling-uuid="55a03dfd-73ca-4c90-b263-b04632ff57d3" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="83044825-9d6a-4549-9690-49af6b33f937" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" sibling-uuid="a1fe614e-182e-492c-bc08-d86c7c8a1d6d" type="field">
          <property name="default" value="a"/>
          <property name="name" value="targets"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="75197df9-1017-46a2-bb88-a5dc9da80f97" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" sibling-uuid="83044825-9d6a-4549-9690-49af6b33f937" type="field">
          <property name="name" value="content"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="3f7fb64f-bfb2-44b0-aa9e-fbef88121556" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" sibling-uuid="75197df9-1017-46a2-bb88-a5dc9da80f97" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="4f98686f-965a-4feb-9b7b-8d45e02b8433" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" sibling-uuid="3f7fb64f-bfb2-44b0-aa9e-fbef88121556" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="910c866c-051f-4ec0-ad9e-d9a68e1d754e" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="e9b544f1-5cbc-49ff-9174-3c29e17f2233" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="910c866c-051f-4ec0-ad9e-d9a68e1d754e" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="77d8be7e-fcec-4422-85d5-fa424e1e47c2" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="e9b544f1-5cbc-49ff-9174-3c29e17f2233" type="field">
          <property name="name" value="from_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="552e9f48-1d65-4a2e-bdcb-2b89cb4060f0" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="77d8be7e-fcec-4422-85d5-fa424e1e47c2" type="field">
          <property name="name" value="from_type"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fbb9f02a-4603-486a-b862-c17800fae867" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="552e9f48-1d65-4a2e-bdcb-2b89cb4060f0" type="field">
          <property name="name" value="from_label"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="8df657ba-96cb-4310-b56a-1d746276eed5" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="fbb9f02a-4603-486a-b862-c17800fae867" type="field">
          <property name="name" value="to_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="90613c0e-faf9-467c-bb39-203370d91399" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="8df657ba-96cb-4310-b56a-1d746276eed5" type="field">
          <property name="name" value="to_type"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="04949edf-025d-45fd-8493-e4ee1e00a51d" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="90613c0e-faf9-467c-bb39-203370d91399" type="field">
          <property name="name" value="to_label"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="84793f30-5a63-4faa-865c-66ee59f869ae" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="04949edf-025d-45fd-8493-e4ee1e00a51d" type="field">
          <property name="name" value="subject"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f625fbee-0512-45fc-96c1-36b8bdfe3c75" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="84793f30-5a63-4faa-865c-66ee59f869ae" type="field">
          <property name="name" value="body"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="072e5813-d177-40d2-9f59-d902cbb7a18f" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="f625fbee-0512-45fc-96c1-36b8bdfe3c75" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="977cafce-1d03-454e-a56d-9ff15401ea03" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="072e5813-d177-40d2-9f59-d902cbb7a18f" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="f30976d2-7287-4333-ad5b-cbb12859ea5c" parent-uuid="c94bd824-635d-40bb-9717-360ea46514e9" sibling-uuid="977cafce-1d03-454e-a56d-9ff15401ea03" type="field">
          <property name="name" value="read_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="d23fb2c3-5c2a-4b15-900b-e55b23d504df" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" type="field">
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="d50a66b1-033c-44cc-81c9-0c8c5b7dbe71" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="d23fb2c3-5c2a-4b15-900b-e55b23d504df" type="field">
          <property name="name" value="user_id"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="7ea84620-4e35-406c-820c-cdc2a4d28663" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="d50a66b1-033c-44cc-81c9-0c8c5b7dbe71" type="field">
          <property name="name" value="client_id"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="106ef390-8efa-495c-bc82-e79e60155c5c" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="7ea84620-4e35-406c-820c-cdc2a4d28663" type="field">
          <property name="name" value="name"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="67464128-bff4-41d4-88bf-b4635daad936" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="106ef390-8efa-495c-bc82-e79e60155c5c" type="field">
          <property name="name" value="scopes"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="d3e298d7-7ed8-4119-ac88-0c41e93f4c7d" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="67464128-bff4-41d4-88bf-b4635daad936" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="fa9741dc-6d5f-4e20-9d46-23442b633619" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="d3e298d7-7ed8-4119-ac88-0c41e93f4c7d" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="d8090b68-4990-4f16-ac85-e6614ddff8ef" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="fa9741dc-6d5f-4e20-9d46-23442b633619" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="2296c9ef-cc0e-45af-be46-86407e950cf8" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" sibling-uuid="d8090b68-4990-4f16-ac85-e6614ddff8ef" type="field">
          <property name="name" value="expires_at"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="a4830069-a3e3-43e6-b0cc-388b2eccfa15" parent-uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" type="field">
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="6194356a-2a44-475e-bd91-fa2ce0d449ef" parent-uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" sibling-uuid="a4830069-a3e3-43e6-b0cc-388b2eccfa15" type="field">
          <property name="name" value="user_id"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5c6e77eb-669f-4064-b81e-0a1d9f5a2c4c" parent-uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" sibling-uuid="6194356a-2a44-475e-bd91-fa2ce0d449ef" type="field">
          <property name="name" value="client_id"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="65695e74-8a66-426e-83fc-5e3702acbaa0" parent-uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" sibling-uuid="5c6e77eb-669f-4064-b81e-0a1d9f5a2c4c" type="field">
          <property name="name" value="scopes"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="61f78b9e-c6d5-44b9-9d88-7c18431785e9" parent-uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" sibling-uuid="65695e74-8a66-426e-83fc-5e3702acbaa0" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="43e1a466-5ffc-41d5-b9f0-71e88ca780f8" parent-uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" sibling-uuid="61f78b9e-c6d5-44b9-9d88-7c18431785e9" type="field">
          <property name="name" value="expires_at"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="1c54a139-0539-437d-9414-f07435833a22" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="75244cbd-b37d-4eba-b332-ea2bfb7ea48d" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="1c54a139-0539-437d-9414-f07435833a22" type="field">
          <property name="name" value="user_id"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="860fa5cf-72ac-43c3-9ab0-df41aa96de42" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="75244cbd-b37d-4eba-b332-ea2bfb7ea48d" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ccb9ae0d-3aea-4b7b-ac3d-7951da091f4a" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="860fa5cf-72ac-43c3-9ab0-df41aa96de42" type="field">
          <property name="name" value="secret"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1a78b317-9d41-4558-a426-0e03b3222557" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="ccb9ae0d-3aea-4b7b-ac3d-7951da091f4a" type="field">
          <property name="name" value="provider"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ab1c5690-6d2a-4765-ba42-a1efb6b45055" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="1a78b317-9d41-4558-a426-0e03b3222557" type="field">
          <property name="name" value="redirect"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="9f2d81c9-b678-4a1e-a7b0-a9afe7e96340" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="ab1c5690-6d2a-4765-ba42-a1efb6b45055" type="field">
          <property name="name" value="personal_access_client"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="793d6176-480a-4fc9-8c11-a585072b3ee3" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="9f2d81c9-b678-4a1e-a7b0-a9afe7e96340" type="field">
          <property name="name" value="password_client"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="bfbaacc8-b1f3-4c48-a24a-487ed6881a86" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="793d6176-480a-4fc9-8c11-a585072b3ee3" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="67dda06d-6317-4a45-ba19-2d2cda49d652" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="bfbaacc8-b1f3-4c48-a24a-487ed6881a86" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="cec5df34-e9da-4bc5-8c91-00626f3215ac" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" sibling-uuid="67dda06d-6317-4a45-ba19-2d2cda49d652" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="662ea3f9-0171-42ee-94ca-a03a4b3c6887" parent-uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="8055801d-3045-418b-bb6b-c9a372a17d79" parent-uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b" sibling-uuid="662ea3f9-0171-42ee-94ca-a03a4b3c6887" type="field">
          <property name="name" value="client_id"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="eaa1ecb5-cac7-4ff0-bea1-afb1524da5c6" parent-uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b" sibling-uuid="8055801d-3045-418b-bb6b-c9a372a17d79" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e363c7a1-116d-482e-963f-************" parent-uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b" sibling-uuid="eaa1ecb5-cac7-4ff0-bea1-afb1524da5c6" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="7ee69630-fc66-4243-85d6-2864cc083df6" parent-uuid="e799a893-7c3e-4ae7-b214-946c90225fca" type="field">
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="07ddb140-e18a-4584-99c2-a87dcc9d5beb" parent-uuid="e799a893-7c3e-4ae7-b214-946c90225fca" sibling-uuid="7ee69630-fc66-4243-85d6-2864cc083df6" type="field">
          <property name="name" value="access_token_id"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c79c3090-4983-4c65-97c9-ebe24864d513" parent-uuid="e799a893-7c3e-4ae7-b214-946c90225fca" sibling-uuid="07ddb140-e18a-4584-99c2-a87dcc9d5beb" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="42e118e3-cbf9-4951-b505-5673ca2a0e74" parent-uuid="e799a893-7c3e-4ae7-b214-946c90225fca" sibling-uuid="c79c3090-4983-4c65-97c9-ebe24864d513" type="field">
          <property name="name" value="expires_at"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="7041d879-296d-4344-aec7-8599a3ef2cc3" parent-uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" type="field">
          <property name="name" value="email"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e031b6db-4649-4cb2-9b0d-a846b4151142" parent-uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" sibling-uuid="7041d879-296d-4344-aec7-8599a3ef2cc3" type="field">
          <property name="name" value="token"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fa50c28c-31e3-468e-b577-b1d539442c4c" parent-uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" sibling-uuid="e031b6db-4649-4cb2-9b0d-a846b4151142" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="da490eda-eb0c-4aa3-aef1-a2da01701f2a" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="3f5e1452-8c01-4c39-afe9-2e08150af66f" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="da490eda-eb0c-4aa3-aef1-a2da01701f2a" type="field">
          <property name="name" value="tokenable_type"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0e8bfb0a-488b-4370-bc43-b980fdc1dd35" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="3f5e1452-8c01-4c39-afe9-2e08150af66f" type="field">
          <property name="name" value="tokenable_id"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2d155006-2cc6-43fc-8823-38e3aa8d18be" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="0e8bfb0a-488b-4370-bc43-b980fdc1dd35" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6e0c0179-206a-480a-9d58-054577b8df87" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="2d155006-2cc6-43fc-8823-38e3aa8d18be" type="field">
          <property name="name" value="token"/>
          <property name="required" value="true"/>
          <property name="size" value="64"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7a0be331-7207-4896-adca-c2b1a769b56c" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="6e0c0179-206a-480a-9d58-054577b8df87" type="field">
          <property name="name" value="abilities"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="fe199e6b-05f2-4516-882c-ac398aa476d9" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="7a0be331-7207-4896-adca-c2b1a769b56c" type="field">
          <property name="name" value="last_used_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="83256cb9-33ef-4d71-bbb8-66ef3d2995cd" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="fe199e6b-05f2-4516-882c-ac398aa476d9" type="field">
          <property name="name" value="expires_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="1b5a321b-bf0c-4090-9fcc-637be0a027b6" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="83256cb9-33ef-4d71-bbb8-66ef3d2995cd" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="3d4296ac-e0e2-489e-bf36-a0a7ee0c691f" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" sibling-uuid="1b5a321b-bf0c-4090-9fcc-637be0a027b6" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="c15b2e00-301e-48ce-9c2c-38655d70ff33" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="003c0421-8f7b-40d5-89bf-3d21c4430dc6" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="c15b2e00-301e-48ce-9c2c-38655d70ff33" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c15f852f-9e48-4af6-bcbc-76dc322c9442" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="003c0421-8f7b-40d5-89bf-3d21c4430dc6" type="field">
          <property name="enum-values" value="'insert','update','delete'"/>
          <property name="name" value="change"/>
          <property name="required" value="true"/>
          <property name="size" value="6"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="6d736061-46fa-4270-afe4-78a6fce64f74" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="c15f852f-9e48-4af6-bcbc-76dc322c9442" type="field">
          <property name="name" value="planningentry_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="fe7e331d-7da2-4a31-b65f-469de5c5f961" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="6d736061-46fa-4270-afe4-78a6fce64f74" type="field">
          <property name="name" value="new_tutor_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="24a9ed41-d222-4075-890b-aab0ff0d2f90" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="fe7e331d-7da2-4a31-b65f-469de5c5f961" type="field">
          <property name="name" value="new_location_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="1f14bd6e-9fd9-460e-b2cd-0fce235e0314" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="24a9ed41-d222-4075-890b-aab0ff0d2f90" type="field">
          <property name="name" value="new_starttime"/>
          <property name="type" value="time"/>
        </element>
        <element action="add" uuid="f806ae9e-f2c3-4448-bf8b-1e28738640e8" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="1f14bd6e-9fd9-460e-b2cd-0fce235e0314" type="field">
          <property name="name" value="new_daynumber"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="aea18727-055b-4767-9cd6-740cac9adebb" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="f806ae9e-f2c3-4448-bf8b-1e28738640e8" type="field">
          <property name="enum-values" value="'odd','even','oddeven','other'"/>
          <property name="name" value="new_oddeven"/>
          <property name="size" value="7"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="674eae94-9f5b-4fc5-95fa-d4f3ed519235" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="aea18727-055b-4767-9cd6-740cac9adebb" type="field">
          <property name="default" value="initial"/>
          <property name="enum-values" value="'initial','handled','failed'"/>
          <property name="name" value="status"/>
          <property name="required" value="true"/>
          <property name="size" value="7"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="9d661525-29cb-4f15-80da-d80585b6d1f9" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="674eae94-9f5b-4fc5-95fa-d4f3ed519235" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="c669af82-419f-450b-a7be-b4e3e9a4233e" parent-uuid="2a98b125-f664-422e-9097-b8896608cafa" sibling-uuid="9d661525-29cb-4f15-80da-d80585b6d1f9" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="93c3cf69-b5bf-48ce-8a4b-da2448b8da56" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="3e431bb1-10ea-487c-a144-f3b179516f33" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="93c3cf69-b5bf-48ce-8a4b-da2448b8da56" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c6a52e92-5b39-4de8-8945-2772caed5190" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="3e431bb1-10ea-487c-a144-f3b179516f33" type="field">
          <property name="name" value="registration_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="670a2898-e934-4335-b623-f1cb0fd49dfd" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="c6a52e92-5b39-4de8-8945-2772caed5190" type="field">
          <property name="name" value="tutor_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="b7699c57-1610-4e14-8f3c-253ef7770c7f" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="670a2898-e934-4335-b623-f1cb0fd49dfd" type="field">
          <property name="name" value="location_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="dd154762-f0c8-430f-9c64-f94912b072bd" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="b7699c57-1610-4e14-8f3c-253ef7770c7f" type="field">
          <property name="name" value="starttime"/>
          <property name="required" value="true"/>
          <property name="type" value="time"/>
        </element>
        <element action="add" uuid="f55d843d-16f7-41b3-b307-7cf98b7914a6" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="dd154762-f0c8-430f-9c64-f94912b072bd" type="field">
          <property name="default" value="1"/>
          <property name="name" value="duration"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="49e39d4b-dc23-4cac-b81e-b74a1720198d" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="f55d843d-16f7-41b3-b307-7cf98b7914a6" type="field">
          <property name="default" value="1"/>
          <property name="name" value="daynumber"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="acf77095-07d1-4c1c-94f5-0ba1515d44c3" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="49e39d4b-dc23-4cac-b81e-b74a1720198d" type="field">
          <property name="default" value="other"/>
          <property name="enum-values" value="'odd','even','oddeven','other'"/>
          <property name="name" value="oddeven"/>
          <property name="required" value="true"/>
          <property name="size" value="7"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="e459fa0e-aeae-4656-9ad3-1840056709e8" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="acf77095-07d1-4c1c-94f5-0ba1515d44c3" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="963efe5c-d1e8-416c-9785-25326be12147" parent-uuid="b7435152-5821-4b19-9e50-701520fe7704" sibling-uuid="e459fa0e-aeae-4656-9ad3-1840056709e8" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="ae9bbe9a-aac1-4abd-9ec6-9680ca4519aa" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6459de8d-374e-4835-b061-23753c902ed6" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="ae9bbe9a-aac1-4abd-9ec6-9680ca4519aa" type="field">
          <property name="name" value="course_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="fd52aa24-9d45-46d5-b245-35d466bbecfc" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="6459de8d-374e-4835-b061-23753c902ed6" type="field">
          <property name="name" value="from_nr_of_students"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="c78d621b-5e9f-41e5-8767-510b0ec8fb60" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="fd52aa24-9d45-46d5-b245-35d466bbecfc" type="field">
          <property name="name" value="to_nr_of_students"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="447e27a3-f259-4c6c-83b2-c9d7cb8e4a0c" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="c78d621b-5e9f-41e5-8767-510b0ec8fb60" type="field">
          <property name="decimal" value="2"/>
          <property name="default" value="0.00"/>
          <property name="name" value="price_ex_tax"/>
          <property name="required" value="true"/>
          <property name="size" value="6"/>
          <property name="type" value="decimal"/>
        </element>
        <element action="add" uuid="d90d784a-af31-4a3d-acda-0f7a8fe8f189" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="447e27a3-f259-4c6c-83b2-c9d7cb8e4a0c" type="field">
          <property name="default" value="month"/>
          <property name="name" value="price_is_per"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="04954199-ef64-416a-8cd0-ff5aadbba9eb" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="d90d784a-af31-4a3d-acda-0f7a8fe8f189" type="field">
          <property name="default" value="0"/>
          <property name="name" value="tax_rate"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="44a791f6-7c9b-454d-a5d0-fa4e2f3bf20d" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="04954199-ef64-416a-8cd0-ff5aadbba9eb" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="1973fa8c-f5b6-4225-867a-852d3665000a" parent-uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" sibling-uuid="44a791f6-7c9b-454d-a5d0-fa4e2f3bf20d" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="38fe07dd-de76-4282-8176-e9f03667d725" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="05dce5c5-accf-4b91-a777-ebf3ac302668" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="38fe07dd-de76-4282-8176-e9f03667d725" type="field">
          <property name="name" value="bucket"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="393ebccc-d117-488d-9891-dbc5adcaba8e" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="05dce5c5-accf-4b91-a777-ebf3ac302668" type="field">
          <property name="name" value="period"/>
          <property name="required" value="true"/>
          <property name="type" value="mediumInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="329900a1-d371-4521-99b0-8ee82ae529d9" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="393ebccc-d117-488d-9891-dbc5adcaba8e" type="field">
          <property name="name" value="type"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ecf6e18d-978e-4785-b03d-fc4c368bb0f4" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="329900a1-d371-4521-99b0-8ee82ae529d9" type="field">
          <property name="name" value="key"/>
          <property name="required" value="true"/>
          <property name="type" value="mediumText"/>
        </element>
        <element action="add" uuid="cad6f013-b04a-480e-a291-abea5731820c" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="ecf6e18d-978e-4785-b03d-fc4c368bb0f4" type="field">
          <property name="name" value="key_hash"/>
          <property name="size" value="16"/>
          <property name="type" value="binary"/>
        </element>
        <element action="add" uuid="2a3a4bee-d088-4070-a451-8df54885ecf8" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="cad6f013-b04a-480e-a291-abea5731820c" type="field">
          <property name="name" value="aggregate"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a2a64dd8-546a-486a-9f46-7e586542adc7" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="2a3a4bee-d088-4070-a451-8df54885ecf8" type="field">
          <property name="decimal" value="2"/>
          <property name="name" value="value"/>
          <property name="required" value="true"/>
          <property name="size" value="20"/>
          <property name="type" value="decimal"/>
        </element>
        <element action="add" uuid="de11156c-ff65-43b4-adc5-2226542a5b74" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" sibling-uuid="a2a64dd8-546a-486a-9f46-7e586542adc7" type="field">
          <property name="name" value="count"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2b61e224-cf73-4800-aecf-b556ccb86862" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="d88ae2ac-319c-4e28-9d49-161f1dffb75d" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" sibling-uuid="2b61e224-cf73-4800-aecf-b556ccb86862" type="field">
          <property name="name" value="timestamp"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="92b15d9f-eacf-49e7-9d50-b35d9322f8f9" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" sibling-uuid="d88ae2ac-319c-4e28-9d49-161f1dffb75d" type="field">
          <property name="name" value="type"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e370c3ba-a738-4c85-a7cf-0b9173d88f8c" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" sibling-uuid="92b15d9f-eacf-49e7-9d50-b35d9322f8f9" type="field">
          <property name="name" value="key"/>
          <property name="required" value="true"/>
          <property name="type" value="mediumText"/>
        </element>
        <element action="add" uuid="81d3b8a4-7dfe-4b79-a7c1-192b3d860397" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" sibling-uuid="e370c3ba-a738-4c85-a7cf-0b9173d88f8c" type="field">
          <property name="name" value="key_hash"/>
          <property name="size" value="16"/>
          <property name="type" value="binary"/>
        </element>
        <element action="add" uuid="d51794f0-00cc-44d6-980d-8315894eb9dd" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" sibling-uuid="81d3b8a4-7dfe-4b79-a7c1-192b3d860397" type="field">
          <property name="name" value="value"/>
          <property name="type" value="bigInteger"/>
        </element>
        <element action="add" uuid="03acc5e3-5257-4883-afe3-c0fb259294ac" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6ec06755-1153-4ee5-9ff9-f11ecfdae9d9" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" sibling-uuid="03acc5e3-5257-4883-afe3-c0fb259294ac" type="field">
          <property name="name" value="timestamp"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="22a0deb7-29a1-44c0-907b-1de200cdafc5" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" sibling-uuid="6ec06755-1153-4ee5-9ff9-f11ecfdae9d9" type="field">
          <property name="name" value="type"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7eed8531-1b95-4ff9-9a25-74727385f9f0" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" sibling-uuid="22a0deb7-29a1-44c0-907b-1de200cdafc5" type="field">
          <property name="name" value="key"/>
          <property name="required" value="true"/>
          <property name="type" value="mediumText"/>
        </element>
        <element action="add" uuid="0c3284db-13ee-4bc8-8a34-f20682c1e2b9" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" sibling-uuid="7eed8531-1b95-4ff9-9a25-74727385f9f0" type="field">
          <property name="name" value="key_hash"/>
          <property name="size" value="16"/>
          <property name="type" value="binary"/>
        </element>
        <element action="add" uuid="24e81dc3-75ec-402a-9b7c-153b27097e82" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" sibling-uuid="0c3284db-13ee-4bc8-8a34-f20682c1e2b9" type="field">
          <property name="name" value="value"/>
          <property name="required" value="true"/>
          <property name="type" value="mediumText"/>
        </element>
        <element action="add" uuid="464fcf97-e931-471e-963a-cfa05d2fdd41" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="079e0266-a34d-49b1-89f3-03303c751f4a" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="464fcf97-e931-471e-963a-cfa05d2fdd41" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6f5368ea-5c44-491f-a296-81769946fd7b" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="079e0266-a34d-49b1-89f3-03303c751f4a" type="field">
          <property name="default" value="0"/>
          <property name="name" value="is_trial_lesson"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="f0f7e0ab-8aa2-4c2b-aa2b-d2904f9c8811" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="6f5368ea-5c44-491f-a296-81769946fd7b" type="field">
          <property name="name" value="description"/>
          <property name="required" value="true"/>
          <property name="size" value="80"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6dd76fc2-dce1-4c19-8cd0-2fe411e19146" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="f0f7e0ab-8aa2-4c2b-aa2b-d2904f9c8811" type="field">
          <property name="decimal" value="2"/>
          <property name="name" value="nr_of_times"/>
          <property name="required" value="true"/>
          <property name="size" value="8"/>
          <property name="type" value="double"/>
        </element>
        <element action="add" uuid="3b8e1785-8122-4419-ab30-c1f9e7212a79" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="6dd76fc2-dce1-4c19-8cd0-2fe411e19146" type="field">
          <property name="name" value="timeunit"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="3a1d6a60-af14-4502-96c3-b5b2f4d0687d" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="3b8e1785-8122-4419-ab30-c1f9e7212a79" type="field">
          <property name="name" value="per_interval"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2b311125-2000-43a2-b867-433e79cd48ab" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="3a1d6a60-af14-4502-96c3-b5b2f4d0687d" type="field">
          <property name="name" value="ends_after_nr_of_occurrences"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="1a9ab171-d225-4bf9-905a-85c00f3c1838" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="2b311125-2000-43a2-b867-433e79cd48ab" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="fafdc948-7037-4a35-bff4-603cc1e6aec8" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" sibling-uuid="1a9ab171-d225-4bf9-905a-85c00f3c1838" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="ac99ce09-6edc-4c1f-9ab1-4c3153ae0c6d" parent-uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" type="field">
          <property name="name" value="user_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="d61cf553-2576-4fdd-8f03-6f3143f4de65" parent-uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" sibling-uuid="ac99ce09-6edc-4c1f-9ab1-4c3153ae0c6d" type="field">
          <property name="name" value="role_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="0b084fee-7fbd-471a-9f02-5c1cac783386" parent-uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" sibling-uuid="d61cf553-2576-4fdd-8f03-6f3143f4de65" type="field">
          <property name="default" value="5f6f7e"/>
          <property name="name" value="calcolor"/>
          <property name="required" value="true"/>
          <property name="size" value="6"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="3c48da3f-4c4e-463c-b24a-accbe568260d" parent-uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" sibling-uuid="0b084fee-7fbd-471a-9f02-5c1cac783386" type="field">
          <property name="default" value="2015-01-01"/>
          <property name="name" value="start_date"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="0d0d0b6c-4f71-499f-83d2-cb2d6b680c18" parent-uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" sibling-uuid="3c48da3f-4c4e-463c-b24a-accbe568260d" type="field">
          <property name="name" value="end_date"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="71e19a15-978f-4d68-9761-5fd11a8470a1" parent-uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" sibling-uuid="0d0d0b6c-4f71-499f-83d2-cb2d6b680c18" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="bcbc0e45-632b-4e4f-9331-cefcc328212c" parent-uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" sibling-uuid="71e19a15-978f-4d68-9761-5fd11a8470a1" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="527c0d31-079c-4dec-8bf4-ec5a0b89e488" parent-uuid="c10c1531-15c0-4e26-8860-4d6068585afa" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f7cb2429-77c3-4fc3-8344-0c1fa4f47232" parent-uuid="c10c1531-15c0-4e26-8860-4d6068585afa" sibling-uuid="527c0d31-079c-4dec-8bf4-ec5a0b89e488" type="field">
          <property name="name" value="rolename"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f969baf7-35ab-49d7-acb8-bb9f3cd8a409" parent-uuid="c10c1531-15c0-4e26-8860-4d6068585afa" sibling-uuid="f7cb2429-77c3-4fc3-8344-0c1fa4f47232" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="8e134f05-9012-466a-bf81-b069fb37e158" parent-uuid="c10c1531-15c0-4e26-8860-4d6068585afa" sibling-uuid="f969baf7-35ab-49d7-acb8-bb9f3cd8a409" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="565a955d-094e-49c6-bbc1-9da9db32ccb1" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2c87cb1a-43db-46b4-8ef2-49889230b99a" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="565a955d-094e-49c6-bbc1-9da9db32ccb1" type="field">
          <property name="name" value="student_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="cf93296c-2949-49ce-86b9-e4ef6fc5682f" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="2c87cb1a-43db-46b4-8ef2-49889230b99a" type="field">
          <property name="name" value="monday"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="680113a3-57ab-40d7-9b45-7595168a7f4f" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="cf93296c-2949-49ce-86b9-e4ef6fc5682f" type="field">
          <property name="name" value="tuesday"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0a38b358-b15d-4cfe-a5a8-e45a7fc9b314" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="680113a3-57ab-40d7-9b45-7595168a7f4f" type="field">
          <property name="name" value="wednesday"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2c1cfdf5-4607-4be0-b01a-a5c967c308d4" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="0a38b358-b15d-4cfe-a5a8-e45a7fc9b314" type="field">
          <property name="name" value="thursday"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4e463394-fef9-4729-89a0-90e1f30ef896" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="2c1cfdf5-4607-4be0-b01a-a5c967c308d4" type="field">
          <property name="name" value="friday"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="8b287f29-b792-4be3-9467-91fbd7358627" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="4e463394-fef9-4729-89a0-90e1f30ef896" type="field">
          <property name="name" value="saturday"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0217e49b-d97c-4025-ac05-6b7599710da7" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="8b287f29-b792-4be3-9467-91fbd7358627" type="field">
          <property name="name" value="sunday"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="84472db6-e05f-402a-937d-83ab2768e0ff" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="0217e49b-d97c-4025-ac05-6b7599710da7" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="832e5a51-8c2d-420f-b4da-3ad3bd8a0e69" parent-uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" sibling-uuid="84472db6-e05f-402a-937d-83ab2768e0ff" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="2aeb2d89-0b28-401a-b9da-39e1c6911a5e" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="eb7e9a8b-b041-4634-b4d2-4fb5284024d0" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" sibling-uuid="2aeb2d89-0b28-401a-b9da-39e1c6911a5e" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="afbe1a9c-b441-4ae3-8cfd-333453b36da4" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" sibling-uuid="eb7e9a8b-b041-4634-b4d2-4fb5284024d0" type="field">
          <property name="name" value="registration_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="52f2b213-05a4-4cef-b0a7-9f587898f59f" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" sibling-uuid="afbe1a9c-b441-4ae3-8cfd-333453b36da4" type="field">
          <property name="name" value="location_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="00954e05-8843-44e8-9414-4896eb43ada1" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" sibling-uuid="52f2b213-05a4-4cef-b0a7-9f587898f59f" type="field">
          <property name="name" value="tutor_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a7fff893-f821-40e9-a904-664603a38e7f" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" sibling-uuid="00954e05-8843-44e8-9414-4896eb43ada1" type="field">
          <property name="name" value="schedule_dt"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a62bc0ed-9416-45bd-87b0-48e5d039278c" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" sibling-uuid="a7fff893-f821-40e9-a904-664603a38e7f" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0b0916b5-8e32-449c-af22-c1c7c7ff6cb3" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" sibling-uuid="a62bc0ed-9416-45bd-87b0-48e5d039278c" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="365aef2a-d72c-4c35-a3d0-7d2ab4fc4612" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="bdeab4fe-af41-493b-816d-f5c05f37682e" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="365aef2a-d72c-4c35-a3d0-7d2ab4fc4612" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ac0bad23-2a56-40c8-8272-aae7fca5be74" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="bdeab4fe-af41-493b-816d-f5c05f37682e" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0c2b83f9-6931-4ea8-8645-04fa7873e6a1" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="ac0bad23-2a56-40c8-8272-aae7fca5be74" type="field">
          <property name="name" value="start_year"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="2f7438a6-9ba1-4e21-baf9-4820b7cf9fdc" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="0c2b83f9-6931-4ea8-8645-04fa7873e6a1" type="field">
          <property name="default" value="1970-01-01"/>
          <property name="name" value="start_date"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="df9f4a68-eb40-4db2-8408-4e26c9d1db42" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="2f7438a6-9ba1-4e21-baf9-4820b7cf9fdc" type="field">
          <property name="default" value="1970-01-01"/>
          <property name="name" value="end_date"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="1e09827f-3f87-4a02-9c3c-ca61928a15b1" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="df9f4a68-eb40-4db2-8408-4e26c9d1db42" type="field">
          <property name="name" value="end_year"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="a3dc9e57-cdc7-4878-b240-8518fc47928f" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="1e09827f-3f87-4a02-9c3c-ca61928a15b1" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="685200d9-4a94-4798-a94f-9ca36d65f22a" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" sibling-uuid="a3dc9e57-cdc7-4878-b240-8518fc47928f" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="286196d9-d502-4b9c-9e6b-9ff2b1c6734a" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a0ea1f1c-c708-4f7f-93a5-b754ccd70361" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" sibling-uuid="286196d9-d502-4b9c-9e6b-9ff2b1c6734a" type="field">
          <property name="default" value="0"/>
          <property name="name" value="student_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="88200540-74b5-44df-8ccf-50f7174b7473" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" sibling-uuid="a0ea1f1c-c708-4f7f-93a5-b754ccd70361" type="field">
          <property name="default" value="0"/>
          <property name="name" value="studentgroup_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6d1eba1c-6a29-43e6-a639-095cee1a30e8" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" sibling-uuid="88200540-74b5-44df-8ccf-50f7174b7473" type="field">
          <property name="default" value="0"/>
          <property name="name" value="as_trial_student"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="542a7639-1c9c-423b-ba5b-b41ba2074694" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" sibling-uuid="6d1eba1c-6a29-43e6-a639-095cee1a30e8" type="field">
          <property name="default" value="2016-01-01"/>
          <property name="name" value="start_date"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="ab02a776-a05a-4385-943c-725ed9ed4d32" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" sibling-uuid="542a7639-1c9c-423b-ba5b-b41ba2074694" type="field">
          <property name="name" value="end_date"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="8cdc11ec-7031-46d0-b167-5b5787ec4e23" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" sibling-uuid="ab02a776-a05a-4385-943c-725ed9ed4d32" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="31288fa7-cd95-42ef-b1b0-359dcc5d7b5e" parent-uuid="72f556d7-609b-405b-88a4-8f6d4587b032" sibling-uuid="8cdc11ec-7031-46d0-b167-5b5787ec4e23" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="8defe66a-4f0f-4297-b0a2-fd1980ee74a1" parent-uuid="e2056103-3473-42f2-a865-4a63f378d655" type="field">
          <property name="name" value="student_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="*************-4f88-8e1d-c7b131b8844e" parent-uuid="e2056103-3473-42f2-a865-4a63f378d655" sibling-uuid="8defe66a-4f0f-4297-b0a2-fd1980ee74a1" type="field">
          <property name="name" value="studentlist_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="cca6af82-cf9e-4927-a63d-8b9b49acdf73" parent-uuid="e2056103-3473-42f2-a865-4a63f378d655" sibling-uuid="*************-4f88-8e1d-c7b131b8844e" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a345e116-b894-4e35-ac13-64847e45b102" parent-uuid="e2056103-3473-42f2-a865-4a63f378d655" sibling-uuid="cca6af82-cf9e-4927-a63d-8b9b49acdf73" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="d62a8bce-f4ce-442a-83b6-94c714adb86e" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="dd1a5992-b975-46a3-a411-01c51a4d18b7" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="d62a8bce-f4ce-442a-83b6-94c714adb86e" type="field">
          <property name="name" value="student_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="82e3ece1-072f-4ac9-9e12-b7efddc24d72" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="dd1a5992-b975-46a3-a411-01c51a4d18b7" type="field">
          <property name="enum-values" value="'telephone','email'"/>
          <property name="name" value="contacttype"/>
          <property name="required" value="true"/>
          <property name="size" value="9"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="1caa7430-3c12-43db-af0c-c08378c4a6f0" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="82e3ece1-072f-4ac9-9e12-b7efddc24d72" type="field">
          <property name="name" value="label"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b52c2a4e-84a7-496d-820c-0e6f06d08c42" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="1caa7430-3c12-43db-af0c-c08378c4a6f0" type="field">
          <property name="name" value="value"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="89bc8b04-70d0-46c1-a5fc-00f9c82234f3" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="b52c2a4e-84a7-496d-820c-0e6f06d08c42" type="field">
          <property name="name" value="apply_for_planning"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="154c26d4-028d-47ce-8434-53d806803d00" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="89bc8b04-70d0-46c1-a5fc-00f9c82234f3" type="field">
          <property name="name" value="apply_for_finance"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="810b405c-4e7a-4bb8-8bae-08559234b14d" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="154c26d4-028d-47ce-8434-53d806803d00" type="field">
          <property name="name" value="apply_for_promotions"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="a5710f43-f4aa-4a2a-b736-129cd280877f" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="810b405c-4e7a-4bb8-8bae-08559234b14d" type="field">
          <property name="name" value="use_salutation"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="3dad6b74-98a9-4a0d-b361-5504594de16c" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="a5710f43-f4aa-4a2a-b736-129cd280877f" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="97cbc9df-3d23-480d-9083-3bda3ce0c79b" parent-uuid="709b0eec-1818-4251-856b-44fd280b3812" sibling-uuid="3dad6b74-98a9-4a0d-b361-5504594de16c" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="7161224c-47d5-4aa3-b85e-67e099f71f86" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6851d581-1bac-45bd-a9d9-b2296d9d8943" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" sibling-uuid="7161224c-47d5-4aa3-b85e-67e099f71f86" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c679bc49-d76a-4302-9d38-b4a7ff133a6f" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" sibling-uuid="6851d581-1bac-45bd-a9d9-b2296d9d8943" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fd548684-668d-4c60-b16d-2d6d9f8f1d5b" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" sibling-uuid="c679bc49-d76a-4302-9d38-b4a7ff133a6f" type="field">
          <property name="name" value="hexcolor"/>
          <property name="required" value="true"/>
          <property name="size" value="6"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="32abdb69-4c45-4f41-96f8-7e947e1fe95e" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" sibling-uuid="fd548684-668d-4c60-b16d-2d6d9f8f1d5b" type="field">
          <property name="name" value="remarks"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="8081c521-d4c9-45a7-8885-dbad51f5368e" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" sibling-uuid="32abdb69-4c45-4f41-96f8-7e947e1fe95e" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="dfb1964c-1868-44ea-9562-0daca79ae6f8" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" sibling-uuid="8081c521-d4c9-45a7-8885-dbad51f5368e" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="113edf63-d0c2-45f2-92ec-bb20794cb5b1" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f6c49026-a67e-4f22-af7c-e329ecd12571" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="113edf63-d0c2-45f2-92ec-bb20794cb5b1" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="60dd1189-be6c-4199-9a75-3a89e6fe23a1" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="f6c49026-a67e-4f22-af7c-e329ecd12571" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="dbc0e15f-3c9a-493a-9bbb-f37c80da60ed" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="60dd1189-be6c-4199-9a75-3a89e6fe23a1" type="field">
          <property name="name" value="firstname"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b9b3836f-ef54-4969-af1b-4084609d209c" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="dbc0e15f-3c9a-493a-9bbb-f37c80da60ed" type="field">
          <property name="name" value="preposition"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6f6411f2-1244-4923-a3f2-277998cfe93b" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="b9b3836f-ef54-4969-af1b-4084609d209c" type="field">
          <property name="name" value="lastname"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="338b3b40-f965-45cc-b802-5cf6ce2c357c" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="6f6411f2-1244-4923-a3f2-277998cfe93b" type="field">
          <property name="name" value="address"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="********-fcd9-4dba-8ca3-588ac392f5ee" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="338b3b40-f965-45cc-b802-5cf6ce2c357c" type="field">
          <property name="name" value="zipcode"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="aaae03e7-b023-4b59-8c6f-b223b3cf98ea" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="********-fcd9-4dba-8ca3-588ac392f5ee" type="field">
          <property name="name" value="city"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ee6a25e5-f05e-44a8-ae65-6576bf78e282" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="aaae03e7-b023-4b59-8c6f-b223b3cf98ea" type="field">
          <property name="name" value="date_of_birth"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="ee300ab5-cd2e-4023-a278-fad45763cf73" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="ee6a25e5-f05e-44a8-ae65-6576bf78e282" type="field">
          <property name="name" value="permission_auto_banktransfer"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f64d1890-db96-48ea-8ae2-1732f20f70b5" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="ee300ab5-cd2e-4023-a278-fad45763cf73" type="field">
          <property name="name" value="bankaccount_name"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fd168a2d-a20f-4650-bf11-c40a4707e6c6" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="f64d1890-db96-48ea-8ae2-1732f20f70b5" type="field">
          <property name="name" value="bankaccount_number"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0795fb82-5952-4f38-9455-d8997f62cc02" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="fd168a2d-a20f-4650-bf11-c40a4707e6c6" type="field">
          <property name="name" value="mandate_number"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e0cc9641-6aa0-4a14-ac9b-5902bdeb5b37" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="0795fb82-5952-4f38-9455-d8997f62cc02" type="field">
          <property name="name" value="remarks"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="a4b5639f-05e9-4ccf-ad05-88b223504622" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="e0cc9641-6aa0-4a14-ac9b-5902bdeb5b37" type="field">
          <property name="name" value="status"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ebf15672-1b48-4b66-8e78-3bd97f67c82e" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="a4b5639f-05e9-4ccf-ad05-88b223504622" type="field">
          <property name="name" value="accesstoken"/>
          <property name="size" value="32"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6ab47dd4-bb42-436a-825a-e65a41e5277f" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="ebf15672-1b48-4b66-8e78-3bd97f67c82e" type="field">
          <property name="name" value="apipin"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="dd5f3d3a-cf8d-4f49-9828-db378718a8cc" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="6ab47dd4-bb42-436a-825a-e65a41e5277f" type="field">
          <property name="name" value="has_access"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="d509d106-f2ee-4f37-85de-1483447a9370" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="dd5f3d3a-cf8d-4f49-9828-db378718a8cc" type="field">
          <property name="default" value="0"/>
          <property name="name" value="agreeSocialShare"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="3c7a9dab-3699-4f69-ae5f-577149ee18ee" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="d509d106-f2ee-4f37-85de-1483447a9370" type="field">
          <property name="default" value="2"/>
          <property name="name" value="min_participants"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e80596c8-064e-4c44-b3e4-e7c7ab84cf12" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="3c7a9dab-3699-4f69-ae5f-577149ee18ee" type="field">
          <property name="default" value="99"/>
          <property name="name" value="max_participants"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="8531861b-a2a5-47f7-aa5e-0bff98a180b3" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="e80596c8-064e-4c44-b3e4-e7c7ab84cf12" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0fbb9032-3058-42f8-9131-799555193dc3" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" sibling-uuid="8531861b-a2a5-47f7-aa5e-0bff98a180b3" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="b2a56956-0c9b-46bd-b816-ee296b5b4ad2" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="3cd43c90-7c8f-4159-9d57-05c9629a8d9d" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="b2a56956-0c9b-46bd-b816-ee296b5b4ad2" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5520291a-9ac4-49fe-8cb5-fc95228f13a7" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="3cd43c90-7c8f-4159-9d57-05c9629a8d9d" type="field">
          <property name="name" value="tasktype_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="b8f97249-bdb0-40c8-8b74-a81576967d87" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="5520291a-9ac4-49fe-8cb5-fc95228f13a7" type="field">
          <property name="name" value="student_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="bd87ab92-ccbe-44f6-97a9-24c4d402b933" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="b8f97249-bdb0-40c8-8b74-a81576967d87" type="field">
          <property name="name" value="course_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f3e6b3f0-d827-4cbf-b9f4-d0793a2f1c6a" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="bd87ab92-ccbe-44f6-97a9-24c4d402b933" type="field">
          <property name="name" value="tutor_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="18f6da4f-ee17-4056-8de7-42684d76e7f6" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="f3e6b3f0-d827-4cbf-b9f4-d0793a2f1c6a" type="field">
          <property name="name" value="registration_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="81782c87-2c5b-4f21-943a-3eda48d66dd2" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="18f6da4f-ee17-4056-8de7-42684d76e7f6" type="field">
          <property name="name" value="event_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="83428949-47b0-48fa-8c3f-8b9502e77549" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="81782c87-2c5b-4f21-943a-3eda48d66dd2" type="field">
          <property name="name" value="assigned_user_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6550080f-85e7-4917-b3db-8e844a691638" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="83428949-47b0-48fa-8c3f-8b9502e77549" type="field">
          <property name="name" value="date_opened"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="ff0cafe4-f12c-46be-b52f-d91a006564d5" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="6550080f-85e7-4917-b3db-8e844a691638" type="field">
          <property name="name" value="date_due"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="5681101c-a184-44c3-9310-3203e01cc0c2" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="ff0cafe4-f12c-46be-b52f-d91a006564d5" type="field">
          <property name="name" value="date_closed"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="cf4f62a8-1543-46fd-8464-292de387d4a3" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="5681101c-a184-44c3-9310-3203e01cc0c2" type="field">
          <property name="name" value="remarks"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="d8e98af2-7fbd-4048-adc3-d3dedd2cc80a" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="cf4f62a8-1543-46fd-8464-292de387d4a3" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="1cb642a9-6fec-4187-8266-f06a61030f22" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" sibling-uuid="d8e98af2-7fbd-4048-adc3-d3dedd2cc80a" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="b22d604a-312c-4255-879d-1a319b7611f2" parent-uuid="f69b0190-690b-46ad-9697-cf47b35a3bdb" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a90195e7-2906-46fc-8641-e19769fabf36" parent-uuid="f69b0190-690b-46ad-9697-cf47b35a3bdb" sibling-uuid="b22d604a-312c-4255-879d-1a319b7611f2" type="field">
          <property name="name" value="description"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0fee319d-394a-4e7c-9bb0-5f90ec953c4f" parent-uuid="f69b0190-690b-46ad-9697-cf47b35a3bdb" sibling-uuid="a90195e7-2906-46fc-8641-e19769fabf36" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="2e8c1798-f985-4696-8694-c641d6279f79" parent-uuid="f69b0190-690b-46ad-9697-cf47b35a3bdb" sibling-uuid="0fee319d-394a-4e7c-9bb0-5f90ec953c4f" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="f3c20d50-867b-4582-b7c3-95476b8cffa7" parent-uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c2c26d84-e4a8-4f7e-9fe5-63d861e716e6" parent-uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" sibling-uuid="f3c20d50-867b-4582-b7c3-95476b8cffa7" type="field">
          <property name="name" value="schoolyear_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c2a0bbcb-fb5a-48e3-927d-3e5310caee81" parent-uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" sibling-uuid="c2c26d84-e4a8-4f7e-9fe5-63d861e716e6" type="field">
          <property name="name" value="course_student_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5b95c570-20d9-4f05-b322-c9abfc8caee4" parent-uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" sibling-uuid="c2a0bbcb-fb5a-48e3-927d-3e5310caee81" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="5a05ddcf-191a-41c5-8ab4-0b27f2e01631" parent-uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" sibling-uuid="5b95c570-20d9-4f05-b322-c9abfc8caee4" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="fd1f21d5-8f6d-4333-ad3d-f70ea35c5e34" parent-uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" sibling-uuid="5a05ddcf-191a-41c5-8ab4-0b27f2e01631" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="410686c2-fbc5-486c-acf0-3e8662666647" parent-uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a6bbe5d7-de4d-4820-983b-e6e25f61f919" parent-uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" sibling-uuid="410686c2-fbc5-486c-acf0-3e8662666647" type="field">
          <property name="name" value="trialcourse_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="e8af8c95-9d02-4de2-baa1-7f1ab7abcce0" parent-uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" sibling-uuid="a6bbe5d7-de4d-4820-983b-e6e25f61f919" type="field">
          <property name="name" value="course_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2b44a90d-405d-420f-883f-8710fda69913" parent-uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" sibling-uuid="e8af8c95-9d02-4de2-baa1-7f1ab7abcce0" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="d2bc8271-a831-4af3-9a88-305bf98206fa" parent-uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" sibling-uuid="2b44a90d-405d-420f-883f-8710fda69913" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="bd735aa4-060b-4d14-8cdb-8db5c49b67a7" parent-uuid="68a489f5-2ff4-4359-9710-085720368ca6" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="054d0b46-db73-4f1e-948e-2c05384e6976" parent-uuid="68a489f5-2ff4-4359-9710-085720368ca6" sibling-uuid="bd735aa4-060b-4d14-8cdb-8db5c49b67a7" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a53e9a18-744a-4f20-a93c-bfff4a8aa144" parent-uuid="68a489f5-2ff4-4359-9710-085720368ca6" sibling-uuid="054d0b46-db73-4f1e-948e-2c05384e6976" type="field">
          <property name="name" value="description"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4e212625-48fa-4c16-810b-b2d41c38bdfb" parent-uuid="68a489f5-2ff4-4359-9710-085720368ca6" sibling-uuid="a53e9a18-744a-4f20-a93c-bfff4a8aa144" type="field">
          <property name="default" value="1"/>
          <property name="name" value="needs_admin_action"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="95be7402-bfa2-474d-b781-972a76ca3805" parent-uuid="68a489f5-2ff4-4359-9710-085720368ca6" sibling-uuid="4e212625-48fa-4c16-810b-b2d41c38bdfb" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="d77f6a3f-ee3c-4f1e-bf9c-d60c4378683d" parent-uuid="68a489f5-2ff4-4359-9710-085720368ca6" sibling-uuid="95be7402-bfa2-474d-b781-972a76ca3805" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="9420aa9c-fbbd-43b3-b827-eb4ec56bb7d5" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="675cbd4d-a761-4539-81cf-3f1df8c2d65a" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="9420aa9c-fbbd-43b3-b827-eb4ec56bb7d5" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="7ab5d2a9-d344-466b-a4f4-7ec98f64e29a" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="675cbd4d-a761-4539-81cf-3f1df8c2d65a" type="field">
          <property name="default" value="1"/>
          <property name="name" value="trialrequeststatus_id"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="72d00514-7f09-48ca-b213-719fd7d6cb14" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="7ab5d2a9-d344-466b-a4f4-7ec98f64e29a" type="field">
          <property name="name" value="firstname"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b1fec482-3995-4c01-bab6-b79515257cc0" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="72d00514-7f09-48ca-b213-719fd7d6cb14" type="field">
          <property name="name" value="preposition"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="90e3fceb-58e2-4ea5-b6f5-df8837a8ff41" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="b1fec482-3995-4c01-bab6-b79515257cc0" type="field">
          <property name="name" value="lastname"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ea6f5555-4a92-4cc0-9551-04e352e970c5" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="90e3fceb-58e2-4ea5-b6f5-df8837a8ff41" type="field">
          <property name="name" value="date_of_birth"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="2ca59906-c8c5-4858-818e-40269693922d" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="ea6f5555-4a92-4cc0-9551-04e352e970c5" type="field">
          <property name="name" value="telephone"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="38865f87-13c3-4885-b2cf-94e4af177296" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="2ca59906-c8c5-4858-818e-40269693922d" type="field">
          <property name="name" value="email"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="94f0f7b6-be22-45f1-9b43-7b251a287cf0" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="38865f87-13c3-4885-b2cf-94e4af177296" type="field">
          <property name="name" value="course_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="1cbdce48-24c7-4387-b049-f7f7e56c944b" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="94f0f7b6-be22-45f1-9b43-7b251a287cf0" type="field">
          <property name="name" value="requested_startdate"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f0b976be-c0c1-42d0-bd1f-b25af1483d9a" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="1cbdce48-24c7-4387-b049-f7f7e56c944b" type="field">
          <property name="name" value="remarks"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="4fc534cb-3636-4f6d-be69-060469d62ef6" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="f0b976be-c0c1-42d0-bd1f-b25af1483d9a" type="field">
          <property name="name" value="generated_registration_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f03ad90e-7bba-4767-8c8a-98085822dc62" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="4fc534cb-3636-4f6d-be69-060469d62ef6" type="field">
          <property name="name" value="generated_student_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ac4f3c15-70ec-4906-a874-806246687fd1" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="f03ad90e-7bba-4767-8c8a-98085822dc62" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0d981367-91da-44c7-8642-438bdb3cd1bd" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" sibling-uuid="ac4f3c15-70ec-4906-a874-806246687fd1" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="2f26a517-9bb5-4f8c-9d98-022d95764af4" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="451ce281-1dee-4273-853a-20ae25e53346" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="2f26a517-9bb5-4f8c-9d98-022d95764af4" type="field">
          <property name="default" value="0"/>
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="97fbcba6-22c3-453a-954b-c32b4ad97f25" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="451ce281-1dee-4273-853a-20ae25e53346" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="918200b1-8e33-4327-a0a7-2e04ca7a1dfa" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="97fbcba6-22c3-453a-954b-c32b4ad97f25" type="field">
          <property name="name" value="avatar"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7fd19a18-73a0-424e-bd09-5dc4ce121412" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="918200b1-8e33-4327-a0a7-2e04ca7a1dfa" type="field">
          <property name="name" value="hexcolor"/>
          <property name="size" value="30"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="99c16f69-e128-4df0-819d-47df458c1900" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="7fd19a18-73a0-424e-bd09-5dc4ce121412" type="field">
          <property name="name" value="email"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c71554a2-4286-472c-a1a4-80cb3789507d" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="99c16f69-e128-4df0-819d-47df458c1900" type="field">
          <property name="name" value="password"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6d7cd4d8-a84b-411c-8e9b-1951ca2598b7" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="c71554a2-4286-472c-a1a4-80cb3789507d" type="field">
          <property name="name" value="telephone"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="afa6893b-d5fa-4ac5-bb9f-8b4a22e7e377" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="6d7cd4d8-a84b-411c-8e9b-1951ca2598b7" type="field">
          <property name="name" value="telephone_extra"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="485e8ba9-41bb-45ab-861d-d8cf6c28da7a" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="afa6893b-d5fa-4ac5-bb9f-8b4a22e7e377" type="field">
          <property name="name" value="remember_token"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="65fefcef-a7e9-4941-8137-574b56d78e2a" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="485e8ba9-41bb-45ab-861d-d8cf6c28da7a" type="field">
          <property name="name" value="preferred_language"/>
          <property name="size" value="2"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="576716fd-6baf-4229-a456-9bc870e71dd6" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="65fefcef-a7e9-4941-8137-574b56d78e2a" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="78f1d441-9ca5-42b8-b423-51d71e86f328" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="576716fd-6baf-4229-a456-9bc870e71dd6" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="62af128d-e8b0-4ff2-be58-7a51b1088d8f" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" sibling-uuid="78f1d441-9ca5-42b8-b423-51d71e86f328" type="field">
          <property name="name" value="last_active_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e5f38fe4-747c-4eef-ab1b-863cf2a7e5fc" parent-uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" type="index">
          <property name="name" value="domain_coursegroup_domain_id_foreign"/>
        </element>
        <element action="add" uuid="bf7b0071-8932-49be-a77a-6b548126c95c" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" type="index">
          <property name="name" value="courses_variant_code_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="32c5c14a-94d2-4c51-9808-c77e5d6a6ee4" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" type="index">
          <property name="name" value="domain_course_domain_id_foreign"/>
        </element>
        <element action="add" uuid="f55b01b1-2add-4fc7-b169-6ea602eaae01" parent-uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" type="index">
          <property name="name" value="Unique variantcode"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="917e372e-2808-4fed-8b3e-e6ae9c8c60bf" parent-uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" type="index">
          <property name="name" value="date_exception_domain_domain_id_foreign"/>
        </element>
        <element action="add" uuid="3778d88e-8edb-4a5a-aba0-c42e1d4eb27c" parent-uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" type="index">
          <property name="name" value="domain_default_checklist_domain_id_foreign"/>
        </element>
        <element action="add" uuid="44407047-01b2-4e2f-8201-3a3fad9f31c1" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" type="index">
          <property name="name" value="domain_document_domain_id_foreign"/>
        </element>
        <element action="add" uuid="529c6595-9fb5-4c8d-9105-6ac7e51e0376" parent-uuid="7b400813-2907-42f8-83e7-05ea7763af1c" type="index">
          <property name="name" value="domain_emaillogentry_domain_id_foreign"/>
        </element>
        <element action="add" uuid="4fe9675a-11ae-4151-9289-f16933d55d43" parent-uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" type="index">
          <property name="name" value="jobs_queue_reserved_at_index"/>
        </element>
        <element action="add" uuid="01f6d54d-02ee-4813-a307-debb08d88074" parent-uuid="ef423929-3822-431c-a4bb-36e67d80a964" type="index">
          <property name="name" value="domain_location_domain_id_foreign"/>
        </element>
        <element action="add" uuid="1522e075-60bf-4374-ae87-9968bbb207de" parent-uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" type="index">
          <property name="name" value="domain_mailtemplate_domain_id_foreign"/>
        </element>
        <element action="add" uuid="cfd14618-8295-4219-b964-ff00b07d0f94" parent-uuid="34879c08-1079-4cee-b770-e42debb31c86" type="index">
          <property name="name" value="oauth_access_tokens_user_id_index"/>
        </element>
        <element action="add" uuid="1556615d-b8aa-4035-b682-e9327a88d7d2" parent-uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" type="index">
          <property name="name" value="oauth_auth_codes_user_id_index"/>
        </element>
        <element action="add" uuid="dd349e11-e52f-4852-b5b4-************" parent-uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" type="index">
          <property name="name" value="oauth_clients_user_id_index"/>
        </element>
        <element action="add" uuid="30e0ad70-839c-4c84-a960-2ad7447fa433" parent-uuid="e799a893-7c3e-4ae7-b214-946c90225fca" type="index">
          <property name="name" value="oauth_refresh_tokens_access_token_id_index"/>
        </element>
        <element action="add" uuid="2aaf6228-86cd-40df-9951-a3102bac5a19" parent-uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" type="index">
          <property name="name" value="password_resets_email_index"/>
        </element>
        <element action="add" uuid="b040c330-0ec8-494c-bb81-47143470ef13" parent-uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" type="index">
          <property name="name" value="password_resets_token_index"/>
        </element>
        <element action="add" uuid="e22ea9cb-892d-4937-b925-0a125b629b59" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" type="index">
          <property name="name" value="personal_access_tokens_token_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="13010421-06ae-4678-b9e8-9853a4d1bd0f" parent-uuid="ed9a1554-55ad-494e-a84a-2217e636774a" type="index">
          <property name="name" value="personal_access_tokens_tokenable_type_tokenable_id_index"/>
        </element>
        <element action="add" uuid="12197dea-96d4-42be-85bc-bb2ef8dccb40" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" type="index">
          <property name="name" value="pulse_aggregates_bucket_period_type_aggregate_key_hash_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="08247cda-18f5-4b94-bf17-29516d8b9ab1" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" type="index">
          <property name="name" value="pulse_aggregates_period_bucket_index"/>
        </element>
        <element action="add" uuid="e7cf5c93-0ff3-4c18-9a83-97e780f1e8b7" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" type="index">
          <property name="name" value="pulse_aggregates_period_type_aggregate_bucket_index"/>
        </element>
        <element action="add" uuid="cafc15d8-5598-47ed-8ad6-ae03d0450814" parent-uuid="5b414e55-7f53-424c-a425-79eb086472b8" type="index">
          <property name="name" value="pulse_aggregates_type_index"/>
        </element>
        <element action="add" uuid="31aace01-c046-4a1c-9fd0-9872ecbb259a" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" type="index">
          <property name="name" value="pulse_entries_key_hash_index"/>
        </element>
        <element action="add" uuid="3cc95419-a8cd-4b65-8d2e-0a5b0341764d" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" type="index">
          <property name="name" value="pulse_entries_timestamp_index"/>
        </element>
        <element action="add" uuid="e5284f32-9012-4b26-b5ef-a3d2595e4e60" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" type="index">
          <property name="name" value="pulse_entries_timestamp_type_key_hash_value_index"/>
        </element>
        <element action="add" uuid="d7552e77-8a9f-44b6-938e-6a4c5bbf676e" parent-uuid="fafee967-9109-4d9c-861f-bdf269339540" type="index">
          <property name="name" value="pulse_entries_type_index"/>
        </element>
        <element action="add" uuid="e37e6296-576b-41de-a5e6-48698ea7584d" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" type="index">
          <property name="name" value="pulse_values_timestamp_index"/>
        </element>
        <element action="add" uuid="04dc175b-5290-44de-9e66-bf28057b4392" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" type="index">
          <property name="name" value="pulse_values_type_index"/>
        </element>
        <element action="add" uuid="11960663-8695-4904-98b1-a634169a7dd2" parent-uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" type="index">
          <property name="name" value="pulse_values_type_key_hash_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="b8eae8dd-b516-4b68-bb5a-abd1525309e6" parent-uuid="253849ed-5411-4cec-bd9d-0053c321f423" type="index">
          <property name="name" value="domain_recurrenceoption_domain_id_foreign"/>
        </element>
        <element action="add" uuid="06d3ce4e-fd0a-4368-8c25-3527019d415b" parent-uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" type="index">
          <property name="name" value="scheduleproposals_registration_id_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="230f471d-e76e-4c6d-b51d-d4c81573c274" parent-uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" type="index">
          <property name="name" value="domain_schoolyear_domain_id_foreign"/>
        </element>
        <element action="add" uuid="5e3bbd99-86bd-444e-a4c5-7b3fbcb13859" parent-uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" type="index">
          <property name="name" value="domain_studentlist_domain_id_foreign"/>
        </element>
        <element action="add" uuid="6ec7281f-d131-4ebd-9685-97ee7e51432f" parent-uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" type="index">
          <property name="name" value="domain_student_domain_id_foreign"/>
        </element>
        <element action="add" uuid="bd79395f-b3f7-4172-a7be-0998b978277d" parent-uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" type="index">
          <property name="name" value="domain_task_domain_id_foreign"/>
        </element>
        <element action="add" uuid="e66b67dc-cef5-481d-9e8c-d26c375c62c7" parent-uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" type="index">
          <property name="name" value="domain_trialstudent_domain_id_foreign"/>
        </element>
        <element action="add" uuid="7dd9b4ee-94fc-4d96-863a-9eb9ae1a0c71" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" type="index">
          <property name="name" value="domain_user_domain_id_foreign"/>
        </element>
        <element action="add" uuid="9b4ed301-d149-42f5-bac7-f69ed436df2b" parent-uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" type="index">
          <property name="name" value="users_email_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="0077a265-d0c6-4403-be90-da17f33a8add" parent-uuid="e5f38fe4-747c-4eef-ab1b-863cf2a7e5fc" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="0c651856-5f33-40d7-845f-f54ec6c91a6a" parent-uuid="bf7b0071-8932-49be-a77a-6b548126c95c" type="index-field">
          <property name="name" value="variant_code"/>
        </element>
        <element action="add" uuid="a4b57c4e-a7b3-467d-adef-01d752226b72" parent-uuid="32c5c14a-94d2-4c51-9808-c77e5d6a6ee4" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="0ff8d544-eda4-4240-beae-98ec57d5ea5b" parent-uuid="f55b01b1-2add-4fc7-b169-6ea602eaae01" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="a48684f2-f827-4fb6-bcb5-76dd28ec5293" parent-uuid="f55b01b1-2add-4fc7-b169-6ea602eaae01" type="index-field">
          <property name="name" value="variant_code"/>
        </element>
        <element action="add" uuid="d9ccd6fe-28fe-466c-a071-5ea331cec466" parent-uuid="917e372e-2808-4fed-8b3e-e6ae9c8c60bf" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="23801e5a-82b0-45ab-b1ab-3058d3e09dcf" parent-uuid="3778d88e-8edb-4a5a-aba0-c42e1d4eb27c" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="8065b284-11d6-4371-b8e8-a1bb5cc6a9df" parent-uuid="44407047-01b2-4e2f-8201-3a3fad9f31c1" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="e08afd3d-d93d-4196-af5e-5ad79334a2d4" parent-uuid="529c6595-9fb5-4c8d-9105-6ac7e51e0376" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="b85aa21e-e981-4086-a2cb-baa80435458e" parent-uuid="4fe9675a-11ae-4151-9289-f16933d55d43" type="index-field">
          <property name="name" value="queue"/>
        </element>
        <element action="add" uuid="337ad00c-d9ed-4e4b-ad1d-ba9757d8587b" parent-uuid="4fe9675a-11ae-4151-9289-f16933d55d43" type="index-field">
          <property name="name" value="reserved_at"/>
        </element>
        <element action="add" uuid="edafa91f-89e7-49d3-8710-53a6a6390852" parent-uuid="01f6d54d-02ee-4813-a307-debb08d88074" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="3b8f58de-84c2-415d-ba8d-cb04fbdb69a2" parent-uuid="1522e075-60bf-4374-ae87-9968bbb207de" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="2bd4f22a-eee0-483d-9363-951d172694fb" parent-uuid="cfd14618-8295-4219-b964-ff00b07d0f94" type="index-field">
          <property name="name" value="user_id"/>
        </element>
        <element action="add" uuid="95f5493c-d7ce-421f-9a36-f57bb723134b" parent-uuid="1556615d-b8aa-4035-b682-e9327a88d7d2" type="index-field">
          <property name="name" value="user_id"/>
        </element>
        <element action="add" uuid="75986d36-96c2-48c3-9a0b-17b2b7a9ac2f" parent-uuid="dd349e11-e52f-4852-b5b4-************" type="index-field">
          <property name="name" value="user_id"/>
        </element>
        <element action="add" uuid="f48f6a5f-a3a4-4086-befb-964bf568a1d4" parent-uuid="30e0ad70-839c-4c84-a960-2ad7447fa433" type="index-field">
          <property name="name" value="access_token_id"/>
        </element>
        <element action="add" uuid="fb1a10fb-ab3d-44e8-bf28-4f083ff1d080" parent-uuid="2aaf6228-86cd-40df-9951-a3102bac5a19" type="index-field">
          <property name="name" value="email"/>
        </element>
        <element action="add" uuid="7c7ee4a2-ed67-43c8-ae4f-980d1775c1a2" parent-uuid="b040c330-0ec8-494c-bb81-47143470ef13" type="index-field">
          <property name="name" value="token"/>
        </element>
        <element action="add" uuid="986c841c-8431-4da0-8cb8-b8e9907d21e4" parent-uuid="e22ea9cb-892d-4937-b925-0a125b629b59" type="index-field">
          <property name="name" value="token"/>
        </element>
        <element action="add" uuid="41d8c114-01f2-4760-966e-cb0fc9d22b2f" parent-uuid="13010421-06ae-4678-b9e8-9853a4d1bd0f" type="index-field">
          <property name="name" value="tokenable_type"/>
        </element>
        <element action="add" uuid="aab69aa2-5b6a-4a34-951d-5cefcdf8b43d" parent-uuid="13010421-06ae-4678-b9e8-9853a4d1bd0f" type="index-field">
          <property name="name" value="tokenable_id"/>
        </element>
        <element action="add" uuid="ddd5f005-1537-4821-8138-0366f192ff54" parent-uuid="12197dea-96d4-42be-85bc-bb2ef8dccb40" type="index-field">
          <property name="name" value="bucket"/>
        </element>
        <element action="add" uuid="ce7c756c-dbae-4e74-a426-8f06642b4f85" parent-uuid="12197dea-96d4-42be-85bc-bb2ef8dccb40" type="index-field">
          <property name="name" value="period"/>
        </element>
        <element action="add" uuid="f0cd2481-2a0a-4edf-ac05-************" parent-uuid="12197dea-96d4-42be-85bc-bb2ef8dccb40" type="index-field">
          <property name="name" value="type"/>
        </element>
        <element action="add" uuid="83268f9e-b835-4b36-bd00-13062cd89ec3" parent-uuid="12197dea-96d4-42be-85bc-bb2ef8dccb40" type="index-field">
          <property name="name" value="key_hash"/>
        </element>
        <element action="add" uuid="339a6486-7486-432d-a773-3fb41f47ce8a" parent-uuid="12197dea-96d4-42be-85bc-bb2ef8dccb40" type="index-field">
          <property name="name" value="aggregate"/>
        </element>
        <element action="add" uuid="e0eaf3d7-40d6-4b21-8d62-2678d5a981a1" parent-uuid="08247cda-18f5-4b94-bf17-29516d8b9ab1" type="index-field">
          <property name="name" value="bucket"/>
        </element>
        <element action="add" uuid="b1fdf7e9-47ff-469c-86fd-ead43731aebc" parent-uuid="08247cda-18f5-4b94-bf17-29516d8b9ab1" type="index-field">
          <property name="name" value="period"/>
        </element>
        <element action="add" uuid="7429a2bd-c283-4ef6-a865-1e0e6106984b" parent-uuid="e7cf5c93-0ff3-4c18-9a83-97e780f1e8b7" type="index-field">
          <property name="name" value="bucket"/>
        </element>
        <element action="add" uuid="70baf195-3d68-4f87-a1e1-40454000ab46" parent-uuid="e7cf5c93-0ff3-4c18-9a83-97e780f1e8b7" type="index-field">
          <property name="name" value="period"/>
        </element>
        <element action="add" uuid="3640fa3e-497c-46b3-8827-9e163dd11f63" parent-uuid="e7cf5c93-0ff3-4c18-9a83-97e780f1e8b7" type="index-field">
          <property name="name" value="type"/>
        </element>
        <element action="add" uuid="9cbd73ef-1ccc-4b8c-af4b-77f9d60b0ecc" parent-uuid="e7cf5c93-0ff3-4c18-9a83-97e780f1e8b7" type="index-field">
          <property name="name" value="aggregate"/>
        </element>
        <element action="add" uuid="3312a77e-c1c1-41ab-88fe-30aa0147423b" parent-uuid="cafc15d8-5598-47ed-8ad6-ae03d0450814" type="index-field">
          <property name="name" value="type"/>
        </element>
        <element action="add" uuid="1708e985-67a8-44f1-8479-7f738dff1f1e" parent-uuid="31aace01-c046-4a1c-9fd0-9872ecbb259a" type="index-field">
          <property name="name" value="key_hash"/>
        </element>
        <element action="add" uuid="3d2b2894-f7b9-4549-a2da-075543da8988" parent-uuid="3cc95419-a8cd-4b65-8d2e-0a5b0341764d" type="index-field">
          <property name="name" value="timestamp"/>
        </element>
        <element action="add" uuid="da399954-a0ec-462e-b39b-d32f21ce5ccb" parent-uuid="e5284f32-9012-4b26-b5ef-a3d2595e4e60" type="index-field">
          <property name="name" value="timestamp"/>
        </element>
        <element action="add" uuid="02c8493c-fa1b-4165-93d4-6531cdfc2e86" parent-uuid="e5284f32-9012-4b26-b5ef-a3d2595e4e60" type="index-field">
          <property name="name" value="type"/>
        </element>
        <element action="add" uuid="a6bc94f2-0fe3-4c24-b24c-803da72a38fa" parent-uuid="e5284f32-9012-4b26-b5ef-a3d2595e4e60" type="index-field">
          <property name="name" value="key_hash"/>
        </element>
        <element action="add" uuid="51b3aa19-492d-49f2-a885-75c3a19d2365" parent-uuid="e5284f32-9012-4b26-b5ef-a3d2595e4e60" type="index-field">
          <property name="name" value="value"/>
        </element>
        <element action="add" uuid="0f8435e1-2459-4ab6-8948-3cd977109399" parent-uuid="d7552e77-8a9f-44b6-938e-6a4c5bbf676e" type="index-field">
          <property name="name" value="type"/>
        </element>
        <element action="add" uuid="72b71e2b-61fa-4f61-b714-32ce1e7849e3" parent-uuid="e37e6296-576b-41de-a5e6-48698ea7584d" type="index-field">
          <property name="name" value="timestamp"/>
        </element>
        <element action="add" uuid="56084b25-9c34-4309-9505-c72eedf98097" parent-uuid="04dc175b-5290-44de-9e66-bf28057b4392" type="index-field">
          <property name="name" value="type"/>
        </element>
        <element action="add" uuid="9bbe3ed2-5d67-4d5d-bf24-aca86b7fdcb2" parent-uuid="11960663-8695-4904-98b1-a634169a7dd2" type="index-field">
          <property name="name" value="type"/>
        </element>
        <element action="add" uuid="a57fa35e-f93d-4d25-ace8-f2fe352e771b" parent-uuid="11960663-8695-4904-98b1-a634169a7dd2" type="index-field">
          <property name="name" value="key_hash"/>
        </element>
        <element action="add" uuid="e477c8e7-7a8a-4ca4-b49a-e8c4a86b246d" parent-uuid="b8eae8dd-b516-4b68-bb5a-abd1525309e6" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="dcbae3cf-0b5f-43c8-b63e-7bcbe5708e43" parent-uuid="06d3ce4e-fd0a-4368-8c25-3527019d415b" type="index-field">
          <property name="name" value="registration_id"/>
        </element>
        <element action="add" uuid="ead0fd57-6c43-496a-842f-74ca9e7c0617" parent-uuid="230f471d-e76e-4c6d-b51d-d4c81573c274" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="0f4bbef0-2667-4d5a-b4be-4b6bc47335da" parent-uuid="5e3bbd99-86bd-444e-a4c5-7b3fbcb13859" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="a9e2b358-011d-40c8-b666-32d603b81a6f" parent-uuid="6ec7281f-d131-4ebd-9685-97ee7e51432f" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="544e5c30-f403-4333-b2f8-9e67bcacaf45" parent-uuid="bd79395f-b3f7-4172-a7be-0998b978277d" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="340cca15-ec34-48f5-a31a-88d73bf553da" parent-uuid="e66b67dc-cef5-481d-9e8c-d26c375c62c7" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="3e7a1fc3-5b42-4170-9714-5272e593a4c6" parent-uuid="7dd9b4ee-94fc-4d96-863a-9eb9ae1a0c71" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="92cdb21d-7ff9-41f1-965b-3222b0d77b34" parent-uuid="9b4ed301-d149-42f5-bac7-f69ed436df2b" type="index-field">
          <property name="name" value="email"/>
        </element>
        <element action="add" uuid="bdb2a6f9-7697-45ba-9382-83ae67fc4c4a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Alert"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="CASCADE"/>
          <property name="owner-alias" value="alerts"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="e88a69d6-065f-43bc-9a63-8c3c4fffa21c" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Attendancenote"/>
          <property name="inverse-alias" value="attendanceoptions"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="attendancenotes"/>
          <property name="to" value="\App\Models\Attendanceoption"/>
        </element>
        <element action="add" uuid="b12184b0-3fa4-41af-b99a-cb6686e346be" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Attendancenote"/>
          <property name="inverse-alias" value="events"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="attendancenotes"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Event"/>
        </element>
        <element action="add" uuid="5025ad30-5fd2-454e-9e6d-c61455d03601" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Attendancenote"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="attendancenotes"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="1d2f74fb-4152-4ae6-8d51-8585820bb9fc" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Attendanceoption"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="attendanceoptions"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="9c5f96bf-16a7-4df3-a146-903c2c81ec1c" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Availability"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="availabilities"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="8aa25083-889c-44ee-818c-bb7037f660b4" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Checklist"/>
          <property name="inverse-alias" value="courseStudent"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="checklists"/>
          <property name="to" value="\App\Models\CourseStudent"/>
        </element>
        <element action="add" uuid="b75fee78-71b7-4ff1-97c9-732ca1caa938" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\CourseStudent"/>
          <property name="inverse-alias" value="courses"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="courseStudents"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="59bc29b0-b5c3-4fb1-9e74-030b4806a69b" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\CourseStudent"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="courseStudents"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="e80160e7-ad70-4b80-9fa1-0ee076ed378a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\CoursegroupTutor"/>
          <property name="inverse-alias" value="coursegroups"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="coursegroupTutors"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Coursegroup"/>
        </element>
        <element action="add" uuid="1cca546b-189b-4c68-af0b-0939ee635e6f" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\CoursegroupTutor"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="coursegroupTutors"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="f1d4fd54-d460-48b6-8990-3024207e3663" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Coursegroup"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="coursegroups"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="fd72c4e9-ff5f-45e3-9fea-2b12877a0326" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Course"/>
          <property name="inverse-alias" value="coursegroups"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="courses"/>
          <property name="to" value="\App\Models\Coursegroup"/>
        </element>
        <element action="add" uuid="650a406d-9890-4ca6-9bc0-0adc03fbe746" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Course"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="courses"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="2dc9846a-15c1-46ad-a896-7e4d8bad600d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Course"/>
          <property name="inverse-alias" value="recurrenceoptions"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="courses"/>
          <property name="to" value="\App\Models\Recurrenceoption"/>
        </element>
        <element action="add" uuid="d7a47c8e-7067-4fb5-9eac-d5380f4977b7" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Coursevariance"/>
          <property name="inverse-alias" value="courses"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="coursevariances"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="690a229d-955e-4c3e-8e0a-9c489ede5366" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Coursevariance"/>
          <property name="inverse-alias" value="recurrenceoptions"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="coursevariances"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Recurrenceoption"/>
        </element>
        <element action="add" uuid="9f54fb6d-abfd-43a0-a513-ea5459b6311a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\DateException"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="dateExceptions"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="9cde0869-dbeb-408e-926d-c9c2e976aa0e" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\DateException"/>
          <property name="inverse-alias" value="locations"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="dateExceptions"/>
          <property name="to" value="\App\Models\Location"/>
        </element>
        <element action="add" uuid="83c5b7e3-**************-c768731e81c4" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\DateException"/>
          <property name="inverse-alias" value="schoolyears"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="dateExceptions"/>
          <property name="to" value="\App\Models\Schoolyear"/>
        </element>
        <element action="add" uuid="f1727a3a-9845-4fde-b400-f62f029741ad" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\DateexceptionTutor"/>
          <property name="inverse-alias" value="dateExceptions"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="false"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="dateexceptionTutors"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\DateException"/>
        </element>
        <element action="add" uuid="3731e6d8-80b7-4073-85ee-2e01a848ae98" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\DateexceptionTutor"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="false"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="dateexceptionTutors"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="a3d4a434-abe4-4264-bfc7-621c02e0c379" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\DefaultChecklist"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="defaultChecklists"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="ab290cce-b8d0-4eb6-b310-765bdb4ad543" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Document"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="documents"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="b4a07b82-dac7-4ca6-ad74-10e3520f2d00" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Document"/>
          <property name="inverse-alias" value="events"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="documents"/>
          <property name="to" value="\App\Models\Event"/>
        </element>
        <element action="add" uuid="203e75e0-d79f-4f66-8ccd-01e16652ac61" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Emaillogentry"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="emaillogentries"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="813ea696-1f40-47e8-9dc2-131d578944fe" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Event"/>
          <property name="inverse-alias" value="locations"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="events"/>
          <property name="to" value="\App\Models\Location"/>
        </element>
        <element action="add" uuid="11bca13d-9211-4098-ab82-31756edc13c7" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Event"/>
          <property name="inverse-alias" value="timetables"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="events"/>
          <property name="to" value="\App\Models\Timetable"/>
        </element>
        <element action="add" uuid="b2693963-f971-428a-92af-1e35240d339b" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Event"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="events"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="c13051a6-cc30-4f84-9826-a2bdd69a105d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Library"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraries"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="dd8d1d23-8267-44f0-b184-b64baaec8740" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Library"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraries"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="deb7a666-a98c-4fe7-86bd-bed6fec8e554" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryCourse"/>
          <property name="inverse-alias" value="courses"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryCourses"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="5eaac0de-fe0c-4628-bed1-a39b7bab1daf" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryCourse"/>
          <property name="inverse-alias" value="libraries"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryCourses"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Library"/>
        </element>
        <element action="add" uuid="8cda6768-4413-46a0-9068-9cf8da9d9270" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryCoursegroup"/>
          <property name="inverse-alias" value="coursegroups"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryCoursegroups"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Coursegroup"/>
        </element>
        <element action="add" uuid="99fec556-aeb7-4e8e-8044-7b4caefbf39d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryCoursegroup"/>
          <property name="inverse-alias" value="libraries"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryCoursegroups"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Library"/>
        </element>
        <element action="add" uuid="6d447a7e-ff56-4a41-97e4-e6c8a75b5ac4" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryDocument"/>
          <property name="inverse-alias" value="documents"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryDocuments"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Document"/>
        </element>
        <element action="add" uuid="f0ac4ad6-1989-4e6e-a700-ac1788868f83" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryDocument"/>
          <property name="inverse-alias" value="libraries"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryDocuments"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Library"/>
        </element>
        <element action="add" uuid="d377efb6-3c55-4402-bd41-eb1a53b4a97c" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryStudent"/>
          <property name="inverse-alias" value="libraries"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryStudents"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Library"/>
        </element>
        <element action="add" uuid="635a8982-006b-4df9-97f8-126d5d07305d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\LibraryStudent"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="libraryStudents"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="314bffe1-fbf6-40c0-9644-62c1d581f016" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Location"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="locations"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="f1340b43-1b0d-44bc-ad76-3537cfc9f409" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Logentry"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="logentries"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="6e25f026-53cc-4fc9-8adf-726cc1924026" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Loginsecurity"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="loginsecurities"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="6b488045-5239-4975-9fc0-7e2b121e998b" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Mailtemplate"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="mailtemplates"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="cb8e56ea-b5e6-4371-921b-f1a9c96bf1d9" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Message"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="messages"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="9d9b83fe-d7d6-4999-8ff4-688c4f0ec4a1" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\PlanningEntriesChange"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningEntriesChanges"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="ca43fd42-322b-46f4-8c9c-f536601d9e83" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\PlanningEntriesChange"/>
          <property name="inverse-alias" value="locations"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningEntriesChanges"/>
          <property name="to" value="\App\Models\Location"/>
        </element>
        <element action="add" uuid="ff09776d-a26f-4ee4-bff3-1183b537b1b5" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\PlanningEntriesChange"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningEntriesChanges"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="805ab5e4-964a-4081-ab94-d2959e0c0b15" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\PlanningEntriesChange"/>
          <property name="inverse-alias" value="planningentries"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningEntriesChanges"/>
          <property name="to" value="\App\Models\Planningentry"/>
        </element>
        <element action="add" uuid="4bede789-0ad8-472f-865f-a6cc56494218" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Planningentry"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningentries"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="b9c597e6-a803-4a3e-85c8-6da346e0c31a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Planningentry"/>
          <property name="inverse-alias" value="locations"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningentries"/>
          <property name="to" value="\App\Models\Location"/>
        </element>
        <element action="add" uuid="12050ccb-2af5-41ec-af76-ab793893484f" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Planningentry"/>
          <property name="inverse-alias" value="courseStudent"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningentries"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\CourseStudent"/>
        </element>
        <element action="add" uuid="af30b4b2-a85b-495c-b30e-772238391dde" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Planningentry"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="planningentries"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="f56ce73a-aaab-46c1-bbc3-63734aec26ee" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Priceoption"/>
          <property name="inverse-alias" value="courses"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="priceoptions"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="6024dcfd-f538-46b7-8059-0f62051ac637" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Recurrenceoption"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="recurrenceoptions"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="09209b58-f2ab-4dbc-af18-ffba95fa3c11" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\RoleUser"/>
          <property name="inverse-alias" value="roles"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="roleUsers"/>
          <property name="to" value="\App\Models\Role"/>
        </element>
        <element action="add" uuid="0c4df872-ce8d-451d-a75b-e4fca4cbe66d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\RoleUser"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="roleUsers"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="81334048-52d0-44c3-992a-a47f0f1c2741" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\SchedulePref"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="CASCADE"/>
          <property name="owner-alias" value="schedulePrefs"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="1086a0dc-2c3b-4431-9151-e02b634a1398" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Scheduleproposal"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="scheduleproposals"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="d7a46353-7aee-4e8f-b553-24f5ebaf3eb3" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Scheduleproposal"/>
          <property name="inverse-alias" value="locations"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="scheduleproposals"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Location"/>
        </element>
        <element action="add" uuid="07f092b7-95d0-43cb-bbcc-7ccb48f83ed8" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Scheduleproposal"/>
          <property name="inverse-alias" value="courseStudent"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="scheduleproposals"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\CourseStudent"/>
        </element>
        <element action="add" uuid="f6665d67-2798-454b-a32b-335b3615df02" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Scheduleproposal"/>
          <property name="inverse-alias" value="users"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="scheduleproposals"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="41bd98a3-ddcc-4ca6-9acb-c05abcc5c5c4" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Schoolyear"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="schoolyears"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="4f888a6c-0349-427f-bbc8-f01b95094805" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\StudentStudentgroup"/>
          <property name="inverse-alias" value="studentsViaStudentgroupId"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="studentStudentgroupViaStudentgroupId"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="3368badc-f7cb-48f0-be21-c3671b02eb91" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\StudentStudentgroup"/>
          <property name="inverse-alias" value="studentsViaStudentId"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="studentStudentgroupViaStudentId"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="9a7f4a49-de23-4b26-88a6-9e5d659290d2" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\StudentStudentlist"/>
          <property name="inverse-alias" value="studentlists"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="false"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="studentStudentlists"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Studentlist"/>
        </element>
        <element action="add" uuid="00d196dd-bb81-4b00-92e7-4444d68c0ea4" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\StudentStudentlist"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="false"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="studentStudentlists"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="bce5d578-2190-4d87-b8b2-586d794f8276" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Studentcontact"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="studentcontacts"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="fdda2275-54e9-4fb5-8464-9cf1b25f2da3" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Studentlist"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="studentlists"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="5263e8f0-4194-4ef6-88ac-ddb52061089d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Student"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="students"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="4b65b521-e809-44a2-8df2-3801fb791855" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="usersViaAssignedUserId"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasksViaAssignedUserId"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="22c2bcd1-4112-42e9-9649-f66fd4c1cbee" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="courses"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasks"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="e8b1d77f-613a-4170-b4e7-6ab5e59d2ec8" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasks"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="269e9223-3b7c-4c7e-a165-0324f30533a3" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="events"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasks"/>
          <property name="to" value="\App\Models\Event"/>
        </element>
        <element action="add" uuid="f7d2bc4c-e95e-4739-b28a-bedf7f52873c" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="courseStudent"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasks"/>
          <property name="to" value="\App\Models\CourseStudent"/>
        </element>
        <element action="add" uuid="e1c528fe-4cf3-4353-a93f-bf475fb5578e" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasks"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="456272d0-2430-4b0c-9ea7-0386bf04c937" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="tasktypes"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasks"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Tasktype"/>
        </element>
        <element action="add" uuid="bf8ffb2a-c900-43e1-8d57-dc8bce94959e" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Task"/>
          <property name="inverse-alias" value="usersViaTutorId"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="tasksViaTutorId"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="e215b9d2-c316-4e13-9d73-5667b7de1c5c" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Timetable"/>
          <property name="inverse-alias" value="courseStudent"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="timetables"/>
          <property name="to" value="\App\Models\CourseStudent"/>
        </element>
        <element action="add" uuid="a4263efa-b60d-4ba4-aa7b-c6f29800179d" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Timetable"/>
          <property name="inverse-alias" value="schoolyears"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="timetables"/>
          <property name="to" value="\App\Models\Schoolyear"/>
        </element>
        <element action="add" uuid="fff36ee4-4b8d-4890-8b80-b23326a4677f" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\TrialcourseCourse"/>
          <property name="inverse-alias" value="coursesViaCourseId"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialcourseCoursesViaCourseId"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="11cbcc23-5253-4b15-b3e6-e3ad22542445" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\TrialcourseCourse"/>
          <property name="inverse-alias" value="coursesViaTrialcourseId"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialcourseCoursesViaTrialcourseId"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="4a667653-9b4a-4cc1-9c49-bb8b2d17426a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Trialrequeststatus"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialrequeststatuses"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="1464b9e0-4852-4abc-8c0d-8548f8a010ef" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Trialstudent"/>
          <property name="inverse-alias" value="courses"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialstudents"/>
          <property name="to" value="\App\Models\Course"/>
        </element>
        <element action="add" uuid="630e42d9-eab3-42ea-8fb6-639af59ae2b7" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Trialstudent"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialstudents"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="3e8e337e-1dd3-4c1d-a885-56e69658e00a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Trialstudent"/>
          <property name="inverse-alias" value="courseStudent"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialstudents"/>
          <property name="to" value="\App\Models\CourseStudent"/>
        </element>
        <element action="add" uuid="7e899f8c-8143-46ed-b6a9-d9917e8e8b6c" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Trialstudent"/>
          <property name="inverse-alias" value="students"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialstudents"/>
          <property name="to" value="\App\Models\Student"/>
        </element>
        <element action="add" uuid="8b3de001-85f4-410a-ad59-8cdc750f6a2b" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\Trialstudent"/>
          <property name="inverse-alias" value="trialrequeststatuses"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="trialstudents"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Trialrequeststatus"/>
        </element>
        <element action="add" uuid="84256974-8cd9-4ac7-b144-451905cb6e66" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="association">
          <property name="from" value="\App\Models\User"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="NO ACTION"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="users"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="09061919-beb8-4a3d-91b6-2b8ee359e42b" parent-uuid="bdb2a6f9-7697-45ba-9382-83ae67fc4c4a" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="d5c99019-50a4-4701-8269-a69c41093359" parent-uuid="e88a69d6-065f-43bc-9a63-8c3c4fffa21c" type="association-field">
          <property name="from" value="attendanceoption_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="b82a0eb2-1364-4e8f-9996-0b13909f7ab7" parent-uuid="b12184b0-3fa4-41af-b99a-cb6686e346be" type="association-field">
          <property name="from" value="event_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="49133189-a842-4f66-ae33-30474a024b85" parent-uuid="5025ad30-5fd2-454e-9e6d-c61455d03601" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="333548f8-8527-4336-a233-3f56226b9528" parent-uuid="1d2f74fb-4152-4ae6-8d51-8585820bb9fc" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="0c949da5-875b-4d4b-b115-22282d1e545f" parent-uuid="9c5f96bf-16a7-4df3-a146-903c2c81ec1c" type="association-field">
          <property name="from" value="tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="b5f24000-6415-4da2-8f3f-fd135e06f3c9" parent-uuid="8aa25083-889c-44ee-818c-bb7037f660b4" type="association-field">
          <property name="from" value="registration_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="845a93b2-77a4-4598-9151-017cd50ac661" parent-uuid="b75fee78-71b7-4ff1-97c9-732ca1caa938" type="association-field">
          <property name="from" value="course_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="85af0068-4bb5-47fe-bd3b-10e06d4aa919" parent-uuid="59bc29b0-b5c3-4fb1-9e74-030b4806a69b" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="8b49d77c-**************-f56c62320874" parent-uuid="e80160e7-ad70-4b80-9fa1-0ee076ed378a" type="association-field">
          <property name="from" value="coursegroup_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="61d511bc-31fe-4e58-85d6-57b125d8a039" parent-uuid="1cca546b-189b-4c68-af0b-0939ee635e6f" type="association-field">
          <property name="from" value="tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="c8f2570f-221b-48c4-9f8d-710e38bd7cb2" parent-uuid="f1d4fd54-d460-48b6-8990-3024207e3663" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="98ccf6fc-238b-4c81-a171-262f6aac38da" parent-uuid="fd72c4e9-ff5f-45e3-9fea-2b12877a0326" type="association-field">
          <property name="from" value="coursegroup_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="f9488815-5607-4ac8-9bb6-3ee1535485fc" parent-uuid="650a406d-9890-4ca6-9bc0-0adc03fbe746" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="2f12ea00-fc73-46d0-a969-4f2e850f1558" parent-uuid="2dc9846a-15c1-46ad-a896-7e4d8bad600d" type="association-field">
          <property name="from" value="recurrenceoption_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="f49f4107-b0ea-4674-b603-829f83636c17" parent-uuid="d7a47c8e-7067-4fb5-9eac-d5380f4977b7" type="association-field">
          <property name="from" value="course_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="aa23da80-7d86-4571-938f-5c9059b9510e" parent-uuid="690a229d-955e-4c3e-8e0a-9c489ede5366" type="association-field">
          <property name="from" value="recurrenceoption_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="a616fd35-dbeb-4c0e-a19a-039c1dcdb983" parent-uuid="9f54fb6d-abfd-43a0-a513-ea5459b6311a" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="51517d07-c476-420d-8d69-15634ecbeb86" parent-uuid="9cde0869-dbeb-408e-926d-c9c2e976aa0e" type="association-field">
          <property name="from" value="location_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="30528ece-e5f8-408e-ac17-2c33408308f0" parent-uuid="83c5b7e3-**************-c768731e81c4" type="association-field">
          <property name="from" value="schoolyear_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="64e3535b-9a21-4461-8327-6acfc7dfb690" parent-uuid="f1727a3a-9845-4fde-b400-f62f029741ad" type="association-field">
          <property name="from" value="date_exception_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="786d6cb6-06d0-4320-a9d1-775b5fbe9a67" parent-uuid="3731e6d8-80b7-4073-85ee-2e01a848ae98" type="association-field">
          <property name="from" value="user_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="b5a02862-b5c4-4805-bad4-0eeda93ee316" parent-uuid="a3d4a434-abe4-4264-bfc7-621c02e0c379" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="ea5671b7-af80-41f1-bc36-61dcddc2db3c" parent-uuid="ab290cce-b8d0-4eb6-b310-765bdb4ad543" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="7de8156e-6ae8-4646-a521-e76a90954597" parent-uuid="b4a07b82-dac7-4ca6-ad74-10e3520f2d00" type="association-field">
          <property name="from" value="event_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="fdf9397b-db4a-419e-8d3e-371a7d093f63" parent-uuid="203e75e0-d79f-4f66-8ccd-01e16652ac61" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="903d7952-4c62-4de9-86cc-6bb6f31217f8" parent-uuid="813ea696-1f40-47e8-9dc2-131d578944fe" type="association-field">
          <property name="from" value="location_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="f0057e4e-d967-4886-b5b4-5e7326f921f6" parent-uuid="11bca13d-9211-4098-ab82-31756edc13c7" type="association-field">
          <property name="from" value="timetable_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="3a85307b-e91e-497e-b9de-a031a5422392" parent-uuid="b2693963-f971-428a-92af-1e35240d339b" type="association-field">
          <property name="from" value="tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="b18bcbbb-987b-44c2-a4b2-158d61afef00" parent-uuid="c13051a6-cc30-4f84-9826-a2bdd69a105d" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="69e9beed-4f7b-4228-aa92-982543693a33" parent-uuid="dd8d1d23-8267-44f0-b184-b64baaec8740" type="association-field">
          <property name="from" value="tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="e87e596c-ea49-4972-a88e-8e4695ac7d2a" parent-uuid="deb7a666-a98c-4fe7-86bd-bed6fec8e554" type="association-field">
          <property name="from" value="course_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="da15748d-151a-4ba5-88e4-3ce0a27fe6db" parent-uuid="5eaac0de-fe0c-4628-bed1-a39b7bab1daf" type="association-field">
          <property name="from" value="library_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="48bf1dfd-6222-4b67-8678-043f50867879" parent-uuid="8cda6768-4413-46a0-9068-9cf8da9d9270" type="association-field">
          <property name="from" value="coursegroup_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="0c82e87a-0b47-401f-a0ce-6e803eadbfaa" parent-uuid="99fec556-aeb7-4e8e-8044-7b4caefbf39d" type="association-field">
          <property name="from" value="library_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="03b74e67-b6f4-486c-b7a8-65a3e6f1caad" parent-uuid="6d447a7e-ff56-4a41-97e4-e6c8a75b5ac4" type="association-field">
          <property name="from" value="document_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="99977f23-022e-4f25-a3d7-81fddbd07e85" parent-uuid="f0ac4ad6-1989-4e6e-a700-ac1788868f83" type="association-field">
          <property name="from" value="library_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="dda6f17a-2fdd-422e-b311-b61433ade3ab" parent-uuid="d377efb6-3c55-4402-bd41-eb1a53b4a97c" type="association-field">
          <property name="from" value="library_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="16de081c-87ae-480d-b26e-f960e58dbdce" parent-uuid="635a8982-006b-4df9-97f8-126d5d07305d" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="9d5b100d-ede5-444e-badf-065f37640bd6" parent-uuid="314bffe1-fbf6-40c0-9644-62c1d581f016" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="95f9715e-263b-4a48-9232-ad54d1391895" parent-uuid="f1340b43-1b0d-44bc-ad76-3537cfc9f409" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="f8837813-9fc2-4251-a4b1-3246eea422cc" parent-uuid="6e25f026-53cc-4fc9-8adf-726cc1924026" type="association-field">
          <property name="from" value="user_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="a2f36b91-1f4c-4db2-ad95-fc8b0c98d370" parent-uuid="6b488045-5239-4975-9fc0-7e2b121e998b" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="844c46b3-8f05-4750-bc95-bc6497ec5754" parent-uuid="cb8e56ea-b5e6-4371-921b-f1a9c96bf1d9" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="7c02c618-5606-4928-ac29-b04a1c1cfe8c" parent-uuid="9d9b83fe-d7d6-4999-8ff4-688c4f0ec4a1" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="8f57028e-9f6b-4164-9e18-50b6390f798c" parent-uuid="ca43fd42-322b-46f4-8c9c-f536601d9e83" type="association-field">
          <property name="from" value="new_location_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="1fb53876-9acb-48d6-a98d-a712ae685333" parent-uuid="ff09776d-a26f-4ee4-bff3-1183b537b1b5" type="association-field">
          <property name="from" value="new_tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="eac25daa-49cd-4026-8240-d55947307f80" parent-uuid="805ab5e4-964a-4081-ab94-d2959e0c0b15" type="association-field">
          <property name="from" value="planningentry_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="003f3a4f-8459-4748-8f68-8d68a122c2b7" parent-uuid="4bede789-0ad8-472f-865f-a6cc56494218" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="32c9b071-7582-4262-8a52-3242736a20f6" parent-uuid="b9c597e6-a803-4a3e-85c8-6da346e0c31a" type="association-field">
          <property name="from" value="location_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="58bc7d3e-39c8-45d5-b452-01fc897b3bc9" parent-uuid="12050ccb-2af5-41ec-af76-ab793893484f" type="association-field">
          <property name="from" value="registration_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="*************-4cc4-afe0-5277ecc86a3c" parent-uuid="af30b4b2-a85b-495c-b30e-772238391dde" type="association-field">
          <property name="from" value="tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="f2f96948-1eb0-43c2-b79e-9e9d53b1930d" parent-uuid="f56ce73a-aaab-46c1-bbc3-63734aec26ee" type="association-field">
          <property name="from" value="course_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="7845bb3b-68fb-4944-bc82-5861485d7bc9" parent-uuid="6024dcfd-f538-46b7-8059-0f62051ac637" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="20652d29-3444-4b43-bf45-4c611d2031ec" parent-uuid="09209b58-f2ab-4dbc-af18-ffba95fa3c11" type="association-field">
          <property name="from" value="role_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="36a38280-d359-4e44-85cb-eff756fa3466" parent-uuid="0c4df872-ce8d-451d-a75b-e4fca4cbe66d" type="association-field">
          <property name="from" value="user_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="5850d881-aec0-4167-bc0b-0491a398effa" parent-uuid="81334048-52d0-44c3-992a-a47f0f1c2741" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="cb551b14-8da8-4429-91e2-47ad152db8a6" parent-uuid="1086a0dc-2c3b-4431-9151-e02b634a1398" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="e26a42a4-db7e-42d3-8862-aa8f845c8ab4" parent-uuid="d7a46353-7aee-4e8f-b553-24f5ebaf3eb3" type="association-field">
          <property name="from" value="location_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="cb1f0036-7b6e-4534-a0ed-47eda4a62d89" parent-uuid="07f092b7-95d0-43cb-bbcc-7ccb48f83ed8" type="association-field">
          <property name="from" value="registration_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="a2c7ba8f-23c7-40bf-b16e-652c9283f9a7" parent-uuid="f6665d67-2798-454b-a32b-335b3615df02" type="association-field">
          <property name="from" value="tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="32608a8b-afe5-4b64-ae17-f8302aba5f82" parent-uuid="41bd98a3-ddcc-4ca6-9acb-c05abcc5c5c4" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="04f29184-a843-4a4e-aee9-b5a527afdbc1" parent-uuid="4f888a6c-0349-427f-bbc8-f01b95094805" type="association-field">
          <property name="from" value="studentgroup_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="d483dfcd-df6a-4a59-8e75-fad27f4e3f64" parent-uuid="3368badc-f7cb-48f0-be21-c3671b02eb91" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="8381c4ab-de2c-4591-b4ba-3c4235b1756a" parent-uuid="9a7f4a49-de23-4b26-88a6-9e5d659290d2" type="association-field">
          <property name="from" value="studentlist_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="c0ca33d4-99b9-433c-9531-057cf967ee54" parent-uuid="00d196dd-bb81-4b00-92e7-4444d68c0ea4" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="0509bb49-2231-4ba0-98c7-34fc375a4a1d" parent-uuid="bce5d578-2190-4d87-b8b2-586d794f8276" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="1775f967-da7a-44d6-87dc-0ee2fbb0ab93" parent-uuid="fdda2275-54e9-4fb5-8464-9cf1b25f2da3" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="2235f5b4-5c42-474b-bc3e-3eaf231daec0" parent-uuid="5263e8f0-4194-4ef6-88ac-ddb52061089d" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="5741fb1c-e983-4740-a1f1-22f45008669e" parent-uuid="4b65b521-e809-44a2-8df2-3801fb791855" type="association-field">
          <property name="from" value="assigned_user_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="4eb37f10-ec53-4337-953a-3ec7f58f3485" parent-uuid="22c2bcd1-4112-42e9-9649-f66fd4c1cbee" type="association-field">
          <property name="from" value="course_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="7e26efa3-5ff6-4d6a-b6e4-96cea4e97e30" parent-uuid="e8b1d77f-613a-4170-b4e7-6ab5e59d2ec8" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="19bc2e40-3ef7-4ca7-815e-2dee01f6760e" parent-uuid="269e9223-3b7c-4c7e-a165-0324f30533a3" type="association-field">
          <property name="from" value="event_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="45c93e80-9b5e-489f-9939-7846f0fc47d6" parent-uuid="f7d2bc4c-e95e-4739-b28a-bedf7f52873c" type="association-field">
          <property name="from" value="registration_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="bfa86b45-8c10-46e9-84c3-9c6f504c0dac" parent-uuid="e1c528fe-4cf3-4353-a93f-bf475fb5578e" type="association-field">
          <property name="from" value="student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="6d564468-063f-4da0-8060-7be00663b84d" parent-uuid="456272d0-2430-4b0c-9ea7-0386bf04c937" type="association-field">
          <property name="from" value="tasktype_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="4db1d496-**************-b14986aef13b" parent-uuid="bf8ffb2a-c900-43e1-8d57-dc8bce94959e" type="association-field">
          <property name="from" value="tutor_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="bf2f2741-cbc1-46ad-a4c1-1711e1d8ee0d" parent-uuid="e215b9d2-c316-4e13-9d73-5667b7de1c5c" type="association-field">
          <property name="from" value="course_student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="926e8130-6a52-4501-8dcd-36fa63ffbf72" parent-uuid="a4263efa-b60d-4ba4-aa7b-c6f29800179d" type="association-field">
          <property name="from" value="schoolyear_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="f3635fe9-7ec3-44b3-8e11-75a64755bee5" parent-uuid="fff36ee4-4b8d-4890-8b80-b23326a4677f" type="association-field">
          <property name="from" value="course_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="bf410293-792f-4dd9-bdee-4cf2138a287a" parent-uuid="11cbcc23-5253-4b15-b3e6-e3ad22542445" type="association-field">
          <property name="from" value="trialcourse_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="d50cf3ad-a057-483b-84ec-23cf54fe2391" parent-uuid="4a667653-9b4a-4cc1-9c49-bb8b2d17426a" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="57ec9fad-b35a-4a77-b13e-803f4fda53ee" parent-uuid="1464b9e0-4852-4abc-8c0d-8548f8a010ef" type="association-field">
          <property name="from" value="course_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="34e7800c-6d8c-4666-af47-38eee3f74eeb" parent-uuid="630e42d9-eab3-42ea-8fb6-639af59ae2b7" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="f180f810-a92d-4628-9e86-781654165e4a" parent-uuid="3e8e337e-1dd3-4c1d-a885-56e69658e00a" type="association-field">
          <property name="from" value="generated_registration_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="292ad35d-5aa6-4faf-9b32-49342bdf7175" parent-uuid="7e899f8c-8143-46ed-b6a9-d9917e8e8b6c" type="association-field">
          <property name="from" value="generated_student_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="1283543d-7a8e-4336-a8aa-ad0d72cd27c6" parent-uuid="8b3de001-85f4-410a-ad59-8cdc750f6a2b" type="association-field">
          <property name="from" value="trialrequeststatus_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="e49a0411-b754-46f4-89ff-978f16886e6a" parent-uuid="84256974-8cd9-4ac7-b144-451905cb6e66" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
      </revision>
      <revision uuid="21df8e90-db34-484e-a2c6-9398d8579377" date="2024-12-01 19:08:00.156928" exportable="true">
        <element action="update" uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="module">
          <property name="local-name" value="Class"/>
          <property name="migrations-path" value="migrations"/>
          <property name="models-disabled" value="true"/>
          <property name="name" value="\Class"/>
        </element>
      </revision>
      <revision uuid="eaef22de-0cc9-4d2f-855c-92c586e3f76d" date="2024-12-02 20:14:14.767059" exportable="true">
        <element action="delete" uuid="f77b86e1-b998-45a5-834a-a16bfb442210" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" type="field"/>
        <element action="delete" uuid="5e7b018b-0fb8-40ca-8b81-2d110b9260a3" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="f77b86e1-b998-45a5-834a-a16bfb442210" type="field"/>
        <element action="delete" uuid="f328e5a0-d17e-440a-a21b-7806c6e86648" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="5e7b018b-0fb8-40ca-8b81-2d110b9260a3" type="field"/>
        <element action="delete" uuid="b04883b0-d287-4640-8db7-6dd58aafd2b1" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="f328e5a0-d17e-440a-a21b-7806c6e86648" type="field"/>
        <element action="delete" uuid="ff75089a-1015-42b0-a66c-6985e8093df4" parent-uuid="6721abcb-5865-4db8-98df-f6679e120741" sibling-uuid="b04883b0-d287-4640-8db7-6dd58aafd2b1" type="field"/>
        <element action="delete" uuid="6721abcb-5865-4db8-98df-f6679e120741" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="entity"/>
        <element action="add" uuid="7d796ff8-b92a-4489-b903-a045ff907135" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="8f2fdea8-8ba7-469b-991e-9610187d4a3a" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="eb68626c-e7e1-44a6-84d9-ecd13d7414be" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="ed70e4c9-3ee4-483a-b44d-04628047d830" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="comment">
          <property name="description" value="These entities are mainly used for Classy, comunicating docs and instructions between tutor and student."/>
        </element>
        <element action="reparent" uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="ef423929-3822-431c-a4bb-36e67d80a964" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="2a98b125-f664-422e-9097-b8896608cafa" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="b7435152-5821-4b19-9e50-701520fe7704" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="entity"/>
        <element action="reparent" uuid="8cd208d8-de83-4695-9468-02073ea1f00f" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="entity"/>
        <element action="reparent" uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="entity"/>
        <element action="reparent" uuid="b8404cea-9e03-48c5-b2e8-d2014a185356" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="entity"/>
        <element action="reparent" uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="entity"/>
        <element action="reparent" uuid="ceb6e565-00ec-43e2-8146-5e052d702270" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="entity"/>
        <element action="reparent" uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="entity"/>
        <element action="reparent" uuid="c94bd824-635d-40bb-9717-360ea46514e9" parent-uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" type="entity"/>
        <element action="reparent" uuid="24be36a3-768b-4bde-bc50-2239b5545201" parent-uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac" type="entity"/>
        <element action="reparent" uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" parent-uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac" type="entity"/>
        <element action="reparent" uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" parent-uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac" type="entity"/>
        <element action="reparent" uuid="c10c1531-15c0-4e26-8860-4d6068585afa" parent-uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac" type="entity"/>
        <element action="reparent" uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" parent-uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac" type="entity"/>
        <element action="reparent" uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="72f556d7-609b-405b-88a4-8f6d4587b032" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="e2056103-3473-42f2-a865-4a63f378d655" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="709b0eec-1818-4251-856b-44fd280b3812" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="68a489f5-2ff4-4359-9710-085720368ca6" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" parent-uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" type="entity"/>
        <element action="reparent" uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" parent-uuid="8f2fdea8-8ba7-469b-991e-9610187d4a3a" type="entity"/>
        <element action="reparent" uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" parent-uuid="8f2fdea8-8ba7-469b-991e-9610187d4a3a" type="entity"/>
        <element action="reparent" uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" parent-uuid="8f2fdea8-8ba7-469b-991e-9610187d4a3a" type="entity"/>
        <element action="reparent" uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" parent-uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" type="entity"/>
        <element action="reparent" uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" parent-uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" type="entity"/>
        <element action="reparent" uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" parent-uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" type="entity"/>
        <element action="reparent" uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" parent-uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" type="entity"/>
        <element action="reparent" uuid="253849ed-5411-4cec-bd9d-0053c321f423" parent-uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" type="entity"/>
        <element action="reparent" uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" parent-uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" type="entity"/>
        <element action="reparent" uuid="dd612244-e406-4048-a3d9-5001719eaf66" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="34879c08-1079-4cee-b770-e42debb31c86" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="e799a893-7c3e-4ae7-b214-946c90225fca" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="ed9a1554-55ad-494e-a84a-2217e636774a" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="5b414e55-7f53-424c-a425-79eb086472b8" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="fafee967-9109-4d9c-861f-bdf269339540" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" parent-uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" type="entity"/>
        <element action="reparent" uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" parent-uuid="eb68626c-e7e1-44a6-84d9-ecd13d7414be" type="entity"/>
        <element action="reparent" uuid="7b400813-2907-42f8-83e7-05ea7763af1c" parent-uuid="eb68626c-e7e1-44a6-84d9-ecd13d7414be" type="entity"/>
        <element action="reparent" uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" parent-uuid="eb68626c-e7e1-44a6-84d9-ecd13d7414be" type="entity"/>
      </revision>
      <revision uuid="729bfbc1-1b7c-4ef5-8706-1c0edb65c4c5" date="2025-01-26 08:57:15.360957" exportable="true">
        <element action="add" uuid="a26a64a6-e2bc-4086-9d36-a2f414b5f290" parent-uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" type="region">
          <property name="description" value="Communication between Class and tutors"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="update" uuid="6f437310-b073-465f-bedd-5f8a217b53fb" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="6f655fed-6fb8-4250-99a2-05841bb55cc7" type="field">
          <property name="enum-values" value="'file','url','audio','video'"/>
        </element>
        <element action="update" uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="28cd60f4-969a-4c15-a377-ec01b2bb80ee" type="field">
          <property name="name" value="file_name"/>
          <property name="required" value="true"/>
        </element>
        <element action="add" uuid="6f655fed-6fb8-4250-99a2-05841bb55cc7" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="14ec30d9-3ce1-4e6d-b78e-a147b3bda63c" parent-uuid="7d796ff8-b92a-4489-b903-a045ff907135" type="comment">
          <property name="description" value="to and from ClassE"/>
        </element>
        <element action="reparent" uuid="c94bd824-635d-40bb-9717-360ea46514e9" parent-uuid="a26a64a6-e2bc-4086-9d36-a2f414b5f290" type="entity"/>
        <element action="reorder" uuid="8a0e2691-9a0e-414b-a351-18a7dc42d1ea" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="96dc2784-1399-4a28-8e5e-80709adc0fe1" previous-sibling-uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" type="field"/>
      </revision>
      <revision uuid="3c3398dd-d577-4a5b-9b05-3266cd088d15" date="2025-01-27 16:32:34.159012" exportable="true">
        <element action="update" uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="28cd60f4-969a-4c15-a377-ec01b2bb80ee" type="field">
          <property name="description" value="name as saved on the stack server"/>
        </element>
        <element action="add" uuid="ac157ee4-9f79-4a66-ac1b-d9f2ca24c3b9" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" type="field">
          <property name="description" value="name of the original file during upload"/>
          <property name="name" value="original_name"/>
          <property name="size" value="155"/>
          <property name="type" value="string"/>
        </element>
        <element action="reorder" uuid="6f655fed-6fb8-4250-99a2-05841bb55cc7" parent-uuid="8cd208d8-de83-4695-9468-02073ea1f00f" sibling-uuid="ac157ee4-9f79-4a66-ac1b-d9f2ca24c3b9" previous-sibling-uuid="127a1101-f2b5-4bab-8c08-e5597c27a814" type="field"/>
      </revision>
    </migrations>
  </module>
  <visual-data>
    <association uuid="00d196dd-bb81-4b00-92e7-4444d68c0ea4" color="#0A783D"/>
    <association uuid="07f092b7-95d0-43cb-bbcc-7ccb48f83ed8" color="#B31F24"/>
    <association uuid="09209b58-f2ab-4dbc-af18-ffba95fa3c11" center-position-x="0" center-position-y="0" color="#A8A8A8"/>
    <association uuid="0c4df872-ce8d-451d-a75b-e4fca4cbe66d" color="#0A783D"/>
    <association uuid="1086a0dc-2c3b-4431-9151-e02b634a1398" color="#CDC32B" split="1"/>
    <association uuid="11bca13d-9211-4098-ab82-31756edc13c7" center-position-x="0" center-position-y="28" color="#CDC32B"/>
    <association uuid="11cbcc23-5253-4b15-b3e6-e3ad22542445" color="#C774AF"/>
    <association uuid="12050ccb-2af5-41ec-af76-ab793893484f" color="#C774AF"/>
    <association uuid="1464b9e0-4852-4abc-8c0d-8548f8a010ef" color="#2E63AF"/>
    <association uuid="1cca546b-189b-4c68-af0b-0939ee635e6f" center-position-x="0" center-position-y="0" color="#C774AF"/>
    <association uuid="1d2f74fb-4152-4ae6-8d51-8585820bb9fc" color="#A8A8A8" split="1"/>
    <association uuid="203e75e0-d79f-4f66-8ccd-01e16652ac61" color="#0A783D" split="1"/>
    <association uuid="22c2bcd1-4112-42e9-9649-f66fd4c1cbee" color="#C774AF"/>
    <association uuid="269e9223-3b7c-4c7e-a165-0324f30533a3" color="#2E63AF"/>
    <association uuid="2dc9846a-15c1-46ad-a896-7e4d8bad600d" color="#A8A8A8"/>
    <association uuid="314bffe1-fbf6-40c0-9644-62c1d581f016" color="#B31F24" split="1"/>
    <association uuid="3368badc-f7cb-48f0-be21-c3671b02eb91" color="#00B4D0"/>
    <association uuid="3731e6d8-80b7-4073-85ee-2e01a848ae98" color="#8A54A2"/>
    <association uuid="3e8e337e-1dd3-4c1d-a885-56e69658e00a" color="#A8A8A8"/>
    <association uuid="41bd98a3-ddcc-4ca6-9acb-c05abcc5c5c4" color="#8A54A2" split="1"/>
    <association uuid="456272d0-2430-4b0c-9ea7-0386bf04c937" color="#0A783D"/>
    <association uuid="4a667653-9b4a-4cc1-9c49-bb8b2d17426a" color="#8A54A2" split="1"/>
    <association uuid="4b65b521-e809-44a2-8df2-3801fb791855" color="#B31F24"/>
    <association uuid="4bede789-0ad8-472f-865f-a6cc56494218" color="#F4931F" split="1"/>
    <association uuid="4f888a6c-0349-427f-bbc8-f01b95094805" color="#2E63AF"/>
    <association uuid="5025ad30-5fd2-454e-9e6d-c61455d03601" color="#00B4D0"/>
    <association uuid="5263e8f0-4194-4ef6-88ac-ddb52061089d" color="#F4931F" split="1"/>
    <association uuid="59bc29b0-b5c3-4fb1-9e74-030b4806a69b" color="#F4931F"/>
    <association uuid="5eaac0de-fe0c-4628-bed1-a39b7bab1daf" color="#2E63AF"/>
    <association uuid="6024dcfd-f538-46b7-8059-0f62051ac637" color="#00B4D0" split="1"/>
    <association uuid="630e42d9-eab3-42ea-8fb6-639af59ae2b7" color="#00B4D0" split="1"/>
    <association uuid="635a8982-006b-4df9-97f8-126d5d07305d" color="#F4931F"/>
    <association uuid="650a406d-9890-4ca6-9bc0-0adc03fbe746" color="#00B4D0" split="1"/>
    <association uuid="690a229d-955e-4c3e-8e0a-9c489ede5366" color="#61D351"/>
    <association uuid="6b488045-5239-4975-9fc0-7e2b121e998b" color="#2E63AF" split="1"/>
    <association uuid="6d447a7e-ff56-4a41-97e4-e6c8a75b5ac4" center-position-x="6" center-position-y="0" color="#0A783D"/>
    <association uuid="6e25f026-53cc-4fc9-8adf-726cc1924026" color="#8A54A2"/>
    <association uuid="7e899f8c-8143-46ed-b6a9-d9917e8e8b6c" color="#0A783D"/>
    <association uuid="805ab5e4-964a-4081-ab94-d2959e0c0b15" color="#CDC32B"/>
    <association uuid="81334048-52d0-44c3-992a-a47f0f1c2741" color="#61D351"/>
    <association uuid="813ea696-1f40-47e8-9dc2-131d578944fe" color="#61D351"/>
    <association uuid="83c5b7e3-**************-c768731e81c4" center-position-x="0" center-position-y="0" color="#B31F24"/>
    <association uuid="84256974-8cd9-4ac7-b144-451905cb6e66" color="#CDC32B" split="1"/>
    <association uuid="8aa25083-889c-44ee-818c-bb7037f660b4" color="#61D351"/>
    <association uuid="8b3de001-85f4-410a-ad59-8cdc750f6a2b" color="#61D351"/>
    <association uuid="8cda6768-4413-46a0-9068-9cf8da9d9270" color="#00B4D0"/>
    <association uuid="99fec556-aeb7-4e8e-8044-7b4caefbf39d" color="#A8A8A8"/>
    <association uuid="9a7f4a49-de23-4b26-88a6-9e5d659290d2" color="#A8A8A8"/>
    <association uuid="9c5f96bf-16a7-4df3-a146-903c2c81ec1c" color="#0A783D"/>
    <association uuid="9cde0869-dbeb-408e-926d-c9c2e976aa0e" color="#F4931F"/>
    <association uuid="9d9b83fe-d7d6-4999-8ff4-688c4f0ec4a1" color="#A8A8A8" split="1"/>
    <association uuid="9f54fb6d-abfd-43a0-a513-ea5459b6311a" color="#CDC32B" split="1"/>
    <association uuid="a3d4a434-abe4-4264-bfc7-621c02e0c379" color="#2E63AF" split="1"/>
    <association uuid="a4263efa-b60d-4ba4-aa7b-c6f29800179d" color="#F4931F"/>
    <association uuid="ab290cce-b8d0-4eb6-b310-765bdb4ad543" color="#00B4D0" split="1"/>
    <association uuid="af30b4b2-a85b-495c-b30e-772238391dde" color="#8A54A2"/>
    <association uuid="b12184b0-3fa4-41af-b99a-cb6686e346be" color="#2E63AF"/>
    <association uuid="b2693963-f971-428a-92af-1e35240d339b" color="#F4931F"/>
    <association uuid="b4a07b82-dac7-4ca6-ad74-10e3520f2d00" color="#A8A8A8"/>
    <association uuid="b75fee78-71b7-4ff1-97c9-732ca1caa938" color="#CDC32B"/>
    <association uuid="b9c597e6-a803-4a3e-85c8-6da346e0c31a" color="#B31F24"/>
    <association uuid="bce5d578-2190-4d87-b8b2-586d794f8276" color="#61D351"/>
    <association uuid="bdb2a6f9-7697-45ba-9382-83ae67fc4c4a" color="#C774AF" split="1"/>
    <association uuid="bf8ffb2a-c900-43e1-8d57-dc8bce94959e" color="#61D351"/>
    <association uuid="c13051a6-cc30-4f84-9826-a2bdd69a105d" color="#B31F24" split="1"/>
    <association uuid="ca43fd42-322b-46f4-8c9c-f536601d9e83" color="#0A783D"/>
    <association uuid="cb8e56ea-b5e6-4371-921b-f1a9c96bf1d9" color="#00B4D0" split="1"/>
    <association uuid="d377efb6-3c55-4402-bd41-eb1a53b4a97c" center-position-x="0" center-position-y="0" color="#CDC32B"/>
    <association uuid="d7a46353-7aee-4e8f-b553-24f5ebaf3eb3" color="#F4931F"/>
    <association uuid="d7a47c8e-7067-4fb5-9eac-d5380f4977b7" color="#0A783D"/>
    <association uuid="dd8d1d23-8267-44f0-b184-b64baaec8740" color="#C774AF"/>
    <association uuid="deb7a666-a98c-4fe7-86bd-bed6fec8e554" color="#8A54A2"/>
    <association uuid="e1c528fe-4cf3-4353-a93f-bf475fb5578e" color="#A8A8A8"/>
    <association uuid="e215b9d2-c316-4e13-9d73-5667b7de1c5c" color="#CDC32B"/>
    <association uuid="e80160e7-ad70-4b80-9fa1-0ee076ed378a" color="#B31F24"/>
    <association uuid="e88a69d6-065f-43bc-9a63-8c3c4fffa21c" color="#8A54A2"/>
    <association uuid="e8b1d77f-613a-4170-b4e7-6ab5e59d2ec8" color="#8A54A2" split="1"/>
    <association uuid="f0ac4ad6-1989-4e6e-a700-ac1788868f83" center-position-x="0" center-position-y="0" color="#61D351"/>
    <association uuid="f1340b43-1b0d-44bc-ad76-3537cfc9f409" color="#C774AF"/>
    <association uuid="f1727a3a-9845-4fde-b400-f62f029741ad" center-position-x="-6" center-position-y="23" color="#C774AF"/>
    <association uuid="f1d4fd54-d460-48b6-8990-3024207e3663" color="#8A54A2" split="1"/>
    <association uuid="f56ce73a-aaab-46c1-bbc3-63734aec26ee" center-position-x="0" center-position-y="0" color="#2E63AF"/>
    <association uuid="f6665d67-2798-454b-a32b-335b3615df02" color="#C774AF"/>
    <association uuid="f7d2bc4c-e95e-4739-b28a-bedf7f52873c" color="#00B4D0"/>
    <association uuid="fd72c4e9-ff5f-45e3-9fea-2b12877a0326" color="#2E63AF"/>
    <association uuid="fdda2275-54e9-4fb5-8464-9cf1b25f2da3" color="#CDC32B" split="1"/>
    <association uuid="ff09776d-a26f-4ee4-bff3-1183b537b1b5" color="#61D351"/>
    <association uuid="fff36ee4-4b8d-4890-8b80-b23326a4677f" color="#B31F24"/>
    <comment uuid="08d97bf2-4057-4e15-912f-4b7149a0a509" bg-color="#FFFFE0" position-x="-58" position-y="311" size-x="0" size-x2="111" size-y="0" size-y2="20" txt-color="#000000"/>
    <comment uuid="14ec30d9-3ce1-4e6d-b78e-a147b3bda63c" bg-color="#FFFFE0" position-x="99" position-y="269" size-x="0" size-x2="93" size-y="0" size-y2="38" txt-color="#000000"/>
    <comment uuid="ed70e4c9-3ee4-483a-b44d-04628047d830" bg-color="#FFFFE0" position-x="332" position-y="1" size-x="0" size-x2="168" size-y="0" size-y2="53" txt-color="#000000"/>
    <entity uuid="011269e1-cbaa-44f1-b534-fb730b2998cd" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="261" position-y="-5" size-x="0" size-x2="124" size-y="0" size-y2="129"/>
    <entity uuid="045c4e15-1067-4210-827e-c9d0cbeb9b6f" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="172" position-y="125" size-x="0" size-x2="109" size-y="0" size-y2="115"/>
    <entity uuid="05cc3b23-f921-457d-9fac-434a5ebcc928" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="54" position-y="351" size-x="0" size-x2="119" size-y="0" size-y2="87"/>
    <entity uuid="14f2cb4a-5605-4c00-89ab-115a2ae38068" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="149" position-y="272" size-x="0" size-x2="167" size-y="0" size-y2="115"/>
    <entity uuid="24be36a3-768b-4bde-bc50-2239b5545201" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="255" position-y="199" size-x="0" size-x2="124" size-y="0" size-y2="115"/>
    <entity uuid="253849ed-5411-4cec-bd9d-0053c321f423" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="447" position-y="413" size-x="0" size-x2="186" size-y="0" size-y2="157"/>
    <entity uuid="2a98b125-f664-422e-9097-b8896608cafa" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-5" position-y="454" size-x="0" size-x2="126" size-y="0" size-y2="185"/>
    <entity uuid="2bfcb045-f8e8-4bef-b158-8187021066ee" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="259" position-y="-13" size-x="0" size-x2="119" size-y="0" size-y2="115"/>
    <entity uuid="34879c08-1079-4cee-b770-e42debb31c86" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="296" position-y="194" size-x="0" size-x2="119" size-y="0" size-y2="143"/>
    <entity uuid="369427ba-e6ee-4c5f-a7f3-8dadeab2f05b" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="809" position-y="233" size-x="0" size-x2="122" size-y="0" size-y2="101"/>
    <entity uuid="38250f77-2e1a-496f-b5ed-449d426d2e32" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="15" position-y="192" size-x="0" size-x2="116" size-y="0" size-y2="59"/>
    <entity uuid="3f4f2663-6d56-449e-863e-635c607f6a6a" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="254" position-y="153" size-x="0" size-x2="119" size-y="0" size-y2="129"/>
    <entity uuid="4e21e5d1-1315-4f71-9d0d-9020d07bad06" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="169" position-y="255" size-x="0" size-x2="111" size-y="0" size-y2="101"/>
    <entity uuid="597fbc0a-850c-4412-a129-68db9c4ec1dc" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="315" position-y="405" size-x="0" size-x2="162" size-y="0" size-y2="241"/>
    <entity uuid="5b414e55-7f53-424c-a425-79eb086472b8" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="23" position-y="475" size-x="0" size-x2="117" size-y="0" size-y2="143"/>
    <entity uuid="61eb0a37-84f1-4811-8fb9-1cf5177eaee0" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="375" position-y="603" size-x="0" size-x2="119" size-y="0" size-y2="143"/>
    <entity uuid="68a489f5-2ff4-4359-9710-085720368ca6" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="536" position-y="489" size-x="0" size-x2="147" size-y="0" size-y2="101"/>
    <entity uuid="6ba88ab3-4143-4362-864a-fac809bc5b14" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="623" position-y="541" size-x="0" size-x2="145" size-y="0" size-y2="199"/>
    <entity uuid="709b0eec-1818-4251-856b-44fd280b3812" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="333" position-y="-40" size-x="0" size-x2="151" size-y="0" size-y2="171"/>
    <entity uuid="72f556d7-609b-405b-88a4-8f6d4587b032" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="504" position-y="232" size-x="0" size-x2="124" size-y="0" size-y2="129"/>
    <entity uuid="7b400813-2907-42f8-83e7-05ea7763af1c" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="43" position-y="10" size-x="0" size-x2="124" size-y="0" size-y2="227"/>
    <entity uuid="87e30c38-9117-4c2e-83b1-38930f88de0e" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="31" position-y="385" size-x="0" size-x2="140" size-y="0" size-y2="423"/>
    <entity uuid="8cd208d8-de83-4695-9468-02073ea1f00f" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="70" position-y="27" size-x="0" size-x2="122" size-y="0" size-y2="185"/>
    <entity uuid="8f5b5fde-f34d-491d-a92b-e12b99f42ddf" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="377" position-y="407" size-x="0" size-x2="122" size-y="0" size-y2="73"/>
    <entity uuid="9a96d8c0-ab1d-4721-81f7-dea7b722aa52" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="590" position-y="172" size-x="0" size-x2="153" size-y="0" size-y2="129"/>
    <entity uuid="a18fecb9-3c7b-4161-8028-3d2a88cff632" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="241" position-y="192" size-x="0" size-x2="138" size-y="0" size-y2="213"/>
    <entity uuid="b7435152-5821-4b19-9e50-701520fe7704" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="173" position-y="476" size-x="0" size-x2="119" size-y="0" size-y2="171"/>
    <entity uuid="b8404cea-9e03-48c5-b2e8-d2014a185356" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="78" position-y="442" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="b8707a27-dbf5-4410-85db-8ce8b4a996c0" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="559" position-y="216" size-x="0" size-x2="139" size-y="0" size-y2="87"/>
    <entity uuid="b8ed7d0d-805d-4107-b7f1-c4e1d8627fa8" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="495" position-y="-192" size-x="0" size-x2="119" size-y="0" size-y2="115"/>
    <entity uuid="bb632cae-73f2-4b84-b6f3-e9e46bd81302" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="225" position-y="84" size-x="0" size-x2="169" size-y="0" size-y2="255"/>
    <entity uuid="bc33afe3-5a0d-40b1-88eb-29d8867a3d1b" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="328" position-y="370" size-x="0" size-x2="128" size-y="0" size-y2="73"/>
    <entity uuid="bf934382-8829-4844-b190-4b8c4bdafe2d" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="356" position-y="32" size-x="0" size-x2="119" size-y="0" size-y2="115"/>
    <entity uuid="c10c1531-15c0-4e26-8860-4d6068585afa" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="529" position-y="73" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="c485d472-08f9-4ec0-9c15-81e0a4463d08" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="244" position-y="386" size-x="0" size-x2="119" size-y="0" size-y2="269"/>
    <entity uuid="c88f264e-5a55-4e7c-a8c5-b9625fd56210" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="67" position-y="-16" size-x="0" size-x2="144" size-y="0" size-y2="213"/>
    <entity uuid="c917f716-a7bb-43c0-bdb4-1502b84f8acf" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="556" position-y="90" size-x="0" size-x2="119" size-y="0" size-y2="115"/>
    <entity uuid="c94bd824-635d-40bb-9717-360ea46514e9" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="39" position-y="54" size-x="0" size-x2="119" size-y="0" size-y2="199"/>
    <entity uuid="c9e0f153-ba0b-4843-9837-1f982d71537d" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="47" position-y="472" size-x="0" size-x2="121" size-y="0" size-y2="171"/>
    <entity uuid="ca9f0021-c1c7-48bc-8830-0c5a2c155770" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="238" position-y="422" size-x="0" size-x2="142" size-y="0" size-y2="143"/>
    <entity uuid="cab4d289-3f17-45a2-be1a-9aee812adf3b" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-190" position-y="280" size-x="0" size-x2="159" size-y="0" size-y2="423"/>
    <entity uuid="ceb6e565-00ec-43e2-8146-5e052d702270" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="318" position-y="143" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="d19a132a-1aea-4e1a-bab9-e92d83edd49d" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="473" position-y="387" size-x="0" size-x2="133" size-y="0" size-y2="101"/>
    <entity uuid="d4b171e0-a4e1-4c67-95c6-897db7ab3d63" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="114" position-y="46" size-x="0" size-x2="183" size-y="0" size-y2="283"/>
    <entity uuid="dd612244-e406-4048-a3d9-5001719eaf66" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="172" position-y="9" size-x="0" size-x2="108" size-y="0" size-y2="101"/>
    <entity uuid="e1508c93-27be-4be1-8b5b-20a4186e32ad" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="294" position-y="9" size-x="0" size-x2="158" size-y="0" size-y2="171"/>
    <entity uuid="e2056103-3473-42f2-a865-4a63f378d655" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="259" position-y="-144" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="e5e8860a-f818-4e0c-9819-7a7ad9255c7f" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="1219" position-y="903" size-x="0" size-x2="133" size-y="0" size-y2="73"/>
    <entity uuid="e799a893-7c3e-4ae7-b214-946c90225fca" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="163" position-y="373" size-x="0" size-x2="142" size-y="0" size-y2="73"/>
    <entity uuid="ed9a1554-55ad-494e-a84a-2217e636774a" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="18" position-y="9" size-x="0" size-x2="136" size-y="0" size-y2="157"/>
    <entity uuid="ee0da20e-71b1-4ffc-9df9-58efbb408adc" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="272" position-y="513" size-x="0" size-x2="111" size-y="0" size-y2="101"/>
    <entity uuid="ef423929-3822-431c-a4bb-36e67d80a964" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="57" position-y="12" size-x="0" size-x2="119" size-y="0" size-y2="87"/>
    <entity uuid="efa306df-9beb-4bb1-b912-04144f1a5b5d" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="674" position-y="1660" size-x="0" size-x2="130" size-y="0" size-y2="227"/>
    <entity uuid="f00c4f9b-38cc-4ab5-b9d8-49223ff56f80" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="300" position-y="-89" size-x="0" size-x2="144" size-y="0" size-y2="101"/>
    <entity uuid="f68c4cc6-53fa-46cb-b6cb-1f4fdb4e9fdc" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-61" position-y="228" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="f69b0190-690b-46ad-9697-cf47b35a3bdb" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="679" position-y="2010" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="f7939eac-fbf5-4f46-be21-a65a5c68d267" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-17" position-y="-152" size-x="0" size-x2="119" size-y="0" size-y2="87"/>
    <entity uuid="f7b47da7-4783-489c-ad9c-d4bc76889c2a" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="29" position-y="67" size-x="0" size-x2="196" size-y="0" size-y2="353"/>
    <entity uuid="fafee967-9109-4d9c-861f-bdf269339540" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="151" position-y="512" size-x="0" size-x2="111" size-y="0" size-y2="101"/>
    <entity uuid="fce932dc-2002-4d18-bd98-7a90131f3b3d" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="1920" position-y="1326" size-x="0" size-x2="156" size-y="0" size-y2="115"/>
    <module uuid="27f3d342-15d2-4073-a51c-cd2da5b30548" bg-color="#EAE4F1" position-x="50" position-y="50" size-x="214" size-x2="2718" size-y="0" size-y2="2569"/>
    <project uuid="7db39806-493a-4dab-b847-b0b4237f689f" size-x="214" size-x2="2960" size-y="0" size-y2="2987"/>
    <region uuid="1a7c01c5-2810-4da1-9c5a-cb26a5425bdb" bg-color="#E7F5FD" position-x="2177" position-y="118" size-x="0" size-x2="503" size-y="24" size-y2="664"/>
    <region uuid="767a9d58-74c4-4924-a3c7-7779ff5e1fd0" bg-color="#D6FFD6" position-x="1411" position-y="1458" size-x="0" size-x2="1139" size-y="0" size-y2="1053"/>
    <region uuid="7d796ff8-b92a-4489-b903-a045ff907135" bg-color="#E7F5FD" position-x="163" position-y="110" size-x="32" size-x2="968" size-y="25" size-y2="836"/>
    <region uuid="8f2fdea8-8ba7-469b-991e-9610187d4a3a" bg-color="#FDEBEF" position-x="908" position-y="1688" size-x="0" size-x2="401" size-y="230" size-y2="828"/>
    <region uuid="a26a64a6-e2bc-4086-9d36-a2f414b5f290" bg-color="#FDEE9A" position-x="1936" position-y="94" size-x="0" size-x2="199" size-y="0" size-y2="692"/>
    <region uuid="b06bf072-e8b5-4685-8d13-d39077c8caaa" bg-color="#FEEFE3" position-x="-134" position-y="1694" size-x="28" size-x2="716" size-y="240" size-y2="823"/>
    <region uuid="d0a613ff-811a-417a-8bbc-d5ba9d5653cc" bg-color="#E7F3E7" position-x="1274" position-y="107" size-x="81" size-x2="611" size-y="14" size-y2="679"/>
    <region uuid="eb68626c-e7e1-44a6-84d9-ecd13d7414be" bg-color="#FFB79D" position-x="-161" position-y="1056" size-x="0" size-x2="473" size-y="52" size-y2="290"/>
    <region uuid="eeca154b-3c5d-4bcc-9cf5-b0e8144618ac" bg-color="#FEFCE8" position-x="1859" position-y="922" size-x="0" size-x2="825" size-y="106" size-y2="383"/>
  </visual-data>
</skipper>
