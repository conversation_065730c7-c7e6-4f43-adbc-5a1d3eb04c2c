version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: class_app
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
      - ./logs:/var/www/html/storage/logs
    networks:
      - class
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=class_test
      - DB_USERNAME=root
      - DB_PASSWORD=root
    depends_on:
      - mysql
    ports:
      - "8000:8000"
    command: sh -c "composer install && \
      php artisan db:wipe && \
      php artisan migrate --seed && \
      php artisan serve --host=0.0.0.0 --port=8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 180s

  mysql:
    image: mysql:8.0
    container_name: class_mysql
    volumes:
      - dbdata:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: class_test
    ports:
      - "3306:3306"
    networks:
      - class

  node:
    image: node:18
    container_name: class_node
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
    networks:
      - class
    command: sh -c "npm install && npm run watch"
    depends_on:
      app:
        condition: service_healthy

  # cypress:
  #   image: cypress/included:12.16.0
  #   container_name: cypress_runner
  #   working_dir: /e2e
  #   volumes:
  #     - .:/e2e
  #   environment:
  #     - CYPRESS_baseUrl=http://app:8000 # Verbind met de class-container
  #   networks:
  #     - class
  #   depends_on:
  #     app:
  #       condition: service_healthy

  mailhog:
    image: mailhog/mailhog
    container_name: mailhog
    ports:
      - "1025:1025" # SMTP poort
      - "8025:8025" # Web interface poort
    networks:
      - class

volumes:
  dbdata:

networks:
  class:
