/node_modules
/public/storage
/public/js/app.js
/public/js/classy.js
/public/js/app.css
/public/js/preferedschedule.js
/public/css/app.css
/public/css/all.css
/public/mix-manifest.json
/public/images/icon_add.png
/public/images/icon_remove.png
/public/js/tmpl3/app.js
/public/css/tmpl3/components.css
/public/css/tmpl3/styles.css

/storage/*
/vendor
/.idea
/.theia
/database/ClassModel.mwb.bak
/database/app/
/database/database/
/wordpress/node_modules/
/wordpress/settings.php
.DS_Store
Homestead.json
Homestead.yaml
.env
/.phpunit.result.cache
/public/js/app.js.LICENSE.txt
/public/js/tmpl3/app.js.LICENSE.txt
/.phpunit.cache/test-results
/public/uploads/
/coverage/
/public/hot
/public/js/[0-9]*.js
/public/js/[0-9]*.js.LICENSE.txt
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
/authState.json
/cypress/
/tests/e2e/screenshots/
/logs/laravel.log
/.vscode/settings.json
/docs/node_modules/
/docs/.vitepress/cache/
/public/js/tmpl3/node_modules_vue-cal_*.js

# Generated component imports
/resources/js/class3/generated/
/.phpunit.cache/
