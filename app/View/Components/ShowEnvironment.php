<?php

namespace App\View\Components;

use Illuminate\View\Component;

class ShowEnvironment extends Component
{
    public $environment = null;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->environment = env("APP_ENV", "production");
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|string
     */
    public function render()
    {
        return view('components.show-environment');
    }
}
