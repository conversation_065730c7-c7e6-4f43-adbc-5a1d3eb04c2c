<?php

namespace App\Traits;

use Illuminate\Support\Facades\Log;

trait LocationOccupationTrait
{
    /**
     * Retrieves the availability status of the location based on the given parameters.
     *
     * The method checks the occupation arrays of the first location and the alternative location
     * to determine the availability status at the specified startDateTime and duration. The method
     * first checks if the first location is available by checking if its occupation array is empty.
     * If it is empty, the method returns "firstlocationisavailable". Otherwise, the method filters
     * the first location's occupation array to find any blocking events based on the startDateTime
     * and duration. If there are no blocking events, the method returns "firstlocationisavailable".
     *
     * If the first location is not available, the method checks if the alternative location is available
     * by checking if its occupation array is empty. If it is empty, the method returns
     * "firstlocationnotavailablealternativelocationisavailable". Otherwise, the method filters the
     * alternative location's occupation array to find any blocking events based on the startDateTime
     * and duration. If there are no blocking events, the method returns
     * "firstlocationnotavailablealternativelocationisavailable".
     *
     * If both the first location and the alternative location are not available, the method returns "bothlocationsnotavailable".
     *
     * @param array $firstLocationOccupation An array containing the events of the first location
     * @param array $altLocationOccupation An array containing the events of the alternative location
     * @param \DateTime $startDateTime The starting date and time of the requested time slot
     * @param int $duration The duration of the requested time slot in minutes
     * @return string The availability status: "firstlocationisavailable" if the first location is available,
     * "firstlocationnotavailablealternativelocationisavailable" if the first location is not available but the alternative
     * location is, "bothlocationsnotavailable" if both locations are not available
     */
    private function getLocationAvailability($firstLocationOccupation, $altLocationOccupation, $startDateTime, $duration): string
    {
        if (empty($firstLocationOccupation)) {
            return "firstlocationisavailable";
        }
        $firstLocationBlockingEvents = $this->getBlockingEventsOfLocation($firstLocationOccupation, $startDateTime, $duration);
        if (empty($firstLocationBlockingEvents)) {
            return "firstlocationisavailable";
        }
        // now check alt location occupation for availability
        if (empty($altLocationOccupation)) {
            return "firstlocationnotavailablealternativelocationisavailable";
        }
        $altLocationBlockingEvents = $this->getBlockingEventsOfLocation($altLocationOccupation, $startDateTime, $duration);
        if (empty($altLocationBlockingEvents)) {
            return "firstlocationnotavailablealternativelocationisavailable";
        }
        return "bothlocationsnotavailable";
    }

    /**
     * Retrieves the blocking events of a location based on the given parameters.
     *
     * The method filters the location array to include only the events that fall within the specified startDateTime
     * and duration. It then checks if the occupied time slots overlap with the requested time slot, identifying
     * the blocking events.
     *
     * @param array $locationArray An array containing the events of the location
     * @param \DateTime $startDateTime The starting date and time of the requested time slot
     * @param int $duration The duration of the requested time slot in minutes
     * @return array An array containing the blocking events
     */
    private function getBlockingEventsOfLocation($locationArray, $startDateTime, $duration)
    {
        $toDateTime = clone $startDateTime;
        $toDateTime->add(new \DateInterval('PT' . $duration . 'M'));
        $toTime = $toDateTime->format('H:i');
        // first split the occupation array to only contain the days that are relevant
        $locationArray = array_filter($locationArray, function ($event) use ($startDateTime, $toDateTime) {
            if (empty($event['datetime'])) {
                return false;
            }
            // make them comparable as numbers, remove the colons and dashes from the strings
            $itemDt = str_replace(['-', ':'], '', $event['datetime']);
            $startDt = str_replace(['-', ':'], '', $startDateTime->format('Y-m-d H:i'));
            $itemTo = str_replace(['-', ':'], '', $event['to_time']);
            $toDt = str_replace(['-', ':'], '', $toDateTime->format('Y-m-d H:i'));
            return ($itemDt >= $startDt && $itemTo <= $toDt);
        });
        // now check if the occupied timeslots overlap the requested timeslot, so looking for blocking events
        $locationArray = array_filter($locationArray, function ($event) use ($startDateTime, $toTime) {
            // (de >= es && de <= et) || (ds <= et && ds >= es)
            return (
                ($event['datetime'] >= $startDateTime->format('Y-m-d H:i') && $event['datetime'] <= $toTime) ||
                ($event['to_time'] <= $toTime && $event['to_time'] >= $startDateTime->format('Y-m-d H:i'))
            );
        });
        return $locationArray;
    }
}
