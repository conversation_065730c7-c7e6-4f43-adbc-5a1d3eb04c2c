<?php
namespace App\Traits;

use App\Http\Resources\StudentCourseDataResource;
use App\Models\Registration;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Studentgroup;
use App\Models\TrialcourseRelation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait RegistrationsTrait {
    /**
     * Get all course registrations for the student (active and inactive)
     * @param $studentId
     * @return StudentCourseDataResource
     */
    public function getCourseData($studentId)
    {
        Log::info("Getting student course data for student $studentId");
        $theSchoolYear = Schoolyear::getCurrentOrFuture();
        $s = Student::findOrFail($studentId);
        $sg = $s->studentgroups;
        // mark the courses for which the student receives tutoring in a group
        // see explanation in docs
        // https://docs.google.com/document/d/1PwYWEYzqFwRGY8juDEGNhyZ4NLL7jguNPpi-FmWWjIQ/edit#
        $coursesInStudentGroup = [];
        $studentGroupLinks = [];
        foreach ($sg as $studentgroup) {
            Log::info("Courses for stgroup: " . $studentgroup->id);
            if (isset($studentgroup->courses[0])) {
                $course = $studentgroup->courses[0];
                $coursesInStudentGroup[] = $course->id;
                $trialCoursesForThisCourse = TrialcourseRelation::trialcoursesForCourse($course->id);
                $coursesInStudentGroup = array_merge($coursesInStudentGroup, $trialCoursesForThisCourse);
                // keep track of which course in which student group
                $studentGroupLinks[$studentgroup->id] = $trialCoursesForThisCourse;
            } else {
                Log::warning("No courses for stgroup: " . $studentgroup->id);
            }
        }
        // remove doubles
        $coursesInStudentGroup = array_unique($coursesInStudentGroup);
        $registrations = $s->courses;
        foreach ($registrations as $key => $registration) {
            // add checklists (is relation of the pivot)
            Log::info("get reg: " . $registration->pivot->id);
            $R = Registration::findOrFail($registration->pivot->id);
            $registrations[$key]["checklists"] = $R->checklists;
            $registrations[$key]["incidental_price_ex_tax"] = $R->incidental_price_ex_tax;

            // now that we are here: mark the course if tutoring will be in a group
            // explicitly convert to a boolean
            $registrations[$key]['tutoringInStudentGroup'] = (array_search($registration->id, $coursesInStudentGroup) !== false);
            // check in which group the student is enlisted, if any
            if ($registrations[$key]['tutoringInStudentGroup']) {
                $query = DB::table("student_studentgroup as ssg")
                    ->select("s.id", "s.lastname")
                    ->leftJoin("course_student as cs", "ssg.studentgroup_id", "cs.student_id")
                    ->leftJoin("students as s", "ssg.studentgroup_id", "s.id")
                    ->where('ssg.student_id', '=',  $studentId);
                if ($registrations[$key]->is_trial_course) {
                    Log::debug("find student group for trial course: " . $registrations[$key]->id);
                    $query->where('as_trial_student', '=', $registrations[$key]->id);
                } else {
                    Log::debug("find student group for course: " . $registrations[$key]->id);
                    $query->where("cs.course_id", $registrations[$key]->id);
                }
                $studentgroup = $query->first();
                $registrations[$key]['studentgroupname'] = $studentgroup ? $studentgroup->lastname : '';
                $registrations[$key]['studentgroupid'] = $studentgroup ? $studentgroup->id : 0;
                // get names and ids of all students that are in this group
                if ($studentgroup) {
                    $studentGroupObj = Studentgroup::find($studentgroup->id);
                    $registrations[$key]['studentgroupstudents'] = $studentGroupObj->students;
                }
            }
            // does the registration have checklists, and if so: have they all been completed?
            $checklists = $registration->checklists;
            $allComplete = true;
            foreach ($checklists as $checklist) {
                if(!$checklist->isComplete()) {
                    $allComplete = false;
                }
            }
            $registrations[$key]['allchecklistscomplete'] = $allComplete;
            // Add number of appointments in the current planning.
            // Function getNrOfAppointments will choose the timetable of the current school year,
            // or the next if we are between school years (summer holiday).
            // If it's a student group, the appointments are registered with the group
            if ($registrations[$key]['tutoringInStudentGroup']) {
                Log::info("checking events for studentgroup: " . $registrations[$key]['studentgroupid']);
                if ($registrations[$key]['studentgroupid'] > 0) {
                    $sg = Studentgroup::find($registrations[$key]['studentgroupid']);
                    $regOfGroup = Registration::find($sg->courses[0]->pivot->id);
                    Log::debug("Get reg for studentgroup: " . $sg->id);
                    $registrations[$key]['registrationIdToEdit'] = $regOfGroup->id;
                    // Get attendance for this registration
                    $registrations[$key]["attendance"] = $regOfGroup->getAttendanceCount($studentId);
                }
            } else {
                $regOfStudent = Registration::find($registration->pivot->id);
                $registrations[$key]['registrationIdToEdit'] = $regOfStudent->id;
                // Get attendance for this registration
                $registrations[$key]["attendance"] = $regOfStudent->getAttendanceCount();
            }
            // specify recurrence-option
            $registrations[$key]['course'] = $R->course;
            if ($R->course->is_trial_course) {
                $registrations[$key]['name'] = ucfirst(trans('generic.triallesson')) . ": " . $registrations[$key]['name'];
            }
            $registrations[$key]['recurrenceoption'] = $registrations[$key]['course']->recurrenceoption;
            $registrations[$key]['actualSchoolYear'] = $theSchoolYear;
        }
        return new StudentCourseDataResource($registrations);
    }
}
