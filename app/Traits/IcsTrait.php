<?php

namespace App\Traits;

use App\Models\Event;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait IcsTrait
{

    /**
     * Gets 1 ICS definition for multiple series
     * @param array|string $seriesUids
     * @return array
     */
    public static function getICSForMultipleSeries($seriesUids=[]) {
        Log::info("--------> assembling ICS parts for multiple (" . count($seriesUids) . ") event series");

        if (is_string($seriesUids)) {
            $seriesUids = array($seriesUids);
        }
        $retArr = [];
        $onlyEvents = false;
        foreach ($seriesUids as $seriesUid) {
            try {
                // use recursive, it only merges the events-subarray after the first loop
                $retArr = array_merge_recursive($retArr, Event::getICSForSeries($seriesUid, $onlyEvents));
            } catch (\Exception $e) {
                Log::error("error retrieving event series $seriesUid");
                Log::error($e->getMessage());
                // on error don't send anything
                return [];
            }
            // from now on, just add the extra events
            $onlyEvents = true;
        }
        // possibly, no future events found, in that case also discard the header an footer section
        if ( empty($retArr["event"]) || (count($retArr["event"]) === 0) ) {
            Log::debug("no events found");
            return [];
        } else {
            Log::debug(count($retArr["event"]) . " events found");
        }

        return $retArr;
    }

    /**
     * Gets the ICS to be send to a user.
     * @param $seriesUID String: unique id of the series
     * @param $onlyEvents Boolean: use the function for extra events, omits the header and timezone section
     *
     * @return array ICS parts or empty array if no events where found
     * @throws \Exception
     */
    private static function getICSForSeries($seriesUID, $onlyEvents=false) {
        $response = [];

        Log::info("----> assembling ICS parts for event series $seriesUID");

        // get all events for this series, but only for future dates
        $events = Event::where('caluniqueid', '=', $seriesUID)
            ->where('datetime', '>', DB::raw('NOW()'))
            ->orderBy('datetime')
            ->get();

        if(count($events) > 0) {
            Log::info("found " . count($events) ." events for series $seriesUID");
            $analysedEvents = Event::analyseEventsInSeries($events);
            $stdEvents = $analysedEvents[0];
            $sepEvents = $analysedEvents[1];

            Log::info("Found " . count ($sepEvents) . " event(s) that differ(s) from the default.");

            if(!$onlyEvents) {
                // Head section
                $response["header"] = Event::getICSHeader();

                // Timezone
                $response["timezonedef"] = Event::gettimezoneDefenition();
            }

            // /////////////////////////////////////////////
            // First get the common recurrence
            // /////////////////////////////////////////////

            // get the recurrence of the assoc course
            // because of the sortBy, $stdEvents[0] will be the smalest date (start)
            if (count($stdEvents) > 0) {
                $course = $stdEvents[0]->course;
                $timespan = $stdEvents[0]->timespan;
            } else {
                $course = $sepEvents[0]->course;
                $timespan = $sepEvents[0]->timespan;
            }
            $recurrence = $course->recurrenceoption;
            Log::info("recurrence option for this course: " . $recurrence->description);
            $occFreq = [
                "week" => "WEEKLY",
                "two weeks" => "WEEKLY",
                "day" => "DAILY",
                "month" => "MONTHLY"
            ];
            $occInterval = [
                "week" => "INTERVAL=1",
                "two weeks" => "INTERVAL=2",
                "day" => "INTERVAL=1",
                "month" => "INTERVAL=1"
            ];

            // Other fields
            $uid = "UID:$seriesUID";
            $summary = "SUMMARY:" . $course->name;
            $location = "LOCATION:" . Auth::user()->domain->name . ", " . Auth::user()->domain->city;
            $dtStamp = "DTSTAMP:" . gmdate("Ymd\THis\Z");
            $transp = "TRANSP:OPAQUE";

            // STD events, series
            if (count($stdEvents) > 0) {
                // RRULE (recurrence)
                if (count($stdEvents) == 1) {
                    $lastoccurence = "COUNT=1";
                } elseif (count($stdEvents) > 1) {
                    $until = str_replace(" ", "T", preg_replace("/\-|:/", "", last($stdEvents)->datetime));
                    $lastoccurence = "UNTIL=" . $until;
                }

                $startDateTimeObj = \DateTime::createFromFormat('Y-m-d H:i:s', $stdEvents[0]->datetime);
                $byday = "BYDAY=" . strtoupper(substr($startDateTimeObj->format('D'), 0, 2));
                $rrule = "RRULE:FREQ=" . $occFreq[$recurrence->per_interval] . ";" . $occInterval[$recurrence->per_interval] . ";" . $lastoccurence . ";" . $byday;
                $startDateTime = str_replace(" ", "T", preg_replace("/\-|:/", "", $stdEvents[0]->datetime));
                // start-end datetime
                $dtStart = "DTSTART;TZID=Europe/Amsterdam:" . $startDateTime;
                $dts = \DateTime::createFromFormat("Y-m-d H:i:s", $stdEvents[0]->datetime);
                // This may be Dutch or English but should always "minutes"
                $tspan = str_replace("minuten", "M", $timespan);
                $tspan = str_replace("minutes", "M", $tspan);
                $dte = $dts->add(new \DateInterval("PT" . str_replace(" ", "", $tspan)));
                $dtEnd = "DTEND;TZID=Europe/Amsterdam:" . $dte->format("Ymd\THis");
                $created = "CREATED:" . \DateTime::createFromFormat("Y-m-d H:i:s", $stdEvents[0]->created_at)->format("Ymd\THis\Z");
                // sequence
                $sequence = "SEQUENCE:" . $stdEvents[0]->sequence;

                $response["event"][] = [
                    "BEGIN:VEVENT",
                    $dtStart,
                    $dtEnd,
                    $rrule,
                    $uid,
                    $summary,
                    $location,
                    $created,
                    $dtStamp,
                    $sequence,
                    $transp,
                    "END:VEVENT"
                ];
            }
            // /////////////////////////////////////////////
            // Now handle the exceptions to the rule, if any
            // /////////////////////////////////////////////
            foreach ($sepEvents as $sepEvent) {
                $orgStartDateTime = str_replace(" ", "T", preg_replace("/\-|:/", "", $sepEvent->original_datetime));
                // this indicates which event in the recurring series will be replaced
                $recId = "RECURRENCE-ID;TZID=Europe/Amsterdam:" . $orgStartDateTime;
                // start-end datetime
                $startDateTime = str_replace(" ", "T", preg_replace("/\-|:/", "", $sepEvent->datetime));
                $dtStart = "DTSTART;TZID=Europe/Amsterdam:" . $startDateTime;
                $dts = \DateTime::createFromFormat("Y-m-d H:i:s", $sepEvent->datetime);
                // This may be Dutch or English but should always "minutes"
                $tspan = str_replace("minuten", "M", $timespan);
                $tspan = str_replace("minutes", "M", $tspan);
                $dte = $dts->add(new \DateInterval("PT" . str_replace(" ", "", $tspan)));
                $dtEnd = "DTEND;TZID=Europe/Amsterdam:" . $dte->format("Ymd\THis");
                $created = "CREATED:" . \DateTime::createFromFormat("Y-m-d H:i:s", $sepEvent->created_at)->format("Ymd\THis\Z");
                //
                $sequence = "SEQUENCE:" . $sepEvent->sequence;
                $response["event"][] = [
                    "BEGIN:VEVENT",
                    $dtStart,
                    $dtEnd,
                    $recId,
                    "UID:$seriesUID", // reusing the UID is officially not valid, but seems to work
                    $summary,
                    $location,
                    $created,
                    $dtStamp,
                    $sequence,
                    $transp,
                    "END:VEVENT"
                ];
            }
            if(!$onlyEvents) {
                // Footer
                $response["footer"][] = "END:VCALENDAR";
            }
        } else {
            Log::info("No (future) events found for series $seriesUID");
        }
        Log::debug("send response");
        return $response;
    }

    /**
     * This function makes sure all events in the set are
     * some multiplication of the first start datetime plus the timespan
     * all other events will be returned in $sepEvents (returnArray[1]) so
     * they can be handled differently in the ICS file
     * @param $events
     * @return array
     */
    private static function analyseEventsInSeries($events) {
        $sepEvents = []; $stdEvents = [];

        foreach ($events as $event) {
            if ($event->original_datetime == $event->datetime) {
                // put it on the heap: stdEvents
                $stdEvents[] = $event;
            } else {
                // needs seperate VEVENT section (exception to the RRULE)
                $sepEvents[] = $event;
            }
        }

        return [$stdEvents, $sepEvents];
    }


    /**
     * Get ICS format for a series and return as string
     * @param $seriesUID
     * @return string
     */
    public static function getICSForSeriesAsString($IcsArray) {
        $resulString = '';
        if (!empty($IcsArray["header"])) {
            $headerSection = $IcsArray["header"];
            $resulString .= implode("\r\n", $headerSection);
        }
        if (!empty($IcsArray["timezonedef"])) {
            $tzdef = $IcsArray["timezonedef"];
            $resulString .= "\r\n" . implode("\r\n", $tzdef);
        }
        if (!empty($IcsArray["event"])) {
            $eventSections = $IcsArray["event"];
            foreach ($eventSections as $event) {
                $resulString .= "\r\n" . implode("\r\n", $event);
            }
        }
        if (!empty($IcsArray["footer"])) {
            $footerSection = $IcsArray["footer"];
            $resulString .= "\r\n" . implode("\r\n", $footerSection);
        }
        return $resulString;
    }

    /**
     * Gets the definition for the timezone to be used in ICS file
     * @return array
     */
    private static function gettimezoneDefenition() {
        return [
            "BEGIN:VTIMEZONE",
            "TZID:Europe/Amsterdam",
            "X-LIC-LOCATION:Europe/Amsterdam",
            "BEGIN:DAYLIGHT",
            "TZOFFSETFROM:+0100",
            "TZOFFSETTO:+0200",
            "TZNAME:CEST",
            "DTSTART:19700329T020000",
            "RRULE:FREQ=YEARLY;BYMONTH=3;BYDAY=-1SU",
            "END:DAYLIGHT",
            "BEGIN:STANDARD",
            "TZOFFSETFROM:+0200",
            "TZOFFSETTO:+0100",
            "TZNAME:CET",
            "DTSTART:19701025T030000",
            "RRULE:FREQ=YEARLY;BYMONTH=10;BYDAY=-1SU",
            "END:STANDARD",
            "END:VTIMEZONE"
        ];
    }

    /**
     * @param string $method (can be PUBLISH (default) or REQUEST)
     * @return array
     */
    private static function getICSHeader($method = "PUBLISH") {
        // Head section
        $response[] = "BEGIN:VCALENDAR";
        $response[] = "PRODID:-//Scolavisa VOF//";
        $response[] = "    CLASS Course Learning Administration Support System//EN";
        $response[] = "VERSION:2.0";
        $response[] = "METHOD:" . $method;
        return $response;
    }

    /**
     * Maybe at some point we need to be able to download the ICS file directly
     * @param $filename
     * @param string $icsAsString
     */
    public static function createICSFile($filename, $icsAsString = '') {
        header("Content-Type: text/Calendar; charset=utf-8");
        header("Content-Disposition: inline; filename={$filename}");
        echo $icsAsString;

    }

}