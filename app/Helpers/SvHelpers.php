<?php
/**
 * Helper for script timing when debugging performance issues
 * @example
 *  $rustart = getrusage();
 *  {code}
 *  $ru1 = getrusage();
 *  Log::debug("This query process used " . rutime($ru1, $rustart, "utime") . " ms for its computations");
 *  Log::debug("It spent " . rutime($ru1, $rustart, "stime") . " ms in system calls");
 * @param $ru
 * @param $rus
 * @param $index
 * @return float|int
 */
function rutime($ru, $rus, $index) {
    return ($ru["ru_$index.tv_sec"]*1000 + intval($ru["ru_$index.tv_usec"]/1000))
        -  ($rus["ru_$index.tv_sec"]*1000 + intval($rus["ru_$index.tv_usec"]/1000));
}

/**
 * Guess the lastname in a compound name by returning everyting after the last space
 * @param $namefield
 * @return mixed
 */
function getLastnameFromAssembledField($namefield) {
    $parts = explode(" ", $namefield);
    return array_pop($parts);
}

/**
 * Guess the preposition in a compound name
 * @param $namefield string containing a lastname and preposition
 * @return string
 */
function getPrepositionFromAssembledField($namefield) {
    // get everyting after the last space
    $parts = explode(" ", $namefield);
    // drop the last element
    array_pop($parts);
    // assemble preposiotion
    return implode(" ", $parts);
}

/**
 * finds out if a stroing contains a serialized variable
 * @param $data
 * @param bool $strict
 * @return bool
 */
function is_serialized($data, $strict = true) {
    // if it isn't a string, it isn't serialized.
    if (!is_string($data)) {
        return false;
    }
    $data = trim($data);
    if ('N;' == $data) {
        return true;
    }
    if (strlen($data) < 4) {
        return false;
    }
    if (':' !== $data[1]) {
        return false;
    }
    if ($strict) {
        $lastc = substr($data, -1);
        if (';' !== $lastc && '}' !== $lastc) {
            return false;
        }
    } else {
        $semicolon = strpos($data, ';');
        $brace = strpos($data, '}');
        // Either ; or } must exist.
        if (false === $semicolon && false === $brace) {
            return false;
        }
        // But neither must be in the first X characters.
        if (false !== $semicolon && $semicolon < 3) {
            return false;
        }
        if (false !== $brace && $brace < 4) {
            return false;
        }
    }
    $token = $data[0];
    switch ($token) {
        case 's' :
            if ($strict) {
                if ('"' !== substr($data, -2, 1)) {
                    return false;
                }
            } elseif (false === strpos($data, '"')) {
                return false;
            }
        // or else fall through
        case 'a' :
        case 'O' :
            return (bool)preg_match("/^{$token}:[0-9]+:/s", $data);
        case 'b' :
        case 'i' :
        case 'd' :
            $end = $strict ? '$' : '';
            return (bool)preg_match("/^{$token}:[0-9.E-]+;$end/", $data);
    }
    return false;
}

/**
 * Determine age today by means of a birth date
 * @param string $birthDate
 * @return int
 * @throws Exception
 */
function calculateAge($birthDate = '') {
    if ($birthDate == '') {
        return 0;
    }
    $tz = new \DateTimeZone('Europe/Brussels');
    return \DateTime::createFromFormat('Y-m-d', $birthDate, $tz)
        ->diff(new \DateTime('now', $tz))
        ->y;
}

/**
 * wrapper for the PHP email filter
 */
function isValidEmail($email_a) {
    return (filter_var($email_a, FILTER_VALIDATE_EMAIL) !== FALSE);
}

/**
 * @param $hexColor // must include the #
 * @return string
 */
function getContrastColor($hexColor) {
    if( strlen($hexColor) !== 7 ) {
        $hexColor = rgba2hex($hexColor);
    }
    //////////// hexColor RGB
    $R1 = hexdec(substr($hexColor, 1, 2));
    $G1 = hexdec(substr($hexColor, 3, 2));
    $B1 = hexdec(substr($hexColor, 5, 2));

    //////////// Black RGB
    $blackColor = "#000000";
    $R2BlackColor = hexdec(substr($blackColor, 1, 2));
    $G2BlackColor = hexdec(substr($blackColor, 3, 2));
    $B2BlackColor = hexdec(substr($blackColor, 5, 2));

    //////////// Calc contrast ratio
    $L1 = 0.2126 * pow($R1 / 255, 2.2) +
          0.7152 * pow($G1 / 255, 2.2) +
          0.0722 * pow($B1 / 255, 2.2);

    $L2 = 0.2126 * pow($R2BlackColor / 255, 2.2) +
          0.7152 * pow($G2BlackColor / 255, 2.2) +
          0.0722 * pow($B2BlackColor / 255, 2.2);

    if ($L1 > $L2) {
        $contrastRatio = (int)(($L1 + 0.05) / ($L2 + 0.05));
    } else {
        $contrastRatio = (int)(($L2 + 0.05) / ($L1 + 0.05));
    }

    //////////// If contrast is more than 5, return black color
    if ($contrastRatio > 5) {
        return 'black';
    } else { //////////// if not, return white color.
        return 'white';
    }
}

/**
 * converts a string like rgba(10,12,155,0.5) to a corresponding hex string
 * @param $string
 * @return string includes the # sign
 */
function rgba2hex($rgbastring) {

    $regex = '#\((([^()]+|(?R))*)\)#';
    if (preg_match_all($regex, $rgbastring ,$matches)) {
        $rgba = explode(',', implode(' ', $matches[1]));
    } else {
        $rgba = explode(',', $rgbastring);
    }

    $rr = dechex($rgba['0']);
    $gg = dechex($rgba['1']);
    $bb = dechex($rgba['2']);
    $aa = '';

    if (array_key_exists('3', $rgba)) {
        $aa = dechex($rgba['3'] * 255);
    }

    return strtoupper("#$aa$rr$gg$bb");
}

/**
 * Get the index of the largest value in the incoming array
 * If two values are the same and biggest, it returns the first
 * @param array $input
 * @return int
 */
function getIndexOfBiggestValueInArray($input) {
    if (count($input) === 0) {
        return -1;
    } else {
        return array_keys($input, max($input))[0];
    }
}
