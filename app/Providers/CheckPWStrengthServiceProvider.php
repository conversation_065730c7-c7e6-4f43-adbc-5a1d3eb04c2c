<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;


class CheckPWStrengthServiceProvider extends ServiceProvider {
    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot() {
        // error message is in the validator translation file
        Validator::extend('pwStrong', function ($attribute, $value, $parameters, $validator) {
            return $this->checkStrength($value);
        });
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register() {
        //
    }

    /**
     * Checks the strength of a password.
     * - It needs to have caps and lowercase characters
     * - It needs to be at least 8 characters long
     * - it needs to have numbers or special chars, or both
     * @param $pass string - password to be checked
     * @return bool
     */
    function checkStrength($pass = '') {
        $minSize = 8;
        $length = strlen($pass);
        $hasCaps = preg_match('/[A-Z]/', $pass);
        $hasLC = preg_match('/[a-z]/', $pass);
        $hasNumbers = preg_match('/[0-9]/', $pass);
        $hasSpecChars = preg_match('/[\'\/~`\!@#\$%\^&\*\(\)_\-\+=\{\}\[\]\|;:"\<\>,\.\?\\\]/', $pass);
        return (($length >= $minSize && $hasCaps && $hasLC) && ($hasNumbers || $hasSpecChars));
    }
}
