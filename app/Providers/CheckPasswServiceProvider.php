<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class CheckPasswServiceProvider extends ServiceProvider {
    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot() {
        // error message is in the validator translation file
        Validator::extend('pwCheck', function ($attribute, $value, $parameters, $validator) {
            return $this->checkPw($value);
        });
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register() {

    }

    private function checkPw($passwd) {
        // check if the user provided the correct old password
        $userhash = Auth::user()->getAuthPassword();
        return (Hash::check($passwd, $userhash));
    }
}
