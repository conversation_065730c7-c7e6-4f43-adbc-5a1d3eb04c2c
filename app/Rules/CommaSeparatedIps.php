<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CommaSeparatedIps implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->passes($attribute, $value)) {
            $fail($this->message());
        }
    }

    public function passes($attribute, $value)
    {
        $ips = explode(',', $value);
        foreach ($ips as $ip) {
            if (!filter_var(trim($ip), FILTER_VALIDATE_IP)) {
                return false;
            }
        }
        return true;
    }

    public function message()
    {
        return 'The :attribute must be a (comma-separated list of) valid IP address(es).';
    }
}
