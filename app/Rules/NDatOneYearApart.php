<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Scolavisa\scolib\Ndat2Mydat;

/**
 * Class NDatOneYearApart
 * Determine if the year part of a date in NDat format is 1 less or equal to another dates year part
 * This is created for validating school years, they should start in one year and end in the same or the next.
 * @package App\Rules
 */
class NDatOneYearApart implements Rule
{
    protected $extraParam;  // startdate
    protected $inValue;     // enddate

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($param)
    {
        $this->extraParam = $param;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $this->inValue = $value;
        $endDate = new \DateTime(Ndat2Mydat::getMydat($value));
        $startDate = new \DateTime(Ndat2Mydat::getMydat($this->extraParam));
        $startYear = intval($startDate->format('Y'));
        $endYear = intval($endDate->format('Y'));
        return $endYear == $startYear || $endYear == ($startYear+1);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return str_replace(
            '_',
            ' ',
            __('validation.custom.ndat_year_apart', ["field1" => $this->inValue, "field2" => $this->extraParam])
        );
    }
}
