<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Scolavisa\scolib\Ndat2Mydat;

/**
 * Class NDatGreaterEqual
 * Determine if a date in NDat format is greater or equal than another date
 * @package App\Rules
 */
class NDatGreaterEqual implements Rule
{
    protected $extraParam;
    protected $inValue;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($param)
    {
        $this->extraParam = $param;
    }
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $this->inValue = $value;
        $date1 = new \DateTime(Ndat2Mydat::getMydat($value));
        $date2 = new \DateTime(Ndat2Mydat::getMydat($this->extraParam));
        return $date1 >= $date2;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return str_replace(
            '_',
            ' ',
            __('validation.custom.ndat_greater_equal', ["field1" => $this->inValue, "field2" => $this->extraParam])
        );
    }
}
