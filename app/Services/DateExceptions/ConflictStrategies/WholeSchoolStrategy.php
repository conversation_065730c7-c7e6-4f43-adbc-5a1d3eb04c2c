<?php

namespace App\Services\DateExceptions\ConflictStrategies;
use App\Models\DateException;

class WholeSchoolStrategy extends BaseStrategy {
    public function hasConflict(DateException $dateException, object $event): bool {
        if (empty($dateException->tutor_id) && empty($dateException->location_id)) {
            return true;
        }
        return $this->next($dateException, $event);
    }
}
