<?php

namespace App\Services\DateExceptions\ConflictStrategies;

use App\Models\DateException;

class LocationStrategy extends BaseStrategy {
    public function hasConflict(DateException $dateException, object $event): bool {
        if (!$dateException->location_id) {
            return $this->next($dateException, $event);
        }
        return $event->location_id === $dateException->location_id 
            || $this->next($dateException, $event);
    }
}

