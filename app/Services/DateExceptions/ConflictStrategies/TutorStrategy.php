<?php

namespace App\Services\DateExceptions\ConflictStrategies;
use App\Models\DateException;

class TutorStrategy extends BaseStrategy {
    public function hasConflict(DateException $dateException, object $event): bool {
        if (!$dateException->tutor_id) {
            return $this->next($dateException, $event);
        }
        return $event->tutor_id === $dateException->tutor_id
            || $this->next($dateException, $event);
    }
}
