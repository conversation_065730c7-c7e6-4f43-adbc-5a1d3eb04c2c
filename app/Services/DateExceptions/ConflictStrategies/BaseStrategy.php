<?php

namespace App\Services\DateExceptions\ConflictStrategies;
use App\Models\DateException;

abstract class BaseStrategy implements ConflictStrategyInterface {
    protected ?ConflictStrategyInterface $nextStrategy = null;
    
    public function setNext(ConflictStrategyInterface $next): ConflictStrategyInterface {
        $this->nextStrategy = $next;
        return $next;
    }
    
    protected function next(DateException $dateException, object $event): bool {
        if ($this->nextStrategy) {
            return $this->nextStrategy->hasConflict($dateException, $event);
        }
        return false;
    }
}
