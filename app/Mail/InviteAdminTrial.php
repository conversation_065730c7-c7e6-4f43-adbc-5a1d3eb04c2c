<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class InviteAdminTrial extends Mailable {
    use Queueable, SerializesModels;

    private $email;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($to) {
        $this->email = $to;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build() {
        Log::info("preparing email invitation for new Trial account: $this->email");

        $this->subject(trans('email.trialinvitesubject'))
        ->view( 'email.sendgenericemail')->with([
            'body'  => $this->bodyHtml(),
            'title' => trans('email.trialinvitetitle')
        ]);
        return $this->withSymfonyMessage(function ($message) {
            // $message contains the full email record
            // Log::debug("mailer response: $message");
        });
    }



    private function bodyHtml() {
        return "<div><h1>".trans('generic.welcome')."</h1></div>";
    }
}
