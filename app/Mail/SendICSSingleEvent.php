<?php

namespace App\Mail;

use App\Models\Emaillogentry;
use App\Models\Student;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use function Sodium\add;
use theantichris\iCalendarCreator\iCalendar;
use theantichris\iCalendarCreator\iCalendarCreator;
use theantichris\iCalendarCreator\Location;

/**
 * Class SendICSSingleEvent
 *
 * We only ever send an email to 1 student at a time,
 * but there may be two emailaddresses (e.g. the parents of a student)
 * The event may also be a studentgroup event. In that case we need to
 * find out which student we mean by looking up the emailaddress
 *
 * @package App\Mail
 */
class SendICSSingleEvent extends Mailable {
	use Queueable, SerializesModels;

	public $event;
	public $mailtext;
	public $emailaddress;
	public $student;

	/**
	 * Create a new message instance.
	 *
	 * @param $event
	 */
	public function __construct($event, $emailaddress, $mailtext='') {
		$this->event = $event;
		$this->mailtext = $mailtext;
		$this->emailaddress = $emailaddress;
		$student = $event->student;
		// if its a studentgroup, find out which of the students in this group we mean
		if ($student->firstname === '-' && $student->date_of_birth === '1800-01-01') {
		    Log::info("This is a studentgroup event. getting student for emailaddress: $emailaddress");
		    $theRealStudent = DB::table("studentcontacts")->select("student_id")->where('value', '=', $emailaddress)->first();
		    $this->student = Student::findOrFail($theRealStudent->student_id);
        } else {
		    $this->student = $student;
        }
	}

    /**
     * Build the message.
     *
     * @return $this
     * @throws \Exception
     */
	public function build() {
		Log::debug("Initiating ICS send mail");

		$course     = $this->event->course;
		$tutor      = $this->event->tutor;
		$location   = $this->event->location;

		$endDateTime    = new \DateTime($this->event->datetime);
		$endDateTime->add(\DateInterval::createFromDateString($this->event->timespan));

		$eventName = $course->name;
		$eventStart = new \DateTime($this->event->datetime);
		$eventEnd = $endDateTime;
		$eventDescription = $course->name . " " . __('generic.by') . " " . $tutor->name . " " . __('generic.in') . " " . $location->name;
		$eventLocation = new Location(
            Auth::user()->domain->name,
            Auth::user()->domain->address1,
            Auth::user()->domain->address2,
            Auth::user()->domain->city,
			'',
            Auth::user()->domain->zip
        );
		$organizerName = Auth::user()->domain->name;
		$organizerEmail = Auth::user()->domain->email;

		// create ICS file
		$calenderEvent = new iCalendar($eventName, $eventStart, $eventEnd, $eventDescription, $eventLocation, $organizerName, $organizerEmail);
		$icsFile = iCalendarCreator::icsFileAsString($calenderEvent, $this->event->caluniqueid);
		$this->subject(ucfirst(__('generic.yourappointmentwith', ['customername' => Auth::user()->domain->name])))
		     ->view( 'email.sendicssingleevent')->with([
				'title'=>ucfirst(__('generic.yourappointmentwith', ['customername' => Auth::user()->domain->name])),
				'student' => $this->student
			])->attachData($icsFile, 'appointment.ics', [
				'mime' => 'text/calendar',
			]);

		return $this->withSymfonyMessage(function ($message) use ($icsFile, $eventDescription, $calenderEvent) {
		    // save the attachment fopr later inspection through the log
            Log::info("Sending email succeeded, logging info in emaillogentries table. Storing attachment");
		    $unique = uniqid($this->student->id . "_", true);
            Storage::disk('local')->put("attachments/" . $unique . "_appointment.ics", $icsFile);
            // log the mailsession
            $emaillog = new Emaillogentry();
            $emaillog->domain_id = Auth::user()->domain->id;
            $emaillog->to = $this->emailaddress;
            $emaillog->from = "CLASS mailer, " . Auth::user()->domain->name;
            $emaillog->body = "ICS mail for $eventDescription from " . $calenderEvent->getEventStart();
            $emaillog->subject = ucfirst(__('generic.yourappointmentwith', ['customername' => Auth::user()->domain->name]));
            $emaillog->studentids = $this->student->id;
            $emaillog->attachments = $unique . "_appointment.ics";
            $emaillog->save();
		});
	}
}
