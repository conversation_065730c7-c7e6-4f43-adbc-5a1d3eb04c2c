<?php

namespace App\Mail;

use App\Models\Emaillogentry;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class PreferenceInvitation extends Mailable {

    use Queueable, SerializesModels;

    public $title;
    public $from;
    public $subject;
    public $bodyHtml;
    public $uniqueToken = null;

    /**
     * Create a new message instance.
     *
     * @param $from
     * @param $subject
     * @param $bodyHtml
     * @param $title
     * @param int $studentId
     */
    public function __construct($from, $subject, $bodyHtml, $title, $uniqueToken = null) {
        $this->from[] = ["address" => $from, "name" => $from];
        $this->title = $title;
        $this->subject = $subject;
        $this->bodyHtml = $bodyHtml;
        $this->uniqueToken = $uniqueToken;
    }

    /**
     * Build the message.
     *
     * @return $this
     * @throws \Exception
     */
    public function build() {
        try {
            Log::info("sending mail");
            Log::info("token for log: " . $this->uniqueToken);
            
            $this->subject($this->subject)
                ->view('email.sendgenericemail')
                ->with([
                    'body'  => $this->bodyHtml,
                    'title' => $this->title
                ]);

            $emaillog = Emaillogentry::query()
                ->where('unique_token', $this->uniqueToken)
                ->first();

            if (!$emaillog) {
                throw new \Exception("email log entry not found for token: " . $this->uniqueToken);
            }

            return $this->withSymfonyMessage(function ($message) use ($emaillog) {
                try {
                    // Als we hier komen zonder exception, dan is het verzenden waarschijnlijk gelukt
                    $emaillog->status = 'sent';
                    $emaillog->save();
                    
                    Log::info("Email verzendstatus: sent");
                } catch (\Exception $e) {
                    $emaillog->status = 'failed';
                    $emaillog->log = $emaillog->log . "|" . $e->getMessage();
                    $emaillog->save();
                    
                    Log::error("Email verzenden mislukt: " . $e->getMessage());
                }
            });
        } catch (\Exception $e) {
            if ($emaillog) {
                $emaillog->status = 'failed';
                $emaillog->log = $emaillog->log . "|" . $e->getMessage();
                $emaillog->save();
            }
            
            Log::error("Email verzenden mislukt: " . $e->getMessage());
            throw $e;
        }
    }

}
