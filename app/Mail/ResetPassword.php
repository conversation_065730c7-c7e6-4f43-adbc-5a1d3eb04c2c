<?php

namespace App\Mail;

use App\Models\Emaillogentry;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ResetPassword extends Mailable
{
    use Queueable, SerializesModels;

    private $domainid;
    private $domainname;
    private $newPassword;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($newPassword, $domain)
    {
        $this->newPassword  = $newPassword;
        $this->domainid     = $domain->id;
        $this->domainname   = $domain->name;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        Log::info("preparing email for new password");
        $this->subject(ucfirst(trans('email.newpassword')))
            ->view( 'email.resetpassword')->with([
                'title' => trans('generic.yournewpassword'),
                'newPW' => $this->newPassword
            ]);

        return $this->withSymfonyMessage(function ($message) {
            // $message contains the full email record. comment out in production
            // Log::debug("mailer response: $message");
            // log the mail session
            $emaillog = new Emaillogentry();
            $emaillog->domain_id = $this->domainid;
            $emaillog->to = $this->to[0]["address"];
            $emaillog->from = "CLASS mailer $this->domainname";
            $emaillog->body = "New password send";
            $emaillog->subject = ucfirst(trans('email.newpassword'));
            $emaillog->studentids = 0;
            $emaillog->save();
        });
    }
}
