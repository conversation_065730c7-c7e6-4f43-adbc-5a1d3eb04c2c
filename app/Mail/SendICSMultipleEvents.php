<?php

namespace App\Mail;

use App\Models\Emaillogentry;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SendICSMultipleEvents extends Mailable {
    use Queueable, SerializesModels;

    private $targetEmail;
    private $mailText;
    private $seriesUids;
    private $studentName;
    private $studentId;
    private $ics;

    /**
     * Create a new message instance.
     * A message is always just 1 student
     *
     * @param $emailaddress String: emailaddress to send the email to
     * @param $mailText     String: text to use in the email
     * @param $seriesUids   array: Series to compile a ICS file for
     *
     * @return void
     */
    public function __construct($targetEmail, $mailText, $seriesUids, $studentId, $studentName, $ics) {
        $this->targetEmail  = $targetEmail;
        $this->mailText     = $mailText;
        $this->seriesUids   = $seriesUids;
        $this->studentId    = $studentId;
        $this->studentName  = $studentName;
        $this->ics          = $ics;
    }

    /**
     * Build the message.
     * Create 1 email with an ICS file with all appointments in it
     *
     * @return $this
     * @throws \Exception
     */
    public function build() {

        Log::debug("Initiating ICS send mail multiple events for student ID $this->studentId");

        $this->subject(ucfirst(__('generic.yourappointmentwith', ['customername' => Auth::user()->domain->name])))
            ->view( 'email.sendicsmultipleevent')->with([
                'title'         => ucfirst(__('generic.yourappointmentwith', ['customername' => Auth::user()->domain->name])),
                'studentName'   => $this->studentName,
                'mailtext'      => $this->mailText
            ])->attachData($this->ics, 'appointments.ics', [
                'mime' => 'text/calendar',
            ]);

        return $this->withSymfonyMessage(function ($message) {
            // save the attachment for later inspection through the log
            Log::info("Sending email succeeded, logging info in emaillogentries table. Storing attachment");
            $unique = uniqid($this->studentId . "_", true);
            Storage::disk('local')->put("attachments/" . $unique . "_appointment.ics", $this->ics);
            // log the mailsession
            $emaillog = new Emaillogentry();
            $emaillog->domain_id = Auth::user()->domain->id;
            $emaillog->to = $this->targetEmail;
            $emaillog->from = "CLASS mailer, " . Auth::user()->domain->name;
            $emaillog->body = $this->mailText;
            $emaillog->subject = ucfirst(__('generic.yourappointmentwith', ['customername' => Auth::user()->domain->name]));
            $emaillog->studentids = $this->studentId;
            $emaillog->attachments = $unique . "_appointment.ics";
            $emaillog->save();
        });

    }
}
