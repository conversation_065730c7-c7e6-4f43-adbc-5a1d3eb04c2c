<?php

namespace App\Mail;

use App\Models\Emaillogentry;
use App\Models\Registration;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use function GuzzleHttp\Psr7\str;

class RequestSignature extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $registration;
    public $salutation;
    public $replacementText;

    /**
     * RequestSignature constructor.
     * @param Registration $registration
     * @param String $salutation
     * @param String $replacementText
     */
    public function __construct(Registration $registration, $replacementText)
    {
        $this->registration = $registration;
        $this->replacementText = $replacementText;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        Log::debug("Initiating sign request mail");
        $replyTo = Auth::user()->domain->email;
        // registration is automatically available in the blade template
        $replyTo = empty($replyTo) ? "<EMAIL>" : $replyTo;
        $this->subject(ucfirst(__('generic.requestsignature', ['customername' => Auth::user()->domain->name])))
            ->replyTo($replyTo)
            ->view('email.request_signature')->with([
                'title'             => ucfirst(__('generic.requestsignature', ['customername' => Auth::user()->domain->name])),
                'replacementText'   => $this->replacementText
            ]);

        return $this->withSymfonyMessage(function ($message) {
            // $message contains the full email record
            // Log::debug("mailer response: $message");
            Log::info("Setting registered course to 'sign requested'");
            $this->registration->sign_request_send = 1;
            $this->registration->sign_requested_at = Carbon::now();
            $this->registration->save();

            // log the mailsession
            $emaillog = new Emaillogentry();
            $emaillog->domain_id = Auth::user()->domain->id;
            $emaillog->to = $this->to[0]["address"];
            $emaillog->from = "CLASS mailer, " . Auth::user()->domain->name;
            $emaillog->body = $this->replacementText;
            $emaillog->subject = ucfirst(__('generic.requestsignature', ['customername' => Auth::user()->domain->name]));
            $emaillog->studentids = $this->registration->student_id;
            $emaillog->save();
        });
    }
}
