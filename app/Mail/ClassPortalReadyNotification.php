<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\URL;

class ClassPortalReadyNotification extends Mailable {
    use Queueable, SerializesModels;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct() {

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build() {
        $title = trans('generic.yourclassinstallationisreadyforuse');
        return $this->from("<EMAIL>", "Class Portal")
                    ->view('email.senddomainreadynotification', compact('title'));
    }
}

