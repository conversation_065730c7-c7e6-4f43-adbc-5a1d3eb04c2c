<?php

namespace App\Http\Controllers;

use App\Models\Document;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * Class FileResolveController
 * This class abstracts downloads from the stack server
 *
 * The URL needs a path parameter to the file on the stack server.
 * That contains the name of the file on the server.
 * The resolver will download the file from the stack server and rename it to the original name.
 * Next it will offer the file for download,
 * e.g. https://localhost:8000/file/9_1155884632.pdf
 */
class FileResolveController extends Controller
{
    /**
     * @param Response $response
     * @param $path
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\StreamedResponse
     * @todo: how will actual urls (like link to youtube) be handled?
     * @todo: for now we only handle files on the stack server, recognized by the stored type field
     */
    public function get($filenameOnServer)
    {
        Log::info("Request download: " . $filenameOnServer);
        $path = env('SFTP_BASE_DIRECTORY') . '/class/library/' . $filenameOnServer;
        $documentFile = Document::query()
            ->where('file_name', '=', $filenameOnServer)
            ->first();
        if (!$documentFile) {
            return response()->json([
                'status' => false,
                'message' => 'File not found'
            ], Response::HTTP_NOT_FOUND);
        } else {
            Log::info("Download file from stack server: " . $path . " and rename to " . $documentFile->original_name);
            try {
                return Storage::disk('sftp')->download($path, $documentFile->original_name);
            } catch (\Exception $e) {
                Log::error("Error downloading file: " . $e->getMessage());
                return response()->json([
                    'status' => false,
                    'message' => 'Error downloading file'
                ], Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        }
    }
}
