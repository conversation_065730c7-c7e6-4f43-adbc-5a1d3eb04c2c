<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;

class HomeController extends Controller {
	/**
	 * Create a new controller instance.
	 */
	public function __construct() {
		$this->middleware('auth');
	}

	/**
	 * Show the application dashboard.
     * @return \Illuminate\Contracts\View\View
     */
	public function index() {
	    $userid = Auth::user()->id;
		return view('home', compact('userid'));
	}
}
