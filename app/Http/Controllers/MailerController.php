<?php

namespace App\Http\Controllers;

use App\Http\Requests\MultiMailerRequest;
use App\Http\Requests\SingleMailerRequest;
use App\Mail\PreferenceInvitation;
use App\Models\Emaillogentry;
use App\Models\Student;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class MailerController extends Controller {

    /**
     * Generic function to send mail through to queue
     * Email is OK, because validation is handled in Request
     * @param SingleMailerRequest $request
     */
    public function sendsingleemail(SingleMailerRequest $request) {
        Log::info("sending email (single student)");
        $student = Student::query()->findOrFail($request->to);
        $emailForPlanning = $student->planningEmail;
        // emailForPlanning field may contain multiple addresses, separated by ","!
        $to = explode(",", $emailForPlanning);
        foreach ($to as $email) {
            $studentArray = [
                "id" => $student->id,
            ];
            // check domain
            if ($student->domain_id != Auth::user()->domain->id) {
                Log::error("Student $student->name does not belong to domain $request->domainid");
                return;
            }
            // check access
            $hasAccess = $student->has_access;
            if (!$hasAccess) {
                Log::error("Student $student->name does not have access to the preferences page");
                return;
            }

            $bodyReplaced = $this->replaceTagsInBody($request->body, $studentArray);
            $uniqueToken = md5($request->subject . $bodyReplaced . time());
            Emaillogentry::insert([
                'domain_id' => Auth::user()->domain->id,
                'to' => $email,
                'from' => $request->from,
                'subject' => $request->subject,
                'body' => $bodyReplaced,
                'unique_token' => $uniqueToken,
                'status' => 'queued',
                'studentids' => $student->id,
                'log' => date("Y-m-d H:m:s") . ' - Class will send this email to the recipient',
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now()
            ]);
            Log::debug("mail will be send to queue addressing: $email. Token: $uniqueToken");
            Mail::to([$email])
                ->queue(
                    new PreferenceInvitation(
                        $request->from,
                        $request->subject,
                        $bodyReplaced,
                        'Invitation preferences',
                        $uniqueToken
                    )
                );
        }
    }

    /**
     * Function to send multiple emails to the queue
     * @param MultiMailerRequest $request
     */
    public function sendmultiemail(MultiMailerRequest $request) {
        Log::info("sending email (multiple students)");
        // request->to are student objects
        foreach ($request->to as $studentArr) {
            // email field may contain multiple addresses, separated by ","!
            $to = explode(",",$studentArr['email']);
            foreach ($to as $email) {
                $to = trim($email);
                // we need the actual student object to verify the domain
                $student = Student::query()->findOrFail($studentArr["id"]);
                // check domain
                if ($student->domain_id != Auth::user()->domain->id) {
                    Log::error("Student $student->name does not belong to domain $request->domainid");
                    continue;
                }
                // check access
                $hasAccess = $student->has_access;
                if (!$hasAccess) {
                    Log::error("Student $student->name does not have access to the preferences page");
                    continue;
                }
                $bodyReplaced = $this->replaceTagsInBody($request->body, $studentArr);
                $uniqueToken = md5($request->subject . $bodyReplaced . time());
                // add to mail log
                Emaillogentry::insert([
                    'domain_id' => Auth::user()->domain->id,
                    'to' => $to,
                    'from' => $request->from,
                    'subject' => $request->subject,
                    'body' => $bodyReplaced,
                    'unique_token' => $uniqueToken,
                    'status' => 'queued',
                    'studentids' => $studentArr["id"],
                    'log' => date("Y-m-d H:m:s") . ' - Class will send this email to the recipient',
                    'created_at' => \Carbon\Carbon::now(),
                    'updated_at' => \Carbon\Carbon::now()
                ]);
                Log::debug("mail will be send to queue addressing: $to. Token: $uniqueToken");
                Mail::to($to)
                    ->queue(new PreferenceInvitation(
                        $request->from,
                        $request->subject,
                        $bodyReplaced,
                        'Invitation preferences',
                        $uniqueToken
                    ));

            }
        }
    }

    /**
     * Replace the placeholders in the body text
     * This may be: %studentname%, %studentfullname%, %studentfirstname%, %schoollogo%, %schoolname%,
     * %schooltelephone%, %schoolcontactperson%, %schoolwebsite%, %studentaccesslink%,
     * %salutationforfinancial%, %salutationforplanning%, %salutationforpromotion%
     *
     * @param string $bodytekst the body text of the mail
     * @param array $studentArr the student array, only the id is used
     */
    private function replaceTagsInBody($bodytekst = '', $studentArr=[]) {
        $student = Student::query()->findOrFail($studentArr["id"]);
        Log::debug("Replace placeholders in mail body tekst");
        $studentAccessLink = "<a href='" . url('/') . "/schedulepreference/" . $student->accesstoken . "'>" . trans('generic.clickhere') . "</a>";
        // please note: schoollogo can only be present once!
        $logopos = strpos($bodytekst, "%schoollogo"); // don't add the second %, there may be parameters there
        if ($logopos !== FALSE) {
            $nextPerc = strpos($bodytekst, '%', $logopos + 1);
            $remainder = substr($bodytekst, $logopos+12, $nextPerc - ($logopos + 12));
            $logo = "<img src='" . Auth::user()->domain->logo_url . "' $remainder />";
            $wholeTag = substr($bodytekst, $logopos, $nextPerc + 1);
            $bodytekst = str_replace($wholeTag, $logo, $bodytekst);
        }
        // name & fullname: variable name has changed at some point
        $bodytekst = str_replace('%studentname%', $student->name, $bodytekst);
        $bodytekst = str_replace('%studentfullname%', $student->name, $bodytekst);
        $bodytekst = str_replace('%studentfirstname%', $student->firstname, $bodytekst);
        // school attributes
        $bodytekst = str_replace('%schoolname%', Auth::user()->domain->name, $bodytekst);
        $bodytekst = str_replace('%schooltelephone%', Auth::user()->domain->telephone, $bodytekst);
        $bodytekst = str_replace('%schoolcontactperson%', Auth::user()->domain->contact_person_name, $bodytekst);
        $bodytekst = str_replace('%schoolwebsite%', Auth::user()->domain->fullwebsiteurl, $bodytekst);
        $bodytekst = str_replace('%studentaccesslink%', $studentAccessLink, $bodytekst);
        // salutation
        $bodytekst = str_replace('%salutationforfinancial%', $student->planningSalutation, $bodytekst);
        $bodytekst = str_replace('%salutationforplanning%', $student->financialSalutation, $bodytekst);
        $bodytekst = str_replace('%salutationforpromotion%', $student->promotionsSalutation, $bodytekst);

        return $bodytekst;
    }
    
}
