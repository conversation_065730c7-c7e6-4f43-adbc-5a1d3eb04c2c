<?php

namespace App\Http\Controllers;

use App\Models\Attendanceoption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AttendanceoptionsController extends Controller
{
    public function get($id)
    {
        return response()->json(Attendanceoption::findOrFail($id));
    }
    public function getAll()
    {
        return response()->json(Attendanceoption::all());
    }
    public function create(Request $request)
    {
        $ao = new Attendanceoption();
        $ao->domain_id = Auth::user()->domain_id;
        $ao->label = $request->input("label");
        $ao->action_tutor = $request->input("action_tutor");
        $ao->save();
        return response()->json(["message"=>"Attendance option created"]);
    }
    public function update(Request $request, $id)
    {
        $ao = Attendanceoption::findOrFail($id);
        $ao->label = $request->input("label");
        $ao->action_tutor = $request->input("action_tutor");
        $ao->save();
        return response()->json(["message"=>"Attendance option updated"]);
    }
    public function destroy($id)
    {
        $ao = Attendanceoption::findOrFail($id);
        $ao->delete();
        return response()->json(["message"=>"Attendance option deleted"]);
    }
}
