<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Student;
use App\Models\Task;
use App\Models\Timetable;
use App\Http\Requests\TasksRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\LocaleDate;
use Scolavisa\scolib\Ndat2Mydat;
use App\Http\Resources\TaskResource;

class TasksController extends Controller {

    /**
     * Display a listing of the resource.

     * @param Request $request
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request) {
        $id = $request->input('id') ?: 0;
        return view('tasks.list', compact('id'));
    }

    /**
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function apiIndex() {
        $tasks = Task::with(['tasktype', 'course', 'student', 'tutor', 'registration', 'assignedTo'])->get();
        return TaskResource::collection($tasks);
    }

    /**
     * @param $id
     * @return TaskResource | \Illuminate\Http\JsonResponse
     */
    public function apiGet($id) {
        Log::info("Get task with id: " . $id);
        $task = Task::with(['tasktype', 'course', 'student', 'tutor', 'registration'])
            ->findOrFail($id);
        if ($task->domain_id === Auth::user()->domain_id) {
            return TaskResource::make($task);
        } else {
            return response()->json("Invalid request", Response::HTTP_BAD_REQUEST);
        }
    }


    public function apiGetForStudent($studentId)
    {
        $student = Student::findOrFail($studentId);
        if ($student->domain_id !== Auth::user()->domain_id) {
            return response()->json("Invalid request", Response::HTTP_BAD_REQUEST);
        }
        Log::info("Get tasks for student with id: " . $studentId);
        $tasks = Task::with(['tasktype', 'course', 'student', 'tutor', 'registration'])
            ->where('student_id', $studentId)
            ->get();
        return TaskResource::collection($tasks);
    }
    
    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiPut(Request $request, $id) {
        Log::info("Update task with id: " . $id);
        $task = Task::findOrFail($id);
        if ($task->domain_id === Auth::user()->domain_id) {
            $task->remarks = $request->remarks;
            $task-> assigned_user_id = $request->assigned_user_id;
            $ds = Ndat2Mydat::getMydat($request->input("date_closed"), true);
            if ($ds) {
                $task->date_closed = $ds;
            }
            $dd = Ndat2Mydat::getMydat($request->input("date_due"), true);
            if ($dd) {
                $task->date_due = $dd;
            }
            Log::info("Saving new task data");
            $task->save();
            return response()->json($task);
        } else {
            return response()->json("Invalid request", Response::HTTP_BAD_REQUEST);
        }
    }

    public function apiDelete($id) {
        Log::info("Get task with id: " . $id);
        $task = Task::findOrFail($id);
        if ($task->domain_id === Auth::user()->domain_id) {
            $task->delete();
            return response()->json("Task has been deleted");
        } else {
            return response()->json("Invalid request", Response::HTTP_BAD_REQUEST);
        }
    }

    public function apiClose($id) {
        Log::info("Close task with id: " . $id);
        $task = Task::findOrFail($id);
        if ($task->domain_id === Auth::user()->domain_id) {
            $task->date_closed = date("Y-m-d");
            $task->remarks .= "<br>" . LocaleDate::getLocaleSpecificDate(date('Y-m-d'), App::getLocale()) . ": " . trans('generic.taskclosedby') . " " . Auth::user()->name;
            $task->save();
            return response()->json($task);
        } else {
            return response()->json("Invalid request", Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {
        $nrOfTasks = Task::count();
        return view('tasks.create', compact('nrOfTasks'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param TasksRequest $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(TasksRequest $request) {

        $event = Event::findOrFail($request->event_id);
        $timetable = Timetable::findOrFail($event->timetable_id);

        Log::info("creating new task for tutor: " . $event->tutor->id);

        $task = new Task();
        $task->domain_id        = Auth::user()->domain->id;
        $task->tasktype_id      = $request->tasktype_id;
        $task->date_opened      = Ndat2Mydat::getMydat($request->date_opened);
        $task->date_closed      = Ndat2Mydat::getMydat($request->date_closed);
        $task->date_due         = Ndat2Mydat::getMydat($request->date_due);
        $task->course_id        = $event->course->id;
        $task->student_id       = $event->student->id;
        $task->tutor_id         = $event->tutor->id;
        $task->registration_id  = $timetable->registration->id;
        $task->event_id         = $request->event_id;
        $task->remarks          = $request->remarks;
        $task->assigned_user_id      = $request->assigned_user_id;
        $task->save();

        $nrOfTasks = Task::count();
        return redirect()->route('tasks.edit', $task->id)->with([
            'message' => ucfirst(trans("generic.datasaved")),
            'nrOfTasks' => $nrOfTasks
        ]);

    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id) {
        return view('tasks.list', compact("id"));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param TasksRequest $request
     * @param  int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function update(TasksRequest $request, $id) {
        $task = Task::findOrFail($id);

        $event = Event::findOrFail($request->event_id);
        $timetable = Timetable::findOrFail($event->timetable_id);

        Log::info("updating task for tutor: " . $event->tutor->id);

        $task->tasktype_id      = $request->tasktype_id;
        $task->date_opened      = Ndat2Mydat::getMydat($request->date_opened);
        $task->date_closed      = Ndat2Mydat::getMydat($request->date_closed);
        $task->date_due         = Ndat2Mydat::getMydat($request->date_due);
        $task->course_id        = $event->course->id;
        $task->student_id       = $event->student->id;
        $task->tutor_id         = $event->tutor->id;
        $task->registration_id  = $timetable->registration->id;
        $task->event_id         = $request->event_id;
        $task->remarks          = $request->remarks;
        $task->assigned_user_id = $request->assigned_user_id;
        $task->save();

        $nrOfTasks = Task::count();
        return redirect()->route('tasks.edit', $task->id)->with([
            'message' => ucfirst(trans("generic.datasaved")),
            'nrOfTasks' => $nrOfTasks
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     *
     * @throws \Exception
     */
    public function destroy($id) {
        try {
            Task::where("id", $id)->delete();
            Log::info("Task $id deleted");
        } catch (Exception $e) {
            Log::error($e);
            throw $e;
        }
    }

}
