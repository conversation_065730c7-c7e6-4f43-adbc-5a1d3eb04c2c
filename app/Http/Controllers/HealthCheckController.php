<?php

namespace App\Http\Controllers;

use App\Models\User;

class HealthCheckController extends Controller
{
    /**
     * @see https://ohdear.app/docs/features/application-health-monitoring#health-check-results-format
     * @return \Illuminate\Http\JsonResponse
     */
    public function ohDearHealthCheck()
    {
        $ohDearSectret = env('OH_DEAR_HEALTH_CHECK_SECRET');
        // first check token in the request header oh-dear-health-check-secret to make sure the request originated from Oh Dear
        if (empty($ohDearSectret) || $ohDearSectret != env('OH_DEAR_HEALTH_CHECK_SECRET')) {
            return response()->json([
                'finishedAt' => time(),
                'checkResults' => [
                    [
                        'name' => 'OhDearHealthCheckSecret',
                        'label' => 'Oh Dear Health Check Secret',
                        'status' => 'skipped',
                        'notificationMessage' => 'Not Valid',
                        'shortSummary' => 'Oh Dear Health Check Secret is not correct'
                    ]
                ]
            ]);
        }
        return response()->json([
            'finishedAt' => time(),
            'checkResults' => [
                $this->checkDBConnection()
            ]
        ]);
    }

    public function checkDBConnection()
    {
        $respArray = [
            'name' => 'DatabaseConnection',
            'label' => 'DB Connection',
            'meta' => []
        ];

        try {
            // measure runtime of the query
            $start = microtime(true);
            $countUsers = User::count();
            $end = microtime(true);
            $runTime = $end - $start;
            $respArray['status'] = 'ok';
            $respArray['notificationMessage'] = 'All good';
            $respArray['shortSummary'] = 'All good';
            $respArray['meta'] = [
                'queryRunTime' => $runTime,
                'query' => 'select count(*) from users',
                'countUsers' => $countUsers
            ];
            if ($runTime > 1) {
                $respArray['status'] = 'warning';
                $respArray['notificationMessage'] = 'Query took more than 1 second';
                $respArray['shortSummary'] = 'Query took more than 1 second';
            }
        } catch (\Exception $e) {
            $end = microtime(true);
            $runTime = $end - $start;
            $respArray['meta'] = [
                'queryRunTime' => $runTime,
                'query' => 'select count(*) from users',
                'countUsers' => 0
            ];
            $respArray['status'] = 'failed';
            $respArray['notificationMessage'] = "Database connection failed";
            $respArray['shortSummary'] = $e->getMessage();
        }
        return $respArray;
    }
}
