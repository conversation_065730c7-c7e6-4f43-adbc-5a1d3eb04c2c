<?php

namespace App\Http\Controllers;

use App\Models\Emaillogentry;
use Illuminate\Http\Request;

class EmaillogentriesController extends Controller {

    /**
     * Show a complete list, data will be provided through API call
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index() {
        return view('email.listlogentries');
    }

    /**
     * Returns all messages as json
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiindex(Request $request) {
        $emailEntries = Emaillogentry::orderby("created_at", "desc")
            ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-7 day'))) // only entries from the last week
            ->limit(100)
            ->get();
        return response()->json($emailEntries);
    }

    /**
     * Delete an entry from the log
     */
    public function destroy($id)
    {
        $emailEntry = Emaillogentry::find($id);
        if ($emailEntry) {
            $emailEntry->delete();
        } else {
            return response()->json(['success' => false], 404);
        }
        return response()->json(['success' => true]);
    }

}
