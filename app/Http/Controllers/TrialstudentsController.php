<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Domain;
use App\Models\Student;
use App\Models\Coursegroup;
use App\Models\Registration;
use App\Models\Trialrequeststatus;
use App\Models\Trialstudent;
use App\Http\Requests\TrialstudentsRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\Ndat2Mydat;

class TrialstudentsController extends Controller {
	/**
	 * Display a listing of the resource.
	 *
     * @param Request $request
     * @return \Illuminate\Contracts\View\View
	 */
    public function index(Request $request) {
        $id = $request->input('id') ?: 0;
        return view('trialstudents.list', compact('id'));
    }

	public function apiIndex() {
        // not older than 1 year
        $tStudents = []; // return array
		$trialstudents = Trialstudent::query()
            ->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-1 year')))
            ->with('course', 'trialrequeststatus')
            ->get();
        Log::info("found " . count($trialstudents) . " trialstudent records");
        foreach ($trialstudents as $trialstudent) {
            // 1.
            // has planning for the registered trialcourse
            $hasPlanning = false;
            if($trialstudent->hasRegistration()) {
                // has a timetable?
                $registration = Registration::findOrFail($trialstudent->generated_registration_id);
                $timetables = $registration->timetables;
                Log::debug("nr of timetables: " . count($timetables) . " for trialstudent $trialstudent->id");
                // has an event? (don't care what schoolyear that was)
                foreach ($timetables as $timetable) {
                    $events = $timetable->events;
                    Log::debug("nr of events: " . count($events) . " for timetable: $timetable->id");
                    if(count($events) > 0) {
                        $hasPlanning = true;
                        break;
                    }
                }
            } else {
                Log::debug("$trialstudent->id has no registration");
            }

            // 2.
            // has followupcourse after the trial course?
            $hasFollowUpCourse = false;
            // which student
            if($trialstudent->hasStudentAccount()) {
                $student = Student::findOrFail($trialstudent->generated_student_id);
                // does this student have a course reg that is not a trial course (don't mind if its planned)
                // we can find this out by asking for the student's courses
                $courses = $student->courses;
                Log::debug("found " . count($courses) . " courses for trialstudent $trialstudent->id");
                foreach ($courses as $course) {
                    if($course->id !== $trialstudent->course_id) {
                        $hasFollowUpCourse = true;
                        break;
                    }
                }
            } else {
                Log::debug("$trialstudent->id has no studentaccount");
            }

            $tStudents[] = [
                "trialstudent" => $trialstudent,
                "hasTrialPlanning" => $hasPlanning,
                "hasFollowUpCourse" => $hasFollowUpCourse
            ];
		}
		return response()->json($tStudents);
	}

	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create(  ) {
		return view('trialstudents.create');
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id) {
		//
	}

	/**
	 * Store a newly created resource in storage.
 	 * This method is available through POST API call /api/trialstudents
     * A serves as a way to store trial requests from the public website
	 *
	 * @param TrialstudentsRequest|Request $request
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function store( TrialstudentsRequest $request ) {
        Log::info("Storing trial student: $request->firstname $request->lastname");
        Log::info("domain: " . $request->sendingdomaindocroot);
        // lookup domain
        $domain_id = $this->getTrialSiteDomain($request->sendingdomaindocroot);
        if ($domain_id == 0) {
            Log::warning("no domain found for this request");
            return response()->json(["error" => "no domain"], 400);
        }
        $addToRemarks = '';
        $trialStudent = new Trialstudent();
        $trialStudent->domain_id = $domain_id;
		$trialStudent->firstname = $request->firstname;
		$trialStudent->lastname = getLastnameFromAssembledField($request->lastname);
		$trialStudent->preposition = isset($request->lastname) ? getPrepositionFromAssembledField($request->lastname) : '';

		$dob = Ndat2Mydat::getMydat($request->date_of_birth, true);
		if ($dob !== false) {
            $trialStudent->date_of_birth = isset($request->date_of_birth) ? Ndat2Mydat::getMydat($request->date_of_birth) : null;
        } else {
            // inserted date may be invalid or empty. Add date input to the remarks, so the info isn't lost
            Log::warning(ucfirst(trans('generic.couldnotsavedob', ['dob' => $request->date_of_birth])));
            $addToRemarks = ucfirst(trans("generic.couldnotsavedob", ['dob' => $request->date_of_birth]));
        }

		$trialStudent->telephone = isset($request->telephone) ? $request->telephone : '';
		$trialStudent->email = isset($request->email) ? $request->email : '';
		if ($request->course_id !== '9999') {
			$trialStudent->course_id = $request->course_id;
		}

        // this is no date but a string! Simply save what was input
        $trialStudent->requested_startdate = isset($request->requested_startdate) ? $request->requested_startdate : null;
		$trialStudent->remarks = isset($request->remarks) ? $request->remarks . $addToRemarks : $addToRemarks;
		$trialStudent->save();
		// check if successful
		if(empty($trialStudent->id) ) {
			Log::error('Failed to save new Trialstudent request!');
			return response()->json( [ 'result' => 'error saving request' ] );
		}
		Log::info("Trial student saved. ID: $trialStudent->id");
		return response()->json(['result' => 'request saved', 'id' => $trialStudent->id]);
	}

	/**
	 * Update the specified resource in storage.
	 * This is used after an user has been created.
     * The trial student then becomes a normal student with a student card.
     * It should also be used when we couple a trial student to an existing student,
     * which is currently not possible, see issue sc-3779.
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @param  int $id
	 *
     * @return \Illuminate\Http\JsonResponse
	 */
	public function update( Request $request, $id ) {
		$trialStudent = Trialstudent::findOrFail($id);
        if (!empty($request->generated_student_id)) {
            $trialStudent->generated_student_id = $request->generated_student_id;
            Log::info("update trial request, added student id: $request->generated_student_id to trialstudent $trialStudent->id");
            // after a student create we need to update the status as well to status 2
            // fixme: this is very specifically the case for MFB,
            // fixme: it should be a choice of the customer to create a workflow
            $trialStudent->trialrequeststatus_id = 2;
            Log::info("update trial request, setting status to: 2 for trialstudent $trialStudent->id");
        }
        if ($request->trialrequeststatus["id"] !== $trialStudent->trialrequeststatus_id) {
            $trialStudent->trialrequeststatus_id = $request->trialrequeststatus["id"];
            Log::info("update trial request, setting status to: " . $request->trialrequeststatus['id'] . " for trialstudent $trialStudent->id");
        }
        $trialStudent->save();
		return response()->json(['result' => 'trial request saved', 'id' => $trialStudent->id]);
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int $id
	 *
	 * @return void
	 * @throws \Exception
	 */
	public function destroy( $id ) {
		Log::info("deleting trial request $id");
		try {
			Trialstudent::where("id", $id)->delete();
		} catch ( \Exception $e ) {
			Log::error( $e );
			throw $e;
		}
	}

	/**
	 * Get all courses that are marked as trial course
	 * This is done by looking at the coursegroup->is_trial_group attribute
	 * This method is available through GET API call /api/trialcourses
     * The request header must contain a lookup-able classdomain (originating path)
	 *
	 * @return \Illuminate\Http\JsonResponse
     * @deprecated
	 */
	public function trialcourses() {
        Log::info('get trial coursegroups');
        // headers?
        $headers = getallheaders();
        $classdomain = $headers["classdomain"];
        $domain_id = $this->getTrialSiteDomain($classdomain);
        if ($domain_id == 0) {
            Log::warning("no domain found for this request");
            return response()->json(["error" => "no domain"], 400);
        }

        Log::debug("domain: $classdomain");

		$trialCourses = [];
		$total = 0;
		Log::info('get trial coursegroups');
		// determine the trial group or groups
		$trialGroups = Coursegroup::where([['is_trial_group', '=', '1'], ['domain_id', '=', $domain_id]])->get();
		Log::info('found ' . count($trialGroups) . ' trial coursegroup(s)');
		foreach ( $trialGroups as $trialGroup ) {
			// get all courses for this group
			$courses = Course::where([['coursegroup_id', '=', $trialGroup->id], ['domain_id', '=', $domain_id]])->get();
			Log::info('found ' . count($courses) . ' trial course(s)');
			$total += count($courses);
			$trialCourses = array_merge($trialCourses, [$trialGroup->name => $courses]);
		}
		Log::info("responding with $total course(s) as JSON.");
		return response()->json($trialCourses);
	}

    /**
     * Get the name of a course with id courseId
     * This method is available through GET API call /api/trialcourse/{id}
     * The request header must contain a lookup-able classdomain (originating path)
     *
     * @param int $courseId
     * @return string
     * @deprecated
     */
    public function trialcourse($courseId=0) {
        Log::info("getting name of courseid:$courseId");
        // headers?
        $headers = getallheaders();
        $classdomain = $headers["classdomain"];
        $domain_id = $this->getTrialSiteDomain($classdomain);
        if ($domain_id == 0) {
            Log::warning("no domain found for this request");
            return response()->json(["error" => "no domain"], 400);
        }
	    $course = Course::findOrFail($courseId);
        if($course->domain_id <> $domain_id) {
            Log::warning("incorrect domain found for this request");
            return response()->json(["error" => "incorrect domain"], 400);
        }
        Log::info("returning: $course->name");
        return response()->json(["course" => ["id" => $courseId, "name" => $course->name]]);
    }

    /**
     * Lookup by lookup directory (historically called "lookup_url")
     * @param string $mappedDir
     * @return int
     */
    public function getTrialSiteDomain ($mappedDir = '') {
        $domain = Domain::select("id")
            ->where("lookup_url", "=", $mappedDir)
            ->get();
        // must be exactly 1
        if ($domain->count() === 1 ) {
           return $domain[0]->id;
        }
        return 0;
    }

    /**
     * Get all possible statuses for a trial request
     */
    public function trialrequeststatuses()
    {
        $statuses = Trialrequeststatus::query()
            ->orderBy('description', 'asc')
            ->get();
        return response()->json($statuses);
    }
}
