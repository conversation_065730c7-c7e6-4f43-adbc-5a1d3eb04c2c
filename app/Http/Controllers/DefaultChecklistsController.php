<?php

namespace App\Http\Controllers;

use App\Models\DefaultChecklist;
use App\Http\Requests\DefaultChecklistFormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DefaultChecklistsController extends Controller {
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Contracts\View\View
     */
	public function index() {
		return view("defaultchecklists.list");
	}

    public function apiIndex()
    {
        $allDefaultChecklists = DefaultChecklist::query()
            ->orderBy('name')
            ->get();
        return response()->json($allDefaultChecklists);
    }

    public function apiStore(DefaultChecklistFormRequest $request)
    {
        Log::info("storing new defaultchecklist");
        $autoAdd = $request->input('auto_add');
        $autoAddBool = ($autoAdd === 'on' || $autoAdd === '1' || $autoAdd === 'true' || $autoAdd === 1);
        $defaultChecklist = new DefaultChecklist();
        $defaultChecklist->domain_id    = Auth::user()->domain->id;
        $defaultChecklist->name         = $request->input('name');
        $defaultChecklist->auto_add     = $autoAddBool;
        $defaultChecklist->item1        = $request->input('item1');
        $defaultChecklist->item2        = $request->input('item2');
        $defaultChecklist->item3        = $request->input('item3');
        $defaultChecklist->item4        = $request->input('item4');
        $defaultChecklist->item5        = $request->input('item5');
        $defaultChecklist->item6        = $request->input('item6');
        $defaultChecklist->item7        = $request->input('item7');
        $defaultChecklist->item8        = $request->input('item8');
        $defaultChecklist->item9        = $request->input('item9');
        $defaultChecklist->item10       = $request->input('item10');
        $defaultChecklist->item11       = $request->input('item11');
        $defaultChecklist->item12       = $request->input('item12');
        $defaultChecklist->save();
        return response()->json($defaultChecklist);
    }

    public function apiUpdate(DefaultChecklistFormRequest $request)
    {
        Log::info("updating defaultchecklist id: " . $request->input('id'));
        $autoAdd = $request->input('auto_add');
        $autoAddBool = ($autoAdd === 'on' || $autoAdd === '1' || $autoAdd === 'true' || $autoAdd === 1);

        $defaultChecklist = DefaultChecklist::findOrFail($request->input('id'));
        $defaultChecklist->name         = $request->input('name');
        $defaultChecklist->auto_add     = $autoAddBool;
        $defaultChecklist->item1        = $request->input('item1');
        $defaultChecklist->item2        = $request->input('item2');
        $defaultChecklist->item3        = $request->input('item3');
        $defaultChecklist->item4        = $request->input('item4');
        $defaultChecklist->item5        = $request->input('item5');
        $defaultChecklist->item6        = $request->input('item6');
        $defaultChecklist->item7        = $request->input('item7');
        $defaultChecklist->item8        = $request->input('item8');
        $defaultChecklist->item9        = $request->input('item9');
        $defaultChecklist->item10       = $request->input('item10');
        $defaultChecklist->item11       = $request->input('item11');
        $defaultChecklist->item12       = $request->input('item12');
        $defaultChecklist->save();
        return response()->json($defaultChecklist);
    }

	/**
	 * Remove the specified resource from storage.
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
	public function apiDestroy($id) {
        Log::info("deleting defaultchecklist id: " . $id);
		DefaultChecklist::where("id", $id)->delete();
        return response()->json(['status' => 'ok']);
	}
}
