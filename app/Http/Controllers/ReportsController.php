<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Registration;
use App\Models\Student;
use Illuminate\Support\Facades\Log;

class ReportsController extends Controller {
    /**
     * prep data for report Registrations
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Exception
     */
	public function registrations() {
		return view('reports.registrations');
	}

    /**
     * retrieve data for report Registrations
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRegistrationData()
    {
        $coursesData	= [];
        $studentsCount	= Student::all()->count();
        $courses		= Course::orderBy('name')->get();
        $courseCount	= $courses->count();

        foreach ($courses as $course) {
            $nActive = 0;
            $nInActive = 0;
            foreach ($course->students as $student) {
                $endDate = new \DateTime($student->pivot->end_date);
                $current_date = new \DateTime();
                // if enddate is empty or in the future, the course counts as active
                if( (empty($student->pivot->end_date)) || ($endDate > $current_date) ) {
                    // counts as active registration
                    $nActive ++;
                } else {
                    // counts as inactive registration
                    $nInActive ++;
                }
            }
            $coursesData[$course->id] = ['name' => $course->name, 'recurrenceoption' => $course->recurrenceoption, 'nstudents' => $nActive, 'unregistered' => $nInActive];
        }

        $registrations = [
            'total-students'	=> $studentsCount,
            'total-courses'		=> $courseCount,
            'courses'			=> $coursesData
        ];

        // registrations, scheduled in this year
        $registrationsThisYear = Registration::getRegistrationForYear();
        return response()->json(['registrations' => $registrations, 'registrationsThisYear' => $registrationsThisYear]);
    }


    /**
     * Show all registration that have a checklist
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
	public function registrationsOpenChecklists() {
		return view('reports.reg_open_checklists');
	}
	/**
	 * retrieve all registration that have a checklist
     * @return \Illuminate\Http\JsonResponse
     */
	public function registrationsOpenChecklistsData() {
		$registrations = Registration::getRegistrationWithChecklists();
        return response()->json($registrations);
	}

	/**
	 * show list of unregisterd students
	 * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
	 */
	public function unregisteredStudents() {
		// data handled through VUE
		return view('reports.list_students');
	}

	public function emaillists() {
		$allAddresses = Registration::getAllStudents();
		$atleastone	= Registration::getStudentsWithAtLeastOneCourse();
		$nocourses	= Registration::getStudentsNoCourse();
		return view('reports.list_email', compact('allAddresses', 'atleastone', 'nocourses'));
	}


    public function activeStudentsGroupedByMonth() {

        // next month, one year ago
        $now = new \DateTime('now');
        try {
            $prevYear = $now->sub(new \DateInterval('P11M'));
        } catch(\Exception $e) {
            Log::error('Couldnt calculate new date');
            Log::error($e->getMessage());
        }
        $year = intval($prevYear->format('Y'));
        $month = intval($prevYear->format('m'));
        // for now: fix 12 months
        $students = Student::findActiveStudentsGroupedByMonth($month, $year, 12);
        return view('reports.list_active_students_grouped', compact('students'));
	}

    public function dashboardoverview()
    {
        return view('reports.dashboardoverview');
    }

}
