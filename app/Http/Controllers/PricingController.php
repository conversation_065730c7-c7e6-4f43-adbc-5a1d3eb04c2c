<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Coursegroup;
use Illuminate\Support\Facades\Log;

class PricingController extends Controller
{
    /**
     * Get all active course groups for the requesting domain,
     * i.e.; there should be at least one course attached that is not archived.
     * At this point token validity has already been checked (middleware WordpressAuth).
     * The requesting domain id should be present in the request as attribute (also handled by the middleware)
     * @return \Illuminate\Http\JsonResponse
     */
    public function wppricing(Request $request)
    {
        $domainId = $request->attributes->get('requesting_domain', 0);
        $courseGroups = Coursegroup::getActiveCoursegroups($domainId);

        // remove groups "proeflessen (id: 10) and "oefenruimteverhuur (id: 11)
        // fixme: this should be done in the database, but for now we do it here
        $courseGroups = $courseGroups->filter(function ($courseGroup) {
            return $courseGroup->id !== 10 && $courseGroup->id !== 11;
        });

        // -------------------------------------------------------------------
        // fixme: the following is no longer needed when we add the agegroup directly to the courses
        // fixme: for now we use this quick and dirty solution

        // get all course -> age group mappings, currently in a hard coded file
        $courseAgeGroups = json_decode(file_get_contents(storage_path('app/course_agegroup.json')), true);
        // now add the agegroup for every course under the coursegroup
        foreach ($courseGroups as $courseGroup) {
            $coursesForThisCourseGroup = $courseGroup->courses;
            // now add the age-from and age-to as field to every course
            foreach ($coursesForThisCourseGroup as $course) {
                $dbCourse = array_filter($courseAgeGroups, function ($dbCourse) use ($course) {
                    return intval($dbCourse['courseId']) === $course->id;
                });
                Log::debug("dbCourse: " . json_encode($dbCourse));
                if (count($dbCourse) === 1) {
                    $foundCoutrseEntry = array_pop($dbCourse);
                    $course->ageFrom = $foundCoutrseEntry['from'];
                    $course->ageTo = $foundCoutrseEntry['to'];
                } else {
                    Log::warning("Course with id " . $course->id . " not found in course_agegroup mapping!");
                }
            }
        }
        // END fixme: the following etc....
        // -------------------------------------------------------------------

        return response()->json($courseGroups);
    }
}
