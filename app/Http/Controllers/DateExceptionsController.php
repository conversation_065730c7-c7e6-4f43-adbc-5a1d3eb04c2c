<?php

namespace App\Http\Controllers;

use App\Http\Resources\DateExceptionsResource;
use App\Models\DateException;
use App\Models\Schoolyear;
use App\Http\Requests\DateExceptionFormRequest;
use App\Services\DateExceptions\ConflictStrategies\LocationStrategy;
use App\Services\DateExceptions\ConflictStrategies\TutorStrategy;
use App\Services\DateExceptions\ConflictStrategies\WholeSchoolStrategy;
use App\Services\DateExceptions\ConflictChecker;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DateExceptionsController extends Controller {
    private ConflictChecker $conflictChecker;

    public function __construct(ConflictChecker $conflictChecker) {
        $this->conflictChecker = $conflictChecker;
    }
    
	/**
	 * Display a listing of the resource.
	 *
     * @return \Illuminate\Contracts\View\View
     */
	public function index() {
        Log::info("List DE");
		// The DateExceptions will be retrieved through Vue
		return view("dateexceptions.list");
	}

    /**
     * Create new date exception
     * @param DateExceptionFormRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function insert(DateExceptionFormRequest $request)
    {
        // $request->isWholeDay may be boolean or string 'on'
        if (!isset($request->isWholeDay)) {
            $wholeDay = false;
        } elseif (is_bool($request->isWholeDay)) {
            $wholeDay = $request->isWholeDay;
        } else {
            $wholeDay = $request->isWholeDay === 'on' || $request->isWholeDay === 1;
        }
        // $request->exclude_from_alerts may be boolean or string 'on'
        if (!isset($request->exclude_from_alerts)) {
            $excludeFromAlerts = false;
        } elseif (is_bool($request->exclude_from_alerts)) {
            $excludeFromAlerts = $request->exclude_from_alerts;
        } else {
            $excludeFromAlerts = $request->exclude_from_alerts === 'on' || $request->exclude_from_alerts === 1;
        }
        // $request->plan_blocking may be boolean or string 'on'
        // plan_blocking has been deprecated by layers planning, remove it at some later stage
        if (!isset($request->plan_blocking)) {
            $planBlocking = false;
        } elseif (is_bool($request->plan_blocking)) {
            $planBlocking = $request->plan_blocking;
        } else {
            $planBlocking = $request->plan_blocking === 'on' || $request->plan_blocking === 1;
        }

        $dtStart = $wholeDay
            ? substr($request->datetime_start, 0, 10) . " 00:00:00"
            : $request->datetime_start;
        $dtEnd = $wholeDay
            ? substr($request->datetime_end, 0, 10) . " 23:59:59"
            : $request->datetime_end;

        $locationId = $request->location_id && $request->location_id > 0 ? $request->location_id : null;

        $data = [
            "dtStart"               => $dtStart,
            "dtEnd"                 => $dtEnd,
            "reason"                => $request->reason,
            "exclude_from_alerts"   => $excludeFromAlerts,
            "plan_blocking"         => $planBlocking,
            "schoolyear_id"         => $request->schoolyear_id,
            "location_id"           => $locationId,
            "tutors"                => $request->tutors
        ];
        // if not plan_blocking, then also exclude_from_alerts
        // see issue sc-2889
        // plan_blocking has been deprecated by layers planning, remove it at some later stage
        // this logic is therefor deprecated as well
//        if (!$planBlocking) {
//            $excludeFromAlerts = true;
//            $data["exclude_from_alerts"] = true;
//        }
        // set color for calendar presentation, default wil be set if empty
        $calendar_color = $request->calendar_color ?? null;
        if (!empty($calendar_color)) {
            $data["calendar_color"] = $calendar_color;
        }
        if (!empty($request->detail_url)) {
            $data["detail_url"] = $request->detail_url;
        }

        $de = $this->createNewDE($data);

        // repeat?
        if (intval($request->repeatAppointment["recurrenceOption"]) !== config('app.REPEAT_NO_REPEAT')) {
            $this->createNewDEAndRepeat([
                "recurrenceOption"      => intval($request->repeatAppointment["recurrenceOption"]),
                "repeatEndsAfterDate"   => $request->repeatAppointment["repeatEndsAfterDate"],
                "schoolyear_id"         => $request->schoolyear_id,
                "location_id"           => $locationId,
                "dtStart"               => $de->datetime_start,
                "dtEnd"                 => $de->datetime_end,
                "reason"                => $request->reason,
                "exclude_from_alerts"   => $excludeFromAlerts,
                "plan_blocking"         => $planBlocking,
                "calendar_color"        => $calendar_color,
                "detail_url"            => $request->detail_url,
                "tutors"                => $request->tutors
            ]);
        }
        return response()->json(["message" => trans('generic.datasaved')]);
    }

    /**
     * This function can be used by insert() 
     * but also update() if recurrence was added after the initial insert
     * @return DateException
     */
    private function createNewDE($newDeData = [])
    {
        Log::info("creating DE for " . $newDeData["reason"] . " at " . $newDeData["dtStart"]);
        if (!empty($newDeData)) {
            $de = new DateException();
            $de->domain_id = Auth::user()->domain_id;
            $de->datetime_start = $newDeData["dtStart"];
            $de->datetime_end = $newDeData["dtEnd"];
            $de->reason = $newDeData["reason"];
            $de->plan_blocking = $newDeData["plan_blocking"] ?? false;
            // if not plan_blocking, then also exclude_from_alerts
            // see issue sc-2889
            $de->exclude_from_alerts = $de->plan_blocking ? $newDeData["exclude_from_alerts"] : true;
            if (!empty($newDeData["calendar_color"])) {
                $de->calendar_color = $newDeData["calendar_color"];
            }
            if (!empty($newDeData["detail_url"])) {
                $de->detail_url = $newDeData["detail_url"];
            }
            $de->schoolyear_id = $newDeData["schoolyear_id"];
            $de->location_id = $newDeData["location_id"];
            $de->save();
            // Associate the tutors if any
            // If it's empty, that means 'whole school' e.g. a holiday or school performance
            $de->tutors()->attach($newDeData["tutors"]);
            return $de;
        }
    }

    private function createNewDEAndRepeat($repeatDeData = [])
    {
        $intervals = ["1 days", "1 week", "2 weeks"];
        $interval = $intervals[$repeatDeData["recurrenceOption"]] ?? "0 days";
        if (!empty($repeatDeData["repeatEndsAfterDate"])) {
            $recurrenceEndDate = new \DateTime($repeatDeData["repeatEndsAfterDate"]);
        } else {
            // repeat until end of school year
            $schoolyear = Schoolyear::findOrFail($repeatDeData["schoolyear_id"]);
            $recurrenceEndDate = new \DateTime($schoolyear->end_date);
        }

        $newStartDate = new \DateTime($repeatDeData["dtStart"]);
        $newStartDate->modify($interval);
        $newEndDate = new \DateTime($repeatDeData["dtEnd"]);
        $newEndDate->modify($interval);

        $failSave = 1; // '1' because one already created, to make the count easy
        while ($newStartDate <= $recurrenceEndDate && $failSave < 100) {
            // create a new DE with this data
            $de = $this->createNewDE([
                "dtStart"               => $newStartDate->format("Y-m-d H:i:s"),
                "dtEnd"                 => $newEndDate->format("Y-m-d H:i:s"),
                "reason"                => $repeatDeData["reason"],
                "schoolyear_id"         => $repeatDeData["schoolyear_id"],
                "location_id"           => $repeatDeData["location_id"],
                "exclude_from_alerts"   => $repeatDeData["exclude_from_alerts"],
                "plan_blocking"         => $repeatDeData["plan_blocking"],
                "calendar_color"        => $repeatDeData["calendar_color"] ?? null,
                "detail_url"            => $repeatDeData["detail_url"] ?? null,
                "tutors"                => $repeatDeData["tutors"]
            ]);
            // calculate next date
            $newStartDate = new \DateTime($de->datetime_start);
            $newStartDate->modify($interval);
            $newEndDate = new \DateTime($de->datetime_end);
            $newEndDate->modify($interval);
            $failSave ++;
        }
        if ($failSave >= 100) {
            Log::warning("Too many dateexceptions in repeat! Quit after 99 repeats");
            return response()->json(["message" => trans('generic.toomanyrepeatsfordateexception')], 401);
        }
        Log::info("created $failSave DE's");
    }
    /**
     * update existing date exception
     * please note: if dtstart time = 00:00:00 and dtend time = 23:59:59
     * then the DE will be considered "wholeday" in any case,
     * so switching off a whole day event and saving it without changing the time
     * will still result in a whole day event
     *
     * @param DateExceptionFormRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(DateExceptionFormRequest $request)
    {
        Log::info("update DE with id: " . $request->id);
        // $request->isWholeDay may be boolean or string 'on'
        if (!isset($request->isWholeDay)) {
            $wholeDay = false;
        } elseif (is_bool($request->isWholeDay)) {
            $wholeDay = $request->isWholeDay;
        } else {
            $wholeDay = $request->isWholeDay === 'on' || $request->isWholeDay === 1;
        }
        // $request->exclude_from_alerts may be boolean or string 'on'
        if (!isset($request->exclude_from_alerts)) {
            $excludeFromAlerts = false;
        } elseif (is_bool($request->exclude_from_alerts)) {
            $excludeFromAlerts = $request->exclude_from_alerts;
        } else {
            $excludeFromAlerts = $request->exclude_from_alerts === 'on' || $request->exclude_from_alerts === 1;
        }
        // $request->plan_blocking may be boolean or string 'on'
        // plan_blocking has been deprecated by layers planning, remove it at some later stage
        if (!isset($request->plan_blocking)) {
            $planBlocking = false;
        } elseif (is_bool($request->plan_blocking)) {
            $planBlocking = $request->plan_blocking;
        } else {
            $planBlocking = $request->plan_blocking === 'on' || $request->plan_blocking === 1;
        }

        $locationId = $request->location_id && $request->location_id > 0 ? $request->location_id : null;

        $dtStart = $wholeDay
            ? substr($request->datetime_start, 0, 10) . " 00:00:00"
            : $request->datetime_start;
        $dtEnd = $wholeDay
            ? substr($request->datetime_end, 0, 10) . " 23:59:59"
            : $request->datetime_end;

        $de = DateException::findOrFail($request->id);
        if ($de->domain_id !== Auth::user()->domain_id) {
            return response()->json(["message" => "error in data!"], 403);
        }
        $de->datetime_start = $dtStart;
        $de->datetime_end = $dtEnd;
        $de->reason = $request->reason;
        $de->location_id = $locationId;
        $de->plan_blocking = $planBlocking;
        $de->exclude_from_alerts = $de->plan_blocking ? $excludeFromAlerts : true;
        if ($request->calendar_color) {
            $de->calendar_color = $request->calendar_color;
        }
        if ($request->detail_url) {
            $de->detail_url = $request->detail_url;
        }
        $de->save();
        // associate the tutors if any
        // When the associated tutors array is empty, that means 'whole school', e.g. a holiday or school performance
        Log::info("syncing tutors: " . json_encode($request->tutors));
        $de->tutors()->sync($request->tutors);

        if (intval($request->repeatAppointment["recurrenceOption"]) !== config('app.REPEAT_NO_REPEAT')) {
            // in this case, we can re-use the insert for all following entries
            $this->createNewDEAndRepeat([
                "recurrenceOption"      => intval($request->repeatAppointment["recurrenceOption"]),
                "repeatEndsAfterDate"   => $request->repeatAppointment["repeatEndsAfterDate"],
                "schoolyear_id"         => $de->schoolyear_id,
                "location_id"           => $de->location_id,
                "dtStart"               => $de->datetime_start,
                "dtEnd"                 => $de->datetime_end,
                "reason"                => $de->reason,
                "exclude_from_alerts"   => $de->exclude_from_alerts,
                "plan_blocking"         => $de->plan_blocking,
                "calendar_color"        => $de->calendar_color ?? null,
                "detail_url"            => $de->detail_url ?? null,
                "tutors"                => $request->tutors
            ]);
        }

        return response()->json(["message" => trans('generic.changessaved')]);
    }

	/**
	 * Display the specified resource.
	 *
	 * @param  int $id
     * @return DateExceptionsResource
     */
	public function show($id) {
	    Log::info("Show DE $id");
		$d = DateException::where("id", "=", $id)->with("tutors")->first();
        return new DateExceptionsResource($d);
	}

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
	public function destroy($id) {
        $de = DateException::findOrFail($id);
        // un-associate all tutors before deleting
        $de->tutors()->detach();
        $de->delete();
        return response()->json(["message"=>"Date Exception deleted"]);
	}

	/**
	 * Api call to get the dateExceptions of a certain year
	 * @param $schoolyearId
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function get($schoolyearId) {
		$retArr = $this->getAsModelObjects($schoolyearId);
		return response()->json($retArr);
	}

	/**
	 * get the dateExceptions of a certain year
	 * and return it as an array
	 * @return array
	 */
	public function getAsModelObjects($schoolyearId) {
		$retArr = ["schoolyearId" => $schoolyearId];
		// I use this function so the pivots are all instantiated
		// otherwise javascript would not have them available
		$des = new DateException();
		$retArr["dateExceptions"] = $des->getFormatted($schoolyearId);
		return $retArr;
	}

	public function getDateexceptionShort( $dateExcId ) {
		$event = Event::findOrFail($dateExcId);
		$s = $event->student;
		$returnArr = [
			"id"            => $event->id,
			"caluniqueid"   => $event->caluniqueid,
			"datetime"      => $event->datetime,
			"student"       => ["id"    => $event->student->id, "name"  => $event->student->name]
		];
		return response()->json($returnArr);

	}

    /**
     * @param bool $onlyFuture
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function dateExceptions($onlyFuture = true)
    {
        if ($onlyFuture) {
            $dateExceptions = DateException::query()
                ->where("datetime_end", ">", Carbon::now())
                ->with("schoolyear")
                ->with('tutors')
                ->with('location')
                ->orderBy('datetime_start')
                ->get();
        } else {
            $dateExceptions = DateException::query()
                ->with("schoolyear")
                ->with('tutors')
                ->with('location')
                ->orderBy('datetime_start')
                ->get();
        }
        // get all tutoring events that occur in the timespan of the Date exceptions
        // and attach them to the DateException

        return DateExceptionsResource::collection($dateExceptions);
    }

    /**
     * Get all date exceptions that are relevant for a certain timetable.
     * This will be used on the planning card of that timetable.
     * @param $ttId
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function dateExceptionsForTimetable($syid)
    {
        // get the schoolyear of the timetable to get min and max date
        // get all date exceptions that are in that range
        $schoolyear = Schoolyear::findOrFail($syid);
        $dateExceptions = DateException::query()
            ->where("datetime_end", ">", $schoolyear->start_date)
            ->where("datetime_start", "<", $schoolyear->end_date)
            ->with("schoolyear")
            ->with('tutors')
            ->with('location')
            ->orderBy('datetime_start')
            ->get();
        return DateExceptionsResource::collection($dateExceptions);
    }

    public function lessonConflicts($id) {
        Log::info("Checking for conflicts for date exception $id");
        $dateException = DateException::findOrFail($id);
    
        $wholeSchoolStrategy = new WholeSchoolStrategy();
        $locationStrategy = new LocationStrategy();
        $tutorStrategy = new TutorStrategy();
        // Chain of responsibility
        // in most cases whole school strategy will return true
        // and the other strategies will not need to be checked
        $wholeSchoolStrategy->setNext($locationStrategy);
        $locationStrategy->setNext($tutorStrategy);

        $this->conflictChecker->setStrategy($wholeSchoolStrategy);

        $conflictingEvents = $this->conflictChecker->getConflictingEvents($dateException);

        return response()->json([
            "dateExceptionId" => $id,
            "dateException" => [
                'id' => $dateException->id,
                'reason' => $dateException->reason,
                'datetime_start' => $dateException->datetime_start,
                'datetime_end' => $dateException->datetime_end
            ],
            "conflicts" => $conflictingEvents
        ]);
    }

}
