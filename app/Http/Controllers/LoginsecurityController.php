<?php

namespace App\Http\Controllers;
use App\Models\Loginsecurity;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use PragmaRX\Google2FAQRCode\Google2FA; //

class LoginsecurityController extends Controller
{
    //
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show 2FA Setting form
     */
    public function show2faForm(Request $request){
        $user = Auth::user();
        $google2fa_url = "";
        $secret_key = "";

        if ($user->loginsecurity()->exists()){
            $google2fa = (new Google2FA());
            $google2fa_url = $google2fa->getQRCodeInline(
                config('app.name'),
                $user->email,
                $user->loginsecurity->google2fa_secret
            );
            $secret_key = $user->loginsecurity->google2fa_secret;
        }

        $data = array(
            'user' => $user,
            'secret' => $secret_key,
            'google2fa_url' => $google2fa_url
        );

        return view('auth.2fa_settings')->with('data', $data);
    }

    /**
     * Generate 2FA secret key
     */
    public function generate2faSecret(Request $request){
        $user = Auth::user();
        // Initialise the 2FA class
        $google2fa = (new Google2FA());

        // Add the secret key to the registration data
        $login_security = Loginsecurity::firstOrNew(array('user_id' => $user->id));
        $login_security->user_id = $user->id;
        $login_security->google2fa_enable = 0;
        $login_security->google2fa_secret = $google2fa->generateSecretKey();
        $login_security->save();

        return redirect('/2fa')->with('success', trans('generic.twofactorsecretkeygenerated'));
    }

    /**
     * Enable 2FA
     */
    public function enable2fa(Request $request){
        $user = Auth::user();
        $google2fa = (new Google2FA());

        $secret = $request->input('secret');
        $valid = $google2fa->verifyKey($user->loginsecurity->google2fa_secret, $secret);

        if ($valid){
            $user->loginsecurity->google2fa_enable = 1;
            $user->loginsecurity->save();
            return redirect('2fa')->with('success', trans('generic.twofactorenabledsuccess'));
        }else{
            return redirect('2fa')->with('error', trans('generic.twofactorinvalidcode'));
        }
    }

    /**
     * Disable 2FA, not used currently
     */
    public function disable2fa(Request $request){
        if (!(Hash::check($request->get('current-password'), Auth::user()->password))) {
            // The password matches
            return redirect()->back()->with("error", trans('auth.wromgpassword'));
        }

        $validatedData = $request->validate([
            'current-password' => 'required',
        ]);
        $user = Auth::user();
        $user->loginsecurity->google2fa_enable = 0;
        $user->loginsecurity->save();
        return redirect('/2fa')->with('success', trans('generic.twofactornowdisabled'));
    }

    /** API section (Class4) */
    public function verify2fa(Request $request){
        $user = Auth::user();
        $google2fa = (new Google2FA());
        $secret = $request->input('secret');
        $valid = $google2fa->verifyKey($user->loginsecurity->google2fa_secret, $secret);
        return response()->json(['success' => $valid]);
    }

    public function get2faSecret()
    {
        $user = Auth::user();
        // Initialise the 2FA class
        $google2fa = (new Google2FA());
        // Add the secret key to the registration data
        $secret = $google2fa->generateSecretKey();
        $login_security = Loginsecurity::firstOrNew(array('user_id' => $user->id));
        $login_security->user_id = $user->id;
        $login_security->google2fa_enable = 0;
        $login_security->google2fa_secret = $secret;
        $login_security->save();
        return response()->json(['secret' => $secret]);
    }
}
