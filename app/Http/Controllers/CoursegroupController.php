<?php

namespace App\Http\Controllers;

use App\Models\Coursegroup;
use App\Models\Tutor;
use App\Http\Requests\CoursegroupFormRequest;
use App\Http\Requests\PutTutorCoursegroupONCheckRequest;
use App\Http\Requests\ToggleAgegroupForTutorRequest;
use App\Http\Requests\ToggleTutorCoursegroupRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery\Exception;

class CoursegroupController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        return view("coursegroups.list");
    }

    public function apiIndex()
    {
        $courseGroups = Coursegroup::with([
            'courses',
            'courses.recurrenceoption',
            'courses.students',
            'courses.currentStudents',
            'tutors'
        ])->orderBy('name')->get();
        foreach ($courseGroups as $cgIndex => $courseGroup) {
            foreach ($courseGroup->courses as $cIndex => $course) {
                $courseGroups[$cgIndex]->courses[$cIndex]["groupSize"] = $course->groupSize;
            }
        }
        return response()->json($courseGroups);
    }

    public function getApi($id)
    {
        $coursegroup = Coursegroup::with([
            'courses',
            'courses.recurrenceoption',
            'courses.students',
            'courses.currentStudents',
            'tutors'
        ])->findOrFail($id);
        foreach ($coursegroup->courses as $cIndex => $course) {
            $coursegroup->courses[$cIndex]["groupSize"] = $course->groupSize;
        }
        return response()->json($coursegroup);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('coursegroups.create');
    }

    public function apiStore(CoursegroupFormRequest $request)
    {
        // Convert isTrialGroup to integer (1 or 0) handling all possible input types
        $isTrialGroup = in_array($request->isTrialGroup, ['on', true, 1], true) ? 1 : 0;
        $coursegroup = new Coursegroup();
        $coursegroup->domain_id = Auth::user()->domain->id;
        $coursegroup->name = $request->name;
        $coursegroup->webdescription = $request->webDescription;
        $coursegroup->is_trial_group = $isTrialGroup;
        $coursegroup->save();
        return response()->json($coursegroup);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        return view('coursegroups.edit', compact('id'));
    }

    public function apiUpdate(CoursegroupFormRequest $request)
    {
        // $request->isTrialGroup can be 'on' or true (boolean) or 1 (integer). convert to be integer
        $isTrialGroup = in_array($request->isTrialGroup, ['on', true, 1], true) ? 1 : 0;
        $coursegroup = Coursegroup::findOrFail($request->id);
        $coursegroup->name = $request->name;
        $coursegroup->webdescription = $request->webDescription;
        $coursegroup->is_trial_group = $isTrialGroup;
        $coursegroup->save();
        return response()->json($coursegroup);
    }

    public function apiDestroy(Request $request)
    {
        $cg = Coursegroup::findOrFail($request->id);
        $courses = $cg->courses;
        if (count($courses) === 0) {
            try {
                // uncouple tutors in de coursegroup_tutors relation
                $cg->tutors()->detach();
                Coursegroup::where("id", $request->id)->delete();
            } catch (Exception $e) {
                Log::error($e);
                throw $e;
            }
        }
        return response()->json(["msg" => "coursegroup deleted"]);
    }

    /**
     * Get all tutors and flag the tutors that are qualified for the given course
     * @param $courseId
     * @return \Illuminate\Http\JsonResponse
     */
    public function gettutorsforcoursegroup($coursegroupId)
    {
        $resp = [];
        $allTutors = Tutor::getActiveTutors();
        $theCoursegroup = Coursegroup::findOrFail($coursegroupId);
        Log::info("getting tutors for coursegroup $coursegroupId ($theCoursegroup->name)");
        $tutorsConnected = $theCoursegroup->tutors;
        $tutorsAble = [];
        Log::info("currently " . count($tutorsConnected) . " tutors associated with course group $coursegroupId");
        // convert connected (=able/associated) tutors to array
        foreach ($tutorsConnected as $tutor) {
            $tutorsAble[] = $tutor;
        }

        foreach ($allTutors as $tutor) {
            //Log::debug("testing tutor: $tutor->id");
            $foundTutor = null;
            foreach ($tutorsAble as $tutorAble) {
                if ($tutorAble->id === $tutor->id) {
                    $foundTutor = $tutorAble;
                    break;
                }
            }

            if (!empty($foundTutor)) {
                $resp[] = [
                    "id" => $tutor->id,
                    "name" => $tutor->name,
                    "assoc" => true,
                    "child" => $foundTutor->pivot->age_group_child,
                    "adolescent" => $foundTutor->pivot->age_group_adolescent,
                    "adult" => $foundTutor->pivot->age_group_adult
                ];
            } else {
                $resp[] = ["id" => $tutor->id, "name" => $tutor->name, "assoc" => false];
            }
        }
        return response()->json($resp);
    }

    /**
     * Toggles between on and off depending on the togglevalue
     * @param ToggleTutorCoursegroupRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggletutorforcoursegroup(ToggleTutorCoursegroupRequest $request)
    {
        $cg = Coursegroup::findOrFail($request->coursegroupid);
        if ($request->toggleval) {
            // switch on, defaults to all age groups
            $cg->tutors()->attach($request->tutorid, [
                "age_group_child" => 1,
                "age_group_adolescent" => 1,
                "age_group_adult" => 1
            ]);
        } else {
            // switch off
            $cg->tutors()->detach($request->tutorid);
        }
        return response()->json(["msg" => "tutor toggle succesfull"]);
    }

    public function toggletutorforcourseandcheck(PutTutorCoursegroupONCheckRequest $request)
    {
        $cg = Coursegroup::findOrFail($request->coursegroupid);
        $agegroupname = $request->agegroup;
        // switch on + pivot values
        $cg->tutors()->attach($request->tutorid, [
            "age_group_child" => $agegroupname === "child" ? 1 : 0,
            "age_group_adolescent" => $agegroupname === "adolescent" ? 1 : 0,
            "age_group_adult" => $agegroupname === "adult" ? 1 : 0
        ]);

        // do we need to set stuf on and off?
        return response()->json(["msg" => "tutor attached and age group $agegroupname"]);
    }

    /**
     * @param ToggleAgegroupForTutorRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleagegroupfortutor(ToggleAgegroupForTutorRequest $request)
    {
        Log::debug("toggle agegroup $request->agegroup for tutor $request->tutorid");
        $cg = Coursegroup::findOrFail($request->coursegroupid);

        $updateArray = ["age_group_" . $request->agegroup => $request->toggleval];

        // switch on + pivot values
        $cg->tutors()->newPivotStatementForId($request->tutorid)
            ->update($updateArray);

        // If, consequently, this tutor has only '0' values
        // then we should delete the association altogether.
        // There's no need to query for the coursegroup_id or tutor_id as well
        DB::table('coursegroup_tutor')
            ->where([
                ['age_group_adolescent', '=', 0],
                ['age_group_adult', '=', 0],
                ['age_group_child', '=', 0]
            ])->delete();

        // do we need to set stuf on and off?
        return response()->json(["msg" => "agegroup $request->agegroup toggle succesfull"]);
    }


}
