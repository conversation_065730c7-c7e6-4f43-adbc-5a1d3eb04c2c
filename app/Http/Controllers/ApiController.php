<?php

namespace App\Http\Controllers;

use App\Models\Availability;
use App\Models\Course;
use App\Models\Coursegroup;
use App\Models\DateException;
use App\Models\Event;
use App\Models\Logentry;
use App\Models\Mailtemplate;
use App\Models\Registration;
use App\Models\Student;
use App\Models\Studentgroup;
use App\Models\Tutor;
use App\Models\Studentlist;
use App\Http\Resources\StudentCourseResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller {

    /**
     * Get all students. This is custom for the purpose of the homepage search list
     * It contains all relevant info for the dashboard
     * @param bool $asArray respond with array rather than json
     * @param bool $sortOnLastname sort on lastname, (default firstname)
     * @return array|\Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function students($asArray = false, $sortOnLastname = false)
    {
        $responseArr = [];
        Log::info('getting studentinfo for dashboard');
        $isNoAdmin = !Auth::user()->userIsA("admin");
        if ($isNoAdmin) {
            $theTutor = Tutor::findOrFail(Auth::user()->id);
            $validStudents = $theTutor->myStudents();
            Log::info(
                "logged in user is a tutor, there are " . count(
                    $validStudents
                ) . " students this tutor can see in the interface."
            );
        }
        // get all students
        $students = Student::orderBy('name')->with('studentlists')->get();
        foreach ($students as $student) {
            // if the logged-in user is not an admin, he or she only sees their own students
            // skip otherwise
            if ($isNoAdmin && array_search($student->id, $validStudents) === false) {
                continue;
            }
            // init vars
            $isActive = false;
            $registrationData = [];
            $allChecklistsCompleted = true;
            $countChecklists = 0;

            // basic information
            $studentData["student"] = [
                "id" => $student->id,
                "name" => $student->name,
                "firstname" => $student->firstname,
                "city" => $student->city,
                "has_access" => $student->has_access,
                "age" => calculateAge($student->date_of_birth),
                "email" => $student->email,
                "telephone" => $student->telephone,

            ];

            // get all registrations for this student
            $courses = $student->courses;
            foreach ($courses as $courseRegistration) {
                // must be a Registration object to be able to use the pivots
                $registration = Registration::findOrFail($courseRegistration->pivot->id);
                // see if enddate is set or in the future
                $regEndDate = new \DateTime($courseRegistration->pivot->end_date);
                $now = new \DateTime();
                $isActiveRegistration = (($courseRegistration->pivot->end_date == '') || ($regEndDate >= $now));
                // if we find 1 registration that is active, the student is active
                if (($courseRegistration->pivot->end_date == '') || ($regEndDate >= $now)) {
                    $isActive = true;
                }

                // get all checklists
                $registrationData[$registration->id] = [
                    "course_id" => $registration->course_id,
                    "checklists" => [],       // initiate array, to be filled in checklists section
                    "name" => $courseRegistration->name,
                    "isCurrent" => $isActiveRegistration,
                    "keepSchedule" => $courseRegistration->pivot->please_keep_scheduled_time
                ];

                // only look at the checklists of active courses
                if ($isActiveRegistration) {
                    $checklists = $registration->checklists;
                    $countChecklists += count($checklists);
                    $registrationChecklistsAllCompleted = true;
                    foreach ($checklists as $checklist) {
                        $completed = $checklist->isComplete();
                        // if we find 1 checklist that is not complete, the registration is incomplete
                        if (!$completed) {
                            // all checklists of this course
                            $registrationChecklistsAllCompleted = false;
                            // grand total of all checklists of all courses
                            $allChecklistsCompleted = false;
                        }
                        // save the checklist ID
                        $registrationData[$registration->id]["checklists"][] = [$checklist->id => ($completed ? 'completed' : 'not completed')];
                    }
                    $registrationData[$registration->id]["completed"] = $registrationChecklistsAllCompleted;
                }
            }

            $studentData["lastprefdate"] = $student->lastDatePrefsFilledIn;
            $studentData["registrations"] = $registrationData;
            $studentData["noChecklists"] = $countChecklists === 0;
            $studentData["allCompleted"] = $allChecklistsCompleted;
            $studentData["isActive"] = $isActive;
            $studentData["studentlists"] = $student->studentlists;
            $studentData["classyaccount"] = $student->classyuser();
            $studentData["apipin"] = $student->apipin;
            //
            $responseArr[] = $studentData;
        }
        if ($asArray) {
            return $responseArr;
        } else {
            return response()->json($responseArr);
        }
    }

    public function studentsjson()
    {
        $students = $this->students(true);
        $response = [];
        foreach ($students as $student) {
            if ($student["isActive"]) {
                $response[] = $student;
            }
        }
        return response()->json($response);
    }

    /**
     * provides dashboard information studentgroups
     */
    public function studentgroups()
    {
        Log::info('getting studentgroupinfo');
        // get all students
        return Studentgroup::orderBy('lastname')->with(['students', 'courses'])->get();
    }

    public function studentgroupsJson()
    {
        Log::info('getting studentgroupinfo as JSON');
        // get all students
        $studentgroups = Studentgroup::orderBy('lastname')->with(['students', 'courses'])->get();
        return response()->json($studentgroups);
    }

    /**
     * Get all courses with a given name
     */
    public function courses()
    {
        Log::info('getting courseinfo for dashboard');

        // get all names like
        $courses = Course::select('courses.*', 'coursegroups.name as cgname')
            ->leftJoin('coursegroups', 'courses.coursegroup_id', '=', 'coursegroups.id')
            ->orderBy('courses.name')
            ->get();

        $retArr = [];
        foreach ($courses as $course) {
            // explicitly bind the pivot
            $course['recurrenceoption'] = $course->recurrenceoption;
            // the pivot is the registration, this is available through students
            $registrations = $course->students;
            $nrOfActiveUsers = 0;
            foreach ($registrations as $registration) {
                // if no enddate, it's future anyway
                $future = true;
                // if there is an enddate: check if its in the future and set variable accordingly
                if (!empty($registration->pivot->end_date)) {
                    $theEndDate = new \DateTime($registration->pivot->end_date);
                    $today = new \DateTime();
                    $future = $theEndDate >= $today;
                }

                if ($future) {
                    $nrOfActiveUsers++;
                    // One is enough for this course to be called 'Active'
                    // but maybe we want to know how many as well in some future case
                    // for performance, break the loop here, otherwise leave commented-out
                    //break;
                }
            }
            // for future use
            $course['nrOfHistoricUsersIncludesActive'] = count($registrations);
            $course['nrOfActiveUsers'] = $nrOfActiveUsers;
            $course["price_ex_tax"] = $course->price_ex_tax;
            $course["price_is_per"] = $course->price_is_per;
            $course["tax_rate"] = Auth::user()->domain->course_tax_rate;
            //
            $course['isActive'] = $nrOfActiveUsers > 0;
            $retArr[] = $course;
        }

        return response()->json($retArr);
    }

    /**
     * Get all coursegroups
     * @return \Illuminate\Http\JsonResponse
     */
    public function coursegroups()
    {
        Log::info("Getting coursegroups");
        $courseGroups = Coursegroup::with(["tutors", "courses"])->get();
        return response()->json($courseGroups);
    }


    /**
     * find all students that have an active registration
     * and are not already coupled to the chosen studentlistgroup
     * @param $studenlistId
     * @return \Illuminate\Http\JsonResponse
     */
    public function addAllActiveRegistrationToStudentlist($studentlistId)
    {
        // get all students with active registration
        Log::info("getting students with active registrations");
        $studentsActive = Registration::getStudentsActive();
        $studentlist = Studentlist::findOrFail($studentlistId);
        Log::info("Iterating students to be attached to list, un-doubling");
        foreach ($studentsActive as $student) {
            // check if already there
            if (!$studentlist->students()->where('id', $student->id)->exists()) {
                Log::debug("adding student: $student->id");
                $studentlist->students()->attach(Student::findOrFail($student->id));
            }
        }
        return response()->json(['result' => 'success']);
    }


    /**
     * Remove all students from the chosen list
     * @param $studentlistId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeAllStudentsFromList($studentlistId)
    {
        $studentlist = Studentlist::findOrFail($studentlistId);
        $studentlist->students()->detach();
        return response()->json(['result' => 'success']);
    }

    public function removeSelectedStudentsFromList(Request $request, $studentlistId)
    {
        $studentlist = Studentlist::findOrFail($studentlistId);
        $studentIds = $request->studentids;
        if (is_array($studentIds)) {
            Log::info(
                "delete selected students from studenlist $studentlistId. " . count($studentIds) . " id(s) to remove"
            );
            if (count($studentIds) > 0) {
                $studentlist->students()->wherePivotIn('student_id', $studentIds)->detach();
            }
            return response()->json(['result' => 'success']);
        } else {
            return response()->json(['result' => 'fail', 'message' => 'studentIds parameter should be an array']);
        }
    }

    /**
     * add the studentids in the request to the studentlist
     * @param Request $request containing studentids[]
     * @param $studentlistId integer the id of the studentlist
     * @return \Illuminate\Http\JsonResponse
     */
    public function addSelectedStudentsToList(Request $request, $studentlistId)
    {
        $studentlist = Studentlist::findOrFail($studentlistId);
        $studentIds = $request->studentids;
        if (is_array($studentIds)) {
            Log::info("Add selected students to studenlist $studentlistId. " . count($studentIds) . " id(s) to add");
            if (count($studentIds) > 0) {
                foreach ($studentIds as $studentId) {
                    // check if already there
                    if (!$studentlist->students()->where('id', $studentId)->exists()) {
                        Log::debug("adding student: $studentId");
                        $studentlist->students()->attach(Student::findOrFail($studentId));
                    }
                }
            }
        } else {
            return response()->json(['result' => 'fail', 'message' => 'studentIds parameter should be an array']);
        }
    }

    /**
     * get students that have no active course
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function studentsnocourses()
    {
        $studentsNoCourse = Registration::getStudentsNoCourse('lastname');
        return StudentCourseResponse::collection($studentsNoCourse);
    }

    /**
     * get students that have at least one active course
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function studentsactive()
    {
        $studentsActive = Registration::getStudentsActive();
        return StudentCourseResponse::collection($studentsActive);
    }

    public function calendarevents(Request $request)
    {
        return response()->json(Event::getCalendarEvents($request->start, $request->end));
    }

    public function dateexceptionevents(Request $request)
    {
        return response()->json(DateException::getCalendarEvents($request->start, $request->end));
    }

    /**
     * get details of a specific logentry
     * @param int $logentryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function logentry($logentryId = 0)
    {
        $logentry = Logentry::findOrFail($logentryId);
        return response()->json($logentry);
    }

    /**
     * Get all logentries of a student
     * @param $studentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function logentries($studentId = 0)
    {
        $logentries = Logentry::where('student_id', '=', $studentId)
            ->orderBy('updated_at', 'desc')
            ->get();
        return response()->json($logentries);
    }

    /**
     * update excising or create new logentry through API
     * @param Request $request
     * @param int $logentryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveLogentry(Request $request, $logentryId = 0)
    {
        if ($logentryId === 0) {
            Log::info("New logentry for student $request->student_id");
            // new record
            $logentry = new Logentry();
            $logentry->student_id = $request->student_id;
        } else {
            Log::info("Update logentry $logentryId");
            // update: can't change student_id
            $logentry = Logentry::findOrFail($logentryId);
        }
        $logentry->entry = $request->entry;
        $logentry->save();
        return response()->json($logentry);
    }

    /**
     * remove logentry through API
     * @param $id
     * @throws \Exception
     */
    public function delLogentry($id)
    {
        Logentry::where("id", $id)->delete();
        Log::info("Deleted logentry $id");
    }

    /**
     * add timeslice for tutor
     * indicating availability, to be used in planning
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function addTimeslice(Request $request)
    {
        // {chosenFromTime: "10:00", chosenEndTime: "11:30", dayNumber: 2, tutorId: "3"}
        if (
            (preg_match("/^\d{1,2}:\d{2}(:\d{2})?$/", $request->chosenFromTime)) &&
            (preg_match("/^\d{1,2}:\d{2}(:\d{2})?$/", $request->chosenEndTime)) &&
            (preg_match("/^\d{1,4}/", $request->tutorId)) &&
            (preg_match("/[1-7]/", $request->dayNumber))
        ) {
            // fixme: check if it's valid times (not 12:90)
            $av = new Availability();
            $av->tutor_id = $request->tutorId;
            $av->day_number = $request->dayNumber;
            $av->from_time = $request->chosenFromTime;
            $av->to_time = $request->chosenEndTime;
            $av->save();
        } else {
            throw new \Exception("data not valid");
        }
        return response()->json(["result" => "success"]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function updateTimeslice(Request $request)
    {
        // {chosenFromTime: "13:30:00", chosenEndTime: "16:00:00", dayNumber: 2, tutorId: "3", timesliceId: 2}

        /* DEBUG
        if (!preg_match("/^\d{1,2}:\d{2}(:\d{2})?$/", $request->chosenFromTime)) {
            Log::error('fail on 1');
        }
        if (!preg_match("/^\d{1,2}:\d{2}(:\d{2})?$/", $request->chosenEndTime)) {
            Log::error('fail on 3');
        }
        if (!preg_match("/^\d{1,4}/", $request->tutorId)) {
            Log::error('fail on 5');
        }
        if (!preg_match("/[1-7]/", $request->dayNumber)) {
            Log::error('fail on 6');
        }
        if (!preg_match("/^\d+$/", $request->timesliceId)) {
            Log::error('fail on 7');
        }
        */

        if (
            (preg_match("/^\d{1,2}:\d{2}(:\d{2})?$/", $request->chosenFromTime)) &&
            (preg_match("/^\d{1,2}:\d{2}(:\d{2})?$/", $request->chosenEndTime)) &&
            (preg_match("/^\d{1,4}/", $request->tutorId)) &&
            (preg_match("/[1-7]/", $request->dayNumber)) &&
            (preg_match("/^\d+$/", $request->timesliceId))
        ) {
            $av = Availability::findOrFail($request->timesliceId);
            if ($av->tutor_id === intval($request->tutorId)) {
                $av->day_number = $request->dayNumber;
                $av->from_time = $request->chosenFromTime;
                $av->to_time = $request->chosenEndTime;
                $av->save();
            } else {
                throw new \Exception("tutor incorrect ($request->tutorId <> $av->tutor_id)");
            }
        } else {
            throw new \Exception("data not valid");
        }
        return response()->json(["result" => "success"]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function deleteTimeslice(Request $request)
    {
        $av = Availability::findOrFail($request->timesliceId);
        if ($av->tutor_id === intval($request->tutorId)) {
            $av->delete();
        } else {
            throw new \Exception("tutor incorrect ($request->tutorId <> $av->tutor_id)");
        }
        return response()->json(["result" => "success"]);
    }

    /**
     * Api request all -current- students with an access token
     * Please note: We are only returning students that have a valid email address!
     * @param $preferedEmailType string type of email that we want from the emailaddresses in contacts.
     *                                  Defaults to 'planning' also valid: 'finance, promotions'
     * @return \Illuminate\Http\JsonResponse
     */
    public function studentswithaccess($preferedEmailType = "planning")
    {
        $retStudents = [];
        $students = Student::where('has_access', '=', '1')
            ->get();
        foreach ($students as $student) {
            // get email
            // is the correct type available: choose that
            // otherwise the first one found
            // if no email available: drop student (set in list of dropped students)
            $contacts = $this->getEmailContactsAsArray($student->id, $preferedEmailType);

            if (count($contacts) == 0) {
                Log::debug('no email found');
                // no email: drop student
            } else {
                $retStudents[] = [
                    "id" => $student->id,
                    "accesstoken" => $student->accesstoken,
                    "has_access" => $student->has_access,
                    "name" => $student->name,
                    "firstname" => $student->firstname,
                    "city" => $student->city,
                    "email" => implode(",", $contacts)
                ];
            }
        }
        return response()->json($retStudents);
    }


    /////////////////////////////////////// TEMPLATES

    public function getTemplates()
    {
        $t = Mailtemplate::all();
        return response()->json($t);
    }

    /**
     * return email addresses for a student as array
     * @param $studentId
     * @param string $forUsage intended use, defaults to 'planning' also valid: 'finance, promotions'
     * @return mixed
     */
    public function getEmailContactsAsArray($studentId, $forUsage = 'planning')
    {
        // correct to reflect the actual fieldname
        $forUsage = "apply_for_$forUsage";

        Log::info("getting emailcontacts for student $studentId for use in $forUsage");
        $emailContacts = DB::table('studentcontacts')
            ->Where('student_id', '=', $studentId)
            ->Where('contacttype', '=', 'email')
            ->get();

        $retEmail = [];

        if (count($emailContacts) == 0) {
            Log::debug("no email found for $studentId");
            // no email found
        } elseif (count($emailContacts) == 1) {
            Log::debug("only one email found, so not looking at type, student: $studentId");
            // only 1, dont bother about the indented use
            array_push($retEmail, $emailContacts[0]->value);
        } else {
            Log::debug("multiple email found, checking type: $forUsage");
            // more than 1, pick the right one
            // if we find more than 1 with this type, add all of them!
            foreach ($emailContacts as $contact) {
                if ($contact->$forUsage == 1) {
                    if (isValidEmail($contact->value)) {
                        array_push($retEmail, $contact->value);
                    }
                }
            }
            // if the result is still empty: take the first one
            if (count($retEmail) === 0 && (isValidEmail($emailContacts[0]->value))) {
                array_push($retEmail, $emailContacts[0]->value);
            }
        }
        return $retEmail;
    }

    /**
     * Return email addresses for a student as json
     * @param $studentId
     * @param string $forUsage intended use, defaults to 'planning' also valid: 'finance, promotions'
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEmailContacts($studentId, $forUsage = 'planning')
    {
        $emailContacts = $this->getEmailContactsAsArray($studentId, $forUsage);
        return response()->json($emailContacts);
    }

}
