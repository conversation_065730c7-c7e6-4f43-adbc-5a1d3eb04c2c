<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateCalEventRequest;
use App\Models\Attendancenote;
use App\Models\DateException;
use App\Models\Event;
use App\Models\Schoolyear;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use phpDocumentor\Reflection\Types\This;
use Scolavisa\scolib\Mydat2Ndat;
use Scolavisa\scolib\Ndat2Mydat;

class CalendarController extends Controller
{
    /**
     * get events until (not upto-and-until!) for a given date range
     * the api needs the second date to be the end + 1
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function calendarevents(Request $request) {
        $endDT = new \DateTime($request->end);
        $endDT->modify('+1 day');
        $end =  $endDT->format('Y-m-d');
        Log::info("getting events for date range:  $request->start - $end");
        $events = Event::getCalendarEvents($request->start, $end);
        $dateExceptions = DateException::getCalendarEvents($request->start, $end);
        return response()->json($this->layerFilter($events, $dateExceptions));
    }

    /**
     * @param $events array of events
     * @param $dateExceptions array of date exceptions
     * @return array merged array of events and date exceptions based on layering logic
     * @throws \Exception
     */
    private function layerFilter(array $events, array $dateExceptions): array
    {
        $result = [];

        // for all events: check if they should appear on the calendar
        foreach ($events as $event) {
            // if the event is set 'sticky', it will always be shown in the calendar
            if (isset($event["flag_sticky"]) && $event["flag_sticky"] === 1) {
                $result[] = $event;
                continue;
            }
            // if not: check if we have a date exception for this day and time segment
            $startDate = new \DateTime($event["start"]);
            $endDate = new \DateTime($event["end"]);
            // now look for a date exception with datetime_start and datetime_end overlapping this event
            $found = array_reduce($dateExceptions, function($carry, $dateException) use ($startDate, $endDate) {
                $deStart = new \DateTime($dateException["start"]);
                $deEnd = new \DateTime($dateException["end"]);
                return $carry ||
                    (
                        ($startDate <= $deStart && $endDate >= $deStart) ||     // startdate before start of DE
                        ($startDate >= $deStart && $startDate <= $deEnd)        // startdate after start of DE
                    ) &&
                    $dateException["isPlanBlocking"] === 1;                      // only blocking date exceptions
            }, false);
            if (!$found) {
                $result[] = $event;
            }
        }

        // now add all date exceptions
        foreach ($dateExceptions as $dateException) {
            $result[] = $dateException;
        }
        return $result;
    }

    /**
     * Update an event, used by the calendar through API call
     * Can't update the timespan(=>enddatetime) because it is determined by the course
     * as of v3.6.0 endtime may be updated if it's a single event see Shortcut 1843
     * as of v3.24.0 tutors are being sent as array (there may be multiple tutors)
     * as of v3.61.1 attendanceoptions are being sent as array (there may be multiple attendance options, student level)
     * as of v3.66.5 if id = 999999, then it's a new event (not in the database yet)
     * @param $eventIdOrg
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function updateCalendarEvent(UpdateCalEventRequest $request, $eventId) {
        Log::info("Receiving cal event update request");
        $applyOccurrence = $request->applyTo; // one of "thisevent", "allevents", "thisandfutureevents"
        $newDateTime = Ndat2Mydat::getMydat($request->startdate) . " " . $request->starttime;
        $newEndtime = $request->endtime;
        $tutors = $request->tutors;
        $locationID = $request->locationId;
        $attendanceoptions = $request->attendanceoptions ?: [];
        $remarks = $request->input('remarks');
        $publishEventFlag = $request->flag_publish;
        if ($eventId === "999999") {
            Log::info("Creating new event, copied from event with id 999999");
            // create a new event
            $event = new Event();
            $event->datetime = $newDateTime;
            $event->original_datetime = $newDateTime;
            $event->timespan = '0 minutes'; // not used for teaching events
            $event->location_id = $locationID;
            $event->caluniqueid = uniqid(null, true); // not in the previous sequence
            $event->sequence = '0';
            $event->timetable_id = $request->timetable_id;

            $event->save();
            $eventId = $event->id;
        }

        $event = Event::findOrFail($eventId);


        // attendanceOptions are always only for the current event, never for the series.
        // For the rest, we may ignore it.
        // The same goes for 'remarks' and the 'publish' flag
        $event->updateAttendance($attendanceoptions);
        $event->updateRemarks($remarks);
        $event->updatePublishFlag($publishEventFlag);

        // if old data === new data: don't save subsequent event.
        // this prevents from breaking the event from the series, turning it into a standalone event
        // if only the value of attendance option changed, we don't need to break the chain of events
        $onlyRemarkOrAttendanceHasChanged =
            (substr($event->datetime, 0, 16) === $newDateTime) &&
            ($event->tutor_id === $tutors[0]) &&
            ($event->location_id === intval($locationID)) &&
            ($event->endtime === $newEndtime);

        if ($onlyRemarkOrAttendanceHasChanged) {
            Log::info("no changes in date-time, tutor or location");
            return response()->json(['result' => ucfirst(trans('generic.nofurtherchangestosave'))]);
        } else {
            switch ($applyOccurrence) {
                case "thisEvent":
                    $timespan = '';
                    if ($event->endtime !== $newEndtime) {
                        $start = new \DateTime($newDateTime);
                        $diff = $start->diff(new \DateTime(substr($newDateTime,0,10) . " " . $newEndtime));
                        $minutes = ($diff->h * 60) + $diff->i;
                        $timespan = $minutes.' minutes';
                    }
                    Log::info("updating only this event");
                    Log::info("setting calevent '$eventId' to $newDateTime, tutor(s): " . implode(",", $tutors) .", location: $locationID and timespan: $timespan.");
                    // breaks the series, create new unique id and reset the series numbering
                    $this->updateOneEvent($eventId, $newDateTime, $tutors, $locationID, uniqid(null, true), false, $timespan, $onlyRemarkOrAttendanceHasChanged);
                    break;
                case "allEvents":
                    Log::info("updating all events for this subscription to $newDateTime, tutor(s)" . implode(",", $tutors) ." and location: $locationID.");
                    $this->updateAllEventsOfCourseRegistration($eventId, $newDateTime, $tutors, $locationID, false, $onlyRemarkOrAttendanceHasChanged);
                    break;
                case "thisAndFutureEvents":
                    // alle oude events zijn gebroken van deze reeks! wordt hier rekening mee gehouden?
                    Log::info("updating this and all future events for this subscription to $newDateTime, tutor(s)" . implode(",", $tutors) ." and location: $locationID.");
                    $this->updateAllEventsOfCourseRegistration($eventId, $newDateTime, $tutors, $locationID,true, $onlyRemarkOrAttendanceHasChanged);
                    break;
                default:
                    // should never happen because of validation of request in UpdateCalEventRequest
                    Log::info("no valid occurrence indication found.");
                    break;
            }
            return response()->json(['result' => ucfirst(trans('generic.newplanningsaved'))]);
        }
    }

    /**
     * Update the datetime of one event.
     * You may send an event object or it's id
     * @param $theEvent
     * @param $newDateTime
     * @param $tutors
     * @param $locationID
     * @param $useCalUniqueID
     * @param bool $resetSequence
     * @param string $timespan
     * @param bool $onlyRemarkOrAttendanceHasChanged
     * @return void
     */
    private function updateOneEvent(
        $theEvent,
        $newDateTime,
        $tutors,
        $locationID,
        $useCalUniqueID,
        bool $resetSequence,
        string $timespan='',
        bool $onlyRemarkOrAttendanceHasChanged = false
    ) {
        if (is_object($theEvent)) {
            $event = $theEvent;
        } else {
            $event = Event::findOrFail($theEvent);
        }
        Log::info("updating event: $event->id");
        $event->datetime = $newDateTime;
        $event->tutor_id = $tutors[0];  // a teaching event has only 1 tutor currently
                                        // this is the only place to fix if we were to allow multiple tutors for a learning event
                                        // currently this is only available for a date exception
        $event->location_id = $locationID;
        if ($timespan !== '') {
            $event->timespan = $timespan;
        }
        if (!$onlyRemarkOrAttendanceHasChanged) {
            $event->caluniqueid = $useCalUniqueID;
        }
        if ($resetSequence && !$onlyRemarkOrAttendanceHasChanged) {
            $event->sequence = '0';
        } else {
            // increment sequence on any change
            $event->sequence = ((int)$event->sequence + 1);
        }
        $event->save();
    }


    /**
     * Find all events of a timetable (course-student-schoolyear) and update them
     * If $onlyCurrentAndFollowing is true, then only update this event and all folowing events
     * In all cases: only if the events initial dow and time are the same as the initializing event's DOW and time
     * @param $eventId
     * @param $newDateTime
     * @param $tutors
     * @param $locationID
     * @param bool $onlyCurrentAndFollowing
     * @param bool $onlyRemarkOrAttendanceHasChanged
     * @return void
     * @throws \Exception
     */
    private function updateAllEventsOfCourseRegistration(
        $eventId,
        $newDateTime,
        $tutors,
        $locationID,
        bool $onlyCurrentAndFollowing = false,
        bool $onlyRemarkOrAttendanceHasChanged=false
    ) {
        Log::info("Updating multiple events based on event $eventId");
        $originatingEvent = Event::findOrFail($eventId);
        Log::info("dt: $originatingEvent->datetime");

        // find all other events of this chain, or the future events
        // chain events have the same caluniqueid AND sequence number
        // if all events are rescheduled, the chain remains connected, so no caluniqueid, but only a new sequence number
        // if we break the original chain, we additionally need a new caluniqueid.
        $caluniqueid = $originatingEvent->caluniqueid;
        $sequence = $originatingEvent->sequence;
        $where = [["caluniqueid", "=", $caluniqueid], ["sequence", "=", $sequence]];
        if ($onlyCurrentAndFollowing) {
            array_push($where, ["datetime", ">", $originatingEvent->datetime]);
        }
        $events = Event::where($where)->get();
        Log::info("Found " . count($events));
        // this breaks the chain, create new caluniqueid
        if (!$onlyCurrentAndFollowing) {
            $orgCaluniqueId = $caluniqueid;
            $caluniqueid = uniqid(null, true);
            Log::info("which will get new caluniqueid. $orgCaluniqueId will be changed to $caluniqueid");
        }
        $this->updateOneEvent(
            $originatingEvent,
            $newDateTime,
            $tutors,
            $locationID,
            $caluniqueid,
            false,
            '',
            $onlyRemarkOrAttendanceHasChanged
        );
        foreach ($events as $event) {
            $newDtObj = new \DateTime($newDateTime);
            $newDOW = $newDtObj->format('N');
            $newH = $newDtObj->format("H");
            $newM = $newDtObj->format("i");

            $myDtObj = new \DateTime($event->datetime);
            $myWEEK = $myDtObj->format("W");
            $myYEAR = $myDtObj->format("Y");

            $newMyDateTime = new \DateTime();
            $newMyDateTime->setISODate($myYEAR, $myWEEK, $newDOW);
            $newMyDateTime->setTime($newH, $newM);

            $event->datetime = $newMyDateTime->format("Y-m-d H:i");
            $this->updateOneEvent(
                $event,
                $newMyDateTime->format("Y-m-d H:i"),
                $tutors,
                $locationID,
                $caluniqueid,
                false,
                '',
                $onlyRemarkOrAttendanceHasChanged
            );
        }
    }

    /**
     * At this point token validity has already been checked (middleware WordpressAuth)
     * The requesting domain id should be present in the request as attribute (also handled by the middleware)
     * @throws \Exception
     * @return \Illuminate\Http\JsonResponse
     */
    public function wpcal(Request $request)
    {
        Log::info("Receiving get cal events request from Wordpress");
        $domainId = $request->attributes->get('requesting_domain', 0);
        if ($domainId === 0) {
            return response()->json(
                ['message' => "Bad request"],
                Response::HTTP_BAD_REQUEST
            );
        }
        // get id of current school year. if not exists: return 404 with a message "no school year"
        $currentSchoolYear = Schoolyear::getCurrentOrFuture($domainId);
        if (empty($currentSchoolYear)) {
            return response()->json(
                ['message' => "no current or future school year found"],
                Response::HTTP_NOT_FOUND
            );
        }
        // create an array for all weeks in the current school year
        $date = new \DateTime($currentSchoolYear->start_date);
        $startWeekNumberFromYear = $date->format("W");

        // the last day of the year may be in week 52 or 53
        // we use 12-28 as reference: which is always in the last week of year
        $date = new \DateTime($currentSchoolYear->start_year . "-12-28");
        $endWeekNumberFromYear = $date->format("W");

        $startWeekNumberToYear = 1;
        $date = new \DateTime($currentSchoolYear->end_date);
        $endWeekNumberToYear = $date->format("W");
        $range = "$startWeekNumberFromYear-$endWeekNumberFromYear and $startWeekNumberToYear-$endWeekNumberToYear";
        Log::info("School year contains week numbers: $range and starts: " . $currentSchoolYear->start_date . " and ends: " . $currentSchoolYear->end_date);

        $deArray = $this->getDateExceptionsAsArray($currentSchoolYear->start_date, $currentSchoolYear->end_date);
        $deRawList = $deArray["list"];
        $dateExceptions = $deArray["byday"];
        Log::info("found " . count($dateExceptions) . " date exception DAYS in this school year");

        // if the start date of the school year is in future, then we are between school years
        // in that case we need to add as many months as there are between now and the start of the school year
        $dateRangeDateIsClosed = [null, null];
        if($currentSchoolYear->start_date > Carbon::now()->format('Y-m-d')) {
            $startWeekOfCurrentMonth = Carbon::create(Carbon::now()->year, Carbon::now()->month, 1)->format('W');
            $extraRange = range($startWeekOfCurrentMonth, $startWeekNumberFromYear);
            // all dates before the start of the school year are closed, add them to the dateExceptions array
            // from the last day of the previous school year until the start of the current school year
            $dateRangeDateIsClosed = Schoolyear::getDatesBetweenSchoolYears(Carbon::now());
            $calEvents = $this->createYearArray($extraRange, Carbon::now()->year, $dateExceptions, $dateRangeDateIsClosed);
        } else {
            $calEvents =[];
        }
        // add the school year weeks of the FROM year
        $calEventsStartYear = range($startWeekNumberFromYear, $endWeekNumberFromYear);
        $calEvents = array_merge($calEvents, $this->createYearArray($calEventsStartYear, $currentSchoolYear->start_year, $dateExceptions, $dateRangeDateIsClosed));
        // add the school year weeks of the TO year
        $calEventsEndYear = range($startWeekNumberToYear, $endWeekNumberToYear);
        $calEvents = array_merge($calEvents, $this->createYearArray($calEventsEndYear, $currentSchoolYear->end_year, $dateExceptions));

        return response()->json(["calEvents" => $calEvents, "dateExceptionsList" => $deRawList]);
    }

    /**
     * Creates a range of arrays containing weeknumber, date and "green" tag
     * meaning "the school is open on this day
     * @param $weekNumbers array of weeknummers [20, 21, 22, ....]
     * @param $startYear int school year to which the week numbers are referring to
     * @param $dateExceptions array containing all days that have a date exception
     * @param array $dateRangeDateIsClosed array [start, end] containing all days that are closed (between school years)
     * @throws \Exception
     * @return \array[][]
     */
    private function createYearArray($weekNumbers, $startYear, $dateExceptions, $dateRangeDateIsClosed = [null, null])
    {
        // loop through all week numbers
        return array_map(function ($week) use($startYear, $dateExceptions, $dateRangeDateIsClosed) {
            $date = new \DateTime();
            $date->setISODate($startYear, $week);
            $loopArr = [];
            // loop through all days of 1 week. left-most is monday!
            for ($i=0; $i<7; $i++){
                $targetDate = $date->format('Y-m-d');
                $displayDate = $date->format('d-m-Y');
                $targetDay = $date->format('d');
                // open/closed based on date exception
                $className = isset($dateExceptions[$targetDate]) ? "closed" : "open";
                $mouseOver = $dateExceptions[$targetDate] ?? $displayDate . " - Open";
                // if the target date is between the start and end of dateRangeDateIsClosed, then it's closed
                if ($dateRangeDateIsClosed[0] !== null && $dateRangeDateIsClosed[1] !== null) {
                    $target = new Carbon($targetDate);
                    $begin = new Carbon($dateRangeDateIsClosed[0]);
                    $end = new Carbon($dateRangeDateIsClosed[1]);
                    if ($target >= $begin && $targetDate <= $end) {
                        // based on 'between school years' date range (this overrides date exceptions)
                        $className = "closed";
                        $mouseOver = trans("generic.schoolclosed");
                    }
                }

                $loopArr[] = [
                    "date" => $targetDate,      // used to find the dates that get a different color
                    "value" => $targetDay,      // will be rendered in the cell
                    "className" => $className,  // CSS class indicating day state
                    "mouseover" =>  $mouseOver  // mouseover / tooltip
                ];
                // next day in this week
                $date->add(new \DateInterval('P1D'));
            }
            return ["weeknumber" => $week, "data" => $loopArr];
        }, $weekNumbers);
    }

    /**
     * Gets the relevant date exceptions and reshuffles them into an easy accessible array
     * Please note: this is not the same as "where schoolyear_id = $currentSchoolYear->id"
     * because some dates (summer holiday of the previous year) may overlap this school year
     *
     * whole-school events are events that are not present in the table dateexception_tutor
     * Non-blocking events have plan_blocking = 0; they also have a calendar_color and may have a detail_url
     *
     * @param $syStartDate
     * @param $syEndDate
     * @return array
     * @throws \Exception
     */
    private function getDateExceptionsAsArray($syStartDate, $syEndDate)
    {
        $dateExceptions = DB::table('date_exceptions')
            ->select('datetime_start', 'datetime_end', 'reason', 'plan_blocking', 'calendar_color', 'detail_url')
            ->leftJoin('dateexception_tutor AS deu', 'deu.date_exception_id', '=', 'date_exceptions.id')
            ->where(function($query) use ($syStartDate, $syEndDate) {
                $query->whereBetween("datetime_end", [$syStartDate, $syEndDate])
                      ->orWhereBetween("datetime_start", [$syStartDate, $syEndDate]);
            })
            ->whereNull('deu.user_id')
            ->get();
        Log:info("found " . count($dateExceptions) . " date exceptions in this school year");
        // get events that should be published in the calendar
        // needs to have the same fields as the date exception, because we merge them later on
        $publishEvents = Event::query()
            ->where('flag_publish', '=', 1)
            ->where('datetime', '>=', $syStartDate)
            ->where('datetime', '<=', $syEndDate)
            ->with('tutor')
            ->get();

        foreach ($publishEvents as $publishEvent) {
            // calculate endtime from datetime and timespan
            $start = new \DateTime($publishEvent->datetime);
            $end = new \DateTime($publishEvent->datetime);
            $timeSpan = explode(" ", $publishEvent->timespan, 2)[0];
            $end->add(new \DateInterval('PT' . $timeSpan . 'M'));
            $publishEvent->datetime_start = $publishEvent->datetime;
            $publishEvent->datetime_end = $end->format("Y-m-d H:i:s");
            $publishEvent->reason = $publishEvent->course->name;
            $publishEvent->plan_blocking = 0;
            // events do not have a color as yet, we'll use the tutor's color as fallback for now
            $publishEvent->calendar_color = $publishEvent->tutor->hexcolor;
            // events have no detail url as yet
            $publishEvent->detail_url = "";
        }

        // now merge the two collections so we can order them by date
        $dateExceptions = $dateExceptions->merge($publishEvents);
        $dateExceptions = $dateExceptions->sortBy('datetime_start');
        // convert to array
        $dateExceptions = $dateExceptions->values()->all();

        // reshuffle for quick access
        $exceptionArray = [];
        $sy_start = new \DateTime($syStartDate);
        foreach ($dateExceptions as $dateException) {
            $begin = new \DateTime($dateException->datetime_start);
            $end = new \DateTime($dateException->datetime_end);
            for ($i = $begin; $i <= $end; $i->modify('+1 day')) {
                if ($i >= $sy_start) { // there may be two events on the same day
                    $reasonString = $dateException->reason;
                    if ($dateException->plan_blocking === 0) {
                        $reasonString = $reasonString . "|" . $dateException->calendar_color;
                        if (!empty($dateException->detail_url)) {
                            $reasonString = $reasonString . "|" . $dateException->detail_url;
                        } else {
                            $reasonString = $reasonString . "|";
                        }
                        // add time for this event if applicable. 00:00-23:59 is whole day, won't be shown as time segment
                        if ($begin->format("H:i") !== "00:00") {
                            $reasonString = $reasonString . "|" . $begin->format("H:i") . "|" . $end->format("H:i");
                        }
                    }
                    $exceptionArray[$i->format("Y-m-d")]
                        = isset($exceptionArray[$i->format("Y-m-d")])
                        ? $exceptionArray[$i->format("Y-m-d")] . ":~:" . $reasonString
                        : $reasonString;
                }
            }
        }
        return ["byday" => $exceptionArray, "list" => $dateExceptions];
    }

    /**
     * Toggle the sticky flag of an event.
     * This flag forces the event to be displayed at the top of the calendar,
     * independent of a conflicting appointment or date exception
     *
     * @param int $eventId The ID of the event to toggle the sticky flag for.
     * @return \Illuminate\Http\JsonResponse The JSON response indicating the result of the toggle operation.
     */
    public function toggleStickyEvent($eventId)
    {
        $event = Event::findOrFail($eventId);
        // check correct domain_id
        if ($event->student->domain_id !== auth()->user()->domain_id) {
            return response()->json(['result' => 'error']);
        }
        $event->flag_sticky = !$event->flag_sticky;
        $event->save();
        return response()->json(['result' => 'ok']);
    }

    public function scheduleForDay()
    {
        return view('planning.scheduleforday');
    }
}
