<?php

namespace App\Http\Controllers;

use App\Http\Requests\RecurrenceOptionRequest;
use App\Models\RecurrenceOption;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RecurrenceOptionsController extends Controller {
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Contracts\View\View
     */
	public function index() {
		return view('recurrenceoptions.list');
	}

    /**
     * get a listing of all recurrence options for API call.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiIndex()
    {
        $allRecOptions = RecurrenceOption::query()
            ->orderBy('nr_of_times')
            ->orderBy('timeunit')
            ->with('courses')
            ->get();
        return response()->json($allRecOptions);
    }

    /**
     * @param RecurrenceOptionRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiStore(RecurrenceOptionRequest $request)
    {
        Log::info("storing new recurrenceoption");
        $recOption = new RecurrenceOption();
        $recOption->domain_id                       = Auth::user()->domain->id;
        $recOption->description                     = $request->input('description');
        $recOption->nr_of_times                     = $request->input('nr_of_times');
        $recOption->timeunit                        = $request->input('timeunit');
        $recOption->per_interval                    = $request->input('per_interval');
        $recOption->ends_after_nr_of_occurrences    = $request->input('ends_after_nr_of_occurrences');
        $recOption->save();
        return response()->json($recOption);
    }

    /**
     * @param RecurrenceOptionRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiUpdate(RecurrenceOptionRequest $request)
    {
        Log::info("updating recurrenceoption id: " . $request->input('id'));
        $recOption = RecurrenceOption::findOrFail($request->input('id'));
        $recOption->description                     = $request->input('description');
        $recOption->nr_of_times                     = $request->input('nr_of_times');
        $recOption->timeunit                        = $request->input('timeunit');
        $recOption->per_interval                    = $request->input('per_interval');
        $recOption->ends_after_nr_of_occurrences    = $request->input('ends_after_nr_of_occurrences');
        $recOption->save();
        return response()->json($recOption);
    }

    /**
     * Remove the specified resource from storage.
     * @param  int $id
     */
	public function apiDestroy($id) {
	    Log::info("deleting recurrenceoption id: $id");
	    RecurrenceOption::query()->where('id', $id)->delete();
        return response()->json(['status' => 'ok']);
	}
}
