<?php

namespace App\Http\Controllers;

use App\Http\Resources\BroadcastDataResource;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class LocationBroadcastController extends Controller
{
    /**
     * List all locations for a domain and return them with their events for today
     * @param Request $request
     * @param int $locationId if specified, only return this location
     * @return BroadcastDataResource
     */
    public function list(Request $request, $locationId = 0)
    {
        $domain = $request->attributes->get('domain');
        Log::info("Request location list for: " . $domain->name);
        $where = [
            ['locations.domain_id', "=", $domain->id]
        ];
        if ($locationId > 0) {
            $where[] = ['locations.id', "=", $locationId];
        }
        $locations = DB::table('locations')->where($where)->get();
        // get today's events for each location
        foreach ($locations as $location) {
            $location->events = DB::table('events')
                ->select('events.id', 'datetime', 'timespan', 'students.firstname as studentname', 'students.lastname as groupname', 'users.name as tutorname')
                ->where('location_id', $location->id)
                ->whereDate('datetime', Carbon::now())
                ->leftJoin('timetables', 'events.timetable_id', '=', 'timetables.id')
                ->leftJoin('course_student', 'timetables.course_student_id', '=', 'course_student.id')
                ->leftJoin('users', 'events.tutor_id', '=', 'users.id')
                ->leftJoin('students', 'course_student.student_id', '=', 'students.id')
                ->orderBy('datetime')
                ->get();
        }
        $data["locations"] = $locations;
        $data["domain"] = $domain;
        return new BroadcastDataResource($data);
    }

    /**
     * Get the token for a domain based on the requesting IP address
     * @return \Illuminate\Http\JsonResponse
     */
    public function getToken()
    {
        $ip = request()->ip();
        $domain = DB::table('domains')
            ->select('id', 'allowed_ip_addresses')
            ->where('allowed_ip_addresses', 'like', '%' . $ip . '%')
            ->first();
        if ($domain) {
            $token = $this->generateToken($domain->id);
            // return secure httponly cookie for the domain token which is valid for 5 minutes
            return response()->json(
                ["domaintoken" => $token, "message" => "OK"])
                ->cookie('domaintoken', $token, 5, '/', null, true, true);
        } else {
            Log::warning("Unauthorized request for token from IP: " . $ip);
            return response()->json(
                ["message" => "Unauthorized"],
                Response::HTTP_UNAUTHORIZED
            );
        }
    }

    public function generateToken($domainId)
    {
        $token = md5($domainId . time());
        DB::table('domains')->where('id', $domainId)->update(['domaintoken' => $token]);
        return $token;
    }

    private function token2domain($domaintoken)
    {
        $domain = DB::table('domains')
            ->select('id', 'allowed_ip_addresses')
            ->where('domaintoken', $domaintoken)
            ->firstOrFail();
        return $domain;
    }
}
