<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;

class SetupController extends Controller {

    public function overview() {

        $incompleteSetupOptions = Auth::user()->domain->setupcomplete();
        if(count($incompleteSetupOptions) !== 0) {
            return view('setup.overview', compact('incompleteSetupOptions'));
        } else {
            return redirect("/");
        }
    }

}
