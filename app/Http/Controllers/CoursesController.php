<?php

namespace App\Http\Controllers;

use App\Http\Resources\CourseResource;
use App\Models\Course;
use App\Models\Coursegroup;
use App\Models\RecurrenceOption;
use App\Models\Registration;
use App\Models\TrialcourseRelation;
use App\Models\Tutor;
use App\Http\Requests\CourseFormRequest;
use App\Http\Requests\LinkCourseTrialcourseRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\StringManipulator;

class CoursesController extends Controller {
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $courses = Course::orderBy("name")->where('archive', '=', 0)->get();
        return view('courses.list', compact('courses'));
    }

    public function indexApi()
    {
        $courses = Course::query()
            ->where('archive', '=', 0)
            ->orderBy('name')
            ->with(['coursegroup']);
        $courses->get();
        foreach ($courses as $key => $course) {
            $courses[$key]["fullname"] = $course->fullname;
            $courses[$key]["price_ex_tax"] = $course->price_ex_tax;
            $courses[$key]["price_is_per"] = $course->price_is_per;
            $courses[$key]["tax_rate"] = Auth::user()->domain->course_tax_rate;
        }
        return response()->json($courses);
    }

    /**
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getAll(Request $request)
    {
        $excludeArchive = $request->query("excludeArchive") !== null ? intval($request->query("excludeArchive")) : 1;
        Log::info("get all courses, " . ($excludeArchive === 1 ? "excluding archived" : "including archived"));
        // if the user is a teacher we only return their own students
        if (!Auth::user()->userIsA('admin')) {
            $tutor = Tutor::findOrFail(Auth::id());
            $c = $tutor->myCourses;
        } else {
            if ($excludeArchive === 1) {
                $c = Course::with('students', 'recurrenceoption')
                    ->where('archive', '=', 0)
                    ->orderBy('name')
                    ->get();
            } else {
                $c = Course::with('students', 'recurrenceoption')
                    ->orderBy('name')
                    ->get();
            }
        }
        return CourseResource::collection($c);
    }

    public function getApi(Request $request, $id)
    {
        $course = Course::with('students', 'recurrenceoption', 'coursegroup')->findOrFail($id);
        return new CourseResource($course);
    }

    public function coursetrialcourse()
    {
        $relations = TrialcourseRelation::trialcourseRelations();
        return response()->json($relations);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Contracts\View\View
     */
    public function create()
    {
        Log::info("create course interface");
        if (Auth::user()->domain->trialLimitReachedCourse) {
            Log::warning(
                "domain " . Auth::user(
                )->domain->name . " has reached the account limit for courses in a trial account."
            );
            Log::warning("not showing entryform for new course");
            return redirect()->route('home')->with(
                'message',
                "error: " . ucfirst(trans("generic.limitoftrialaccountreachedcourse"))
            );
        }
        return view('courses.create');
    }

    /**
     * Store a newly created resource in storage through API call.
     *
     * @param CourseFormRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeApi(CourseFormRequest $request)
    {
        Log::info("create / save new course");
        if (Auth::user()->domain->trialLimitReachedCourse) {
            Log::warning(
                "domain " . Auth::user(
                )->domain->name . " has reached the account limit for courses in a trial account."
            );
            Log::warning("not saving new course");
            return response()->json(
                [
                    'message' => ucfirst(trans("generic.limitoftrialaccountreachedcourse"))
                ],
                403
            );
        }
        $course = new Course();
        Log::debug('$request->price_ex_tax:' . $request->price_ex_tax);
        Log::debug('str_replace' . str_replace(',', '.', $request->price_ex_tax));

        $course->name = $request->name;
        $course->domain_id = Auth::user()->domain->id;
        $course->price_ex_tax = str_replace(',', '.', $request->price_ex_tax);
        $course->price_ex_tax_sub_adult = str_replace(',', '.', $request->price_ex_tax_sub_adult);
        $course->price_invoice = str_replace(',', '.', $request->price_invoice);
        $course->price_is_per = $request->price_is_per === '' ? 'month' : $request->price_is_per;
        $course->tax_rate = Auth::user()->domain->course_tax_rate;
        $course->coursegroup_id = $request->coursegroup;
        $course->recurrenceoption_id = $request->recurrenceoption_id;
        $course->is_trial_course = $request->is_trial_course;
        $course->archive = $request->archive || 0;
        $course->variant_code = $request->variant_code;
        $course->group_size_min = $request->group_size_min;
        $course->group_size_max = $request->group_size_max;
        $course->save();
        return response()->json(["message" => "course saved", "course" => $course]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        return view('courses.edit', compact('id'));
    }

    /**
     * @param CourseFormRequest $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateApi(CourseFormRequest $request, $id)
    {
        Log::info("updating course data");
        $course = Course::findOrFail($id);
        $course->name = $request->name;
        $course->price_ex_tax_sub_adult = StringManipulator::price2database($request->price_ex_tax_sub_adult);
        $course->price_invoice = StringManipulator::price2database($request->price_invoice);
        $course->price_is_per = $request->price_is_per === '' ? 'month' : $request->price_is_per;
        $course->tax_rate = $course->tax_rate ?? Auth::user()->domain->course_tax_rate;
        $course->coursegroup_id = $request->coursegroup;
        $course->recurrenceoption_id = $request->recurrenceoption_id;
        $course->is_trial_course = $request->is_trial_course; // don't change if not present in request;
        $course->archive = $request->archive; // don't change if not present in request;
        $course->variant_code = $request->variant_code;
        $course->group_size_min = $request->group_size_min;
        $course->group_size_max = $request->group_size_max;
        $course->save();
        return response()->json(["message" => "course saved", "course" => $course]);
    }

    /**
     * Remove the specified resource from storage.
     * But only if there are no students coupled
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function destroy($id)
    {
        // only if there are no students
        $students = Registration::getStudentsForCourse($id);
        if (count($students) == 0) {
            Course::findOrFail($id)->delete();
        }
    }

    /**
     * response for api call to get all studentgroups for a course
     * @param $courseid
     * @return \Illuminate\Http\JsonResponse
     */
    public static function getStudentgroupsOfCourse($courseid)
    {
        $c = Course::findOrFail($courseid);
        return response()->json($c->studentgroups);
    }

    public function archived()
    {
        return response()->view('courses.archived');
    }

    /**
     * response for api call to get all students for a course
     * @param $courseid
     * @return \Illuminate\Http\JsonResponse
     */
    public static function getStudentsOfCourse($courseid)
    {
        $c = Course::findOrFail($courseid);
        return response()->json($c->students);
    }

    public function linkcoursetrialcourse(LinkCourseTrialcourseRequest $request)
    {
        // check if this link is already in place
        $nrOfLinks = TrialcourseRelation::where([
            ['trialcourse_id', '=', $request->trialCourse],
            ['course_id', '=', $request->courseId]
        ])->count();
        // then link trialcourse and course
        if ($nrOfLinks === 0) {
            $newLink = new TrialcourseRelation();
            $newLink->trialcourse_id = $request->trialCourse;
            $newLink->course_id = $request->courseId;
            $newLink->save();
        }
    }

    public function unlinkcoursetrialcourse(Request $request)
    {
        TrialcourseRelation::where([
            ['trialcourse_id', '=', $request->trialCourseId],
            ['course_id', '=', $request->courseId]
        ])->delete();
    }

    public function coursesfortargetcourse(Request $request, $courseId = 0)
    {
        return response()->json(TrialcourseRelation::trialcoursesForCourse($courseId));
    }
}
