<?php

namespace App\Http\Controllers;

use App\Http\Requests\AttendancenoteRequest;
use App\Http\Requests\CalendarEventsRequest;
use App\Http\Requests\UrlForEventRequest;
use App\Http\Resources\AttendanceForEventResource;
use App\Http\Resources\CalendarEventsResource;
use App\Http\Resources\MessageResource;
use App\Http\Resources\TutorResource;
use App\Models\Attendancenote;
use App\Models\Attendanceoption;
use App\Models\DateException;
use App\Models\Document;
use App\Models\Event;
use App\Models\Message;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Tutor;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClassEController extends Controller
{
    /**
     * Get the events for a logged-in user for today.
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function myDay()
    {
        Log::info("get today for user: " . Auth::user()->id);
        $user = Auth::user();
        $user->last_active_at = DB::raw("NOW()");
        $user->save();

        $today = new \DateTime();
        // $tomorrow = new \DateTime('tomorrow');
        // $tomorrow->sub(\DateInterval::createFromDateString('1 second'));

        $events = self::getEventsAndAppointmentsInRange(
            $today->format('Y-m-d 00:00:00'),
            $today->format('Y-m-d 23:59:59')
        );
        Log::info("found " . count($events) . " events and date exceptions for tutor " . Auth::user()->id);
        return CalendarEventsResource::collection($events);
    }

    /**
     * Get all events for the logged-in user for a period as indicated in the request (from-to)
     * Parameter 'to' should always be at least one day bigger than from,
     * One day meaning the whole day indicated by 'from'
     * If 'to' is omitted then it will be initialised as 1 day after 'from'
     *
     * @param CalendarEventsRequest $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getCalendarEvents(CalendarEventsRequest $request)
    {
        Log::info("get day for user: " . Auth::user()->id);
        $from = strip_tags($request->from);
        if (empty($request->to)) {
            if (strlen($from) == 10)
                $to = $from . " 23:59:59";
            else
                $to = $from; // will not return any events
        } else {
            $to = strip_tags($request->to);
        }

        Log::info("getting day events: $from - $to userid: " . Auth::user()->id);
        $events = self::getEventsAndAppointmentsInRange($from, $to);
        return CalendarEventsResource::collection($events);
    }

    /**
     * Get a single event for the logged-in user
     * @param $eventId
     * @return CalendarEventsResource|\Illuminate\Http\JsonResponse
     */
    public function getCalendarEvent($eventId)
    {
        $event = Event::findOrFail($eventId);
        if ($event->tutor_id === Auth::user()->id || Auth::user()->userIsA("admin")) {
            return new CalendarEventsResource($event);
        }
        return response()->json(["message" => "not allowed"], Response::HTTP_FORBIDDEN);
    }

    /**
     * Generic function to get events and date exceptions for a given tutor within a given date-range
     * I want to use union for all queries, to be able to sort on datetime_start over the whole result set.
     * That means I need to make sure all queries have the same column-count.
     * @param $from string start from this date
     * @param $to string up and until this date
     * @param $tutorId int which tutor (whole school will be returned regardless)
     * @param $onlyAppointments boolean skip getting the tutor events
     * @return \Illuminate\Support\Collection
     */
    private static function getEventsAndAppointmentsInRange($from, $to, $onlyAppointments = false)
    {
        Log::info("getEventsAndAppointmentsInRange for $from - $to " .
            ($onlyAppointments ? "(only appointments)" : "(appointments & tutor events)")
        );
        $domainId = Auth::user()->domain_id;
        $tutorId = Auth::user()->id;


        $dateExceptions = DB::table("date_exceptions as de")
            ->select(DB::raw("de.id, datetime_start, datetime_end, TIMEDIFF(de.datetime_end, de.datetime_start) as timespan, 
            '-' as remarks, '-' as student_name, '-' as course_name, '-' as is_trial_course, l.name as location_name, de.reason,
            'dateexception' as event_type, IF(dt.user_id is null, '1', '0') as wholeschool"))
            ->leftJoin("dateexception_tutor as dt", "de.id", "=", "dt.date_exception_id")
            ->leftJoin("locations as l", "l.id", "=", "de.location_id")
            ->where('de.domain_id', '=', $domainId)
            ->where(function ($query) use ($tutorId) {
                $query->where('dt.user_id', '=', $tutorId)
                    ->orWhereNull('dt.user_id'); // whole school
            })
            ->where(function ($q) use ($from, $to) {
                // date exception is within the requested range
                $q->whereBetween('datetime_start', [$from, $to])
                    // or hasn't ended yet
                    ->orWhereBetween('datetime_end', [$from, $to])
                    // or starts before and ends after the requested range
                    ->orWhere([
                        ["datetime_start", "<", $from],
                        ["datetime_end", ">=", $to]
                    ]);
            })
            ->limit(160);

        // skip tutoring events
        if (!$onlyAppointments) {
            // GET all teaching events for this tutor
            $events = DB::table('events as ev')
                ->select(DB::raw("ev.id, ev.datetime AS datetime_start,
                DATE_ADD(ev.datetime, INTERVAL SUBSTR(ev.timespan, 1, 3) minute) AS datetime_end, timespan, ev.remarks,
                s.name as student_name, c.name as course_name, c.is_trial_course, l.name as location_name, '-' AS reason, 
                'event' AS event_type, '0' as wholeschool"
                ))
                ->leftJoin("timetables as tt", "tt.id", "=", "ev.timetable_id")
                ->leftJoin("course_student as cs", "cs.id", "=", "tt.course_student_id")
                ->leftJoin("students as s", "s.id", "=", "cs.student_id")
                ->leftJoin("courses as c", "c.id", "=", "cs.course_id")
                ->leftJoin("users as t", "t.id", "=", "ev.tutor_id")
                ->leftJoin("locations as l", "l.id", "=", "ev.location_id")
                ->where([
                    ["s.domain_id", "=", $domainId],
                    ["t.id", "=", $tutorId]
                ])
                ->whereBetween('ev.datetime', [$from, $to])
                ->limit(160);
        }
        // Combine (union) the separate queries to be able to sort over the whole response set
        $appointmentsToReturn = $onlyAppointments
            ? $dateExceptions
                ->orderBy("datetime_start")
                ->get()
            : $dateExceptions
                ->union($events)
                ->orderBy("datetime_start")
                ->get();
        // check partial participation for student groups
        foreach ($appointmentsToReturn as $appointment) {
            // check if the students are all present at the event date/time
            if ($appointment->event_type === 'event') {
                $appointment->attendingStudents = Event::getParticipatingStudents($appointment->id);
            }
        }
        Log::info(count($appointmentsToReturn) . " events and date exceptions found");
        return $appointmentsToReturn;
    }

    /**
     * Gets all messages for the tutor (read and unread) = tutors inbox
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getMyInbox()
    {
        Log::info("getting message inbox for tutor " . Auth::user()->id);
        $messages = Message::where([
            ["to_type", "=", "tutor"],
            ["to_id", "=", Auth::user()->id]
        ])
            ->orderBy('created_at', 'desc')
            ->get();
        Log::info(count($messages) . " messages found");
        return MessageResource::collection($messages);
    }

    /**
     * tag a tutors message as opened
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function messageOpened(Request $request)
    {
        $messageId = $request->input('messageId');
        $message = Message::findOrFail($messageId);
        if (($message->domain_id === Auth::user()->domain_id) && ($message->to_id === Auth::user()->id)) {
            Log::info("set message $messageId to 'read' at " . Carbon::now());
            $message->read_at = Carbon::now();
            $message->save();
            return response()->json(["message" => "message updated"]);
        }
        Log::error("message $messageId could not be updated by user " .
            Auth::user()->id . ": insufficient user rights");
        Log::error("to: " . $message->to_id . " user: " . Auth::user()->id);
        return response()->json(["message" => "message could not be updated"], Response::HTTP_FORBIDDEN);
    }

    /**
     * Get info about the authenticated user
     * @return TutorResource
     */
    public function user()
    {
        Log::info("getting info about tutor " . Auth::user()->id);
        return new TutorResource(Auth::user());
    }

    /* **************************** */
    /* RESOURCE APPOINTMENTS        */
    /* **************************** */
    /**
     * Get a logged-in tutors future appointments
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getMyAppointments()
    {
        $today = new \DateTime();
        $from = substr($today->format('Y-m-d H:i:s'), 0, 10) . " 00:00:00";
        $nextYear = new \DateTimeImmutable("+1 year");
        $to = $nextYear->format('Y-m-d H:i:s');

        Log::info("getting appointments: $from / $to / " . Auth::user()->id);
        $dateExceptions = self::getEventsAndAppointmentsInRange($from, $to, true);
        return CalendarEventsResource::collection($dateExceptions);
    }

    /**
     * Confirm an appointment
     * @param $appointmentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirmAppointment($appointmentId)
    {
        $tutor = Tutor::findOrFail(Auth::user()->id);
        Log::info("Set confirmed to 1 for appointment $appointmentId for tutor: $tutor->id");
        DateException::findOrFail($appointmentId)
            ->tutors()
            ->updateExistingPivot(
                $tutor->id,
                ['confirmed' => 1]
            );
        return response()->json(["message" => "appointment updated"]);
    }


    /**
     * Helper for querying past events for the requested tutor that have no attendance filled out
     * This function does the actual query and returns the rows
     * business rule: the tutor can only see events for the current school year and up until half an hour before now
     * to prevent tutors filling in attendance for events that will not start closer than half an hour in the future
     * events that will start further in the future will not be returned
     * @param $schoolYearId
     * @param $tutorId
     * @return \Illuminate\Database\Eloquent\Collection|int
     */
    private function getUnAccountedPastEvents($schoolYearId, $tutorId)
    {
        return Event::query()
            ->select(['events.id', 'events.datetime', 'students.id as student_id', 'students.name as student_name', 'students.date_of_birth as student_date_of_birth', 'courses.id as course_id', 'courses.name as course_name'])
            ->leftJoin('attendancenotes', 'events.id', '=', 'attendancenotes.event_id')
            ->leftJoin('timetables', 'timetables.id', '=', 'events.timetable_id')
            ->leftJoin('course_student', 'course_student.id', '=', 'timetables.course_student_id')
            ->leftJoin('students', 'students.id', '=', 'course_student.student_id')
            ->leftJoin('courses', 'courses.id', '=', 'course_student.course_id')
            ->where('events.tutor_id', '=', $tutorId)
            ->where('timetables.schoolyear_id', '=', $schoolYearId)
            ->where('events.datetime', '<=', \Carbon\Carbon::now()->addMinutes(180))
            ->whereNull('attendancenotes.id')
            ->orderBy('events.datetime', 'desc') // most recent first, todo: remove after development
            ->limit(5)  // limit to 5 events, todo: set to 10 after development
            ->get();
    }

    /**
     * Same as attendanceCount but returns the actual events instead of the count
     * @param $tutorId
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function emptyAttendanceNotes()
    {
        // get current school year
        $sy = Schoolyear::getCurrentOrFuture(Auth::user()->domain_id, 'fail');
        $user = Auth::user();
        if ($user->userIsA('tutor')) {
            $tutorId = $user->id;
        } else {
            return response()->json(["message" => "not allowed"], Response::HTTP_NOT_ACCEPTABLE);
        }
        if (!empty($sy)) {
            // get past events for this tutor, for this school year without attendance notice (not in studentEventLog)
            return response()->json([
                "tutor" => $tutorId,
                "events" => AttendanceForEventResource::collection($this->getUnAccountedPastEvents($sy->id, $tutorId)),
                "message" => "events without attendance note for a student"
            ]);
        } else {
            Log::warning("can't get events without attendance notes if we are between school years");
            return response()->json([
                "message" => "can't get events without attendance notes if we are between school years"
            ], 204);
        }
    }

    /**
     * Returns the events that have no attendance filled out for the logged-in user (should be a tutor)
     * @param $tutorId
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function emptyAttendanceNotesForTutor($tutorId = 0)
    {
        if (Auth::user()->userIsA("admin")) {
            $tutor = Tutor::findOrFail($tutorId);
            if ($tutor->domain_id === Auth::user()->domain_id) {
                return $this->emptyAttendanceNotes($tutorId);
            } else {
                // not allowed
                Log::error("User " . Auth::user()->id . " is not allowed to request data on tutor " . $tutorId . " (domain mismatch)");
                return response()->json(["message" => "not allowed"], 405);
            }
        } else if (Auth::user()->userIsA("tutor")) {
            return $this->emptyAttendanceNotes(Auth::user()->id);
        }
        return response()->json([]);
    }

    /**
     * todo: check if attendance note already exists
     * if admin: insert
     * if tutor: only if this tutor is also present in the event
     * @param AttendancenoteRequest $request
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function upsertattendance(AttendancenoteRequest $request)
    {
        Log::info("upsert attendance notes for students at event " . $request->input('eventId') . " by user " . Auth::user()->id );
        $event = Event::findOrFail($request->input('eventId'));
        $studentAttendances = $request->input('studentAttendance');
        DB::beginTransaction();
        foreach ($studentAttendances as $studentAttendance) {
            $student = Student::findOrFail($studentAttendance['student_id']);
            $attoption = Attendanceoption::findOrFail($studentAttendance['attendanceoption_id']);
            if (
                (Auth::user()->userIsA("admin") && $student->domain_id === Auth::user()->domain_id) ||
                ($event->tutor_id === Auth::user()->id && $student->domain_id === Auth::user()->domain_id)) {
                Attendancenote::upsert($event->id, $student->id, $attoption->id);
            } else {
                // insufficient rights
                Log::error("User " . Auth::user()->id . " is not allowed to set attendance for " . $event->id . " event");
                DB::rollBack();
                return response()->json(["message" => "not allowed"], Response::HTTP_FORBIDDEN);
            }
        }
        DB::commit();
        return response()->json(["message" => "Attendanceoption(s) saved"]);
    }
    
    /**
     * get list of possible attendance options
     * @return \Illuminate\Http\JsonResponse
     */
    public function attendanceOptions()
    {
        return response()->json(Attendanceoption::select("id", "label", "action_tutor")->get());
    }

    /**
     * todo: check if this url is already present for this event
     * @param UrlForEventRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveURLforEvent(UrlForEventRequest $request)
    {
        try {
            $doc = new Document();
            $doc->domain_id = Auth::user()->domain_id;
            $doc->event_id = $request->input('eventID');
            $doc->url = $request->input('videoURL');
            $doc->label = $request->input('label') ?? "video " . date("Y-m-d H:i:s");
            $doc->type = "url";
            $doc->save();
            return response()->json(["message" => "URL saved"]);
        } catch (\Exception $e) {
            Log::error("Error saving URL for event " . $request->input('eventID') . ": " . $e->getMessage());
            return response()->json(["message" => "error saving URL"], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * todo: check if the tutor is a tutor for the event
     * @param $docId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteURLfromEvent($docId)
    {
        try {
            $doc = Document::findOrFail($docId);
        } catch (\Exception $e) {
            Log::error("Error deleting URL " . $docId . ": " . $e->getMessage());
            return response()->json(["message" => "error deleting URL"], Response::HTTP_NOT_FOUND);
        }
        if ($doc->domain_id === Auth::user()->domain_id) {
            $doc->delete();
            return response()->json(["message" => "URL deleted"]);
        } else {
            Log::error("User " . Auth::user()->id . " is not allowed to delete URL " . $docId . " (domain mismatch)");
            return response()->json(["message" => "not allowed"], Response::HTTP_FORBIDDEN);
        }
    }


    /**
     * Get the attendance notes that are set to have a tutor action, but,
     * because the video url is empty, we conclude that the tutor has not yet acted on it
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttendanceNotesWithEmptyTutorAction($tutorId = 0)
    {
        $tutor = Auth::user();
        if ($tutor->userIsA("admin") && $tutorId > 0) {
            $tutor = Tutor::findOrFail($tutorId);
        }
        $events = DB::table('attendancenotes as an')
            ->select('event_id', 'events.datetime', 'student_id', 'action_tutor', 'students.name')
            ->leftJoin('events', 'an.event_id', '=', 'events.id')
            ->leftJoin('attendanceoptions', 'an.attendanceoption_id', '=', 'attendanceoptions.id')
            ->leftJoin('students', 'an.student_id', '=', 'students.id')
            ->where('events.tutor_id', '=', $tutor->id)
            ->where('attendanceoptions.action_tutor', '<>', '')
            ->whereNotIn('event_id',
                DB::table('documents')
                    ->select('event_id')
                    ->where('url', '<>', '')
            )
            ->get();
        // also add the course for every event
        foreach ($events as $event) {
            $event->course = Event::findOrFail($event->event_id)?->course?->name;
        }
        return response()->json($events);
    }
}
