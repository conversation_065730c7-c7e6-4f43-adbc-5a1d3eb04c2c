<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateMessageRequest;
use App\Http\Resources\MessageResource;
use App\Models\Message;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MessageController extends Controller {

    /**
     * Create a message for ClassE communication
     * from id and to id are always users in the users table
     * get the role to set the type.
     * The type can be sent along to the api because if a user has two roles,
     * we want to know the role for this message, usa: admin or tutor
     * @param CreateMessageRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(CreateMessageRequest $request)
    {
        Log::info("Create new message for $request->toId, from $request->fromId");
        // recipients (to) data
        $recipient = User::findOrFail($request->toId);
        $recipientLabel = $recipient->name;
        // currently: only student (future, ClassY) or tutor.
        $recipientType = (isset($request->toType) && in_array($request->toType, ["student", "tutor"]))
            ? $request->toType
            : ($recipient->userIsA('tutor') ? 'tutor' : 'student');

        // senders (from) data
        $sender = Auth::user();
        $senderLabel = $sender->name;
        $senderType = (isset($request->fromType) && in_array($request->toType, ["admin", "tutor"]))
            ? $request->fromType
            : ($sender->userIsA('admin') ? 'admin' : 'tutor');

        // save message to database
        $message = new Message();
        $message->domain_id = $sender->domain_id;
        $message->from_id = $sender->id;
        $message->from_type = $senderType;
        $message->from_label = $senderLabel;
        $message->to_id = $request->toId;
        $message->to_type = $recipientType;
        $message->to_label = $recipientLabel;
        $message->subject = $request->subject;
        $message->body = $request->body;
        try {
            $message->save();
            Log::info("Message saved successfully");
            return response()->json(["result" => "success", "message" => "Message saved successfully"]);
        } catch (\Exception $e) {
            Log::error("error saving message: " . $e->getMessage());
            return response()->json(["result" => "fail", "message" => $e->getMessage()], 403);
        }
    }

    public function get()
    {
        // for now limit to 20 last messages
        $messages = Message::orderBy("created_at", "desc")->take(20)->get();
        return response()->json($messages);
    }
    /**
     * temp(?) interface to quicly swo all messages sent to ClassE
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function list()
    {
        return view('messages.list');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     * @throws \Exception
     */
    public function destroy($id) {
        Message::where("id", $id)->delete();
    }

}
