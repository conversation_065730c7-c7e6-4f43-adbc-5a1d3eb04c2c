<?php

namespace App\Http\Controllers;

use App\Models\Registration;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Task;
use App\Models\Timetable;
use App\Models\Trialstudent;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Scolavisa\scolib\LocaleDate;
use Scolavisa\scolib\MiscDate;

class AlertController extends Controller
{
    /**
     * Get alert status of the database
     * See view homesections._alerts, which contains a Vue component: alert section
     * which is implemented in resources/assets/js/components/AlertSection.vue
     * 'showas' attribute can be alert|notice
     * @return \Illuminate\Http\JsonResponse
     */
    public function status()
    {
        Log::info("Getting alert status for alertbox");
        $result = [];

        /// Section: tasks single lesson need invoicing
        /// these are tasks that have startdate in the past and are not closed
        /// also get tasks that are not closed and are past due date
        Log::info("Getting tasks past due and past openened but not closed for alertbox");
        $result = array_merge($result, $this->getActiveTasks(true));

        /// Section: timetable conflicts
        Log::info("Getting calendar conflicts for alertbox");
        $result = array_merge($result, $this->getTimeTableConflicts(true));

        // Get cal conflicts on Location and/or tutors
        Log::info("Getting tutor and location conflicts for alertbox");
        $result = array_merge($result, $this->getTutorAndLocationConflicts(true));

        // Getting student without any registrations
        Log::info("Getting students without any registration for alertbox");
        $result = array_merge($result, $this->getStudentWOReg(true));


        // next the trial lessons
        Log::info("Getting trial lessons for alertbox");
        $result = array_merge($result, $this->getTrialsRequestWOStudentAccount());

        // next: *active* students without email address
        Log::info("Getting active students without e-mailaddress for alertbox");
        $result = array_merge($result, $this->getActiveStudentsWOEmailaddress(true));

        // Trial account for CLASS?
        if (Auth::user()->domain->status === "trial") {
            // Limits reached?
            Log::info("Getting trial limits reached for alertbox");
            $result = array_merge($result, $this->getTrialLimits());
        }

        // do we have a valid schoolyear
        Log::info("Checking active schoolyear available for alertbox");
        $result = array_merge($result, $this->getActiveSchoolyearAvailable());

        // Any requested but not signed registrations
        // Any registrations that should have not been requested to sign
        $result= array_merge($result, $this->getSignRequestsBacklog(true));

        Log::info("Checking for incomplete checklists");
        $result= array_merge($result, $this->getTotalIncompleteChecklists(true));

        return response()->json(["error" => $result]);
    }

    /**
     * Get timetable conflicts as separate array of alerts
     * @param bool $onlySummary
     * @return array
     */
    public function getTimeTableConflicts($onlySummary=false)
    {
        $result = [];
        $resultTimetableConflicts = Timetable::getCurrentConflicts();
        $count = count($resultTimetableConflicts);
        Log::warning("Found $count calender conflict(s).");

        if ($onlySummary & $count > 0) {
            $result[] = [
                "result"    => "error",
                "showas"    => "alert",
                "type"      => "calendarconflict",
                "message"   => trans("generic.eventconflicts") . ": $count"
            ];
        } else {
            foreach ($resultTimetableConflicts as $timetableConflict) {
                $result[] = [
                    "result" => "error",
                    "showas" => "alert",
                    "type" => "calendarconflict",
                    "student" => [
                        "id" => $timetableConflict["student_id"],
                        "name" => $timetableConflict["student_name"],
                    ],
                    "expires" => $timetableConflict["startdt"],
                    "message" => trans("generic.eventconflictswith", [
                        "coursename" => $timetableConflict["course_name"],
                        "eventdate" => LocaleDate::getLocaleSpecificDate($timetableConflict["startdt"], App::getLocale()),
                        "reason" => $timetableConflict["reason"]
                    ]),
                    "link" => "/timetableedit/" . $timetableConflict["course_student_id"]
                ];
            }
        }
        return $result;
    }

    /**
     * @param bool $onlySummary
     * @return array
     */
    public function getStudentWOReg($onlySummary=false)
    {
        $result = [];
        $studentsWithoutRegistration = Student::getStudentsWithoutRegistration();
        $count = count($studentsWithoutRegistration);
        Log::warning("Found $count students without registration.");
        if ($onlySummary & $count > 0) {
            $result[] = [
                "result"    => "notice",
                "showas"    => "notice",
                "type"      => "studentworegistration",
                "message"   => trans("generic.studentworegistration") . ": $count"
            ];
        } else {
            foreach ($studentsWithoutRegistration as $studentWithoutRegistration) {
                $result[] = [
                    "result" => "notice",
                    "showas" => "notice",
                    "type" => "studentworegistration",
                    "student" => [
                        "id" => $studentWithoutRegistration->id,
                        "name" => $studentWithoutRegistration->name
                    ],
                    "message" => ucfirst(trans('generic.studentworegistration') . ": $studentWithoutRegistration->name"),
                    "link" => "/students/" . $studentWithoutRegistration->id . "/edit"
                ];
            }
        }
        return $result;
    }

    /**
     * @param bool $onlySummary
     * @return array
     */
    public function getActiveTasks($onlySummary=false)
    {
        $result = [];
        $tasks = Task::getActiveTasks();
        $count = count($tasks);
        Log::info("Found $count Task(s).");
        if ($onlySummary & $count > 0) {
            // split the results by past due date or not (severity alert or notice, result both 'notice')
            $pastDue = [];
            $notPastDue = [];
            foreach ($tasks as $task) {
                if (!empty($task->date_due) && $task->date_due < Carbon::now()) {
                    $pastDue[] = $task;
                } else {
                    $notPastDue[] = $task;
                }
            }
            if (count($pastDue) > 0) {
                $result[] = [
                    "result"    => "error",
                    "showas"    => "alert",
                    "type"      => "activetask",
                    "message"   => trans_choice("generic.actionnaftertaskpastdue", count($pastDue), ["student" => count($pastDue)])
                ];
            }
            if (count($notPastDue) > 0) {
                $result[] = [
                    "result"    => "notice",
                    "showas"    => "notice",
                    "type"      => "activetask",
                    "message"   => trans_choice("generic.actionnaftertask", count($notPastDue), ["student" => count($notPastDue)])
                ];
            }
        } else {
            foreach ($tasks as $task) {
                $result[] = [
                    "result" => "notice",
                    "showas" => (!empty($task->date_due) && $task->date_due < Carbon::now()) ? "alert" : "notice",
                    "type" => "activetask",
                    "student" => [
                        "id" => $task->student_id,
                        "name" => $task->name
                    ],
                    "message" => ucfirst(trans_choice('generic.' . $task->tasktype->description , 1, ["student" => $task->name])),
                    "link" => "/tasks/$task->id/edit"
                ];
            }
        }
        return $result;
    }

    /**
     * @param bool $onlySummary
     * @return array
     */
    public function getTutorAndLocationConflicts($onlySummary=false)
    {
        $result = [];
        $resultTimetableConflicts = Timetable::getLocationAndTutorConflicts();
        $count = count($resultTimetableConflicts);
        Log::info("Found $count Conflicts for tutors and locations.");
        if ($onlySummary & $count > 0) {
            $result[] = [
                "result"    => "error",
                "showas"    => "alert",
                "type"      => "calendarconflict",
                "message"   => trans("generic.calendarconflicttutororlocation") . ": $count"
            ];
        } else {
            foreach ($resultTimetableConflicts as $timetableConflict) {
                $result[] = [
                    "result" => "error",
                    "showas" => "alert",
                    "type" => "calendarconflict",
                    "student" => [
                        "id" => $timetableConflict->student_id,
                        "name" => $timetableConflict->name,
                    ],
                    "message" =>
                        trans("generic.calendarconflicttutororlocation") . " " .
                        trans('generic.for') . " " . $timetableConflict->name,
                    "link" => "/timetableedit/$timetableConflict->registration_id"
                ];
            }
        }
        return $result;
    }

    /**
     * @return array
     */
    public function getTrialsRequestWOStudentAccount()
    {
        $result = [];
        $openTrialstudents = Trialstudent::trialrequestsWithoutStudentAccount();
        foreach ($openTrialstudents as $trialstudent) {
            $name = $trialstudent->firstname . (empty($trialstudent->preposition) ? ' ' : " $trialstudent->preposition " ) . $trialstudent->lastname;
            $result[] = [
                "result"    => "notice",
                "showas"    => "notice",
                "type"      => "trialrequest",
                "student"   => [
                    "id"    => 0,       // trial student has nu student id yet
                    "name"  => $name
                ],
                "message"   => ucfirst(trans_choice('generic.newtriallessonrequests', 1) . ": $name"),
                "link"      => "/trialstudents"
            ];
        }
        Log::info("Found " . count($openTrialstudents) . " trialstudents without a connected student account");
        return $result;
    }

    /**
     * @param bool $onlySummary
     * @return array
     */
    public function getActiveStudentsWOEmailaddress(bool $onlySummary=false)
    {
        $result = [];
        $studentWOEmail = Student::getActiveStudentsWithoutEmail();
        $count = count($studentWOEmail);
        Log::info("found $count students without email address");
        if ($onlySummary & $count > 0) {
            $result[] = [
                "result"    => "error",
                "showas"    => "alert",
                "type"      => "studentwoemail",
                "message"   => trans("generic.studentwoemailaddress") . ": $count"
            ];
        } else {
            foreach ($studentWOEmail as $studentWithoutEmail) {
                $result[] = [
                    "result" => "error",
                    "showas" => "alert",
                    "type" => "studentwoemail",
                    "student" => [
                        "id" => $studentWithoutEmail->id,
                        "name" => $studentWithoutEmail->name
                    ],
                    "message" => ucfirst(trans('generic.studentwoemailaddress') . ": $studentWithoutEmail->name"),
                    "link" => "/students/" . $studentWithoutEmail->id . "/edit"
                ];
            }
        }
        return $result;
    }

    /**
     * @return array
     */
    public function getIncompletChecklists()
    {
        $result = [];
        $registrations = Registration::getRegistrationWithChecklists(true);
        foreach ($registrations as $registration) {
            $result[] = [
                "result"    => "error",
                "showas"    => "alert",
                "type"      => "incompletechecklistspresent",
                "student"   => [
                    "id"    => $registration->student_id,
                    "name"  => $registration->name
                ],
                "message"   => ucfirst(trans('generic.incompletechecklistspresent') . ": $registration->name for course: $registration->course, checklist: $registration->checklistname"),
                "link"      => "/registrationedit/" . $registration->id
            ];
        }
        Log::info("found " . count($registrations) . " registrations with incomplete checklist");
        return $result;
    }

    /**
     * only summary, not detailed view
     * @return array
     */
    public function getTrialLimits()
    {
        $result = [];
        if (Auth::user()->domain->trialLimitReachedStudent) {
            $result[] = [
                "result" => "notice",
                "showas" => "notice",
                "type" => "limitoftrialaccountreached",
                "message" => trans("generic.triallimitstudentsreached")
            ];
            Log::info("trial limit reached: # students");
        }
        if (Auth::user()->domain->trialLimitReachedTutor) {
            $result[] = [
                "result" => "notice",
                "showas" => "notice",
                "type" => "limitoftrialaccountreached",
                "message" => trans("generic.triallimittutorsreached")
            ];
            Log::info("trial limit reached: # tutors");
        }
        if (Auth::user()->domain->trialLimitReachedCourse) {
            $result[] = [
                "result" => "notice",
                "showas" => "notice",
                "type" => "limitoftrialaccountreached",
                "message" => trans("generic.triallimitcoursesreached")
            ];
            Log::info("trial limit reached: # courses");
        }
        return $result;
    }

    /**
     * @return array
     */
    public function getActiveSchoolyearAvailable()
    {
        $result = [];
        $nrOfSchoolyears = Schoolyear::where('end_date', '>', DB::raw('now()'))->count();
        if ($nrOfSchoolyears === 0) {
            // alert user they need to add a school year
            $result[] = [
                "result"    => "error",
                "showas"    => "alert",
                "type"      => "noschoolyear",
                "message"   => ucfirst(trans('generic.noschoolyearavailable')),
                "link"      => "/schoolyears/create"
            ];
            Log::info("Domain has no active school year!");
        }
        return $result;
    }
    /**
     * Get the current birthdays
     * this is shown on the view homesections._birthdays, which contains the Vue component: birthdaysection
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function birthdays()
    {
        $result = [];

        $result["setting"]["adultThreshold"] = intval(Auth::user()->domain->adult_threshold);

        $daysBeforeBirthday = intval(Auth::user()->domain->warn_before_birthday);
        $daysBeforeAdultBirthday = intval(Auth::user()->domain->warn_before_adult);

        // get upcoming birthdays for the given period
        $result["birthdays"] = $this->getbirthdays($daysBeforeBirthday);
        Log::debug("Found " . count($result["birthdays"]) . " birthdays within $daysBeforeBirthday days");
        if ($daysBeforeAdultBirthday > 0 && $daysBeforeBirthday <= $daysBeforeAdultBirthday) {
            $adults = $this->getbirthdays($daysBeforeAdultBirthday, true);
            Log::debug("Found " . count($adults) . " birthdays becoming adult within $daysBeforeAdultBirthday days");
            // merge
            $allBirthdays = array_merge($adults, $result["birthdays"]);
            // undouble
            $allBirthdays = array_unique($allBirthdays, SORT_REGULAR);
            // sort by closest birthdate
            usort($allBirthdays, function ($bda, $bdb) {
                $datetime1 = intval(str_replace("-", "", substr($bda->date_of_birth, 4)));
                $datetime2 = intval(str_replace("-", "", substr($bdb->date_of_birth, 4)));
                return $datetime1 <=> $datetime2;
            });
            $result["birthdays"] = $allBirthdays;
        }
        return response()->json($result);
    }

    /**
     * Get all records of students that will be celebrating their birthday within $nrOfDaysAhead days
     * Add indication if student becomes customerAdultThreshold that may mean extra action to take for the admin
     * @param $nrOfDaysAhead
     * @return mixed
     * @throws \Exception
     */
    private function getbirthdays($nrOfDaysAhead, $onlyAdultThreshold=false)
    {
        $adultThreshold = intval(Auth::user()->domain->adult_threshold);

        if (is_int($nrOfDaysAhead)) {
            $q = "SELECT * " .
                "FROM students " .
                "WHERE DATE_ADD(date_of_birth, " .
                "INTERVAL YEAR(CURDATE())-YEAR(date_of_birth) " .
                " + IF(DAYOFYEAR(CURDATE()) > DAYOFYEAR(date_of_birth),1,0) YEAR) " .
                "BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL " . $nrOfDaysAhead . " DAY) " .
                "AND (firstname <> '-' AND date_of_birth <> '1800-01-01') " .
                "AND domain_id = '" . Auth::user()->domain->id . "' " .
                "ORDER BY MONTH(date_of_birth) ASC, DAYOFMONTH(date_of_birth) ASC";
        }

        if ($onlyAdultThreshold) {
            Log::debug("checking for adults only (1 year before age: $adultThreshold)");
        }

        $students = DB::select($q);
        foreach ($students as $index => $student) {
            $birthday = substr($student->date_of_birth, 5);
            $today = date('m-d');
            $students[$index]->itsMyBirthday = ($today === $birthday);
            $students[$index]->age = MiscDate::calculateAge($student->date_of_birth);
            $students[$index]->threshold = $adultThreshold;
            // -1: because it's not their birthday yet
            if (($onlyAdultThreshold) && (intval($students[$index]->age) !== $adultThreshold -1)) {
                unset($students[$index]);
            }
        }
        return $students;
    }

    /**
     * @param bool $onlySummary
     * @return array
     */
    private function getSignRequestsBacklog($onlySummary=false) {
        $result = [];
        $regsNotSigned = Registration::select(
            "course_student.id as registration_id", "course_student.start_date", "course_student.sign_requested_at",
            "students.id as student_id", "students.name as student_name",
            "courses.id as course_id", "courses.name as course_name"
        )
            ->leftJoin("students",  "student_id", "=", "students.id")
            ->leftJoin("courses",  "course_id", "=", "courses.id")
            ->where("students.domain_id", "=", Auth::user()->domain_id)
            ->whereNotNull("sign_code")
            ->where("sign_request_send", "=", "1")
            ->whereNull("signed")
            ->where(function($q) {
                $q->whereNull("end_date")
                    ->orWhere("end_date", ">", Carbon::now());
            })
            ->where("sign_requested_at", "<", Carbon::now()->subDays("7"))
            ->where("firstname", "<>", "-")
            ->where("date_of_birth", "<>", "1800-01-01")
            ->get();

        $count = count($regsNotSigned);
        Log::warning("Found $count registrations not signed after request.");

        if ($onlySummary & $count > 0) {
            $result[] = [
                "result"    => "notice",
                "showas"    => "notice",
                "type"      => "registrationnotsignedafterrequest",
                "message"   => trans("generic.registrationnotsignedafterrequest") . ": $count"
            ];
        } else {
            foreach ($regsNotSigned as $reg) {
                $result[] = [
                    "result" => "notice",
                    "showas" => "notice",
                    "type" => "registrationnotsignedafterrequest",
                    "student" => [
                        "id" => $reg->student_id,
                        "name" => $reg->student_name
                    ],
                    "course" => [
                        "id" => $reg->course_id,
                        "name" => $reg->course_name
                    ],
                    "message" => ucfirst(trans('generic.registrationnotsignedafterrequest')) .
                        ": " . $reg->student_name . ", " . $reg->course_name,
                    "link" => "registrationedit/" . $reg->registration_id
                ];
            }
        }

        $regsNotSignRequested = Registration::select(
            "course_student.id as registration_id", "course_student.start_date",
            "students.id as student_id", "students.name as student_name",
            "courses.id as course_id", "courses.name as course_name"
        )
            ->leftJoin("students",  "student_id", "=", "students.id")
            ->leftJoin("courses",  "course_id", "=", "courses.id")
            ->leftJoin("recurrenceoptions", "courses.recurrenceoption_id", "recurrenceoptions.id")
            ->where("students.domain_id", "=", Auth::user()->domain_id)
            ->whereNull("sign_request_send")
            ->whereNull("signed")
            ->where("firstname", "<>", "-")
            ->where("date_of_birth", "<>", "1800-01-01")
            ->where("ends_after_nr_of_occurrences", "<>", "1") // single lesson or trial lesson
            ->where("is_trial_course", "<>", "1") // better safe than sorry
            ->where(function($q) {
                $q->whereNull("end_date")
                    ->orWhere("end_date", ">", Carbon::now());
            })
            ->get();
        $count = count($regsNotSignRequested);
        Log::warning("Found $count registrations without signature requested.");

        if ($onlySummary & $count > 0) {
            $result[] = [
                "result"    => "error",
                "showas"    => "notice",
                "type"      => "registrationnotsignrequested",
                "message"   => trans("generic.registrationnotsignrequested") . ": $count"
            ];
        } else {

            foreach ($regsNotSignRequested as $reg) {
                $result[] = [
                    "result" => "error",
                    "showas" => "notice",
                    "type" => "registrationnotsignrequested",
                    "student" => [
                        "id" => $reg->student_id,
                        "name" => $reg->student_name
                    ],
                    "course" => [
                        "id" => $reg->course_id,
                        "name" => $reg->course_name
                    ],
                    "message" => ucfirst(trans('generic.registrationnotsignrequested')) .
                        ": " . $reg->student_name . ", " . $reg->course_name,
                    "link" => "registrationedit/" . $reg->registration_id
                ];
            }
        }
        return $result;
    }

    /**
     * this function has only a summary, no detailed response (that would be too large)
     * parameter $onlySummary is there for consistency with all other alert functions
     * @param bool $onlySummary
     * @return array
     */
    public function getTotalIncompleteChecklists($onlySummary=true)
    {
        $result = [];
        $registrations = Registration::getRegistrationWithChecklists(true);
        $count = count($registrations);
        Log::info("found $count incomplete checklists");
        if ($count > 0 & $onlySummary) {
            $result[] = [
                "result"    => "notice",
                "showas"    => "notice",
                "type"      => "incompletechecklistspresent",
                "message"   => count($registrations) . " " . trans("generic.incompletechecklistspresent")
            ];
        }
        return $result;
    }

    /**
     * Start page with overall view of all existing alarms
     * @return \Illuminate\Contracts\View\View
     */
    public function reportAlarms()
    {
        return view('reports.alarms');
    }

    /**
     * api call for every separate alert
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function alertstatus(Request $request)
    {
        $status = [];
        $subject = $request->input('subject');
        switch ($subject) {
            case "timetableconflicts":
                $status = $this->getTimeTableConflicts();
                break;
            case "tutorlocationconflicts":
                $status = $this->getTutorAndLocationConflicts();
                break;
            case "conflictsstudentsnoregistration":
                $status = $this->getStudentWOReg();
                break;
            case "conflictsstudentsnoemail":
                $status = $this->getActiveStudentsWOEmailaddress();
                break;
            case "activetasks":
                $status = $this->getActiveTasks();
                break;
            case "triallessons":
                $status = $this->getTrialsRequestWOStudentAccount();
                break;
            case "activeschoolyearavailable":
                $status = $this->getActiveSchoolyearAvailable();
                break;
            case "signrequestbacklog":
                $status = $this->getSignRequestsBacklog();
                break;
            case "incompletechecklist":
                $status = $this->getIncompletChecklists();
                break;
            default:
                break;
        }

        return response()->json([
            "status"    => $status,
            "subject"   => $subject
        ]);
    }
}
