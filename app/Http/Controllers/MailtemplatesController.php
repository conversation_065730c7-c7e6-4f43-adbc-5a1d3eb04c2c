<?php

namespace App\Http\Controllers;

use App\Models\Mailtemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MailtemplatesController extends Controller {
    //
    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {
        $mailtemplate = new Mailtemplate();
        $mailtemplate->domain_id = Auth::user()->domain->id;
        /*
         label
         mailtype_id
         entry
         */
        $mailtemplate->save();
    }

}
