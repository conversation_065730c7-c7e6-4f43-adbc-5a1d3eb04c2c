<?php

namespace App\Http\Controllers;

use App\Http\Requests\StudentlistRequest;
use App\Http\Resources\StudentStudentListResource;
use App\Models\Student;
use App\Models\Studentlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class StudentlistController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index() {
        return view("studentlists.list");
    }

    /**
     * Get all student lists
     * @return \Illuminate\Http\JsonResponse
     */
    public function studentlists()
    {
        Log::info("Getting studentlists");
        $studentlists = Studentlist::with("students")->get();
        return response()->json($studentlists);
    }

    /**
     * Add a new studentlist
     * @param StudentlistRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addStudentlist(StudentlistRequest $request)
    {
        $studentlist = new Studentlist();
        $studentlist->domain_id = Auth::user()->domain->id;
        $studentlist->name = $request->name;
        $studentlist->hexcolor= substr($request->hexcolor, 1); // skip the # character
        $studentlist->remarks = $request->remarks;
        $studentlist->save();
        return response()->json($studentlist);
    }

    /**
     * Update an existing studentlist
     * @param StudentlistRequest $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStudentlist(StudentlistRequest $request, $id)
    {
        Log::info("Updating studentlist $id");
        $studentlist = Studentlist::findOrFail($id);
        $studentlist->name = $request->name;

        // dump request
        Log::info("Request: " . json_encode($request->all()));


        // we always save hex color without the # character
        // if we receive a rgb color (due to some frontend component), convert to hex
        if (strpos($request->hexcolor, "rgb") !== false) {
            $rgb = explode(",", str_replace("rgb(", "", str_replace(")", "", $request->hexcolor)));
            $hex = "#";
            foreach ($rgb as $value) {
                $hex .= str_pad(dechex($value), 2, "0", STR_PAD_LEFT);
            }
            $request->hexcolor = $hex;
        }
        // if we receive a rgba color, convert to hex
        else if (strpos($request->hexcolor, "rgba") !== false) {
            $rgb = sscanf($request->hexcolor, "rgba(%d, %d, %d, %f)");
            $request->hexcolor = sprintf("#%02x%02x%02x", $rgb[0], $rgb[1], $rgb[2]);
        }
        $studentlist->hexcolor= substr($request->hexcolor, 1); // skip the # character
        $studentlist->remarks = $request->remarks;
        $studentlist->save();
        return response()->json($studentlist);
    }

    /**
     * remove studentlist through API
     * @param Request $request
     * @throws \Exception
     */
    public function delStudentlist($slid)
    {
        $studentlist = Studentlist::findOrFail($slid);

        // First detach all students from the list
        $studentlist->students()->detach();

        // Then delete the list itself
        $studentlist->delete();

        Log::info("Deleted studentlist $slid");

        return response()->json(['success' => true]);
    }


    /**
     * @param $studentId
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getStudentListsOfStudent($studentId)
    {
        $alllists = Studentlist::all();
        foreach ($alllists as $key => $list) {
            // check if $studentId is member of this list
            $alllists[$key]["participating"] = $list->isMemberOf($studentId);
        }
        return StudentStudentListResource::collection($alllists);
    }

    /**
     * @param Request $request
     * @param $studentId
     * @return StudentStudentListResource
     */
    public function saveStudentListsParticipationOfStudent(Request $request, $studentId)
    {
        Log::info("Modify studentlist. action $request->action for student $studentId on studentlist $request->studentlistId");
        $theList = Studentlist::findOrFail($request->studentlistId);
        $theStudent = Student::findOrFail($studentId);
        if ($theList) {
            if (($request->action === 'subscribe') && (!$theList->isMemberOf($studentId))) {
                // couple student
                $theList->students()->attach($theStudent);
            } elseif (($request->action === 'unsubscribe') && ($theList->isMemberOf($studentId))) {
                // decouple student from list
                $theList->students()->detach($theStudent);
            }
        }
        $theList->save();
        return new StudentStudentListResource($theList);
    }

}
