<?php

namespace App\Http\Controllers;

use App\Models\Domain;
use App\Models\Registration;
use App\Models\SchedulePrefs;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Http\Requests\UpdateStudentPrefsRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use League\CommonMark\Extension\TableOfContents\TableOfContentsGenerator;

class SchedulepreferenceController extends Controller
{

    /**
     * Edit schedule preferences by student
     * This function is public, gain access by means of an access token
     * Admin has the same form, but needs to be logged in. Admin uses route: StudentsController -> preferencesPage
     *
     * @param $accesstoken
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \Exception
     */
    public function edit($accesstoken)
    {
        Log::info("Student Access for preferences using accesstoken: " . $accesstoken);
        $planningDetails = [];
        $student = Student::getByAccessToken($accesstoken);
        if (!isset($student)) {
            return view('errors.403', [
                "message" => __('generic.notvalid')
            ], [
                'id' => 'no_access',
                'status' => '403'
            ]);
        }
        if (!empty($student)) {
            if ($student->has_access) {
                // OK, open the view
                return view('students.schedulepreferrences');
            } else {
                Log::warning("Student: $student->name ($student->id) has no access to the public schedulepreferences page.");
                return view('errors.403', [
                    "message" => __('generic.noaccess')
                ], [
                    'id' => 'no_access',
                    'status' => '403'
                ]);
            }
        }

        // fall-through: error in access token
        Log::warning("Access token not found: " . $accesstoken);
        return view('errors.404', [
            "message" => __('generic.accesstokeninvalid')
        ], [
            'id' => 'not_found',
            'status' => '404'
        ]);
    }

    /**
     * Get current user prefs from DB, either by accesstoken or by userid (admin)
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserPrefs(Request $request)
    {
        if (isset($request->token) && strlen($request->token) > 10) {
            Log::debug("get student by ACCESSTOKEN");
            // lookup student if we receive an accesstoken
            $student = Student::getByAccessToken($request->token);
            if (!isset($student)) {
                return response()->json(["error" => "invalid call"], 403);
            } else {
                $studentId = $student->id;
            }
        } else {
            if (Auth::user()) {
                Log::info("check domain for admin request / student");
                $studentId = $request->student;
                // get student
                $student = Student::findOrFail($request->student);
                // check domain
                if (Auth::user()->domain_id !== $student->domain_id) {
                    return response()->json(["error" => "invalid call"], 403);
                }
            } else {
                Log::debug("Invalid request, not AUTHENTICATED and no Accesstoken");
                return response()->json(["error" => "invalid call"], 403);
            }
        }

        // add contact data to the student object (student->contacts)
        $contacts = $student->contacts;
        $student->contacts = $contacts;

        Log::info("Get prefs for student $studentId");
        $activeRegistrations = $student->getActiveRegistrations();
        // for every course: check if we have an existing planning
        foreach ($activeRegistrations as $key => $registration) {
            $planning = Schoolyear::getLastSchoolyearForPlanning($registration->id, $student->domain_id);
            $activeRegistrations[$key]["course"]["planning"] = $planning;
        }

        // ask studentcontroller->get() for student
        $studentGroups = $student->studentgroups;
        // now check if any of the courses are tutored in a studentgroup
        foreach ($studentGroups as $studentGroup) {
            foreach ($studentGroup->courses as $course) {
                // check if the course is in the active registrations
                foreach ($activeRegistrations as $key => $registration) {
                    if ($registration->course->id === $course->id) {
                        $registration->course->studentgroup = ["id" => $studentGroup->id, "name" => $studentGroup->lastname];
                    }
                }
            }
        }

        $initPrefs = [
            "monday" => "",
            "tuesday" => "",
            "wednesday" => "",
            "thursday" => "",
            "friday" => "",
            "saturday" => "",
            "sunday" => ""
        ];
        $prefs = SchedulePrefs::where('student_id', '=', $studentId)->first();
        $prefs = empty($prefs) ? [] : $prefs->toArray();
        $prefs = array_merge($initPrefs, $prefs ?? []);

        // serialize values for every weekday
        $data = [
            'student_id' => $studentId,
            'monday' => explode("|", $prefs["monday"]),
            'tuesday' => explode("|", $prefs["tuesday"]),
            'wednesday' => explode("|", $prefs["wednesday"]),
            'thursday' => explode("|", $prefs["thursday"]),
            'friday' => explode("|", $prefs["friday"]),
            'saturday' => explode("|", $prefs["saturday"]),
            'sunday' => explode("|", $prefs["sunday"])
        ];
        return response()->json(["preferences" => $data, "student" => $student, "activeRegistrations" => $activeRegistrations]);
    }

    /**
     * Update user prefs
     * @param UpdateStudentPrefsRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function saveforuser(UpdateStudentPrefsRequest $request)
    {
        if (isset($request->token) && strlen($request->token) > 5) {
            Log::debug("get student by ACCESSTOKEN");
            // lookup student if we receive an accesstoken
            $studentId = Student::getByAccessToken($request->token)->id;
        } else {
            if (!Auth::guest()) {
                Log::info("check domain for admin request / student");
                $studentId = $request->student;
                // get student
                $student = Student::findOrFail($request->student);
                // check domain
                if (Auth::user()->domain_id !== $student->domain_id) {
                    return response()->json(["error" => "invalid call"], 401);
                }
            } else {
                Log::debug("Invalid request, no AUTH and no Accesstoken");
                return response()->json(["error" => "invalid call"], 401);
            }
        }

        // remove current prefs for this user, if any
        SchedulePrefs::where('student_id', '=', $studentId)->delete();
        $prefs = $request->availability;
        $data = [
            'student_id' => $studentId,
            'monday' => implode("|", $prefs["monday"]),
            'tuesday' => implode("|", $prefs["tuesday"]),
            'wednesday' => implode("|", $prefs["wednesday"]),
            'thursday' => implode("|", $prefs["thursday"]),
            'friday' => implode("|", $prefs["friday"]),
            'saturday' => implode("|", $prefs["saturday"]),
            'sunday' => implode("|", $prefs["sunday"]),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        SchedulePrefs::insert($data);
        return response()->json(['Success' => 'data saved'], 200);
    }

    /**
     * Admin version of update "keep schedule" settings for these registrations
     *
     * @param $studentId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminkeepscheduletime($studentId, Request $request)
    {
        $regs = isset($request->registrations) ? $request->registrations : [];
        foreach ($regs as $reg) {
            $courseStudent = Registration::findOrFail($reg["regid"]);
            // reg is of the same student as student id in request
            if (($courseStudent->student->domain_id === Auth::user()->domain_id) && ($courseStudent->student->id === intVal($studentId))) {
                // $reg["keepSchedule"] may be null
                $courseStudent->please_keep_scheduled_time = $reg["keepSchedule"] ? '1' : '0';
                $courseStudent->save();
                // now also update the updated_at in SchedulePrefs
                $sp = SchedulePrefs::where('student_id', $studentId)->first();
                $sp->updated_at = date('Y-m-d H:i:s');
                $sp->save();
            } else {
                return response()->json("Validation error! ", 401);
            }
        }
        return response()->json("Data updated");
    }

    /**
     * Student version of update "keep schedule" settings for these registrations
     * no admin, open interface for student. Uses access token instead of user id
     *
     * @param $accesstoken
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function keepscheduletime($accesstoken, Request $request)
    {
        $regs = isset($request->registrations) ? $request->registrations : [];
        // remove empty entries
        $regs = array_filter($regs);
        $student = Student::getByAccessToken($accesstoken);
        if (!isset($student)) {
            return response()->json("Validation error! ", 401);
        }
        foreach ($regs as $reg) {
            Log::info('Saving "keep my current schedule" for ' . $reg["regid"]);
            $courseStudent = Registration::findOrFail($reg["regid"]);
            // reg is of same student as student id of access token in the request
            if (
                ($courseStudent->student->domain_id === $student->domain_id) &&
                ($courseStudent->student->id === $student->id)
            ) {
                // $reg["keepSchedule"] may be null
                $courseStudent->please_keep_scheduled_time = $reg["keepSchedule"] ? '1' : '0';
                $courseStudent->save();
                // now also update the updated_at in schedule prefs
                $sp = SchedulePrefs::where('student_id', $student->id)->first();
                // if at this point we have no current schedule, we are not able to save last updated in the prefs object.
                if ($sp) {
                    $sp->updated_at = date('Y-m-d H:i:s');
                    $sp->save();
                } else {
                    Log::info("can't save last updated because no preferred times available");
                }
            } else {
                return response()->json("Validation error! ", 401);
            }
        }
        return response()->json("Data updated");

    }
}
