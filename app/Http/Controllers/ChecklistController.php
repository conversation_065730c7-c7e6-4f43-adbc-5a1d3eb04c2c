<?php

namespace App\Http\Controllers;

use App\Models\Checklist;


class ChecklistController extends Controller {
	/**
	 * Get checklist with all items an model them so they can be used
	 * @param $checklistId
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function get($checklistId) {

		$retArr = ["checklistItems" => [], "checklistName" => ""];

		$checklist = Checklist::findOrFail($checklistId);
		$completed = $checklist->isComplete();
		$checklistName = $checklist->name;
		for($i = 1; $i<=12; $i++) {
			if(!empty($checklist["item" . $i])) {
				$clItem = [
					'itemId' => $i,
					'itemName' => $checklist["item" . $i],
					'checked' => $checklist["item" . $i . "_checked"] == '1',
				];
				array_push($retArr["checklistItems"], $clItem);
			}
		}
		$retArr["checklistName"] = $checklistName;
		$retArr["completed"] = $completed;

		return response()->json($retArr);

	}

	/**
	 * Set or unset an item for a checklist
	 * @param $checklistId
	 * @param $itemId
	 * @param $value {0|1}
	 */
	public function setItem($checklistId, $itemId, $value) {
		$checklist = Checklist::findOrFail($checklistId);
		$fieldName = "item" . $itemId . "_checked";
		$checklist[$fieldName] = $value;
		$checklist->save();
	}
}
