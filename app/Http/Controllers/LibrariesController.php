<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Library;
use App\Http\Requests\AddLinkRequest;
use App\Http\Requests\FileUploadRenewRequest;
use App\Http\Requests\FileUploadRequest;
use App\Http\Requests\LibraryFormRequest;
use App\Http\Requests\ToggleWholeSchoolShareRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\Response;


class LibrariesController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function index() {
        return view('libraries.list');
    }


    public function getAll()
    {
        $allLibraries = Library::query()
            ->with("courses", "coursegroups", "documents", "students", "studentgroups")
            ->get();
        foreach ($allLibraries as $library) {
            foreach ($library->courses as $index => $course) {
                // trigger fullname attribute of each course
                $library->courses[$index]["fullname"] = $course->fullname;
            }
        }
        return response()->json($allLibraries);
    }

    public function apicreate(LibraryFormRequest $request) {
        Log::info("creating lib");
        $library = new Library();
        $library->domain_id = Auth::user()->domain->id;
        $library->label = $request->label;
        $library->tutor_id = Auth::user()->id;
        $library->save();
        return response()->json(["message" => "Library created"]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiupdate(LibraryFormRequest $request, $id) {
        Log::info("updating lib $id");
        $library = Library::findOrFail($id);
        $library->label = $request->label;
        $library->save();
        return response()->json(["message" => "Library updated"]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apidestroy($id) {
        Log::info("deleting lib $id");
        $studentLibrary = Library::findOrFail($id);
        
        // Detach all relations before deleting the library
        $studentLibrary->documents()->detach();
        $studentLibrary->courses()->detach();
        $studentLibrary->students()->detach();
        $studentLibrary->coursegroups()->detach();
        
        $studentLibrary->delete();
        return response()->json(["message" => "Library en alle relaties succesvol verwijderd"]);
    }

    /**
     * Upload a new file and attach it to a given library
     * @param FileUploadRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function fileupload(FileUploadRequest $request) {
        Log::info("uploading new file");
        // upload the file
        if($request->hasFile('fileitem')) {
            if($request->file('fileitem')->isValid()) {
                $extension = $request->file( 'fileitem' )->getClientOriginalExtension();
                $mime = $request->file( 'fileitem' )->getClientMimeType();
                $originalName = $request->file( 'fileitem' )->getClientOriginalName();
                $fileName =
                    Auth::user()->domain->id . '_' .
                    uniqid() . "." .
                    $extension;

                try {
                    $storedFileName = $request->file( 'fileitem' )->storeAs(
                        env('SFTP_BASE_DIRECTORY') . '/class/library',
                        $fileName,
                        'sftp'
                    );
                } catch (\Exception $e) {
                    Log::error("Error uploading file: " . $e->getMessage());
                    return response()->json(["message" => "Error uploading file"], Response::HTTP_INTERNAL_SERVER_ERROR);
                }
                if (empty($storedFileName)) {
                    Log::error("File Upload Failed: cloud storage failed");
                    return response()->json([
                        'status' => false,
                        'message' => 'File Upload to cloud storage failed!'
                    ], Response::HTTP_INTERNAL_SERVER_ERROR);
                }
                Log::info("file uploaded successfully fileName: " . $storedFileName);

                // for now we don't check if the file already exists
                // create new document record
                $document                   = new Document();
                $document->domain_id        = Auth::user()->domain->id;
                $document->file_name        = $fileName;
                $document->original_name    = $originalName;    // naam met extensie
                $document->type             = 'file';
                $document->url              = '';
                $document->label            = pathinfo($originalName, PATHINFO_FILENAME); // naam zonder extensie
                $document->content_type     = $mime;
                $document->created_at       = now();
                $document->updated_at       = now();
                $document->save();
                Log::info( "New file saved. ID = " . $document->id );

                return response()->json([
                    "message" => "File upload successful", 
                    "documentId" => $document->id
                ], Response::HTTP_OK);
            } else {
                Log::error("File upload was unsuccesfull!");
                return response()->json(["message" => "File upload failed"], Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        } else {
            Log::error("File upload was unsuccesfull!");
            return response()->json(["message" => "File upload failed"], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * add a link to a library
     */
    public function addlinktolib(AddLinkRequest $request) {
        Log::info("attach link to library");
        $url = $request->url;
        $crc = md5($url);
        // is this link already present?
        $alreadyPresentUrl = Document::where("crc", "=", $crc)->count();
        if ((!empty($alreadyPresentUrl)) && ($alreadyPresentUrl === 1)) {
            $response = [
                "error"             => "url already present",
                "errorcode"         => 11,
                "url"               => $url,
            ];
            return response()->json($response, 409);
        }

        // create new document record
        $document                   = new Document();
        $document->domain_id        = Auth::user()->domain->id;
        $document->type             = 'url';
        $document->url              = $url;
        $document->label            = $url; // fixme: visit url to retrieve title of response
        $document->crc              = $crc;
        $document->save();
        Log::debug( "Link saved. ID = " . $document->id );
        // connect to the studentlibrary
        $studentLibrary = Library::findOrFail($request->libraryid);
        $studentLibrary->documents()->attach($document->id);
        return response()->json(["registering weblink was successful"]);

    }

    /**
     * renew an already present file
     * @param FileUploadRenewRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatefile(FileUploadRenewRequest $request) {
        Log::info("uploading new file");
        // upload the file
        if ($request->hasFile('uploadedfile')) {
            if ($request->file('uploadedfile')->isValid()) {
                $ref = uniqid();
                $ext = $request->file('uploadedfile')->getClientOriginalExtension();
                // use the chosen ref for the filename
                $fileName = Auth::user()->domain->id . '_' . $ref . "." . $ext;
                Log::info("storing new file: " . storage_path() . '/app/public/files/' . $fileName);
                $request->file("uploadedfile")->move(
                    storage_path() . '/app/public/files/',
                    $fileName
                );

                // now save the specs
                $uploadedFileName = $request->file('uploadedfile')->getClientOriginalName();
                Log::info("original name: " . $uploadedFileName);

                // if the doc is already physically present, there must be a document entry that has the same crc
                $crc = $this->getCRC(storage_path() . "/app/public/files/$fileName");
                $docsWitCRC = Document::where("crc", "=", $crc)->first();

                if(sizeof($docsWitCRC) === 1) {
                    // remove uploaded file
                    unlink(storage_path() . "/app/public/files/$fileName");
                    // maybe only the name changed
                    if (($uploadedFileName === $docsWitCRC->label) && (intval($request->oldfileid) === $docsWitCRC->id)) {
                        // same file, same name no action needed
                        return response()->json(["message" => "no action needed, this is the same file"]);
                    } elseif (($uploadedFileName !== $docsWitCRC->label) && (intval($request->oldfileid) === $docsWitCRC->id)) {
                        Log::debug("sit 2");
                        // new name, no other changes: change the name and save
                        $docsWitCRC->label = $uploadedFileName;
                        $docsWitCRC->save();
                        return response()->json(["message" => "updating file successful: renamed the already existing document"]);
                    } else {
                        // different document than the one we are updating, but it's the same file.
                        // this cannot be what you want, because you clicked "update -this- file ==> no-go
                        $response = [
                            "error"             => "document already present",
                            "errorcode"         => 10,
                            "nameYouUploaded"   => $uploadedFileName,
                            "nameOnServer"      => $docsWitCRC->label,
                            "namesAreTheSame"   => ($uploadedFileName === $docsWitCRC->label),
                            "oldfileid"         => intval($request->oldfileid),
                            "presentfileid"     => $docsWitCRC->id
                        ];
                        return response()->json($response, 409);
                    }
                } else {
                    $document = Document::findOrFail($request->oldfileid);
                    // remove the old file fysically before getting rid of the old ref
                    unlink(storage_path() . '/app/public/files/' . $document->url);
                    // update to the new file
                    $document->url = $fileName;
                    $document->label = $uploadedFileName;
                    $document->crc = $crc;
                    $document->save();
                    return response()->json(["message" => "uploading file successful"]);
                }
            }
        } else {
            Log::error("File upload was unsuccesfull!");
            return response()->json(["error" => "generic error uploading file"], 500);
        }

    }

    /**
     * @param $documentid
     * @param $libid
     * @return \Illuminate\Http\JsonResponse
     */
    public function deletedocfromlib($libid, $documentid) {
        Log::info("detach doc $documentid from library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->documents()->detach($documentid);
        return response()->json(["detaching document successful"]);
    }

    /**
     * Connect a document to a lib
     * @param $documentid
     * @param $libid
     * @return \Illuminate\Http\JsonResponse
     */
    public function adddoctolib($libid, $documentid) {
        Log::info("attach doc $documentid to library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->documents()->attach($documentid);
        return response()->json(["attaching document successful"]);
    }

    /**
     * get the CRC of a file by contents
     * @param $filepath
     * @return mixed
     */
    private function getCRC($filepath) {
        $hash = hash_file('crc32b', $filepath);
        $array = unpack('N', pack('H*', $hash));
        return $array[1];
    }

    /**
     * Get all documents except the ones connected to a library
     * @param $libid
     * @return \Illuminate\Http\JsonResponse
     */
    public function documentsExcludingLib ($libid) {
        $lib = Library::findOrFail($libid);
        $documentsAlreadyCoupled = $lib->documents;
        $ids = [];
        foreach ($documentsAlreadyCoupled as $item) {
            $ids[] = $item->id;
        }
        $documents = Document::whereNotIn("id", $ids)->get();
        return response()->json($documents);
    }



    /** 
     * Update the shares of a library
     * @param $libid
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiupdateShares($libid) {
        Log::info("updating shares for library $libid");
        DB::beginTransaction();
        $library = Library::findOrFail($libid);
        // first, remove alle existing shares
        $library->courses()->detach();
        $library->coursegroups()->detach();
        $library->students()->detach();
        $library->studentgroups()->detach();
        $library->share_with_whole_school = false;

        // now add the new shares
        // request data is e.g. 
        // {"shareWholeSchool":false,"shareCourses":[1],"shareCourseGroups":[],"shareStudents":[],"shareStudentGroups":[]}
        $shares = request()->all();
        // if shareWholeSchool, set the share_with_whole_school to true
        if ($shares['shareWholeSchool']) {
            $library->share_with_whole_school = true;
            $library->save();
            DB::commit();
            return response()->json(["message" => "Shares updated"]);
        }

        // Voeg de nieuwe koppelingen toe met attach()
        if (!empty($shares['shareCourses'])) {
            $library->courses()->attach($shares['shareCourses']);
        }
        if (!empty($shares['shareCourseGroups'])) {
            $library->coursegroups()->attach($shares['shareCourseGroups']);
        }
        if (!empty($shares['shareStudents'])) {
            $library->students()->attach($shares['shareStudents']);
        }
        if (!empty($shares['shareStudentGroups'])) {
            $library->studentgroups()->attach($shares['shareStudentGroups']);
        }

        $library->save();
        DB::commit();
        return response()->json(["message" => "Shares updated"]);
    }




    /**
     * Share a library with anyone registered to a course
     * @param $libid
     * @param $courseid
     * @return \Illuminate\Http\JsonResponse
     */
    public function addcourseshare($libid, $courseid) {
        Log::info("attach course share $courseid to library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->courses()->attach($courseid);
        return response()->json(["attaching course share successful"]);
    }

    /**
     * @param $libid
     * @param $courseid
     * @return \Illuminate\Http\JsonResponse
     */
    public function removecourseshare($libid, $courseid) {
        Log::info("detach course $courseid from library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->courses()->detach($courseid);
        return response()->json(["detaching course share successful"]);
    }
    /**
     * Share a library with a coursegroup
     * @param $libid
     * @param $coursegroupid
     * @return \Illuminate\Http\JsonResponse
     */
    public function addcoursegroupshare($libid, $coursegroupid) {
        Log::info("attach coursegroup $coursegroupid to library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->coursegroups()->attach($coursegroupid);
        return response()->json(["attaching coursegroup share: successful"]);
    }
    /**
     * @param $libid
     * @param $coursegroupid
     * @return \Illuminate\Http\JsonResponse
     */
    public function removecoursegroupshare($libid, $coursegroupid) {
        Log::info("detach coursegroup $coursegroupid from library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->coursegroups()->detach($coursegroupid);
        return response()->json(["detaching coursegroup share: successful"]);
    }

    /**
     * Share a library with a coursegroup
     * @param $libid
     * @param $studentgroupid
     * @return \Illuminate\Http\JsonResponse
     */
    public function addstudentgroupshare($libid, $studentgroupid) {
        Log::info("attach coursegroup $studentgroupid to library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->studentgroups()->attach($studentgroupid);
        return response()->json(["attaching studentgroup share: successful"]);
    }
    /**
     * @param $libid
     * @param $coursegroupid
     * @return \Illuminate\Http\JsonResponse
     */
    public function removestudentgroupshare($libid, $studentgroupid) {
        Log::info("detach coursegroup $studentgroupid from library $libid");
        $studentLibrary = Library::findOrFail($libid);
        $studentLibrary->studentgroups()->detach($studentgroupid);
        return response()->json(["detaching studentgroup share: successful"]);
    }

    /**
     * Toggle the share of the lib for the whole school
     * @param ToggleWholeSchoolShareRequest $request
     */
    public function setlibforwholeschool(ToggleWholeSchoolShareRequest $request) {
        $studentLibrary = Library::findOrFail($request->editLibId);
        $studentLibrary->share_with_whole_school = $request->toggleVal;
        $studentLibrary->save();
    }


}
