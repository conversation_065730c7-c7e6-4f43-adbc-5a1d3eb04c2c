<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Http\Requests\UpdatePasswordRequest;
use App\Http\Requests\UserProfileFormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class UsersController extends Controller {
	//
	public function profile() {
        // determine pref lang: is not filled in users table, default to domain language

		return view('users.profile');
	}

    public function getProfile() {
        return response()->json(Auth::user());
    }

	/**
	 * Opslaan updates van het profile
	 * @param UserProfileFormRequest $request
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function updateprofile(UserProfileFormRequest $request) {
		$user = User::findOrFail(Auth::user()->id);
        $user->preferred_language   = $request->preferred_language ?: Auth::user()->domain->language;
		$user->name 	            = $request->name;
		$user->avatar 	            = $request->avatar;
		$user->save();
		return response()->json(['message' => ucfirst(trans("generic.datasaved"))]);
	}

    /**
     * Save a changed password for the current user
     *
     * checks for correct current pw, pw1==pw2 and strength of new password
     * are all done in the UpdatePasswordRequest.
     * all we need to do here is save the new password
     *
     * @param UpdatePasswordRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changepassword(UpdatePasswordRequest $request) {
        $user = User::findOrFail(Auth::user()->id); // copy of Auth::user()
        $user->password = Hash::make($request->pwnw1);
        $user->save();
        return response()->json(['message' => ucfirst(trans("generic.datasaved"))]);
	}

    /**
     * All user info to return in api call straight after login
     * @return \Illuminate\Http\JsonResponse
     */
    public function userApiResponse()
    {
        $u = User::query()
            ->where('domain_id', Auth::user()->domain_id)
            ->with(['domain', 'roles'])
            ->find(Auth::id());
        return response()->json($u);
    }

    public static function getActiveUsers()
    {
        Log::info('Getting active users');
        $users = User::query()
            ->with(['domain', 'roles'])
            ->where('domain_id', Auth::user()->domain_id)
            ->get();
        // check if user is active plus turn into array
        $retArr = [];
        foreach ($users as $user) {
            if ($user->active) {
                $retArr[] = $user;
            }
        }

        return response()->json($retArr);
    }

}
