<?php

namespace App\Http\Controllers;

use App\Http\Requests\LocationFormRequest;
use App\Http\Resources\LocationResource;
use App\Models\Location;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LocationsController extends Controller {
    public function getAll()
    {
        $locations = Location::orderBy('name')
            ->withCount('events')
            ->get()
            ->each(function ($location) {
                $location->in_use = $location->events_count > 0;
				$location->icon = "location_" . $location->domain_id . '_' . $location->id;
            });
        return LocationResource::collection($locations);
    }

	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
    {
		$allLocations = Location::get();
		return view('locations.list', compact('allLocations'));
	}

	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create()
    {
		$allLocations = Location::get();
		return view('locations.create', compact('allLocations'));
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param LocationFormRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
	public function store_old(LocationFormRequest $request)
    {
		$courseLocation = new Location();
        $courseLocation->domain_id  = Auth::user()->domain->id;
		$courseLocation->name       = $request->name;
		$courseLocation->save();
		// redirect to edit, catch form refresh causing accidental save
		return redirect()->route('locations.edit', $courseLocation->id)->with('message', ucfirst(trans("generic.datasaved")));
	}

	public function store(LocationFormRequest $request)
    {
		$courseLocation = new Location();
		$courseLocation->domain_id  = Auth::user()->domain->id;
		$courseLocation->name       = $request->name;
		$courseLocation->save();
		return response()->json($courseLocation);
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
    {
		//
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  int $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
	public function edit($id)
    {
		$allLocations = Location::get();
		$courseLocation = Location::findOrFail($id);
		// check if we have an icon, if not, get one
        $domain_id  = Auth::user()->domain->id;
        if (!file_exists(base_path() . "/storage/app/public/location_icon_$domain_id" . "_" . "$id.svg")) {
            // Remote image URL
            $url = 'https://avatars.dicebear.com/v2/jdenticon/' .
                str_replace(" ", "", $courseLocation->name). '.svg';
            // Image path
            $img = base_path() . "/storage/app/public/location_icon_$domain_id" . "_" . "$id.svg";
            // Save image
            file_put_contents($img, file_get_contents($url));
        }

		return view('locations.edit', compact('courseLocation', 'allLocations'));
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param LocationFormRequest $request
	 * @param  int $id
     * @return \Illuminate\Http\RedirectResponse
     */
	public function update_old(LocationFormRequest $request, $id)
    {
		$courseLocation = Location::findOrFail($id);
		if ($courseLocation->name !== $request->name) {
		    Log::info("retrieving new icon for this location");
		    Log::info("url: " . 'https://avatars.dicebear.com/v2/jdenticon/' .
                str_replace(" ", "", $request->name) . '.svg');
            // need to retrieve new icon for this location.
            $domain_id  = Auth::user()->domain->id;
            if (!file_exists(base_path() . "/storage/app/public/location_icon_$domain_id" . "_" . "$id.svg")) {
                unlink(base_path() . "/storage/app/public/location_icon_$domain_id" . "_" . "$id.svg");
            }
            // Remote image URL
            $url = 'https://avatars.dicebear.com/v2/jdenticon/' .
                str_replace(" ", "", $request->name) . '.svg';
            // Image path
            $img = base_path() . "/storage/app/public/location_icon_$domain_id" . "_" . "$id.svg";
            // Save image
            file_put_contents($img, file_get_contents($url));
        } else {
            Log::info("No need to retrieve new icon for this location");
        }
		$courseLocation->name = $request->name;
		$courseLocation->save();
		// redirect to edit, catch form refresh causing accidental save
		return redirect()->route('locations.edit', $courseLocation->id)->with('message', ucfirst(trans("generic.datasaved")));
	}


	public function update(LocationFormRequest $request, $id)
    {
		$courseLocation = Location::findOrFail($id);
		$courseLocation->name = $request->name;
		$courseLocation->save();
		return response()->json($courseLocation);
	}

    /**
     * returns the location occupation for future events
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFutureOccupied()
    {
        $occ = Location::getFutureOccupied();
        return response()->json($occ);
	}

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws \Exception
     */
	public function destroy($id)
    {
		Location::where("id", $id)->delete();
	}

	public function get()
    {
		$allLocations = Location::get();
		return response()->json($allLocations);
	}
}
