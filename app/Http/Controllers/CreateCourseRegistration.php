<?php

namespace App\Http\Controllers;

use App\Models\Checklist;
use App\Models\Course;
use App\Models\DefaultChecklist;
use App\Models\Registration;
use App\Models\Student;
use App\Models\Trialstudent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Traits\RegistrationsTrait;

class CreateCourseRegistration extends Controller
{
    Use RegistrationsTrait;

    /**
     * Handle the incoming request.
     *
     * @param Request $request
     * @return \App\Http\Resources\StudentCourseDataResource|\Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        Log::info("add course $request->courseId registration for student $request->studentId");
        // user must be admin
        if (Auth::user()->userIsA('admin')) {
            $s = Student::findOrFail($request->studentId);
            $c = Course::findOrFail($request->courseId);
            // both must be of the correct domain, same as logged in user
            if (Auth::user()->domain_id === $s->domain_id && Auth::user()->domain_id === $c->domain_id) {
                $registration = new Registration();
                $registration->student_id = $request->studentId;
                $registration->course_id = $request->courseId;
                $registration->start_date = date('Y-m-d');
                $registration->sign_code = uniqid("", true);
                $registration->save();
                // maybe add checklist if type is 'auto-add'
                $this->initiateChecklists($registration->id);
                // this maybe a registration for a triallesson request:
                // --> is student id present in trialstudents?
                // --> has this trialstudent no registration yet?
                // --> is the course_id empty? ('other course' choosen)
                // then: register this registration with this trialstudent,
                // to make sure we know we can now schedule this registration
                $ts = Trialstudent::where([
                    "generated_student_id" => $s->id,
                ])
                    ->whereNull("generated_registration_id")
                    ->whereNull("course_id")
                    ->first();
                if (!empty($ts)) {
                    Log::info("add registration for trial lesson student $registration->id");
                    $ts->generated_registration_id = $registration->id;
                    $ts->save();
                } else {
                    // else: dont touch it
                    Log::info("no trial lesson student, skipping this step");
                }
                // return all registrations for this user to be able to re-render.
                return $this->getCourseData($s->id);
            } else {
                Log::error("logged in user has different domein");
                return response(['error' => 'incorrect domain'], 403);
            }
        }
        Log::error("logged in user is not an admin");
        return response(['error' => 'incorrect usertype'], 403);
    }

    /**
     * if no checklist id was added through the request, we try if a default checklist should be added
     * i.e. one or more that are marked as 'auto-add'
     * if not we return null.
     *
     * @param int $registrationId course_student coupling table
     * @param int $checklistFromRequest
     *
     * @return array
     */
    private function initiateChecklists($registrationId, $checklistFromRequest = 0)
    {
        $retChecklistIds = [];
        if (empty($checklistFromRequest)) {
            // this may be 0, 1 or more than 1 checklist
            $defaultChecklists = $this->getAutoAddChecklists();
            // see if any checklists should be added
            if (count($defaultChecklists) > 0) {
                // this will be multiple checklists on a registration in a future version
                // for now: only one checklist, because the course_student table can currently only hold one checklist
                foreach ($defaultChecklists as $autoAddChecklistId) {
                    // create a copy of the template checklist
                    //$retChecklistId = Checklist::createNewChecklist( $autoAddChecklistId->id );
                    $retChecklistIds[] = Checklist::createNewChecklist($autoAddChecklistId->id, $registrationId);
                }
            }
        }

        return (count($retChecklistIds) == 0 ? null : $retChecklistIds);
    }

    /**
     * Return the checklist(s) that should automatically be added to a registration, if any
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    private function getAutoAddChecklists()
    {
        $autoAddChecklistIds = DefaultChecklist::select('id')->where('auto_add', '1')->get();
        return $autoAddChecklistIds;
    }

}
