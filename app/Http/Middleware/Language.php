<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class Language
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (isset(Auth::user()->id)) {
            $prefLang = Auth::user()->preferred_language ?? Auth::user()->domain->language;
            App::setLocale(($prefLang ?? 'en'));
        }
        return $next($request);
    }
}
