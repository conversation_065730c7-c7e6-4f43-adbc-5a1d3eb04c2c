<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class CheckIfDefaultPassword {
	/**
	 * Handle an incoming request.
	 * This function checks to see if a user has the default password.
	 * If so, they must change their password
	 *
	 * @param  \Illuminate\Http\Request $request
	 * @param  \Closure $next
	 * @return mixed
	 */
	public function handle($request, Closure $next) {

	    // if we are not going to the logout page
		if(strpos($request->getPathInfo(), 'logout') !== false) {
			return $next($request);
		}

		// make sure we don't get an endless loop
        // this would be the case if we're redirecting because the user currently uses the default password
		if(strpos($request->getPathInfo(), 'users') !== false) {
		    return $next($request);
        }

		// We must also be able to change our password, this is after the redirect
        // to make this somewhat tighter: the new password must not be the default password
        // if we make the defaultpassword not a 'strong' password however, the interface
        // will not except it anyway
		if(strpos($request->getPathInfo(), 'passwchange') !== false) {
		    return $next($request);
        }

		// if we are logged in and not going to the "change your password" page
		if(!empty(Auth::user())) {
			$userhash = Auth::user()->getAuthPassword();
			// if the user uses the default password
			if (Hash::check(Auth::user()->domain->default_password, $userhash)) {
				// redirect to the "change passsword" page
                // pass back errorcode when redirecting
                // this will make the modal open up and the correct message appear
                return redirect('users/profile')->with('error_code', 5);
			}
		}

		return $next($request);
	}
}
