<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;


class MustBeTutor
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        Log::info("check if user is a tutor");
        if (!Auth::user()->userIsA('tutor')) {
            Log::info("User account is not a tutor. Logging out");
            Auth::user()->tokens()->delete();
            exit();
        }
        Log::info("User account is a tutor");
        return $next($request);
    }
}
