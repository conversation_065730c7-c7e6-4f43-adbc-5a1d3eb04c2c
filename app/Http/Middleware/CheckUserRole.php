<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckUserRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!empty(Auth::user())) {
            Log::info("check account's user role");
            if (!Auth::user()->userIsA('admin')) {
                Log::info("User account is not an admin. Logging out");
                auth()->logout();
                return redirect()->route('login')->withMessage("error: " . trans('auth.onlyaccessforadmin'));
            }
        }
        return $next($request);
    }
}
