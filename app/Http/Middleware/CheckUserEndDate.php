<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckUserEndDate {
    /**
     * If the user's account is not active logout and show suspended message
     * An account is considered inactive if at least one role is inactive
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     * @throws \Exception
     */
    public function handle($request, Closure $next) {
        if (!empty(Auth::user())) {
            Log::info("check account active (user: " . Auth::user()->name . ", IP: " . $_SERVER['REMOTE_ADDR'] . ")");
            if (!Auth::user()->isActive) {
                Log::info("User account is not active. Logging out");
                $message = trans('auth.inactive');
                auth()->logout();
                return redirect()->route('login')->withMessage("error: $message");
            }
        }
        return $next($request);
    }
}
