<?php

namespace App\Http\Middleware;

use Illuminate\Support\Facades\Auth;
use Closure;
use Illuminate\Support\Facades\Log;

class CheckSetupComplete {
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next) {
        //Log::info("checking setup status");
        $pathinfo = $request->getPathInfo();
        $querystring = $request->getQueryString();

        //Log::info("pathinfo: $pathinfo");
        //Log::info("querystring: $querystring");

        // if we are going to the setupwizard page anyway, we don't need to check anything
        if (strpos($pathinfo, 'setup') !== false) {
            Log::info("skipping setup status check (1: opening setup wizard)");
            return $next($request);
        }

        // if we are trying to fix a setup problem, add setupfix=true to the querystring
        // otherwise this middleware will send you back to the settup missing page
        if (strpos($querystring, 'setup') !== false) {
            Log::info("skipping setup status check (2: setup action)");
            return $next($request);
        }
        // if we are trying to save something, it maybe an action to fix setup incompleteness
        if ($request->isMethod("post") || $request->isMethod("put") || $request->isMethod("delete")) {
            Log::info("skipping setup status check (3: POST/PUT/DELETE request)");
            return $next($request);
        }

        // it maybe an oauth passport request, that doesn't use the API for some reason
        if (strpos($pathinfo, 'scopes') !== false) {
            Log::info("skipping setup status check (4: passport request, scopes)");
            return $next($request);
        }
        if (strpos($pathinfo, 'personal-access-tokens') !== false) {
            Log::info("skipping setup status check (5: personal access token)");
            return $next($request);
        }
        // setting up 2FA
        if (strpos($pathinfo, '2fa') !== false) {
            Log::info("skipping setup status check (6: setting up 2FA)");
            return $next($request);
        }

        // lastly if logged in, check if setup is complete (we need the user's domain)
        if(!empty(Auth::user())) {
            $setupComplete = Auth::user()->domain->setupcomplete();
            if(count($setupComplete) !== 0) {
                Log::info("setup is inclomplete. redirecting");
                return redirect()->action('SetupController@overview');
            } else {
                Log::info("Setup complete");
            }
        }
        return $next($request);
    }
}
