<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckIpAddress
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // check cookie for domain token
        $domainToken = $request->cookie('domaintoken');
        // if the domain token is present, it needs to be a request from an authorized IP address
        if ($domainToken) {
            Log::info("request to broadcast list with token $domainToken");
            // validate the token
            if (strlen($domainToken) !== 32) {
                Log::warning("request to broadcast list but token is not 32 chars");
                return response()->json(
                    ["message" => "Invalid token"],
                    Response::HTTP_UNAUTHORIZED
                );
            }
            // cleanup the token in case there's illegal characters in there
            $re = '/[ ;:!*$<>\\\\\/?{}()@#]/m';
            $domainToken = preg_replace($re, "", $domainToken);
            // now we know that the token is OK, get the domain
            $domain = DB::table('domains')->where('domaintoken', $domainToken)->first();
            if (!$domain) {
                Log::warning("request to broadcast list but domain was not found by token");
                return response()->json(
                    ["message" => "Invalid token"],
                    Response::HTTP_UNAUTHORIZED
                );
            }
            $allowedIpAddresses = $domain->allowed_ip_addresses;
            if (!$allowedIpAddresses) {
                Log::warning("request to broadcast list but domain has no allowed IP addresses");
                return response()->json(
                    ["message" => "Not allowed"],
                    Response::HTTP_UNAUTHORIZED
                );
            }
            // turn into an array to make check easier
            $allowedIpAddresses = explode(",", $allowedIpAddresses);
            // check if the request comes from the correct IP address
            $ipAddress = $request->ip();
            if (!in_array($ipAddress, $allowedIpAddresses)) {
                Log::warning("request to broadcast list but IP address $ipAddress is not allowed");
                return response()->json(
                    ["message" => "Not allowed"],
                    Response::HTTP_UNAUTHORIZED
                );
            }
            // add domain to the container, so we can use it in the controller
            $request->attributes->add(['domain' => $domain]);
            return $next($request);
        } else {
            // if the request dit not contain a domaintoken, it needs to be originated from an authorized IP address
            $ipAddress = $request->ip();
            Log::info("request to broadcast list without a token from IP address $ipAddress");
            $domain = DB::table('domains')
                ->select('id', 'allowed_ip_addresses')
                ->where('allowed_ip_addresses', 'like', '%' . $ipAddress . '%')
                ->first();
            if (!$domain) {
                Log::warning("request to broadcast list from this IP address $ipAddress is not allowed");
                return response()->json(
                    ["message" => "Not allowed"],
                    Response::HTTP_UNAUTHORIZED
                );
            }
            // this request can only be for /getdomaintoken otherwise it's not allowed
            if ($request->path() !== 'getdomaintoken') {
                Log::warning("request from this IP address is only allowed for /getdomaintoken if the request does not have a domaintoken");
                return response()->json(
                    ["message" => "Not allowed"],
                    Response::HTTP_UNAUTHORIZED
                );
            }
            $request->attributes->add(['domain' => $domain]);
            return $next($request);
        }
    }
}
