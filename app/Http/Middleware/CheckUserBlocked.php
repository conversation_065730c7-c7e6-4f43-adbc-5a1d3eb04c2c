<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckUserBlocked
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!empty(Auth::user())) {
            Log::info("check account is blocked");
            if (Auth::user()->is_blocked) {
                Log::info("User account is blocked for login. Logging out");
                auth()->logout();
                return redirect()->route('login')->withMessage("error: " . trans('auth.suspended'));
            }
        }
        return $next($request);
    }
}
