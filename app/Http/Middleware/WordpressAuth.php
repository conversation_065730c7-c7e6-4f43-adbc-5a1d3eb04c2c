<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WordpressAuth
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function handle(Request $request, Closure $next)
    {
        Log::info("WordpressAuth middleware");
        $expires = new \DateTime(); // now
        // something like Bearer 123456
        $token = $request->header('Authorization');
        $token = trim(substr($token, 6));
        $scope = 'wp_website';
        Log::info("Token expired before? " . $expires->format('Y-m-d H:i:s'));
        // token valid?
        $result = DB::table('oauth_auth_codes')
            ->where("id", "=", $token)
            ->where("scopes", "=", $scope)
            ->where("revoked", "=", "0")
            ->where("expires_at", ">", $expires->format('Y-m-d H:i:s'))
            ->select("client_id") // <<< = domain id
            ->first();
        if (!$result || !$result->client_id) {
            return response()->json(["message" => "invalid token"], 401);
        }
        $request->attributes->add(["requesting_domain" => $result->client_id]);
        return $next($request);
    }
}
