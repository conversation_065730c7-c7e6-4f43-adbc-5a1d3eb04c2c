<?php

namespace App\Http;

use App\Http\Middleware\CheckUserRole;
use App\Http\Middleware\RedirectIfAuthenticated;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel {
    /**
     * The application's global HTTP middleware stack.
     *
     * These middlewares are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \Illuminate\Http\Middleware\HandleCors::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        // note: throttling is handled in RouteServiceProvider
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\CheckUserRole::class,
            \App\Http\Middleware\CheckUserBlocked::class,
            \App\Http\Middleware\CheckIfDefaultPassword::class,
            \App\Http\Middleware\CheckSetupComplete::class,
            \App\Http\Middleware\CheckUserEndDate::class,
            \App\Http\Middleware\SetLastActivityForUser::class,
            \Laravel\Passport\Http\Middleware\CreateFreshApiToken::class,
            \App\Http\Middleware\Language::class
        ],

        'api' => [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            \App\Http\Middleware\CheckUserBlocked::class,
            \App\Http\Middleware\CheckUserEndDate::class,
            'bindings',
            \App\Http\Middleware\Language::class
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middlewares may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $middlewareAliases = [
        'auth' => \Illuminate\Auth\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        '2fa' => \App\Http\Middleware\LoginsecurityMiddleware::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'redirectifauthenticated' => RedirectIfAuthenticated::class,
        'wordpressauth' => \App\Http\Middleware\WordpressAuth::class,
        'checkipaddress' => \App\Http\Middleware\CheckIpAddress::class,
        'mustbetutor' => \App\Http\Middleware\MustBeTutor::class
    ];
}
