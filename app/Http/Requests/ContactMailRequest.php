<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ContactMailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cbStudents' => 'required|boolean',
            'cbStaff' => 'required|boolean',
            'emailType' => 'required_if:cbStudents,true|string',
            'extraEmailAddresses' => 'nullable|array',
            'mailBody' => 'required|string',
            'mailSubject' => 'required|string',
        ];
    }
}
