<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class StudentlistRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing StudentlistRequest');
        Log::info('Request content: ' . json_encode($this->all()));
        Log::info('Raw content: ' . $this->getContent());
        Log::info('Request method: ' . $this->method());
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
            'name' => 'required',
            'hexcolor' => 'required|min:6|max:7'
        ];
    }
    
    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation($validator)
    {
        Log::error('Validation failed: ' . json_encode($validator->errors()->toArray()));
        parent::failedValidation($validator);
    }
}
