<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class seriesEventDateTimeRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
            // English, no caps
            'newDay'        => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            // valid time
            'newTime'       => 'required|date_format:"H:i"',
            // valid UID of an event e.g. 5c379185957351.03160155
            'seriesUID'     => 'required|regex:/.{14}\.\d{8}/'
        ];
    }
}
