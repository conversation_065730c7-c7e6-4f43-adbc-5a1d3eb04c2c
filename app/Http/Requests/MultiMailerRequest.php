<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class MultiMailerRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * to field is an array of objects, each object has an email field
     * the email field may contain multiple emailaddresses, separated by ","
     *
     * @return array
     */
    public function rules() {
        $rules = [
            'to'        => 'required|array|min:1',
            'to.*.email'=> ['required', 'regex:/^([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,},?)+$/'],
            'from'      => 'required|email',
            'subject'   => 'required|min:5',
            'body'      => 'required|min:20',
        ];

        return $rules;
    }
}
