<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AnalysePlanDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            "date" => "required|date",
            "time" => "required|date_format:H:i",
            "locationId" => "required|exists:locations,id",
            "tutorId" => "required|exists:users,id",
            "repeats" => "required|integer|min:0|max:52",
            "courseRegistrationId" => "required|exists:course_student,id",
            "isStudentGroup" => "required|boolean",
            // nor required, but if it is not null it must be a valid id in the database
            "locationIdAlt" => "nullable|exists:locations,id",
            // if isStudentGroup is true, the studentGroupId must be a valid id in the database
            "studentGroupId" => "nullable|required_if:isStudentGroup,true|exists:students,id",
            // if isStudentGroup is false, the studentId must be a valid id in the database
            "studentId" => "nullable|required_if:isStudentGroup,false|exists:students,id",
        ];
    }
}
