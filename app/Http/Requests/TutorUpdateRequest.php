<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class TutorUpdateRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
            'name'              => 'required',
            'email'             => 'required|email',
            'telephone'         => 'nullable|max:15',
            'telephone_extra'   => 'nullable|max:15',
            'hexcolor'          => 'required',
            'start_date'        => 'required'           // convert to mysqldate will be done in the function
        ];
    }
}
