<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use App\Rules\NDatGreaterEqual;
use App\Rules\NDatOneYearApart;

class SchoolyearFormRequest extends FormRequest {
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules(Request $request) {
		return [
			'label'			=> 'required|max:25',
			'start_date'	=> 'required',
			'end_date'		=> [
			    'required',
                new NDatGreaterEqual($request->start_date),
                new NDatOneYearApart($request->start_date)
            ],
		];
	}
}
