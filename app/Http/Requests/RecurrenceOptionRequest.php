<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class RecurrenceOptionRequest extends FormRequest {
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules() {
		return [
			"description"	=> "required|min:3",
			"per_interval"	=> "required|in:day,week,two weeks,month,notapply",
			"timeunit" 		=> "required|in:hour,minutes",
			"nr_of_times"	=> "required|numeric",
			"ends_after_nr_of_occurrences" => "nullable|numeric"
		];
	}
}
