<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class CourseFormRequest extends FormRequest {
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules() {
		return [
			'name'					    => 'required',
			'price_ex_tax_sub_adult'    => 'required|regex:/[0-9]+[.,]?[0-9]*/',
			'price_invoice'		        => 'required|regex:/[0-9]+[.,]?[0-9]*/',
			'price_is_per'			    => 'required',
            'group_size_min'            => 'required|numeric|min:1',
            'group_size_max'            => 'required|numeric|min:1|gte:group_size_min',
			'recurrenceoption_id'	    => 'required|numeric',
			'coursegroup'			    => 'required|numeric'
		];
	}
}
