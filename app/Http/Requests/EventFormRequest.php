<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class EventFormRequest extends FormRequest {
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules() {
		$rules = [
			'timetable_id' 	=> 'required|numeric',
			'location_id'	=> 'required|numeric',
			'tutor_id'	    => 'required|numeric',
			'timespan'	    => 'required',
            'dates'         => 'required',
            'times'         => 'required',
		];

		return $rules;
	}
}
