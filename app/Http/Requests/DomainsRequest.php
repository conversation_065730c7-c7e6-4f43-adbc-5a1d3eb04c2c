<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\ValidLogoUrl;
use App\Rules\CommaSeparatedIps;

class DomainsRequest extends FormRequest {


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {

        return [
            'name'                  => 'required',                              //
            'domainName'            => 'required',                              //
            'logoUrl'               => ['nullable', new ValidLogoUrl()],        //
            'ratesConditionsUrl'    => 'nullable|url',                          //
            'privacyUrl'            => 'nullable|url',                          //
            'address1'              => 'required',                              //
            'address2'              => 'nullable',                              //
            'zip'                   => 'required',                              //
            'city'                  => 'required',                              //
            'email'                 => 'required|email',                        //
            'scheduleThreshold'     => 'nullable|int|min:1|max:365',            // debatable...
            'adultThreshold'        => 'nullable|int|min:18|max:25',            // debatable...
            'courseTaxRate'         => 'required|numeric|min:0',                // required, but '0' is ok
            'warnBeforeBirthday'    => 'int|min:1|max:25',                      //
            'warnBeforeAdult'       => 'int|min:1|max:75',                      //
            'telephone'             => 'nullable',                              //
            'websiteUrl'            => 'nullable|url',                          //
            'contactPersonName'     => 'nullable',                              //
            'allowedIpAddresses'    => ['nullable', new CommaSeparatedIps()]    //
        ];
    }
}
