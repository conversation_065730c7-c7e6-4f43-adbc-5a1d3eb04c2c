<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCalEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "applyTo"               => ["required", Rule::in(['thisEvent', 'allEvents', 'thisAndFutureEvents'])],
            "startdate"             => "required|date",
            "starttime"             => "required|date_format:H:i",
            "enddate"               => "date",
            "endtime"               => "date_format:H:i",
            "tutors"                => "required|array",
            "locationId"            => "required|integer",
            "attendanceoptions"     => "nullable|array",
        ];
    }
}
