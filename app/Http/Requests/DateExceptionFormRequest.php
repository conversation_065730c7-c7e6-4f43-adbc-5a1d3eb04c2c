<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class DateExceptionFormRequest extends FormRequest {
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
    public function authorize()
    {
        return true;
    }

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules() {
		return [
			'datetime_start' 	=> 'required|',
			'datetime_end' 		=> 'required|after_or_equal:datetime_start|after_or_equal:today',
			'reason' 			=> 'required|string|min:4',
            'schoolyear_id'     => 'required|integer'
		];
	}
}
