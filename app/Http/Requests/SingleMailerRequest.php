<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class SingleMailerRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * the email field may contain multiple emailaddresses, separated by ","
     * @return array
     */
    public function rules() {
        return [
            'to'        => 'required|int', // the student id
            'from'      => 'required|email',
            'subject'   => 'required',
            'body'      => 'required',
        ];
    }
}
