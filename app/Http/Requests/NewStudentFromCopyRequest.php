<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class NewStudentFromCopyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "studentId"                 => "required",
            "newLastName"               => "required",
            "newFirstName"              => "nullable|min:2",
            "newPreposition"            => "nullable|min:2",
            "newDateOfBirth"            => "required",
            "copyToggleAddress"         => "required|boolean",
            "copyToggleContact"         => "required|boolean",
            "copyToggleBank"            => "required|boolean",
            "copyToggleStudentlists"    => "required|boolean"
        ];
    }
}
