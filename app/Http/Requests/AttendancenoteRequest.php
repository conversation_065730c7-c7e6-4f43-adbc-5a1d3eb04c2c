<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AttendancenoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // array: [[studentId => 1, attendanceOptionId => 2],[studentId => 1, attendanceOptionId => 2]]
        return [
            "studentAttendance" => "required|array",
            "eventId" => "required|int"
        ];
    }
}
