<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;

class StudentFormRequest extends FormRequest {
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
    public function authorize()
    {
        Log::info('Authorizing form request');
        return true;
    }

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules() {
		$rules = [
			'firstname'          => 'nullable',
			'preposition'        => 'nullable',
			'lastname'           => 'required',
			'date_of_birth'      => 'required|date',
			'bankaccount_name'   => 'required_if:permission_auto_banktransfer,Ja',
			'bankaccount_number' => 'required_if:permission_auto_banktransfer,Ja'
		];
		// rules for the array fields, we don't know beforehand how many there will be (if any)
		$labels = $this->request->get( 'label' );
		if( !empty($labels) ) {
			// rules for the array fields, we don't know beforehand how many there will be
			foreach ( $labels as $key => $val ) {
				$rules[ 'label.' . $key ] = 'nullable|max:25';
			}
		}
		return $rules;
	}

	/**
	 * Validation messages for label
	 * (this is an array of fields all called label)
	 * @return array
	 */
	public function messages() {
		$messages = [];
		$labels = $this->request->get( 'label' );
		if ( !empty($labels) ) {
			foreach ( $labels as $key => $val ) {
				$messages[ 'label.' . $key . '.max' ] =
					ucfirst( __( 'generic.fieldmaynotexceedlength',
						[ 'fieldname' => __( 'generic.label' ), 'max' => ':max' ] ) );
			}
		}
		return $messages;
	}
}
