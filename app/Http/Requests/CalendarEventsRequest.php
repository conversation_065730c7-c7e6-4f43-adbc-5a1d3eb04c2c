<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CalendarEventsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * Is logged in and is a tutor. No point otherwise
     * @return bool
     */
    public function authorize()
    {
        return Auth::user()->userIsA('tutor');
    }

    /**
     * to date must be at least 1 day bigger than start date (meaning range = 1 day)
     *
     * @return array
     */
    public function rules()
    {
        return [
            'from'  => 'required|date_format:Y-m-d',
            'to'    => 'date_format:Y-m-d|after:from'
        ];
    }
}
