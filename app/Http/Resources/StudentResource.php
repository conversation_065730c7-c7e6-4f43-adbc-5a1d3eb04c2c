<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $coursesSerialized = empty($this->currentCourses) ? "" : implode(", ", $this->currentCourses);

        return [
            "id"                    => $this->id,
            "learnertype"           => $this->type,
            "participants"          => $this->participants,
            "name"                  => ($this->type === 'studentgroup' ? $this->lastname : $this->name),
            "firstname"             => $this->firstname,
            "preposition"           => $this->preposition,
            "lastname"              => $this->lastname,
            "date_of_birth"         => $this->date_of_birth,
            "age"                   => $this->age,
            "courses"               => $this->courses,
            "contacts"              => $this->contacts,
            "currentcoursesstring"  => $coursesSerialized,
            "currentcoursessarray"  => $this->currentCourses,
            "currentcoursesdetails" => $this->currentCoursesDetails,
            "lastprefdate"          => $this->lastDatePrefsFilledIn,
            "apipin"                => $this->apipin,
            "type"                  => $this->type,
            // deprecated
            "mycourses"             => $coursesSerialized,
            "mycoursesarray"        => $this->currentCourses
        ];
    }
}
