<?php

namespace App\Http\Resources;

use App\Models\Registration;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;
use App\Models\Schoolyear;

class StudentgroupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            "id"                    => $this->id,
            "name"                  => $this->lastname,
            "students"              => $this->students,
            "course"                => $this->course,
            "future_appointments"   => $this->futureAppointments,
            "appointments"          => $this->appointments
        ];
    }
}
