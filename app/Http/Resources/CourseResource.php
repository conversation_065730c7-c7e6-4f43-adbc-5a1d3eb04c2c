<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CourseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $studentsSerialized = empty($this->currentStudents) ? "" : implode(", ", $this->currentStudents);
        return [
            "id"                    => $this->id,
            "name"                  => $this->name,
            "students"              => $this->students,
            "coursegroup_id"        => $this->coursegroup_id,
            "currentstudentsstring" => $studentsSerialized,
            "currentstudentsarray"  => $this->currentStudents,
            "recurrenceoption_id"   => $this->recurrenceoption_id,
            "recurrenceoption"      => $this->recurrenceoption->description,
            "price_ex_tax"          => $this->price_ex_tax,
            "price_invoice"         => $this->price_invoice,
            "price_ex_tax_sub_adult"=> $this->price_ex_tax_sub_adult,
            "price_is_per"          => $this->price_is_per,
            "is_trial_course"       => $this->is_trial_course,
            "variant_code"          => $this->variant_code,
            "archive"               => $this->archive,
            "groupSize"             => $this->groupSize,        // to be used as a label
            "group_size_max"        => $this->group_size_max,   // to be used for logic
            "group_size_min"        => $this->group_size_min,   // to be used for logic
            "lesson_table"          => $this->lessonTable,
        ];
    }
}
