<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Event;
class BroadcastDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $locations = [];
        foreach ($this["locations"] as $location) {
            $id = $location->id;
            $events = [];
            $name = $location->name;
            foreach ($location->events as $event) {
                // this is going to slow things down...
                $ev = Event::find($event->id);
                $course = $ev->course;
                $events[] = [
                    "id"            => $event->id,
                    "datetime"      => $event->datetime,
                    "timespan"      => $event->timespan,
                    "studentname"   => $event->studentname,
                    "groupname"     => $event->groupname,
                    "tutorname"     => $event->tutorname,
                    "course"        => $course->name,
                    "isTrialLesson" => $course->is_trial_course,
                ];
            }
            $locations[] = [
                "id" => $id,
                "name" => $name,
                "events" => $events,
            ];
        }
        // filter out data we don't need for the broadcast
        return [
            "locations" => $locations,
            "domain" => [
                "id"                => $this["domain"]->id,
                "domain_name"       => $this["domain"]->domain_name,
                "language"          => $this["domain"]->language,
                "logo_url"          => $this["domain"]->logo_url,
                "name"              => $this["domain"]->name,
                "email"             => $this["domain"]->email,
                "broadcast_colors"  => $this["domain"]->broadcast_colors,
            ]
        ];
    }
}
