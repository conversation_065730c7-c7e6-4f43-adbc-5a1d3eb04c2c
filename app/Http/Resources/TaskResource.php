<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // if course is empty add a mock course to prevent errors in the frontend id=0, name="none"
        if (empty($this->course)) {
            $this->course = (object) [
                "id" => 0,
                "name" => trans('generic.none')
            ];
        }
        // if student is empty add a mock student to prevent errors in the frontend id=0, name="none"
        if (empty($this->student)) {
            $this->student = (object) [
                "id" => 0,
                "name" => trans('generic.none')
            ];
        }
        // if tutor is empty add a mock tutor to prevent errors in the frontend id=0, name="none"
        if (empty($this->tutor)) {
            $this->tutor = (object) [
                "id" => 0,
                "name" => trans('generic.none')
            ];
        }
        // if registration is empty add a mock registration to prevent errors in the frontend id=0, name="none"
        if (empty($this->registration)) {
            $this->registration = (object) [
                "id" => 0,
                "name" => trans('generic.none')
            ];
        }
        // if assignedTo is empty add a mock assignedTo to prevent errors in the frontend id=0, name="none"
        if (empty($this->assignedTo)) {
            $this->assignedTo = (object) [
                "id" => 0,
                "name" => trans('generic.none')
            ];
        }
        return [
            'id' => $this->id,
            'domain_id' => $this->domain_id,
            'tasktype_id' => $this->tasktype_id,
            'student_id' => $this->student_id,
            'course_id' => $this->course_id,
            'tutor_id' => $this->tutor_id,
            'registration_id' => $this->registration_id,
            'event_id' => $this->event_id,
            'remarks' => $this->remarks,
            'date_opened' => $this->date_opened,
            'date_closed' => $this->date_closed,
            'date_due' => $this->date_due,
            'assigned_user_id' => $this->assigned_user_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'tasktype' => $this->tasktype,
            'course' => $this->course,
            'student' => $this->student,
            'tutor' => $this->tutor,
            'registration' => $this->registration,
            'assignedTo' => $this->assignedTo
        ];
    }
}
