<?php

namespace App\Http\Resources;

use App\Models\DateException;
use Illuminate\Http\Resources\Json\JsonResource;

class DateExceptionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $result = parent::toArray($request);
        $de = DateException::findOrFail($result["id"]);
        $result["isWholeDay"] = $de->isWholeDay;

        return $result;
    }
}
