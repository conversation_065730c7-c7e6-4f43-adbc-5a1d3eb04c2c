<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentDomainInfoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'schoolName' => $this->name,
            'websiteUrl' => $this->website_url,
            'logoUrl' => $this->logo_url,
            'ratesConditionsUrl' => $this->rates_conditions_url,
            'privacyUrl' => $this->privacy_url,
            'strAddress' => [
                "name"  => $this->name,
                "address1" => $this->address1,
                "address2" => $this->address2,
                "zip" => $this->zip,
                "city" => $this->city,
                "telephone" => $this->telephone,
                "email" => $this->email,
                "contactPersonName" => $this->contact_person_name
            ]
        ];
    }
}
