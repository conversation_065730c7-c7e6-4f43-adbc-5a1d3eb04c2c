<?php

namespace App\Http\Resources;

use App\Models\Course;
use App\Models\Coursegroup;
use App\Models\RecurrenceOption;
use App\Models\Registration;
use App\Models\Student;
use App\Models\Studentgroup;
use App\Models\Timetable;
use App\Models\Tutor;
use App\Models\Location;
use Illuminate\Http\Resources\Json\JsonResource;

class PlanningTableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $tutor              = Tutor::findOrFail($this->tutor_id);
        $location           = Location::findOrFail($this->location_id);
        $registration       = Registration::findOrFail($this->registration_id);
        $course             = Course::findOrFail($registration->course_id);
        $recurrence         = RecurrenceOption::findOrFail($course->recurrenceoption_id);
        // individual or group?
        $isStudentgroup     = false;
        $student            = Student::find($registration->student_id);
        $sog                = [];
        if ($student === null) {
            $isStudentgroup = true;
            $student        = Studentgroup::find($registration->student_id);
            $studentsOfGroup= $student->students;
            foreach ($studentsOfGroup as $s) {
                array_push($sog, [
                    "id"            => $s->id,
                    "name"          => $s->name
                ]);
            }
        }
        $realEndTime = $this->getRealEndTime($this->starttime, $this->duration);
        return [
            "id"                    => $this->id,
            "registration_id"       => $this->registration_id,
            "starttime"             => $this->starttime,
            "endtime"               => $this->getEndTime($this->starttime, $this->duration),
            "realEndTime"           => $realEndTime,
            "duration"              => $this->duration,
            "timeSegments"          => $this->getTimeSegments($this->starttime, $realEndTime),
            "daynumber"             => $this->daynumber,
            "oddeven"               => $this->oddeven,
            "isStudentGroup"        => $isStudentgroup,
            "tutor"                 => [
                "id"                => $tutor->id,
                "name"              => $tutor->name,
                "hexcolor"          => $tutor->hexcolor
            ],
            "location"              => [
                "id"                => $location->id,
                "name"              => $location->name
            ],
            "student"               => [
                "id"                => $student->id,
                "name"              => $student->name,
                "lastName"          => $student->lastname
            ],
            "studentsOfGroup"       => $sog,
            "course"                => [
                "id"                => $course->id,
                "name"              => $course->name,
                "is_trial_course"   => $course->is_trial_course
            ],
            "recurrence"            => [
                "id"                => $recurrence->id,
                "description"       => $recurrence->description,
                "nr_of_times"       => $recurrence->nr_of_times,
                "timeunit"          => $recurrence->timeunit,
                "per_interval"      => $recurrence->per_interval,
                "ends_after_nr_of_occurrences" => $recurrence->ends_after_nr_of_occurrences
            ]
        ];
    }

    /**
     * Endtime based on start time and duration
     * @param $startTime
     * @param $duration
     * @return string
     * @throws \Exception
     */
    function getEndTime($startTime, $duration) {
        $start = new \DateTime($startTime);
        $start->add(new \DateInterval('PT' . $duration . 'M'));
        return $start->format('H:i');
    }

    /**
     * Endtime based on start time and duration, minus 1 minute
     * because in reality, that's when the next time segment starts
     * @param $startTime
     * @param $duration
     * @return string
     * @throws \Exception
     */
    function getRealEndTime($startTime, $duration) {
        $start = new \DateTime($startTime);
        $start->add(new \DateInterval('PT' . ($duration - 1) . 'M'));
        return $start->format('H:i');
    }

    /**
     * Determine in which timesegments this appointment is present.
     * e.g. 10:45 - 12:10 -> 10, 11 and 12
     *      10:45 - 12:00 -> 10 and 11
     * @param $startTime
     * @param $endTime
     * @return array
     */
    function getTimeSegments($startTime, $endTime) {
        $start = intval(substr($startTime, 0, 2));
        $end = intval(substr($endTime, 0, 2));
        return  range($start, $end);
    }
}
