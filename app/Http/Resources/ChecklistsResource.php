<?php

namespace App\Http\Resources;

use App\Models\Checklist;
use Illuminate\Http\Resources\Json\JsonResource;

class ChecklistsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $checklist = parent::toArray($request);
        $c = Checklist::findOrFail($checklist['id']);
        $isComplete = $c->isComplete();
        return array_merge($checklist, ["isComplete" => $isComplete]);
    }
}
