<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentCourseRegistrationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $all = parent::toArray($request);
        return array_merge($all, [
            'future_appointments' => $this->getNrOfAppointments(true),
            'appointments' => $this->getNrOfAppointments()
        ]);
    }
}
