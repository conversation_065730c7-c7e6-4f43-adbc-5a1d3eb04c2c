<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DomainInfoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'schoolName'            => $this->name,
            'domainName'            => $this->domain_name,
            'websiteUrl'            => $this->website_url,
            'language'              => $this->language,
            'defaultPassword'       => $this->default_password,
            'logoUrl'               => $this->logo_url,
            'scheduleThreshold'     => $this->schedule_threshold,
            'courseTaxRate'         => $this->course_tax_rate,
            'adultThreshold'        => $this->adult_threshold,
            'warnBeforeBirthday'    => $this->warn_before_birthday,
            'warnBeforeAdult'       => $this->warn_before_adult,
            'ratesConditionsUrl'    => $this->rates_conditions_url,
            'privacyUrl'            => $this->privacy_url,
            'allowedIpAddresses'    => $this->allowed_ip_addresses,
            'strAddress' => [
                "name"              => $this->name,
                "address1"          => $this->address1,
                "address2"          => $this->address2,
                "zip"               => $this->zip,
                "city"              => $this->city,
                "telephone"         => $this->telephone,
                "email"             => $this->email,
                "contactPersonName" => $this->contact_person_name
            ]
        ];
    }
}
