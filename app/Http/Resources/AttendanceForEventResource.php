<?php

namespace App\Http\Resources;

use App\Models\Studentgroup;
use App\Models\Event;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceForEventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $event = Event::findOrFail($this->id);
        $isGroup = false;
        // if the student in the event is actually a group,
        // we need to return the group name and the participanting students
        if (substr($this->student_name, 0, 1) === '-' && $this->student_date_of_birth === '1800-01-01') {
            $isGroup = true;
            $sg = Studentgroup::findOrFail($this->student_id);
            $participants = $sg->students->map(function ($student) use ($event) {
                return [
                    "id" => $student->id,
                    "name" => $student->name,
                    "email" => $student->email,
                    "attendance" => $event->attendancenotes->where('student_id', $student->id)->first() ?: [
                        "student_id" => $student->id,
                        "event_id" => $event->id,
                        "attendanceoption_id" => 0,
                        "notes" => ""
                    ]
                ];
            });
        } else {
            $participants = [[
                "id" => $this->student_id,
                "name" => $this->student_name,
                "email" => $this->student_email,
                "attendance" => $event->attendancenotes->where('student_id', $this->student_id)->first() ?: [
                    "student_id" => $this->student_id,
                    "event_id" => $event->id,
                    "attendanceoption_id" => 0,
                    "notes" => ""
                ]
            ]];
        }

        return [
            "id" => $this->id,  // = event_id
            "datetime" => $this->datetime,
            "course_id" => $this->course_id,
            "course_name" => $this->course_name,
            "student_id" => $this->student_id,
            "student_name" => $isGroup ? trim(substr($this->student_name, 1)) : $this->student_name,
            "is_group" => $isGroup,
            "attendingStudents" => $participants ?? []
        ];
    }
}
