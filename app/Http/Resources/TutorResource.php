<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Event;
use App\Models\DateException;

class TutorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        // check if exists in appointments (events or dateexceptions)
        $countEvents = Event::countNrOfEventsForTutor($this->id);
        $countDateExceptions = DateException::countNrOfDateExeptionsForTutor($this->id);

        return [
            "id"                    => $this->id,
            "inUse"                 => ($countEvents > 0 || $countDateExceptions > 0),  // anywhere, past present, futur
            "is_blocked"            => $this->is_blocked,
            "has_future_events"     => $this->hasFutureTutorEvents(),                   // only future
            "has_password"          => (strlen($this->password) > 10),
            "name"                  => $this->name,
            "hexcolor"              => $this->hexcolor,
            "email"                 => $this->email,
            "telephone"             => $this->telephone,
            "telephone_extra"       => $this->telephone_extra,
            "preferred_language"    => $this->preferred_language,
            "last_active_at"        => $this->last_active_at,
            "start_date"            => $this->start_date,
            "end_date"              => $this->end_date,
            "address_street_1"      => $this->address_street_1,
            "address_street_2"      => $this->address_street_2,
            "address_zipcode"       => $this->address_zipcode,
            "address_city"          => $this->address_city,
            "address_country"       => $this->address_country
        ];

        // return parent::toArray($request);
    }
}
