<?php

namespace App\Http\Resources;

use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentCourseResponse extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $s = Student::findOrFail($this->id);
        return [
            "id" => $this->id,
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "preposition" => $this->preposition,
            "name" => $this->name,
            "city" => $this->city,
            "email" => $s->getContactWithSpecOrFirstOfType('email'),
        ];
    }
}
