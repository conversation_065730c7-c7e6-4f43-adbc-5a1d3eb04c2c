<?php

namespace App\Http\Resources;

use App\Models\Registration;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class RegistrationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $registrationArr = parent::toArray($request);
        $r = Registration::findOrFail($registrationArr['id']);
        $student = $r->student;
        $finEmail = $student->financialEmail;
        $finSalut = $student->financialSalutation;
        $isAdult = $student->isAdult;
        $age = $student->age;
        $domainData = Auth::user()->domain;
        $registrationArr["student"]["age"] = $age;
        $registrationArr["student"]["isAdult"] = $isAdult;
        return array_merge($registrationArr, [
            "finEmail"      => $finEmail,
            "finSalutation" => $finSalut,
            "tax_rate"      => $domainData->course_tax_rate,
            "domain"        => [
                "adult_threshold"   => $domainData->adult_threshold,
                "course_tax_rate"   => $domainData->course_tax_rate,
                "name"              => $domainData->name
            ]
        ]);
    }
}
