<?php

namespace App\Http\Resources;

use App\Models\DateException;
use App\Models\Document;
use App\Models\Event;
use App\Models\Student;
use App\Models\Tutor;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CalendarEventsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $tutors = [];
        $studentsInfo = [];
        $docs = null;
        // ****************************************************
        // for every date exceptions that or not whole school
        if ($this->wholeschool !== '1' && $this->event_type === 'dateexception') {
            $de = DateException::find($this->id);
            // get pivot for this DE for this Tutor
            $tInfo = $de->tutors()->where("user_id", Auth::user()->id)->first();
            $tutors = $de->tutors;
        }
        // ****************************************************
        // for every date exception
        if ($this->event_type === 'dateexception') {
            // fix 1 second problem in timespan of whole-day date exceptions
            $tsParts = explode(":", $this->timespan);
            $hours = ($tsParts[1] === '59' && $tsParts[2] === '59')
                ? (intval($tsParts[0]) + 1)
                : intval($tsParts[0]);
        } else {
            // ****************************************************
            // for every tutoring event
            $tutors = [Auth::user()];
            $hours = $this->timespan;
            // get participating students - group or individual
            $event = Event::find($this->id);
            $s = $event->student; // this might be a student group
            $attendanceNotes = $event->attendanceNotes;
            if ($s->firstname === '-' && $s->date_of_birth === "1800-01-01") {
                $s = $s->students;
                foreach ($s as $student) {
                    // first check if this student (who is part of the groep) is actually attending this event,
                    // they might not have a valid registration for this particular date
                    $isParticipating = $event->getStudentParticipation($student);
                    if (!$isParticipating) {
                        Log::info("Student " . $student->id . " is not participating in event " . $event->id . " although they are part of the group");
                        continue;
                    }
                    // if we have an attendance note for this student, then add it to the student's info
                    $noteForStudent = $attendanceNotes->where('student_id', $student->id)->first();
                    $studentsInfo[] = [
                        "id"                => $student->id,
                        "name"              => $student->name,
                        "email"             => explode(",", $student->planning_email),
                        "telephone"         => explode(",", $student->planning_telephone),
                        "attendance_note"   => $noteForStudent
                    ];
                }
            } else {
                $noteForStudent = $attendanceNotes->where('student_id', $s->id)->first();
                $studentsInfo[] = [
                    "id"                => $s->id,
                    "name"              => $s->name,
                    "email"             => explode(",", $s->planning_email),
                    "telephone"         => explode(",", $s->planning_telephone),
                    "attendance_note"   => $noteForStudent
                ];
            }
            // add docs
            $docs = Document::where('event_id', $event->id)->get();
        }

        return [
            "id"                => $this->id,
            "fromDT"            => $this->datetime_start,
            "toDT"              => $this->datetime_end,
            "from"              => substr($this->datetime_start, 11, 5),
            "to"                => substr($this->datetime_end, 11, 5),
            "timespan"          => $hours,
            "tsReadable"        => $this->event_type === 'dateexception'
                                    ? floor($hours / 24) .
                                        (
                                            $hours % 24 === 0
                                                ? " days"
                                                : " days " . $hours % 24 . " hours"
                                        )
                                    : $hours,
            "studentName"       => substr($this->student_name, 0,1) === '-' // fix student group name
                                        ? substr($this->student_name, 2)
                                        : $this->student_name,
            "attendingStudents" => $studentsInfo, // this will contain all attending students names and contact info
            "courseName"        => $this->course_name,
            "is_trial_course"   => $this->is_trial_course,
            "locationName"      => $this->location_name,
            "reason"            => $this->reason,
            "remarks"           => $this->remarks,
            "eventType"         => $this->event_type,
            "isWholeDay"        => (substr($this->datetime_start, 11, 5) === "00:00" && substr($this->datetime_end, 11, 5) === "23:59"),
            "isWholeSchool"     => $this->wholeschool === '1',
            "tutors"            => $tutors,
            "pivot"             => (isset($tInfo) ? $tInfo->pivot : []), // pivot info for THIS tutor
            "docs"              => $docs
        ];
    }
}
