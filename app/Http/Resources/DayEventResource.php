<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DayEventResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $start = new \DateTime($this->datetime);
        $timespanNr = intval($this->timespan);
        if (preg_match('/[a-z]/i', $this->timespan, $match)) {
            $timespanLetter = ucfirst($match[0]);
        }
        if (empty($timespanNr) || empty($timespanLetter)) {
            $timespanNr = 0;
            $timespanLetter = "M";
        }
        $endDateTime = $start->add(new \DateInterval("PT" . $timespanNr . $timespanLetter))->format('Y-m-d H:i:s');
        $acronym = "";
        foreach(explode(' ', $this->name) as $namePart) $acronym .= mb_substr($namePart, 0, 1, 'utf-8');

        return [
            "eventId"       => $this->eventId,
            "datetime"      => $this->datetime,
            "enddatetime"   => $endDateTime,
            "timespan"      => $this->timespan,
            "tutor_id"      => $this->tutor_id,
            "name"          => $acronym,
            "courseName"    => $this->courseName,
            "studentName"   => $this->studentName
        ];
    }
}
