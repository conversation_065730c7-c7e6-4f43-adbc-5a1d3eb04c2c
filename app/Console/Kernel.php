<?php

namespace App\Console;

use App\Console\Invocable\CheckNoAccess4InactiveStudents;
use App\Console\Invocable\CheckTrialStudentInStudentGroup;
use App\Console\Invocable\ClassTrialActivate;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel {
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule) {
        // ////////////////////////////////////////////////////////////////////////////////
        // check if we received a trial request for CLASS from the website
        // if so: create a trial domain and admin account
        // ////////////////////////////////////////////////////////////////////////////////
        $schedule->call(new ClassTrialActivate)
            ->name('ClassTrialActivate')
            ->everyTenMinutes();

        // ////////////////////////////////////////////////////////////////////////////////
        // check if a student with trial reg in a student group has had a course event.
        // if no action-after-trial task => create it
        // ////////////////////////////////////////////////////////////////////////////////
        $schedule->call(new CheckTrialStudentInStudentGroup)
            ->name('CheckTrialStudentInStudentGroup')
            ->everySixHours();

        // ////////////////////////////////////////////////////////////////////////////////
        // check if every student that has access to the preferred time interface
        // is actually an active student. If not: remove access
        // ////////////////////////////////////////////////////////////////////////////////
        $schedule->call(new CheckNoAccess4InactiveStudents)
            ->name('CheckNoAccess4InactiveStudents')
            ->everyFifteenMinutes();
    }

    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands() {
        require base_path('routes/console.php');
        $this->load(__DIR__.'/Commands');
    }
}
