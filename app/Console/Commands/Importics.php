<?php

namespace App\Console\Commands;

use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Studentgroup;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class Importics extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'class:importics {icsfile} {--D|domain=} {--L|locationID=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import appointments from an external ics file like google calendar into CLASS';

    /**
     * @var array
     */
    protected $tutors = [
        "m" => 3,
        "a" => 7,
        "j" => 16
    ];

    /**
     * @var int
     */
    protected $domain = 0;

    /**
     * @var object
     */
    protected $schoolyear = null;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(): void
    {
        $this->domain = $this->option("domain");
        try {
            $activeSchoolyear = Schoolyear::getCurrentOrFuture($this->domain);
        } catch (\Exception $e) {
            $this->error("NO CURRENT or FUTURE SCHOOLYEAR!");
            exit();
        }
        if (empty($activeSchoolyear)) {
            $this->error("NO CURRENT or FUTURE SCHOOLYEAR!");
            exit();
        }
        $this->schoolyear = $activeSchoolyear->id;
        $location = $this->option("locationID");

        if (empty($this->domain)) {
            $this->error("NO DOMAIN SPECIFIED!");
            exit();
        }

        $icsfile = $this->argument('icsfile');
        // see if we can find the ics file
        if (!file_exists($icsfile)) {
            $this->error("I couldn't find your ICS file. Please pass the full path to your ICS file");
        }
        $h = fopen($icsfile, 'r');

        $events = [];
        $count = -1;
        while (!feof($h)) {
            // remove \n with rtrim
            $result = rtrim(fgets($h));

            switch ($result) {
                case "BEGIN:VEVENT":
                    // split on first :
                    $delimiter = $this->getDelimiter($result);
                    if ($delimiter !== "*") {
                        list($key, $data) = explode($delimiter, $result, 2);
                        // begin event: increment counter
                        $events[++$count][$key] = $data;
                    } else {
                        $events[++$count][uniqid()] = $result;
                    }

                    break;
                default:
                    if (($count > -1) && ($result !== "END:VCALENDAR")) {
                        // split on first :
                        $delimiter = $this->getDelimiter($result);
                        if ($delimiter !== "*") {
                            list($key, $data) = explode($delimiter, $result, 2);
                            $events[$count][$key] = $data;
                        } else {
                            $events[$count][uniqid()] = $result;
                        }
                    }
                    break;
            }
        }

        fclose($h);

        foreach ($events as $evnr => $event) {
            $fromdt = $this->extractDateTime($event["DTSTART"]);
            $todt = $this->extractDateTime($event["DTEND"]);
            $this->line(
                $fromdt . " - " .
                $todt . ": " .
                $event["SUMMARY"]
            );
            $analysis = $this->analyseEntry($event["SUMMARY"], $event["DESCRIPTION"]);
            $this->info(
                "registration: " . $analysis["registrationId"] .
                " - action: " . $analysis["action"] .
                " - location: " . $location .
                " - tutor: " . $analysis["tutor"] .
                " - schoolYear: " . $this->schoolyear .
                " - timeTable: " . $analysis["timetable"] .
                " - from: " . $fromdt .
                " - to: " . $todt
            );
        }
    }

    /**
     * Converts an entry to a set of values to be used in creating the correct events in the database
     * @param string $summary
     * @param string $descr
     * @return array
     */
    private function analyseEntry($summary, $descr): array
    {
        $action = "undecided";
        $regID = 0;
        $tutor = 0;

        if (substr($summary, 1, 1) === ":") {
            $tutorFlag = strtolower(substr($summary, 0, 1));
            $tutor = $this->tutors[$tutorFlag] ?: 0;

            $rest = trim(substr($summary, 2));
            // look for a student or group at this point we do not know which
            $students = Student::where("lastname", "like", "%" . $rest . "%")->get();
            if (count($students) === 0) {
                // couldn't find student / group
            } elseif (count($students) === 1) {
                $theStudent = $students->first();
                if ($theStudent->isAStudentgroup) {
                    $stgId = $theStudent->id;
                    $stg = Studentgroup::findOrFail($stgId);
                    $theStudent = null;
                    $action = "found studentgroup " . $stg->lastname;
                    $regs = $stg->getActiveRegistrations();
                    if (count($regs) === 1) {
                        $regID = $regs[0]->id;
                    } else {
                        $action .= " - found " . count($regs) . " active registrations";
                    }
                } else {
                    $action = "found student " . $theStudent->lastname;
                    $regs = $theStudent->getActiveRegistrations();
                    if (count($regs) === 1) {
                        $regID = $regs[0]->id;
                    } else {
                        $action .= " - found " . count($regs) . " active registrations";
                    }
                }
            } else {
                //
                $action = "found " . count($students) . " students";
            }
        }

        if (!empty($regID)) {
            $timetable = $this->getTimetable($regID, $this->schoolyear);
        } else {
            $timetable = 0;
        }

        return [
            "registrationId" => $regID,
            "action" => $action,
            "tutor" => $tutor,
            "timetable" => $timetable
        ];
    }


    /**
     * finds first candidate for a delimiter
     * @param $input
     * @return string
     */
    private function getDelimiter($input)
    {
        $colon = strpos($input, ":");
        $semicolon = strpos($input, ";");

        if (($colon === false) && ($semicolon === false)) {
            // meaning: don't try to split
            return "*";
        } elseif (($colon === false) && ($semicolon !== false)) {
            return ";";
        } elseif (($colon !== false) && ($semicolon === false)) {
            return ":";
        }
        return ($colon < $semicolon) ? ":" : ";";
    }

    /**
     * return a datetime string from a generically formatted date/time or date string
     * like:
     *  TZID=Europe/Amsterdam:20190904T134000
     *  20191016T143000Z
     *
     * @param string $input
     * @return string
     */
    private function extractDateTime($input)
    {
        // FORMAT: TZID=Europe/Amsterdam:20190904T134000
        if (substr($input, 0, 4) === "TZID") {
            $test2 = substr($input, 5);
            $parts = explode(":", $test2);
            try {
                $dt = new \DateTime($parts[1], new \DateTimeZone($parts[0]));
                return $dt->format("Y-m-d H:i:sP");
            } catch (\Exception $e) {
                return "(1) unable to convert $input: " . $e->getMessage();
            }
        } elseif ((strlen($input) === 16) && (strpos($input, "T") !== false) && (strpos($input, "Z") !== false)) {
            // FORMAT: 20191016T143000Z
            try {
                $dt = new \DateTime($input);
                return $dt->format("Y-m-d H:i:sP");
            } catch (\Exception $e) {
                return "(3) unable to convert $input: " . $e->getMessage();
            }
        } elseif ((strlen($input) === 19) && (substr($input, 0, 11) === "VALUE=DATE:")) {
            // FORMAT: VALUE=DATE:20191117
            try {
                $date = new \DateTime(substr($input, 11));
                return $date->format("Y-m-d H:i:sP");
            } catch (\Exception $e) {
                return "(4) unable to convert $input: " . $e->getMessage();
            }
        } else {
            return "(2) unable to convert $input - format not recognized";
        }
    }

    /**
     * if the combination of schoolyear and registration already has a timetable rturn it's ID
     * otherwise: return 0
     * @param int $registration
     * @param int $schoolyear
     * @return int
     */
    private function getTimetable($registration = 0, $schoolyear = 0): int
    {
        $resp = 0;
        $tt = DB::table("timetables")
            ->where([
                ["course_student_id", "=", $registration],
                ["schoolyear_id", "=", $schoolyear]
            ])->get();
        if (count($tt) === 1) {
            return $tt->id;
        }
        return $resp;
    }

    /**
     * get recurrence info from RRULE section
     * @param $input
     * @return array
     */
    private function extractRecurrenceOption($input): array
    {
        // monday = day-number 0, so param 'WKST' should be 'MO'
        $daynumbers = ["MO", "TU", "WE", "TH", "FR", "SA", "SU"];
    }

/*
* https://dateutil.readthedocs.io/en/stable/rrule.html
[RRULE] => FREQ=WEEKLY;WKST=MO;UNTIL=20190910T215959Z;BYDAY=WE
wkst = week start (on monday)
until
BYDAY = When given, these variables will define the weekdays where the recurrence will be applied.
 *
 */
}
