<?php

namespace App\Console\Invocable;

use App\Models\Student;
use Illuminate\Support\Facades\Log;

/**
 * Business rule:
 *   Students with access to the preferred schedule time interface
 *   should have at least one active registration
 */
class CheckNoAccess4InactiveStudents
{
    public function __invoke()
    {
        Log::info("Running task: Check no access to preferred time interface for inactive students");
        $students = Student::query()
            ->where("has_access", "=", "1")
            ->get();
        Log::info("Found " . count($students) . " with preferred schedule access");
        $countAccessRemoved = 0;
        foreach ($students as $student) {
            if (count($student->currentCourses) === 0) {
                Log::info("found inactive student with access: " . $student->name . ". Removing access");
                // not active, remove access
                $student->has_access = 0;
                $student->save();
                $countAccessRemoved ++;
            }
        }
        Log::info("Removed access for $countAccessRemoved students");
    }
}