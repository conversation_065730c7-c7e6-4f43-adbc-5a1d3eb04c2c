<?php

namespace App\Console\Invocable;

use App\Models\Event;
use App\Models\Registration;
use App\Models\Schoolyear;
use App\Models\Studentgroup;
use App\Models\Task;
use App\Models\Timetable;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckTrialStudentInStudentGroup
{
    public function __invoke()
    {
        Log::info("Running CheckTrialStudentInStudentGroup");
        // which students are currently in a group as trial student?
        $trialStudentsInGroup = DB::table('student_studentgroup as ssg')
            ->select('studentgroup_id', 'student_id', 'as_trial_student')
            ->where('as_trial_student', '>', 0)
            ->get()
            ->toArray();
        if (count($trialStudentsInGroup) === 0) {
            Log::info("No trial students in groups found");
            return;
        }
        foreach ($trialStudentsInGroup as $trialStudentInGroup) {
            // is this student's reg stil active?
            $regOfStudent = Registration::query()
                ->select('course_student.id', 'start_date', 'end_date', 's.domain_id')
                ->leftJoin('students as s', 's.id', '=', 'course_student.student_id')
                ->where([
                    ['student_id', '=', $trialStudentInGroup->student_id],
                    ['course_id', '=', $trialStudentInGroup->as_trial_student]
                ])
                ->where(function($query){
                    $query->where('end_date', '>', DB::raw('NOW()'))
                        ->orWhereNull('end_date');
                })
                ->first();
            // registration of student is (no longer) active
            // this is meant to clean up if this procedure hasn't run correctly for a while
            if (empty($regOfStudent)) {
                Log::info("Found no active registration for student $trialStudentInGroup->student_id ".
                    "on course $trialStudentInGroup->as_trial_student. Possible already closed");
                // remove this students registration from the studentgroup
                $this->deleteTrialStudentFromStudentGroup(
                    $trialStudentInGroup->studentgroup_id,
                    $trialStudentInGroup->student_id
                );
                continue;
            }
            $regOfStudentGroup= Registration::query()
                ->select('id', 'start_date', 'end_date')
                ->where('student_id', '=', $trialStudentInGroup->studentgroup_id)
                ->where(function($query){
                    $query->where('end_date', '>', DB::raw('NOW()'))
                        ->orWhereNull('end_date');
                })
                ->first();

            $currentSchoolYear = Schoolyear::getCurrentOrFuture($regOfStudent->domain_id, 'past');

            if (empty($currentSchoolYear)) {
                continue;
            }
            $timetable = Timetable::query()
                ->where('course_student_id', '=', $regOfStudentGroup->id)
                ->where('schoolyear_id', '=', $currentSchoolYear->id)
                ->first();

            if (empty($timetable)) {
                continue;
            }
            $pastEventsAfterTrialCourseReg = Event::query()
                ->where('timetable_id', '=', $timetable->id)
                ->where('datetime', '>', $regOfStudent->start_date)
                ->where('datetime', '<=', Carbon::yesterday())
                ->first();

            // if this is not empty, which means: if there has been a course event after the student
            // has registered for the trial course and has been made a member of the student group....
            // then the trial course event has been held in the past
            // and the student should now decide whether they want to join the regular course
            if (!empty($pastEventsAfterTrialCourseReg)) {
                Log::info(
                    "Trial course " . $trialStudentInGroup->as_trial_student .
                    " for student " . $trialStudentInGroup->student_id . " has been tutored on " .
                    $pastEventsAfterTrialCourseReg->datetime . "."
                );

                // set end_date in the trial course registration of the student
                $regOfStudent->end_date = $pastEventsAfterTrialCourseReg->datetime;
                $regOfStudent->save();
                Log::info("student subscription for trial lesson end date set to $pastEventsAfterTrialCourseReg->datetime");
                // unsubscribe the student from the student group
                $studentgroup = Studentgroup::findOrFail($trialStudentInGroup->studentgroup_id);
                if (empty($studentgroup)) {
                    Log::warning("no studentgroup found");
                    continue;
                }
                $studentgroup->students()->detach($trialStudentInGroup->student_id);
                Log::info("student subscription to student group based on trial course removed");
                // create task: action after trial course, if it doesn't exist yet
                $this->createActionIfNotExists(
                    $regOfStudent->domain_id,
                    $trialStudentInGroup->student_id,
                    $trialStudentInGroup->as_trial_student,
                    $timetable->tutor_id,
                    $regOfStudent->id,
                    $pastEventsAfterTrialCourseReg->id
                );
            } else {
                Log::info("Trial course " . $trialStudentInGroup->as_trial_student .
                    " for " . $trialStudentInGroup->student_id . " not tutored yet: no action.");
            }
        }
    }

    /**
     * create task: action after trial course, if it doesn't exist yet
     * can't use firstOrCreate because date_opened is unknown in case the task already exists
     * cant use eloquent because there's no logged-in user (see Task model -> newQuery)
     * @param $domainId
     * @param $trialStudentId
     * @param $asTrialStudentCourseId
     * @param $tutorId
     * @param $registrationId
     * @param $eventId
     * @return void
     */
    private function createActionIfNotExists($domainId, $trialStudentId, $asTrialStudentCourseId, $tutorId, $registrationId, $eventId)
    {
        $taskExists = DB::table('tasks')
            ->where([
                ['domain_id'         ,'=', $domainId],
                ['tasktype_id'       ,'=', 1], // action after trial course
                ['student_id'        ,'=', $trialStudentId],
                ['course_id'         ,'=', $asTrialStudentCourseId],
                ['tutor_id'          ,'=', $tutorId],
                ['registration_id'   ,'=', $registrationId],
                ['event_id'          ,'=', $eventId],
            ])
            ->count();
        if ($taskExists === 0) {
            DB::table('tasks')->insert([
                "domain_id"        => $domainId,
                "tasktype_id"      => 1, // action after trial course
                "student_id"       => $trialStudentId,
                "course_id"        => $asTrialStudentCourseId,
                "tutor_id"         => $tutorId,
                "registration_id"  => $registrationId,
                "event_id"         => $eventId,
                "date_opened"      => Carbon::now(),
                "remarks"          => Carbon::now() . ": " .
                    trans('generic.addedtaskfor') . " " .
                    trans('generic.actionnaftertriallesson')
            ]);
            Log::info("task: action after trial lesson created");
        } else {
            Log::info("task: action after trial lesson already exists, no action needed.");
        }
    }

    private function deleteTrialStudentFromStudentGroup($studentGroupId, $studentId)
    {
        DB::table('student_studentgroup')
            ->where([
                ['studentgroup_id', '=', $studentGroupId],
                ['student_id', '=', $studentId],
                ['as_trial_student', '>', 0] // safety check, making sure it's a trial participant
            ])
            ->delete();
        Log::info("Removed student $studentId from group $studentGroupId as trial student.");
    }
}
