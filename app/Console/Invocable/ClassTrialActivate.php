<?php

namespace App\Console\Invocable;

use App\Mail\ClassPortalReadyNotification;
use App\Models\Classportal;
use App\Models\Domain;
use App\Models\RecurrenceOption;
use App\Models\RoleUser;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ClassTrialActivate
{
    public function __invoke()
    {
        Log::info("Running task check trial request for CLASS");
        // get status of Classportal DB
        $accountRequests = Classportal::where("accountstatus", "=", "readyforinstall")->get();
        Log::info("found " . $accountRequests->count() . " request(s).");
        if ($accountRequests->count() > 0 ) {
            foreach ($accountRequests as $accountRequest) {
                $instance = $this;
                DB::transaction(function() use ($accountRequest, $instance) {
                    Log::info("creating trial account for $accountRequest->buss_name");
                    $d = new Domain();
                    $d->name                    = $accountRequest->buss_name;
                    $d->lookup_url              = "not set";
                    $d->domain_name             = $accountRequest->buss_domain;
                    // this will set CLASS language to the language that was used during the registration process
                    $d->language                = $accountRequest->preferred_language ?: "nl";
                    $d->telephone               = $accountRequest->buss_phone ?: "";
                    $d->email                   = $accountRequest->buss_email ?: "";
                    $d->address1                = $accountRequest->buss_address1 ?: "";
                    $d->address2                = $accountRequest->buss_address2 ?: "";
                    $d->zip                     = $accountRequest->buss_zip ?: "";
                    $d->city                    = $accountRequest->buss_city ?: "";
                    $d->logo_url                = $accountRequest->buss_logo_url ?: "";
                    $d->rates_conditions_url    = $accountRequest->buss_rates_conditions_url ?: "";
                    $d->contact_person_name     = $accountRequest->buss_name_contactperson ?: "";
                    $d->adult_threshold         = $accountRequest->adult_threshold ?: "";
                    $d->schedule_threshold      = "60";
                    $d->default_password        = $instance->generateDefAccountPW($accountRequest->buss_name);
                    $d->status                  = "trial";
                    $d->save();
                    // Create admin account for this domain
                    Log::info("Creating user account for $accountRequest->email");
                    $user = new User();
                    $user->domain_id            = $d->id;
                    $user->email                = $accountRequest->email;
                    $user->name                 = $accountRequest->name;
                    $user->password             = $accountRequest->password;
                    // this will set CLASS language to the language that was used during the registration process
                    $user->preferred_language   = $accountRequest->preferred_language ?: "nl";
                    $user->save();

                    // this user is an admin
                    Log::info("Giving user $user->id ADMIN role");
                    $role2 = new RoleUser();
                    $role2->user_id = $user->id;
                    $role2->role_id = config('app.ADMINROLEID');
                    $role2->start_date = DB::raw('now()');
                    $role2->save();

                    // we need 1 trial recurrence option
                    $trialoption = new RecurrenceOption();
                    $trialoption->domain_id = $d->id;
                    $trialoption->description = trans('generic.defaulttrialrecurrenceoption');
                    $trialoption->nr_of_times = '1';
                    $trialoption->timeunit = 'hour';
                    $trialoption->per_interval = 'day';
                    $trialoption->ends_after_nr_of_occurrences = '1';
                    $trialoption->save();
                    // add some other recurrence options to get the user started
                    $data = array (
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption1'),
                            "nr_of_times"                   => "1.5",
                            "timeunit"                      => "hour",
                            "per_interval"                  => "two weeks",
                            "ends_after_nr_of_occurrences"  => null
                        ),
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption2'),
                            "nr_of_times"                   => "1.5",
                            "timeunit"                      => "hour",
                            "per_interval"                  => "week",
                            "ends_after_nr_of_occurrences"  => null
                        ),
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption3'),
                            "nr_of_times"                   => "1",
                            "timeunit"                      => "hour",
                            "per_interval"                  => "week",
                            "ends_after_nr_of_occurrences"  => null
                        ),
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption4'),
                            "nr_of_times"                   => "1",
                            "timeunit"                      => "hour",
                            "per_interval"                  => "two weeks",
                            "ends_after_nr_of_occurrences"  => null
                        ),
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption5'),
                            "nr_of_times"                   => "45",
                            "timeunit"                      => "minutes",
                            "per_interval"                  => "week",
                            "ends_after_nr_of_occurrences"  => null
                        ),
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption6'),
                            "nr_of_times"                   => "30",
                            "timeunit"                      => "minutes",
                            "per_interval"                  => "week",
                            "ends_after_nr_of_occurrences"  => null
                        ),
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption7'),
                            "nr_of_times"                   => "1",
                            "timeunit"                      => "hour",
                            "per_interval"                  => "day",
                            "ends_after_nr_of_occurrences"  => "1"
                        ),
                        array (
                            "domain_id"                     => $d->id,
                            "description"                   => trans('generic.defaultrecoption8'),
                            "nr_of_times"                   => "1.5",
                            "timeunit"                      => "hour",
                            "per_interval"                  => "week",
                            "ends_after_nr_of_occurrences"  => "4"
                        )
                    );
                    RecurrenceOption::insert($data);

                    // update status of account to "active" in classportal
                    Log::info("Setting request to status active");
                    $accountRequest->accountstatus = "active";
                    $accountRequest->save();

                    // send email to admin including the link to the loginpage of class
                    Log::info("mail to queue: $accountRequest->email");

                    Mail::to($accountRequest->email)
                        ->queue(new ClassPortalReadyNotification());
                }); // end transaction
            } // end foreach
        } // end if
    }

    /**
     * Generate a 'default' password for creating new tutors
     * After logging in they are forced to change their password
     * @param string $name
     * @return string
     */
    private function generateDefAccountPW($name = '') {
        $symbols = ['@', '$', '!', '&', '(', '{', '+'];
        $parts = explode(" ", $name);
        // find the longest word in the name
        $max = 0;
        $longestIndex = 0;
        foreach ($parts as $key => $part) {
            if (strlen($part) > $max) {
                $max = strlen($part);
                $longestIndex = $key;
            }
        }
        $len = strlen($parts[$longestIndex]);
        $len = ($len > 5 ? 5 : $len);
        $returnString = "";
        while(strlen($returnString) < 10) {
            $returnString .= substr($parts[$longestIndex], 0, $len) . $symbols[rand(0, 6)];
        }
        return $returnString;
    }
}

