<?php

namespace App\Jobs;

use App\Models\Emaillogentry;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Mailgun\Mailgun;

class ProcessMailgunApiCallJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Data needed for Mailgun API call.
     * $data['token'] = $uniqueToken
     * $data['domain_id'] = Auth::user()->domain->id
     * $data['mail_data'] = ["to", "from", "subject", "body"]
     * $data['email_address'] = $emailAddress
     */
    protected $mgData; // additional data to be sent with the API call

    /**
     * Create a new job instance.
     */
    public function __construct(array $data = [])
    {
        $this->mgData = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!isset($this->mgData['token'])) {
            Log::error('Mailgun API call not executed because of missing token');
            return;
        }
        // check if all data is present and valid
        if (
            !isset($this->mgData['mail_data']) ||
            !isset($this->mgData['mail_data']['subject']) ||
            !isset($this->mgData['mail_data']['body']) ||
            !isset($this->mgData['mail_data']['to']) ||
            !isset($this->mgData['mail_data']['from']) ||
            !isset($this->mgData['domain_id']))
        {
            $this->setMailStatus($this->mgData['token'], 'failed', 'Missing data prevents sending email');
            Log::error(
                'Mailgun API call not executed because of missing data. Setting status of ' .
                $this->mgData["token"] .
                ' to failed.'
            );
            return;
        }
        if (!isValidEmail($this->mgData['mail_data']['to']) ) {
            $this->setMailStatus($this->mgData['token'], 'failed', 'Invalid email address prevents sending email');
            Log::error(
                'Mailgun API call not executed because of invalid email address: ' .
                $this->mgData['mail_data']['to']
            );
            return;
        }

        $apiKey = env("MAILGUN_SECRET_" . $this->mgData['domain_id']);
        $mgSendingDomain = env("MAILGUN_SENDING_DOMAIN_" . $this->mgData['domain_id']);
        $mgApiEndpoint = env("MAILGUN_ENDPOINT");
        $mgClient = Mailgun::create($apiKey, "https://$mgApiEndpoint");
        if (!$apiKey || !$mgSendingDomain || !$mgApiEndpoint) {
            $this->setMailStatus($this->mgData['token'], 'failed', 'Missing environment variables prevents sending email');
            Log::error('Mailgun API call not executed because of missing environment variables');
            return;
        }

        // Data needed for Mailgun API call. The rendered body (using the view) is passed in from the EmailController.
        $params = [
            'from'    => $this->mgData['mail_data']['from'],
            'to'      => $this->mgData['mail_data']['to'],
            'subject' => $this->mgData['mail_data']['subject'],
            'html'    => $this->mgData['mail_data']['body'],
        ];
        try {
            $mgClient->messages()->send($mgSendingDomain, $params);
        } catch (\Exception $e) {
            $this->setMailStatus($this->mgData['token'], 'failed', $e->getMessage());
            Log::error('Mailgun API call failed: ' . $e->getMessage());
            return;
        }
        Log::info('Mailgun API call executed successfully, setting status of ' . $this->mgData["token"] . ' to sent');
        $this->setMailStatus($this->mgData['token'], 'sent', 'Mail has been sent successfully');
    }

    private function setMailStatus ($token, $status='unknown', $message=null): void
    {
        $message = date('Y-m-d H:i:s') . ' - ' . $message;
        $emailLogEntry = Emaillogentry::where('unique_token', $token)->first();
        if (!$emailLogEntry) {
            Log::error("EmailLogEntry with token $token not found, can't update status to $status");
            return;
        }
        $emailLogEntry->status = $status; // depends on response
        if ($message) {
            if ($emailLogEntry->log === null || $emailLogEntry->log === '') // first log entry
                $emailLogEntry->log = $message;
            else // append to existing log (if any)
                $emailLogEntry->log = $emailLogEntry->log . '<br>' . $message;
        }
        $emailLogEntry->updated_at = now();
        $emailLogEntry->save();
    }
}
