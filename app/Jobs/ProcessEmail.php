<?php

namespace App\Jobs;

use App\Mail\PreferenceInvitation;
use Doctrine\DBAL\Query\QueryException;
use Illuminate\Bus\Queueable;
use Illuminate\Database\DetectsLostConnections;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;

class ProcessEmail implements ShouldQueue {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, DetectsLostConnections;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;
    protected $preferenceInvitation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(PreferenceInvitation $preferenceInvitation) {
        $this->preferenceInvitation = $preferenceInvitation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() {
        try {
            $this->preferenceInvitation->build();
        } catch (QueryException $e) {
            // see https://github.com/laravel/framework/issues/19072
            if ($this->causedByLostConnection($e)) {
                Log::error($e->getMessage());
                exit();
            }
        }
    }
}
