<?php

namespace App\Exports;

use App\Models\Student;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentMaillistExport implements FromCollection, ShouldAutoSize, WithHeadings, WithStyles
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return Student::getAllStudentRecordsForMaillist();
    }

    public function headings(): array
    {
        return [
            trans('generic.firstname'),
            trans('generic.lastname'),
            trans('generic.preposition'),
            trans('generic.address'),
            trans('generic.zipcode'),
            trans('generic.city'),
            trans('generic.email'),
            trans('generic.prefsurl')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return \bool[][][]
     * @see https://phpspreadsheet.readthedocs.io/en/latest/topics/recipes/#valid-array-keys-for-style-applyfromarray
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text (heading row).
            1   => ['font' => ['bold' => true]],
            // 'E' => ['alignment' => ['horizontal' => 'center']]
        ];
    }
}
