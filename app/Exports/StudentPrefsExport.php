<?php

namespace App\Exports;

use App\Models\Student;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentPrefsExport implements FromCollection, ShouldAutoSize, WithHeadings, WithStyles
{

    var $sortOrder;

    public function __construct($sortOrder = "lastName")
    {
        $this->sortOrder = $sortOrder;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $forSpreadSheet = true;
        return Student::getCurrentlyActiveStudents($forSpreadSheet, $this->sortOrder);
    }

    /**
     * @return string[]
     */
    public function headings(): array
    {
        return [
            trans('generic.firstname'),
            trans('generic.lastname'),
            trans('generic.coursename'),
            // 'Id',   // schedule prefs id
            trans('generic.lastupdate'),
            // 'S-id', // student id
            trans('generic.keepcurrentschedule'),
            trans('generic.currentlessontime'),
            trans('localisation.monday'),
            trans('localisation.tuesday'),
            trans('localisation.wednesday'),
            trans('localisation.thursday'),
            trans('localisation.friday'),
            trans('localisation.saturday'),
            trans('localisation.sunday')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return \bool[][][]
     * @see https://phpspreadsheet.readthedocs.io/en/latest/topics/recipes/#valid-array-keys-for-style-applyfromarray
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1   => ['font' => ['bold' => true]],
            'E' => ['alignment' => ['horizontal' => 'center']]
        ];
    }

}
