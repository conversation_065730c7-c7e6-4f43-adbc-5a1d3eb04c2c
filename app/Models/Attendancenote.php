<?php

/****************************************************************
 * 'attendancenotes' is a three-way relation between student, event and attendanceoption
 * this makes retrieval and updating more difficult
 */
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Attendancenote extends Model
{
    use HasFactory;

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function attendanceoption()
    {
        return $this->belongsTo(Attendanceoption::class, 'attendanceoption_id', 'id', 'attendanceoptions');
    }

    /**
     * Check if this attendance is already present,
     * if so update the existing one otherwise, create a new one
     * Looks like 'delete()' is a reset for att note
     * @param $eventId
     * @param $studentId
     * @param $attoptionId
     * @return string
     */
    public static function upsert($eventId, $studentId, $attoptionId)
    {
        $existingAttendancenote = Attendancenote::query()
            ->where('student_id', '=', $studentId)
            ->where('event_id', '=', $eventId)
            ->first();
        // insert, update or delete?
        if (empty($existingAttendancenote) && intval($attoptionId) > 0) {
            $action = "created";
            $attendancenote = new Attendancenote();
        } elseif (!empty($existingAttendancenote) && intval($attoptionId) === 0) {
            $action = "deleted";
            $existingAttendancenote->delete(); // detach?
        } elseif (!empty($existingAttendancenote) && intval($attoptionId) > 0) {
            $action = "updated";
            $attendancenote = $existingAttendancenote;
        } else {
            $action = "no-op";
        }
        Log::info("Action attendancenote: $action");
        if ($action === "created" || $action === "updated") {
            $attendancenote->student_id = $studentId;
            $attendancenote->event_id = $eventId;
            $attendancenote->attendanceoption_id = $attoptionId;
            // todo future: add remarks in the notes field
            $attendancenote->save();
        }
        return $action;
    }
}
