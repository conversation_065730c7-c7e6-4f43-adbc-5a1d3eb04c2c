<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Task extends Model {

    protected $fillable = [
        'domain_id',
        'tasktype_id',
        'student_id',
        'course_id',
        'tutor_id',
        'registration_id',
        'event_id'
    ];

    /**
     * Set standard filtering on queries:
     * makes sure only tutors belonging to the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['tasks.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    // Relations
	public function tasktype(  ) {
		return $this->belongsTo(Tasktype::class);
	}

	public function course(  ) {
		return $this->belongsTo(Course::class);
	}

	public function student(  ) {
		return $this->belongsTo(Student::class);
	}

	public function tutor(  ) {
		return $this->belongsTo(User::class, "tutor_id", "id");
	}

	public function registration(  ) {
		return $this->belongsTo(Registration::class, "registration_id", "id");
	}

    public function assignedTo(  ) {
        return $this->belongsTo(User::class, "assigned_user_id", "id");
    }

    /**
     * Get all tasks that currently require attention
     * i.e. that have opened in the past and are not (yet) closed
     */
    public static function getActiveTasks() {
        return Task::query()
            ->select("tasks.*", "students.name")
            ->leftJoin("students", "students.id", "=", "tasks.student_id")
            ->where("date_opened", "<=", Carbon::now())
            ->whereNull("date_closed")
            ->get();
    }

}
