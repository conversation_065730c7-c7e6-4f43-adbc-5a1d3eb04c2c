<?php

namespace App\Models;

use App\Traits\FullCalendarTrait;
use App\Traits\IcsTrait;
use App\Traits\LocationOccupationTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\Ndat2Mydat;

class Event extends Model {
    use FullCalendarTrait, IcsTrait, HasFactory, LocationOccupationTrait;
	// get course as an attribute for this event
	protected $appends = ['course','student'];

    protected static function booted()
    {
        static::deleting(function ($event) {
            Log::info("deleting event " . $event->id . ", deleting associated tasks, docs and attendancenotes");
            $event->attendancenotes()->delete();
            $event->tasks()->delete();
            $event->documents()->delete();
        });
    }

	public function timetable() {
		return $this->belongsTo(Timetable::class);
	}

	public function location() {
		return $this->belongsTo(Location::class);
	}

	public function tutor() {
		return $this->belongsTo(User::class);
	}

    public function tasks() {
        return $this->hasMany(Task::class);
    }

    /**
     * 'attendancenotes' is a three-way relation between student, event and attendanceoption
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function attendancenotes()
    {
        return $this->hasMany(Attendancenote::class)->with(['attendanceoption', 'student']);
    }

    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    public function getEndtimeAttribute()
    {
        $durationInMinutes = str_replace(" minutes", '', $this->timespan);
        $durationInMinutes = str_replace(" minuten", '', $durationInMinutes);
        $start = new \DateTime($this->datetime);
        $start->add(new \DateInterval("PT" . $durationInMinutes . "M"));
        return $start->format("H:i");
    }

	public function getCourseAttribute() {
		return isset($this->timetable) ? $this->timetable->registration->course : null;
	}

	public function getRegistrationAttribute() {
		return isset($this->timetable) ? $this->timetable->registration : null;
	}

	public function getStudentAttribute() {
		return isset($this->timetable) ? $this->timetable->registration->student : null;
	}

    public function getStartedAsPartOfSeriesAttribute() {
        $count = Event::where("caluniqueid", "=", $this->caluniqueid)->count();
        return ($count > 1);
	}

    /**
     * @param $tutorId
     * @return int
     */
	static function countNrOfEventsForTutor($tutorId) {
	    return Event::where("tutor_id", "=", $tutorId)->count();
    }

    /**
     * Delete all events after a certain date for a certain registration
     * @param $date
     */
    public static function deleteEventsAfter($date, $regid=0) {
        Log::info("deleting all events after $date for registration $regid");
        // get the timetable ids
        try {
            $reg = Registration::findOrFail($regid);
            $timetables = $reg->timetables;
            foreach ($timetables as $timetable) {
                $eventsAffected = Event::where([
                    ["timetable_id", "=", $timetable->id],
                    ["datetime", ">", Ndat2Mydat::getMydat($date) . " 23:59:59"]
                ])->delete();
                Log::info("found " . $eventsAffected . " (event)s > $date in timetable $timetable->id (" . $timetable->schoolyear->start_year . " - " . $timetable->schoolyear->end_year . ")");
            }
        } catch(\Exception $e) {
            Log::error("Error retrieving registration: " . $e->getMessage());
        }
    }

    /**
     * An event is considered solitary if the sequence number combined with the caluniqueid is unique.
     * @param $caluniqueid
     * @param $sequence
     * @return bool
     */
    public static function isSolitary($caluniqueid, $sequence) {
        return (Event::where([["caluniqueid", "=", $caluniqueid], ["sequence", "=", $sequence]])->count() === 1);
    }

    /**
     * check for potential conflicts with
     * - tutor working schedule
     * - location occupation
     * - tutor occupation
     * - blocking date exceptions
     * @see PlanningController -> analysePlanRequest for comparable checks
     */
    public function getFlags(): array
    {
        Log::info("checking flags for event " . $this->id);
        // setup variables for checks
        $durationInMinutes = str_replace(" minutes", '', $this->timespan);
        $durationInMinutes = str_replace(" minuten", '', $durationInMinutes);
        $tutor = Tutor::findOrFail($this->tutor_id);
        $date = explode(" ", $this->datetime)[0];
        $time = explode(" ", $this->datetime)[1];
        $flags = ["errors" => [], "warnings" => []];

        // check for potential conflicts with tutor working schedule
        $isAvailableWorkSchedule = $tutor->isAvailableAccodingToTutorSchedule($date, $time, $durationInMinutes);
        if (!$isAvailableWorkSchedule) {
            $flags["errors"][] = ucfirst(trans('generic.tutornotavailableaccordingtoschedule'));
        }

        // check location availability
        $locationOccupation = Location::isOccupiedOn($this->location_id, $date, $time, $durationInMinutes, $this->id);
        if (count($locationOccupation) > 0) {
            $flags["errors"][] = ucfirst(trans('generic.locationoccupied')) .
                ": " .
                implode(", ", $locationOccupation);
        }

        // check tutor occupation
        $tutorOccupation = Tutor::isOccupiedOn($this->tutor_id, $date, $time, $durationInMinutes, $this->id);
        if (count($tutorOccupation) > 0) {
            $flags["errors"][] = ucfirst(trans('generic.tutornotavailablehasevent')) .
                ": " .
                implode(", ", $tutorOccupation);
        }

        // check for blocking date exceptions (holidays, etc) on the date/time
        $blockingDateException = DateException::isBlocking($date, $time, $this->location_id, $this->tutor_id, $durationInMinutes);
        if (count($blockingDateException) > 0) {
            $flags["warnings"][] = ucfirst(trans_choice('generic.dateexceptions', 1)) .
                " " .
                trans('generic.planblocking') .
                ": " .
                implode(", ", $blockingDateException);
        }

        if ($flags["errors"] === [] && $flags["warnings"] === []) {
            Log::info("no flags for event " . $this->id);
        } else {
            Log::info("flags: " . json_encode($flags));
        }
        return $flags;
    }

    /**
     * Check if a student is participating in this event
     * todo: we don't check the time of the event yet,
     * todo: so in workshops currently you cant have different participation of students on the same day
     * @param Student $student
     * @return bool Returns true if the student is participating, false otherwise.
     * @throws \Exception
     */
    public function getStudentParticipation(Student $student)
    {
        Log::info("Checking if student " . $student->id . " is participating in event " . $this->id . " based on course registration");
        if (count($student->courses) === 0) {
            Log::debug("Student " . $student->id . " has no courses");
            return false;
        }
        // find the course with which the student is participating - or not
        $relatedCourses = TrialcourseRelation::trialcoursesForCourse($student->courses[0]->id);
        $theCourse = null;
        foreach ($student->courses as $course) {
            $theCourse = intval($course->id) === intval($this->course->id) ? $course : null;
            if ($theCourse) {
                break;
            }
            // try to find the student's course in the related courses
            foreach ($relatedCourses as $relatedCourse) {
                $theCourse = $relatedCourse === $this->course->id ? $course : null;
                if ($theCourse) {
                    break;
                }
            }
        }
        // still no course found?
        if (!$theCourse) {
            return false;
        }
        Log::info("Checking if student " . $student->id . " is participating in event " . $this->id . " based on date/time of registration");
        // see todo above
        $startDate = new \DateTime($theCourse->pivot->start_date);
        $endDate = $theCourse->pivot->end_date === null ? null : new \DateTime($theCourse->pivot->end_date);
        $eventDateTime = new \DateTime($this->datetime);
        return $startDate <= $eventDateTime && ($endDate >= $eventDateTime || $endDate === null);
    }

    /**
     * Update attendance for the event
     *
     * @param array $attendanceOptionsOfStudents The attendance options for each student
     * @return void
     */
    public function updateAttendance($attendanceOptionsOfStudents)
    {
        Log::info("updating attendance for event " . $this->id);
        foreach ($attendanceOptionsOfStudents as $attendanceoption) {
            $studentId = $attendanceoption["studentId"];
            $attendanceOptionId = $attendanceoption["attendanceoptionId"];
            $attendanceNote = Attendancenote::where([["event_id", "=", $this->id], ["student_id", "=", $studentId]])->first();
            if ($attendanceNote) {
                if ($attendanceOptionId === 0 || $attendanceOptionId === "0") {
                    $attendanceNote->delete();
                    continue;
                }
                $attendanceNote->attendanceoption_id = $attendanceOptionId;
                $attendanceNote->save();
            } else {
                if ($attendanceOptionId === 0 || $attendanceOptionId === "0") {
                    continue;
                }
                $attendanceNote = new Attendancenote();
                $attendanceNote->event_id = $this->id;
                $attendanceNote->student_id = $studentId;
                $attendanceNote->attendanceoption_id = $attendanceOptionId;
                $attendanceNote->save();
            }
        }
    }

    public function updateRemarks($remarks)
    {
        Log::info("updating remarks for event " . $this->id);
        $remarks = strip_tags($remarks);
        $this->remarks = $remarks;
        $this->save();
    }

    public function updatePublishFlag($publishEventFlag = false)
    {
        Log::info("updating remarks for event " . $this->id);
        // maybe 'on' 'true', '1' or true
        $publishEventFlag = filter_var($publishEventFlag, FILTER_VALIDATE_BOOLEAN);
        $this->flag_publish = $publishEventFlag;
        $this->save();
    }

    public static function getParticipatingStudents($eventId) {
        Log::info("getting participating students for event " . $eventId);
        $event = Event::findOrFail($eventId);
        $st = $event->timetable->registration->student;
        // if this is a student group, get the students from the group
        // otherwise use the student but as an array
        if ($st->isAStudentGroup) {
            $students = $st->students;
        } else {
            $students = [$st];
        }
        $participatingStudents = [];
        foreach ($students as $student) {
            if ($event->getStudentParticipation($student)) {
                $participatingStudents[] = $student;
            } else {
                $participatingStudents[] = $student; // debug
            }
        }
        return $participatingStudents;
    }

    /**
     * Calculate summary statistics based on given events
     * - if flag_sticky is set, the events will take place whatever happens -> don't count as blocked
     * - if we have warnings or errors, the event will be blocked -> count as blocked
     * - if the event is in the past, it is a past event -> count as past
     * - if the event is in the future, it is a future event -> count as future
     * Finally calculate the total number of events that will actually take place (not blocked)
     *
     * @param array $events List of events
     * @throws \Exception
     * @return array Summary statistics
     */
    public static function getSummary($events)
    {
        $return = [
            "blocked" => 0,
            "past" => 0,
            "future" => 0,
            "total-events" => count($events)
        ];
        $now = new \DateTime();
        foreach ($events as $event) {
            if ((count($event["flags"]["errors"]) > 0 || count($event["flags"]["warnings"]) > 0) && !($event["flag_sticky"])) {
                $return["blocked"]++;
            } elseif (new \DateTime($event["start"]) < $now) {
                $return["past"]++;
            } else {
                $return["future"]++;
            }
        }
        $return["total-occuring"] = $return["total-events"] - $return["blocked"];
        return $return;
    }

}
