<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Student extends Model {
    use hasFactory;

    /**
     * Set standard filtering on queries:
     * Provides a standard separation between students and studentgroups.
     * A studentgroep has a firstname '-' and date of birth 1800-01-01
     * Also makes sure only students from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if(!Auth::guest()) {
            $query->where([
                ['firstname', '<>', '-'],
                ['date_of_birth', '<>', '1800-01-01'],
                ['domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

	/**
	 * get courses associated with a student
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
	 */
	public function courses() {
		return $this
            ->belongsToMany( Course::class )
            ->withPivot( 'id', 'start_date', 'end_date', 'signed', 'sign_request_send', 'checklist_id', 'status', 'please_keep_scheduled_time')
            ->withTimestamps();
	}

    /**
     * 'attendancenotes' is a three-way relation between student, event and attendanceoption
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function attendancenotes()
    {
        return $this->hasMany(Attendancenote::class);
    }

    public function telephoneNumbers()
    {
        return $this->hasMany(Studentcontact::class)->where("contacttype", "=", "telephone");
    }

    public function domain() {
        return $this->belongsTo(Domain::class);
    }

    public function getCurrentCoursesAttribute() {
        return $this->courses->filter(function($course) {
            return $course->pivot->end_date == null ||
                strtotime($course->pivot->end_date) > time();
        })->map(function($item) {
            return $item->is_trial_course === 1
                ? trans('generic.triallesson') . ": " . $item->name
                : $item->name;
        })->all();
    }

    public function getCurrentCoursesDetailsAttribute() {
        return $this->courses->filter(function($course) {
            return $course->pivot->end_date == null ||
                strtotime($course->pivot->end_date) > time();
        })->map(function($item) {
            return [
                "name" => $item->is_trial_course === 1
                    ? trans('generic.triallesson') . ": " . $item->name
                    : $item->name,
                "please_keep_scheduled_time" => $item->pivot->please_keep_scheduled_time
            ];
        })->all();
    }

    public function studentlists() {
        return $this->belongsToMany(Studentlist::class);
    }

    public function studentgroups() {
        return $this->belongsToMany(Studentgroup::class)->withTimestamps()->with('courses');
    }

    public function libraries() {
        return $this->belongsToMany(Library::class,
            "library_student", "student_id", "library_id");
    }

    /**
     * based on the domain indicator (setting) adult_threshold
     */
    public function getIsAdultAttribute() {
        // get student age and compare with setting: domain->adult_threshold
        // if the student uses the confirmation form, they are not logged in.
        // Look for the domain of the student in that case
        if (Auth::guest()) {
            $domain = Domain::findOrFail($this->domain_id);
            $threshold = $domain->adult_threshold;
        } else {
            $threshold = Auth::user()->domain->adult_threshold;
        }
	    return ($this->age >= $threshold);
    }


    public function getAgeAttribute() {
        try {
            return calculateAge($this->date_of_birth);
        } catch (\Exception $e) {
            Log::error('agecalculation failed! ' . $e->getMessage());
        }
    }
    /**
     * quick way to find out if the student is actually a studentgroup
     * @return bool
     */
    public function getIsAStudentgroupAttribute() {
        return (($this->date_of_birth === "1800-01-01") && ($this->firstname === "-"));
    }

	/**
	 * get associated contacts
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function contacts() {
		return $this->hasMany( Studentcontact::class );
	}

    public function schedulepreferences() {
        return $this->hasMany(SchedulePrefs::class);
	}

    public function getLastDatePrefsFilledInAttribute() {
        $relevant = SchedulePrefs::where('student_id', '=', $this->id)->first();
        if(empty($relevant) || empty($relevant->updated_at)) {
            return '-';
        } else {
            $datetime = new \DateTime($relevant->updated_at);
            $datetime->setTimezone(new \DateTimeZone('Europe/Amsterdam'));
            return $datetime->format('d-m-Y');
        }
	}
	/**
	 * get associated logentries
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function logentries() {
		return $this->hasMany( Logentry::class );
	}

	/**
	 * get registrations (hasMany)
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function registrations() {
		return $this->hasMany( Registration::class );
	}

    /**
     * retrieve all contacts values that should be used for a given spec, like planning, finance, etc.
     * fallback to first contact value of the requested type if none found with the given spec
     * if spec is 'all', return all contacts of the requested type (like all email addresses of this student)
     * if spec is 'first', return first contact of the requested type
     * usage: $student->getContactWithSpecOrFirstOfType('telephone', 'planning')
     * usage: $student->getContactWithSpecOrFirstOfType('telephone', 'all')
     * @param $contactType
     * @param $spec (one of 'planning', 'finance', 'promotions', 'all', 'first')
     * @throws \InvalidArgumentException
     * @return string (comma separated)
     */
    public function getContactWithSpecOrFirstOfType($contactType, $spec = 'all')
    {
        $allowedSpecs = ['planning', 'finance', 'promotions', 'all', 'first'];
        if (!in_array($spec, $allowedSpecs)) {
            throw new \InvalidArgumentException("Spec must be one of " . implode(', ', $allowedSpecs));
        }
        $allContactInfo = $this->contacts;
        if (count($allContactInfo) === 0) {
            return '';
        }
        $allOfRequestedContactType = $allContactInfo->where('contacttype', $contactType);
        $firstOfRequestedContactType = count($allOfRequestedContactType) > 0
            ? $allOfRequestedContactType->first()->value
            : "";

        if ($spec === 'all') {
            return count($allOfRequestedContactType) > 0
                ? $allOfRequestedContactType->implode('value', ',')
                : "";
        }
        if ($spec === 'first') {
            return $firstOfRequestedContactType;
        }

        $allOfTypeAndSpec = $this->contacts
            ->where('contacttype', $contactType)
            ->where("apply_for_$spec", 1);
        if (count($allOfTypeAndSpec) === 0) {
            Log::info("No $contactType found for student " . $this->id . " with $spec - fallback to first $contactType");
            return $firstOfRequestedContactType;
        }
        return $allOfTypeAndSpec->implode('value', ',');
    }

	/**
	 * get most likely candidate for e-mail from the students contactinfo
	 * this is an accessor, so we don't have to change any code using $student->email
	 * which will respond with the output of this function
	 * @return string
	 */
	public function getEmailAttribute() {
        return $this->getContactWithSpecOrFirstOfType('email', 'first');
	}

    /**
	 * get most likely candidate for telephone from the students contactinfo
	 * this is an accessor, so we don't have to change any code using $student->email
	 * which will respond with the output of this function
	 * @return string
	 */
	public function getTelephoneAttribute() {
        return $this->getContactWithSpecOrFirstOfType('telephone', 'first');
	}

    /**
     * retrieve all email addresses with the apply_for_planning marker
     * fallback to first email address if none found
     * usage: $student->planning_email
     * @return string (comma separated)
     */
	public function getPlanningEmailAttribute() {
        return $this->getContactWithSpecOrFirstOfType('email', 'planning');
	}

    /**
     * retrieve all telephone numbers with the apply_for_planning marker
     * fallback to first telephone number if none found
     * usage: $student->planning_telephone
     * @return string (comma separated)
     */
    public function getPlanningTelephoneAttribute() {
        return $this->getContactWithSpecOrFirstOfType('telephone', 'planning');
    }

    /**
     * retrieve all email addresses with the apply_for_finance marker
     * usage: $student->financial_email
     * @return bool|string
     */
    public function getFinancialEmailAttribute() {
        return $this->getContactWithSpecOrFirstOfType('email', 'finance');
    }

    /**
     * retrieve all email addresses with the apply_for_promotions marker
     * usage: $student->promotions_email
     * @return bool|string
     */
    public function getPromotionsEmailAttribute() {
        return $this->getContactWithSpecOrFirstOfType('email', 'promotions');
    }
    /**
     * retrieve all salutations with the apply_for_planning marker in the email types of contact
     * usage: $student->planning_salutation
     * @return bool|string
     */
	public function getPlanningSalutationAttribute() {
	    $salutation = '';
        $allContactInfo = $this->contacts;
        if (empty($allContactInfo)) {
            return '';
        }
        foreach ( $allContactInfo as $contact ) {
			if ( ( $contact->contacttype === "email" ) && ( $contact->apply_for_planning == 1) ) {
                // keep track of the first email address found
                $salutation .= $contact->use_salutation . ', ';
			}
		}
        // remove last ,
        return ($salutation === '' || $salutation === ', ')
            ? $this->firstname
            : substr($salutation, 0, -2);
	}

    /**
     * retrieve all salutation with the apply_for_finance marker in the email types of contact
     * usage: $student->financial_salutation
     * @return bool|string
     */
    public function getFinancialSalutationAttribute() {
        $salutation = '';
        $allContactInfo = $this->contacts;
        if (empty($allContactInfo)) {
            return '';
        }
        foreach ( $allContactInfo as $contact ) {
            if ( ( $contact->contacttype === "email" ) && ( $contact->apply_for_finance == 1) ) {
                $salutation .= $contact->use_salutation . ', ';
            }
        }
        // remove last ,
        return ($salutation === '' || $salutation === ', ')
            ? $this->firstname
            : substr($salutation, 0, -2);
    }

    /**
     * retrieve all salutation with the apply_for_promotions marker in the email types of contact
     * usage: $student->promotions_salutation
     * @return bool|string
     */
    public function getPromotionsSalutationAttribute() {
        $salutation = '';
        $allContactInfo = $this->contacts;
        if (empty($allContactInfo)) {
            return '';
        }
        foreach ( $allContactInfo as $contact ) {
            if ( ( $contact->contacttype === "email" ) && ( $contact->apply_for_promotions == 1) ) {
                $salutation .= $contact->use_salutation . ', ';
            }
        }
        // remove last ,
        return ($salutation === '' || $salutation === ', ')
            ? $this->firstname
            : substr($salutation, 0, -2);
    }


    /**
     * Return students that have an active course, now plus in future (not started yet)
     * Plus their planning details
     * @param bool $forSpreadsheet
     * @param string $sortOrder
     * @return \Illuminate\Support\Collection
     */
    public static function getCurrentlyActiveStudents($forSpreadsheet=false, $sortOrder="lastName") {
        Log::info("get currently active students, now plus future");
        $sortDirection = "ASC";
        switch ($sortOrder) {
            case "firstName":
                $sortColumn = "s.firstname";
                break;
            case "courseName":
                $sortColumn = "c.name";
                break;
            case "lastModified":
                $sortColumn = "sp.updated_at";
                $sortDirection = "DESC";
                break;
            default:
                $sortColumn = "s.lastname";
                break;
        }

        $select = $forSpreadsheet
            ? [ 's.firstname as studentfirstname',
                's.lastname as studentlastname',
                'c.name as coursename',
                // 'sp.id as prefId',
                'sp.updated_at',
                // 's.id as studentId',
                DB::raw("CASE WHEN cs.please_keep_scheduled_time = 1" .
                    " THEN '" . strtoupper(trans('generic.yes')) . "'" .
                    " ELSE '' END"),
                'cs.id as registrationId', // will be replaced by this registrations current schedule (foreach below)
                'sp.monday',
                'sp.tuesday',
                'sp.wednesday',
                'sp.thursday',
                'sp.friday',
                'sp.saturday',
                'sp.sunday']
            : ['s.id as studentId', 'sp.id as prefId', 's.*', 'sp.*', 'sp.updated_at AS lastSchedulePrefChanged'];
        $students = DB::table('students AS s')
            ->select($select)
            ->distinct()
            ->leftJoin('course_student AS cs', 's.id', '=', 'cs.student_id')
            ->leftJoin('schedule_prefs AS sp', 's.id', '=', 'sp.student_id')
            ->leftJoin('courses AS c', 'cs.course_id', '=', 'c.id')
            ->where([
                ["s.domain_id", "=", Auth::user()->domain->id],
                ["s.firstname", "<>", "-"],                     // no student groups
                ["s.date_of_birth", "<>", "1800-01-01"]         // no student groups
            ])
            ->whereNotNull('cs.course_id')
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere("end_date", ">", DB::raw("NOW()"));
            })
            ->orderBy($sortColumn, $sortDirection)
            ->get();

        // add probable current lesson day/time
        if ($forSpreadsheet) {
            foreach ($students as $student) {
                $planning = Schoolyear::getLastSchoolyearForPlanning($student->registrationId);
                if (!empty($planning)) {
                    $student->registrationId =
                        trans('localisation.' . strtolower($planning[0]->dayname)) . ", " .
                        substr($planning[0]->time, 0, 5);
                } else {
                    $student->registrationId = ucfirst(trans('generic.notavailable'));
                }
            }
        }
        return $students;
    }

    /**
     * Get studentrecords and contact info
     * Used in export to spreadsheet
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function getAllStudentRecords()
    {
        Log::info('getting all active student records and contact info');
        // all active students
        $activeStudents = Student::select(
            DB::raw('"active" as type, students.id, firstname, lastname, preposition, address, zipcode, city')
        )
            ->leftJoin('course_student AS cs', 'students.id', '=', 'cs.student_id')
            ->where(function($query) {
                $query->whereNull('cs.end_date')
                    ->orWhere('cs.end_date', '>', Carbon::now());
            })
            ->distinct()
            ->orderBy('lastname')->get();

        Log::info("getting student contacts for " . count($activeStudents) . " active students");
        // 'with' doesn't work, we'll have to get contacts the hard way
        foreach ($activeStudents as $index => $activeStudent) {
            $arr = $activeStudent->contacts->toArray();
            $eC = array_filter($arr, function ($entry) {
                return $entry["contacttype"] === 'email';
            });
            $sE = array_map(function ($eCE) {
                return $eCE["value"];
            }, $eC);
            // don't use field name 'email', this is already defined on the model
            $activeStudents[$index]->mailaddress = implode(', ', $sE);

            // Serialize telephone entries
            $arrT = $activeStudent->contacts->toArray();
            $tC = array_filter($arrT, function ($entry) {
                return $entry["contacttype"] === 'telephone';
            });
            $sT = array_map(function ($tCE) {
                return $tCE["value"];
            }, $tC);
            $activeStudents[$index]->telephone = implode(", ", $sT);
        }
        return $activeStudents;
    }
    /**
     * Get studentrecords for maillist
     * Used in export to spreadsheet
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function getAllStudentRecordsForMaillist($order='lastname')
    {
        Log::info('getting all active student records and contact info for export to maillist');
        // all active students
        $activeStudents = Student::whereHas('registrations', function($query) {
            $query->whereNull('end_date')
                ->orWhere('end_date', '>', \Carbon\Carbon::now());
        })
            ->with('contacts')
            ->orderBy('lastname')
            ->get();

        $retColl = new Collection();
        foreach ($activeStudents as $activeStudent) {
            $student = [
                "firstname"     => $activeStudent->firstname,
                "lastname"      => $activeStudent->lastname,
                "preposition"   => $activeStudent->preposition,
                "address"       => $activeStudent->address,
                "zipcode"       => $activeStudent->zipcode,
                "city"          => $activeStudent->city,
                "email"         => $activeStudent->promotionsEmail,
                "url"           => env("APP_URL") . "/schedulepreference/" . $activeStudent->accesstoken,
            ];
            $retColl->add($student);
        }
        return $retColl;
    }

    /**
     * returns active students on the 1st day of a requested month and year
     * @param $month
     * @param $year
     * @return mixed
     */
    private static function getActiveStudents($month, $year) {
        Log::debug("Find active students for Y-M: $year-$month");
        $mNum = intval($month);
        $yNum = intval($year);
        
        if(is_numeric($mNum) && is_numeric($yNum) && $mNum > 0 && $mNum < 13 && $yNum > 2000) {
            $firstDayOfMonth = "$year-$month-01";
            $lastDayOfMonth = date('Y-m-t', strtotime($firstDayOfMonth));
            
            $students = DB::table('students AS s')
                ->select('s.id', 's.firstname', 's.lastname', 's.preposition', 
                        's.address', 's.zipcode', 's.city', 's.date_of_birth', 
                        's.accesstoken', 's.domain_id', 's.created_at', 's.updated_at')
                ->distinct()
                ->leftJoin('course_student AS cs', 's.id', '=', 'cs.student_id')
                ->where([
                    ["s.domain_id", "=", Auth::user()->domain->id],
                    ["s.firstname", "<>", "-"],
                    ["s.date_of_birth", "<>", "1800-01-01"]
                ])
                ->where(function($query) use ($firstDayOfMonth, $lastDayOfMonth) {
                    $query->where(function($q) use ($firstDayOfMonth, $lastDayOfMonth) {
                        $q->whereBetween('cs.start_date', [$firstDayOfMonth, $lastDayOfMonth]);
                    })->orWhere(function($q) use ($firstDayOfMonth, $lastDayOfMonth) {
                        $q->where('cs.start_date', '<=', $firstDayOfMonth)
                          ->where(function($sq) use ($firstDayOfMonth) {
                              $sq->whereNull('cs.end_date')
                                 ->orWhere('cs.end_date', '>=', $firstDayOfMonth);
                          });
                    });
                })
                ->groupBy('s.id')
                ->get();
                
            return $students;
        }
        return null;
    }

    /**
     * return active students, grouped by requested consecutive months
     * @param $startMonth
     * @param $startYear
     * @param int $numberOfMonth
     * @return array
     * @throws \Exception
     */
    public static function findActiveStudentsGroupedByMonth($startMonth, $startYear, $numberOfMonth=12) {
        Log::info("Find active students from Y-M: $startYear-$startMonth, for $numberOfMonth month(s)");
        $retStudents = [];
        
        if(is_numeric($startMonth) && is_numeric($startYear) && is_numeric($numberOfMonth) && 
           $startMonth > 0 && $startMonth < 13 && $startYear > 2016) {
            
            $askDate = new \DateTime("$startYear-$startMonth-01");
            for($count = 1; $count <= $numberOfMonth; $count++) {
                $monthKey = $askDate->format('m') . "-" . $askDate->format('Y');
                $retStudents[$monthKey] = Student::getActiveStudents($askDate->format('m'), $askDate->format('Y'));
                
                try {
                    $askDate->modify('first day of next month');
                } catch(\Exception $e) {
                    Log::error("Kan maand niet toevoegen: " . $e->getMessage());
                }
            }
        }
        return $retStudents;
    }

    /**
     * search for a student by access token
     * @param string $accesstoken
     * @return Student|Model|null
     */
    public static function getByAccessToken($accesstoken='') {
        // no code/scripting etc in the get parameter
        $re = '/^[a-zA-Z0-9]*$/m';
        if(!empty($accesstoken) && preg_match($re, $accesstoken)) {
            $student = Student::where('accesstoken', '=', $accesstoken)
                ->first();
            // if we found the student == if we have an ID
            if (isset($student->id)) {
                Log::info("YES! Found student: $student->id for token: $accesstoken");
                return $student;
            } else {
                Log::error("Student not found, using access token $accesstoken");
            }
        }
        return null;
    }

    public function getActiveRegistrations() {
        return Registration::getActiveRegistrationsForStudent($this->id);
    }

    /**
     * Check if there is an incomplete checklist at all
     * only checks active courses (also future active courses)
     */
    public function getHasIncompleteChecklistAttribute() {
        $result = false;
        $regs = $this->getActiveRegistrations();
        foreach ($regs as $reg) {
            // get checklists for this registration
            foreach ($reg->checklists as $checklist) {
                if(!$checklist->isComplete()) {
                    $result = true;
                    break;
                }
            }
            if($result) break;
        }
        return $result;
    }

    /**
     * check status of this account in Classy
     */
    public function classyuser() {
        return Classyuser::where('student_id', "=", $this->id)->first();
    }

    /**
     * students that are active students (no groups)
     * but have no registered e-mail address
     * @return mixed
     */
    public static function getActiveStudentsWithoutEmail()
    {
        return Student::query()
            ->select("students.id", "students.name")
            ->leftJoin("course_student as cs", "cs.student_id", "=", "students.id")
            ->where("cs.start_date", "<", Carbon::now())
            ->where(function($query) {
                $query->whereNull("cs.end_date")
                    ->orWhere("cs.end_date", ">=", Carbon::now());
            })
            ->where("students.date_of_birth", "<>", "1800-01-01")
            ->where("students.firstname", "<>", "-")
            ->whereDoesntHave('contacts', function ($query) {
                $query->where('contacttype', 'email');
            })
            ->get();
    }
    /**
     * students that have never had a course registration (for alert box)
     * @return mixed
     */
    public static function getStudentsWithoutRegistration()
    {
        return Student::select('students.id', 'students.name')
            ->leftJoin('course_student as cs', 'cs.student_id', '=', 'students.id')
            ->whereNull('cs.id')
            ->get();
    }

}

