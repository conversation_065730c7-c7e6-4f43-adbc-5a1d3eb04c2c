<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Classyuser extends Model {

    protected $table = "classy_users";

    /**
     * Set standard filtering on queries:
     * makes sure only schoolyears from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if (!Auth::guest()) {
            $query->where([
                ['classy_users.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }
}
