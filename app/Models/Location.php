<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\Mydat2Ndat;

class Location extends Model {

    use HasFactory;

    /**
     * Set standard filtering on queries:
     * makes sure only locations from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['locations.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    public function events() {
        return $this->hasMany(Event::class);
    }

    public function futureEvents() {
        return $this->hasMany(Event::class)->where("datetime", ">", DB::raw("NOW()"));
    }

    public function scheduleproposals() {
        return $this->hasMany(Scheduleproposal::class);
    }

    /**
     * return an array containing all future occupied time segments of locations
     * to be used during planning of a series of events
     */
    public static function getFutureOccupied() {
        $retArr = [];
        $events = Event::select("events.id as id", "datetime", "timespan", "timetable_id", "course_student_id",
            "location_id", "locations.name", "course_id", "student_id", "students.name as studentname", "courses.name as coursename")
            ->leftJoin("locations", "location_id", "=", "locations.id")
            ->leftJoin("timetables", "timetable_id", "=", "timetables.id")
            ->leftJoin("course_student", "course_student_id", "=", "course_student.id")
            ->leftJoin("courses", "courses.id", "=", "course_student.course_id")
            ->leftJoin("students", "students.id", "=", "course_student.student_id")
            ->where("datetime", ">=", DB::raw("NOW()"))
            ->orderBy("datetime")
            ->get();
        // create an assoc array to be able to quickly find occupied location on a datetime/segment
        foreach ($events as $event) {

            $to_time = new \DateTime($event->datetime);
            date_add($to_time, date_interval_create_from_date_string(str_replace("minuten", "minutes",$event->timespan)));

            $retArr[$event->location_id][] = [
                'event_id'          => $event->id,
                'datetime'          => $event->datetime,
                'course'            => $event->course_id,
                'coursename'        => $event->coursename,
                "timespan"          => $event->timespan,
                "to_time"           => $to_time->format("H:i"),
                "timetable_id"      => $event->timetable_id,
                "course_student_id" => $event->course_student_id,
                "student_id"        => $event->student_id,
                "studentname"       => $event->studentname,
                "location_id"       => $event->location_id,
                "location_name"     => $event->name
            ];
        }
        return $retArr;
    }


    /**
     * Check if an event is blocking on a given date and time during a given duration
     * So it checks if there is any overlap between the event and the given date and time and duration
     * @param int $locationId The tutor's ID
     * @param string $date The date in 'YYYY-MM-DD' format
     * @param string $time The start time in 'HH:MM' format
     * @param int $duration The duration in number of minutes
     * @param int $skipEventId The event to be skipped
     * @return array
     * @throws \Exception
     */
    public static function isOccupiedOn(int $locationId, string $date, string $time, int $duration, int $skipEventId = 0)
    {
        Log::info("checking if location $locationId is occupied on $date from $time for $duration minutes");
        $blockingEvents = [];
        // first check if tutor is of the logged in domain
        $untilTime = date('H:i', strtotime($time) + $duration * 60);

        // Select only events of a specific tutor on a specific day (pre-select relevant events)
        $events = Event::where('location_id', $locationId)
            ->where('id', '<>', $skipEventId)
            ->whereDate('datetime', '=', $date)
            ->get();

        $requestedStart = new \DateTime($date . " " . $time);
        $requestedEnd = new \DateTime($date . " " . $untilTime);

        $blockingEventObjects = $events->filter(function ($event) use ($requestedStart, $requestedEnd) {
            $eventStart = new \DateTime($event->datetime);
            $eventEnd = clone $eventStart;
            $duration = $event->timespan; // can e.g. be '10 minutes' or '10 minuten'
            $duration = str_replace(" minutes", '',
                str_replace(" minuten", '', $duration));
            $eventEnd->add(new \DateInterval('PT' . $duration . 'M'));
            return ($eventStart > $requestedStart && $eventStart < $requestedEnd) ||
                ($eventEnd > $requestedStart && $eventEnd < $requestedEnd) ||
                ($eventStart < $requestedStart && $eventEnd > $requestedEnd);
        });

        if (count($blockingEventObjects) > 0) {
            // list the blocking event's names (field=title)
            foreach ($blockingEventObjects as $fev) {
                $blockingEvents[] = $fev->course->name . " " . $fev->student->name . " " . Mydat2Ndat::getNdat($fev->datetime);
            }
        }
        Log::info("found " . count($blockingEvents) . " possibly blocking events due to location occupation");
        return $blockingEvents;
    }
}
