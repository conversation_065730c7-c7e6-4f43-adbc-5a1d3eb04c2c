<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\MiscDate;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Timetable extends Model {

    use HasFactory;

    /**
     * get the schoolyear for this timetable
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function schoolyear() {
        return $this->belongsTo(Schoolyear::class);
    }

    /**
     * get the corresponding registration.
     * note the foreign_key parameter because it doesn't conform to the naming convention
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function registration() {
        return $this->belongsTo(Registration::class, 'course_student_id', 'id');
    }

    /**
     * get the events for this timetable
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function events() {
        return $this->hasMany(Event::class)->orderBy('datetime');
    }

    /**
     * Get future events of a timetable (including today)
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function futureEvents() {
        return $this->hasMany(Event::class)
            ->orderBy('datetime')
            ->where("datetime", ">=", DB::raw("NOW()"))
            ->with("location", "tutor");
    }

    /**
     * Get all events of a timetable
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function allEvents() {
        return $this->hasMany(Event::class)
            ->orderBy('datetime')
            ->with("location", "tutor");
    }

    /**
     * Get related planningsection (optional)
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function planningsection() {
        return $this->belongsTo(Planningsection::class);
    }

    public static function getLocationAndTutorConflicts()
    {
        // in alerts link to cs.id: ../timetableedit/{cs.id}
        $q = "SELECT distinct cs.id as registration_id, st.id as student_id, st.name
        FROM events as e1
                 LEFT JOIN events e2 on (
                    e2.datetime < e1.datetime
                AND DATE_ADD(e2.datetime, INTERVAL (substr(e2.timespan,1,3) -1) MINUTE) >= e1.datetime
                AND SUBSTR(e1.datetime, 1, 10) = SUBSTR(e2.datetime, 1, 10)
            )
                 LEFT JOIN locations l on e1.location_id = l.id
                 LEFT JOIN timetables t on e1.timetable_id = t.id
                 LEFT JOIN course_student cs on t.course_student_id = cs.id
                 LEFT JOIN students st on cs.student_id = st.id
        WHERE (e1.tutor_id = e2.tutor_id OR e1.location_id = e2.location_id)
          AND  e1.datetime >= now()
          AND l.domain_id = " . Auth::user()->domain_id;

        return DB::select($q);
    }

    /**
     * Complex query-set to determine if there are any conflicts between appointments with students and date-exceptions
     * @throws \Exception
     * @return array
     */
    public static function getCurrentConflicts(): array
    {
        // all future events thay may or may not be in conflict with a date exception
        $deConflicts = [];
        $endDtSelect =
            "       CASE LENGTH(events.timespan)" .
            "           WHEN 10 THEN DATE_ADD(events.datetime, INTERVAL SUBSTR(events.timespan,1,2) MINUTE)" .
            "           WHEN 11 THEN DATE_ADD(events.datetime, INTERVAL SUBSTR(events.timespan,1,3) MINUTE)" .
            "       END as enddt";
        // we need the join with `users` to be able to filter on domain_id, which is only derivative in `events`
        $resultEvents = Event::query()
            ->leftJoin("users as tutor", "events.tutor_id", "=", "tutor.id")
            ->select(
                "events.id as event_id",
                "tutor_id",
                "tutor.name as tutor_name",
                "location_id",
                "datetime as startdt",
                DB::raw($endDtSelect)
            )
            ->where([
                ["datetime", ">=", Carbon::now()],
                ["tutor.domain_id", "=", Auth::user()->domain_id]
            ])->get();
        if ($resultEvents) {
            // all future date exceptions that may or may not be in conflict with an event
            $resultDateExceptions = DateException::query()
                ->where("datetime_start", ">=", Carbon::now())
                ->where([
                    ["domain_id", "=", Auth::user()->domain_id],
                    ["exclude_from_alerts", "=", 0]
                ])->get();
            // nested foreach, so we try to keep the arrays as small as possible
            foreach ($resultDateExceptions as $resultDateException) {
                // find overlap in date/time
                foreach ($resultEvents as $resultEvent) {
                    if (MiscDate::overlaps(
                        $resultEvent->startdt,
                        $resultEvent->enddt ?? "",
                        $resultDateException->datetime_start,
                        $resultDateException->datetime_end ?? "")
                    ) {
                        // we have overlap, now check if it is really a conflict
                        // if the DE has no tutors that means: whole school closed: always a conflict
                        if (count($resultDateException->tutors) === 0) {
                            Log::info("Conflict found: SCHOOL_CLOSED");
                            $conflict = [
                                "conflict_because"  => config('app.SCHOOL_CLOSED'), // value: 1
                                "conflict_info"     => []
                            ];
                        }
                        // if the tutor in the event is also present in the tutors of the date exception
                        else if (in_array($resultEvent->tutor_id, $resultDateException->tutors->toArray())) {
                            Log::info("Conflict found: TUTOR_OCCUPIED");
                            $conflict = [
                                "conflict_because"  => config('app.TUTOR_OCCUPIED'), // value: 2
                                "conflict_info"     => ["tutor" => $resultEvent->tutor_id],
                            ];
                        }
                        // if the location in the event is the same as the location in the date exception
                        else if ($resultEvent->location_id === $resultDateException->location_id) { // CHECKEN!
                            Log::info("Conflict found: LOCATION_OCCUPIED");
                            $conflict = [
                                "conflict_because"  => config('app.LOCATION_OCCUPIED'), // value: 3
                                "conflict_info"     => ["location" => $resultEvent->location_id],
                            ];
                        }
                        if (isset($conflict)) {
                            // only now turn this event into a model instance to be able to use methods
                            // (now we know we need it)
                            $ev = Event::findOrFail($resultEvent->event_id);
                            $deConflicts[] = array_merge($conflict, [
                                "event_id"          => $ev->id,
                                "course_id"         => $ev->course->id,
                                "student_id"        => $ev->student->id,
                                "student_name"      => $ev->student->name,
                                "course_name"       => $ev->course->name,
                                "course_student_id" => $ev->timetable->course_student_id,
                                "startdt"           => $ev->datetime,
                                "orgtimespan"       => $ev->timespan,
                                "event_tutor_id"    => $ev->tutor_id,
                                "tutor_name"        => $ev->tutor_name,
                                "conflict_info"     => [],
                                "de_datetime_start" => $resultDateException->datetime_start,
                                "de_datetime_end"   => $resultDateException->datetime_end,
                                "reason"            => $resultDateException->reason,
                                "event_start"       => $ev->startdt,
                                "event_end"         => $ev->enddt
                            ]);
                            unset($conflict);
                        } else {
                            Log::debug("Verdict: No conflict found");
                        }
                    }
                }
            }
        }
        Log::info("Found " . count($deConflicts) . " calendar conflicts");
        return $deConflicts;
    }

    /**
     * Return the events of a registration for a given schoolyear
     * the timetable was created when the student registered
     * @param int $registrationId
     * @param int $schoolyearId
     * @return Event[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function getScheduleOfRegistrationInSchoolyear($registrationId = 0, $schoolyearId = 0) {
        $t = Timetable::where([
            ["schoolyear_id", "=", $schoolyearId],
            ["course_student_id", "=", $registrationId]
        ])->first();

        if(!empty($t)) {
            return Event::where("timetable_id", "=", $t->id)->get();
        } else {
            return [];
        }
    }

    /**
     * Get the timetable for a registration in a certain school year
     * @param int $regId
     * @param int $schoolyearId
     * @return Timetable
     */
    public static function getTimetableFromRegAndSchoolYear($regId, $schoolyearId) {
        return Timetable::where([
            ["course_student_id", "=", $regId],
            ["schoolyear_id", "=", $schoolyearId]
        ])->first();
    }
}
