<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TrialcourseRelation extends Model
{
    protected $table='trialcourse_courses';

    /**
     * returns which courses are available given the requested trial course
     * @param $trialCourseId
     * @return array
     */
    static public function trialcourseRelations($trialCourseId = 0)
    {
        $retArray = [];
        $trialCoursesQuery = Course::query()->where('is_trial_course', '=', '1');
        if ($trialCourseId > 0) {
            $trialCoursesQuery->where('id', '=', $trialCourseId);
        }
        $trialCourses = $trialCoursesQuery->get();

        foreach ($trialCourses as $trialCourse) {
            $tcRels = TrialcourseRelation::select("course_id")
                ->where('trialcourse_id', '=', $trialCourse->id)
                ->get();
            foreach ($tcRels as $tcRel) {
                $retArray[$trialCourse->id][] = $tcRel->course_id;
            }
        }
        return $retArray;
    }

    /**
     * returns which trial courses lead to the requested course
     * @param $courseId
     * @return array
     */
    static public function trialcoursesForCourse($courseId)
    {
        log::info("get prepare course for courseId: " . $courseId);
        return TrialcourseRelation::query()
            ->select("trialcourse_id")
            ->leftJoin("courses", "courses.id", "=", "trialcourse_courses.trialcourse_id")
            ->where([
                ["courses.domain_id", "=", Auth::user()->domain_id],
                ["course_id", "=", $courseId],
                ['courses.archive', '=', '0']
            ])
            ->pluck('trialcourse_id')
            ->all();
    }
}
