<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Passport\HasApiTokens;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Authenticatable
{
	use HasApiTokens, Notifiable, hasFactory ;

    /**
	 * The attributes that are mass assignable.
	 *
	 * @var array
	 */
	protected $fillable = [
		'id', 'name', 'email', 'password',
	];

	/**
	 * The attributes that should be hidden for arrays.
	 *
	 * @var array
	 */
	protected $hidden = [
		'password', 'remember_token',
	];

	public function roles()
    {
		return $this->belongsToMany(Role::class)->withPivot(['start_date', 'end_date']);
	}

    // check availabilities of tutor
    public function availabilities()
    {
        return $this->hasMany(Availability::class, 'tutor_id', 'id');
    }

    // used for getting the domain of the logged in user
    public function domain()
    {
	   return $this->belongsTo(Domain::class);
    }

    /**
     * these are dateexception for this tutor, not the whole school!
     */
    public function dateExceptions()
    {
        return $this->belongsToMany(DateException::class, "dateexception_tutor");
    }

    // for 2FA
    public function loginsecurity()
    {
        return $this->hasOne(Loginsecurity::class);
    }

    /**
     * Get all tutors of a domain
     * @return Users as Tutor objects
     * @deprecated
     */
	public static function getTutors()
    {
		$tutors =  User::leftJoin("role_user", "users.id", "=", "role_user.user_id")
			->where([
			    ["role_id", "=", config("app.TUTORROLEID")],
                ["domain_id", "=", Auth::user()->domain->id]
            ])
            ->with("availabilities")
			->get();
        foreach ($tutors as $tutor) {
            // check if exists in appointments (events or dateexceptions)
            $countEvents = Event::countNrOfEventsForTutor($tutor->id);
            $countDateExceptions = DateException::countNrOfDateExeptionsForTutor($tutor->id);
            $tutor->inUse = ($countEvents > 0 || $countDateExceptions > 0);
		}

		return $tutors;
	}

    /**
     *
     */
    public static function getActiveTutors()
    {
        $tutors =  User::leftJoin("role_user", "users.id", "=", "role_user.user_id")
            ->where([
                ["role_id", "=", config("app.TUTORROLEID")],
                ["domain_id", "=", Auth::user()->domain->id]
            ])
            ->where(function($query) {
                return $query->whereNull("end_date")->orWhere("end_date", ">", DB::raw("now()"));
            })
            ->with("availabilities")
            ->orderBy('name')
            ->get();

        foreach ($tutors as $index => &$tutor) {
            // check if exists in appointments (events or dateexceptions)
            $countEvents = Event::countNrOfEventsForTutor($tutor->id);
            $countDateExceptions = DateException::countNrOfDateExeptionsForTutor($tutor->id);
            $tutor->inUse = ($countEvents > 0 || $countDateExceptions > 0);
        }

        return $tutors;
    }

    /**
     *
     */
    public static function getInactiveTutors()
    {
        $tutors =  User::leftJoin("role_user", "users.id", "=", "role_user.user_id")
            ->where([
                ["role_id", "=", config("app.TUTORROLEID")],
                ["domain_id", "=", Auth::user()->domain->id],
                ["end_date", "<", DB::raw("now()")]
            ])
            ->with("availabilities")
            ->get();

        foreach ($tutors as $index => $tutor) {
            // check if exists in appointments (events or dateexceptions)
            $countEvents = Event::countNrOfEventsForTutor($tutor->id);
            $countDateExceptions = DateException::countNrOfDateExeptionsForTutor($tutor->id);
            $tutor->inUse = ($countEvents > 0 || $countDateExceptions > 0);
        }

        return $tutors;
    }


    /**
     * Get the tutor object of a logged in user
     * Is used to limit the shown user data to a logged
     * in tutor without admin rights
     * @param $userid
     * @return User as Tutor
     */
	public function getTutor($userid)
    {
		$tutor =  User::leftJoin("role_user", "users.id", "=", "role_user.user_id")
			->where([
			    ["role_id", "=", config("app.TUTORROLEID")],
                ["users.id", "=", $userid],
                ["domain_id", "=", Auth::user()->domain->id]
            ])
            ->with("availabilities")
			->get();

        // check if exists in appointments (events or dateexceptions)
        $countEvents = Event::countNrOfEventsForTutor($tutor->id);
        $countDateExceptions = DateException::countNrOfDateExeptionsForTutor($tutor->id);
        $tutor->inUse = ($countEvents > 0 || $countDateExceptions > 0);

		return $tutor;
	}



    /**
     * Determine if the user has the requested role
     *
     * If an array of role names was sent: determine if the user has at least one of the requested roles
     * 'scolavisa' is a superuser. If we find the scolavisa role on a user and the requested
     * role is a valid role, this function will return true
     *
     * -- ------------------------- --
     * -- run unit test if changed! --
     * -- ------------------------- --
     *
     * @param array|string $requestedRoles the requested role names
     * @return bool
     */
	public function userIsA($requestedRoles)
    {
	    // setup
        $validRoles = [];
        $allRoles = Role::all();
        foreach ($allRoles as $role) $validRoles[] = $role->rolename;

        // always array, even if only 1 requested role
        $requestedRoles =  is_string($requestedRoles) ? [$requestedRoles] : $requestedRoles;

        // all requested roles need to be valid roles
        foreach ($requestedRoles as $requestedRole) {
            if (!in_array($requestedRole,$validRoles)) return false;
        }

        // now determine if the user is 'scolavisa'
        // or i this user has the requested role
        $myRoles = $this->roles;
        foreach ($myRoles as $myRole) {
            if ($myRole->rolename === 'scolavisa') return true;
            if (in_array($myRole->rolename,$requestedRoles)) return true;
        }

        // all other cases return false
        return false;
    }

    /**
     * get the start- and enddate of a users role
     * @param $roleId
     * @return array
     */
    public function roledates($roleId)
    {
        Log::info("getting dates");
        // get the tutor role
        $role = DB::table("role_user")
            ->select("start_date", "end_date")
            ->where("user_id", "=", $this->id)
            ->where("role_id", "=", $roleId)
            ->first();
        return ["start_date" => $role->start_date, "end_date" => $role->end_date];
    }


    /**
     * check if this user has future events
     */
    public function hasFutureTutorEvents()
    {
        $eventsCount = Event::where([
            ['tutor_id', "=", $this->id],
            ['datetime', ">=", DB::raw('now()')]
        ])->count();
        return $eventsCount > 0;
    }

    /**
     * Check if we have at least one inactive role to return false
     * meaning inactive has precedence over active
     * 2024-03-10: this is weird, refactored in getActiveAttribute
     * @deprecated use getActiveAttribute
     * @return bool
     * @throws \Exception
     */
    public function getIsActiveAttribute()
    {
        // get roles
        $roles = $this->roles;
        foreach ($roles as $role) {
            $now = new \DateTime();
            $start_date = new \DateTime($role->pivot->start_date);
            if($now > $start_date) {
                // start_date OK, now check end_date
                if(!empty($role->pivot->end_date)) {
                    $end_date = new \DateTime($role->pivot->end_date);
                    if($end_date < $now) {
                        return false;
                    }
                }
            } else {
                return false;
            }
        }

        // found no inactive role
        return true;
    }

    /**
     * Check if the user is active based on their roles.
     * If the user has a role that is not yet active or has expired, they are considered inactive.
     *
     * If the user has the role 'scolavisa' they are always considered inactive.
     * This prevents scolavisa users from showing up in the user interfaces.
     *
     * @return bool Returns true if the user is active, false otherwise.
     */
    public function getActiveAttribute()
    {
        // get roles
        $roles = $this->roles;
        // if one of the roles is scolavisa (superuser) we are always inactive (skip from interfaces)
        $return = true;
        $now = new \DateTime();
        foreach ($roles as $role) {
            if ($role->id === intval(config("app.SCOLAVISAROLEID"))) {
                // exclude any scolavisa test accounts
                $return = false;
            } else {
                try {
                    $start_date = new \DateTime($role->pivot->start_date);
                } catch (\Exception $e) {
                    // no start_date, so this role is not active
                    $return = false;
                }
                if ($now < $start_date) {
                    // start_date not yet reached
                    $return = false;
                } else {
                    // start_date OK, now check end_date
                    if (!empty($role->pivot->end_date)) {
                        try {
                            $end_date = new \DateTime($role->pivot->end_date);
                        } catch (\Exception $e) {
                            // no valid end_date, so this role is not active
                            $return = false;
                        }
                        if ($end_date < $now) {
                            $return = false;
                        }
                    }
                }
            }
            if (!$return) break; // no need to check further
        }
        return $return;
    }


    public function setLastActive()
    {
        $this->last_active_at = date('Y-m-d H:i:s');
        $this->save();
    }
}
