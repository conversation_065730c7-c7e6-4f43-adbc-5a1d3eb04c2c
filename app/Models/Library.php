<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Library extends Model {
    /**
     * Set standard filtering on queries:
     * makes sure only locations from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['libraries.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    public function courses() {
        return $this->belongsToMany(Course::class,
            "library_course", "library_id", "course_id");
    }

    public function coursegroups() {
        return $this->belongsToMany(Coursegroup::class,
            "library_coursegroup", "library_id", "coursegroup_id");
    }

    public function documents() {
        return $this->belongsToMany(Document::class,
            "library_document", "library_id", "document_id");
    }

    public function students() {
        return $this->belongsToMany(Student::class,
            "library_student", "library_id", "student_id")
            ->where([['firstname', '<>', '-'], ['date_of_birth', '<>', '1800-01-01']]);
    }

    public function studentgroups() {
        return $this->belongsToMany(Studentgroup::class,
            "library_student", "library_id", "student_id")
            ->where([['firstname', '=', '-'], ['date_of_birth', '=', '1800-01-01']]);
    }
}
