<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RecurrenceOption extends Model {

    use HasFactory;

	protected $table = "recurrenceoptions";

    /**
     * Set standard filtering on queries:
     * makes sure only recurrenceoptions from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if(!Auth::guest()) {
            $query->where([
                ['recurrenceoptions.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

    public function courses() {
		return $this->hasMany(Course::class, 'recurrenceoption_id', 'id');
	}

}
