<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Planningentries extends Model
{
    use HasFactory;
    /**
     * Set standard filtering on queries:
     * makes sure only courses from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['planningentries.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }
}
