<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DateException extends Model {

    use HasFactory;

    /**
     * Set standard filtering on queries:
     * makes sure only dateexceptions from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['date_exceptions.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    /**
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function schoolyear() {
		return $this->belongsTo(Schoolyear::class);
	}

	public function tutors() {
        return $this->belongsToMany(User::class, "dateexception_tutor", 'date_exception_id', 'user_id')->withPivot(["confirmed", "mandatory"]);
    }

    public function location() {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get DateExceptions for a given school year.
     * @param int $schoolyearId
     * @param bool $onlyfuture only return future date exceptions
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
	public function getFormatted($schoolyearId = 0, $onlyfuture = false) {

	    if($onlyfuture) {
	        $des = DateException::where([
	            ["schoolyear_id", "=", "$schoolyearId"],
                ["datetime_start", ">=", DB::raw("now()")],
                ["domain_id", "=", Auth::user()->domain->id]
            ])
                ->with("tutors")
                ->orderBy("datetime_start")
                ->get();
        } else {
            $des = DateException::where([
                ["schoolyear_id", "=", "$schoolyearId"],
                ["domain_id", "=", Auth::user()->domain->id]
            ])
                ->with("tutors")
                ->orderBy("datetime_start")
                ->get();
        }
		return $des;
	}

	/**
	 * Return the date exceptions within the requested period
	 * Also add DE's that end within the requested period
	 * Also add DE's that start before and end after the requested period
	 * @param $startDate
	 * @param $endDate
	 *
	 * @return array
	 */
	public static function getCalendarEvents($startDate, $endDate) {
		$retArray = [];

        Log::debug("getCalendarEvents Date Exceptions: $startDate - $endDate");
		// see if logged-in user is an admin...
		if(Auth::user()->userIsA('admin')) {
			// get all events (whole school)
			$events = DateException::where("domain_id", "=", Auth::user()->domain->id)
                ->where(function($query) use ($startDate, $endDate) {
                    $query->whereBetween("datetime_start", [$startDate, $endDate])
                        ->orWhereRaw('datetime_end BETWEEN ? AND ?', [$startDate, $endDate])
                        ->orWhereRaw("datetime_start < ? AND datetime_end > ?", [$startDate, $endDate]);
                })
				->get();
		} else {
			// ...otherwise: get the logged-in user's events
			$events = DateException::where("domain_id", "=", Auth::user()->domain->id)
                ->whereBetween("datetime_start", [$startDate, $endDate])
				->where(function($query) {
					$query->where("tutor_id", "=", Auth::user()->id)
						  ->orWhere("tutor_id", "=", null);
				})->get();
		}

		foreach ($events as $event) {
            // we define a (range of) all-day event(s) as starting on 00:00:00 and ending on 23:59:59
            $isAllDayEvent = (str_contains($event->datetime_start, '00:00:00') && str_contains($event->datetime_end, "23:59:59"));
            if ($isAllDayEvent) {
                $start = substr($event->datetime_start, 0, 10);
                $dateEnd = new \DateTime($event->datetime_end);
                $end = $dateEnd->format('Y-m-d');
            } else {
                $start = $event->datetime_start;
                $end = $event->datetime_end;
            }
            $title = $event->reason;
            if(!empty($event->tutor_id)) {
                $tutor = $event->tutor;
                $title = $tutor->name . "/" . $title;
            }
            $deColor = "#C75454"; // fallback color
            if (!$event->plan_blocking) {
                $deColor = $event->calendar_color ?? "#C75454";
                $title = $title . " (" . trans('generic.nonblocking') . ")";
            }
			$retArray[] = [
				"id"                => $event->id,
				"title"             => $title,
				"allDay"            => $isAllDayEvent,
				"start"             => $start,
				"end"               => $end,
                "isPlanBlocking"    => $event->plan_blocking,
                "class"             => !$event->plan_blocking ? "non-blocking_" . $event->id : "",
                "backgroundColor"   => isset($tutor) ? $tutor->hexcolor : $deColor,
                "borderColor"       => isset($tutor) ? $tutor->hexcolor : $deColor,
                "tutor"             => isset($tutor) ? $tutor->id : '0',
                "tutorColor"        => isset($tutor) ? $tutor->hexcolor : $deColor,
				"eventType"         => 'dateException'
			];
		}

		return $retArray;
	}

        /**
     * @param $tutorId
     * @return int
     */
    static function countNrOfDateExeptionsForTutor($tutorId) {
        return DateException::query()
            ->leftJoin("dateexception_tutor as dt", "date_exceptions.id", "=", "dt.date_exception_id")
            ->where("dt.user_id", "=", $tutorId)->count();
    }

    public function getIsWholeDayAttribute()
    {
        return str_contains($this->datetime_start, "00:00:00") && str_contains($this->datetime_end, "23:59:59");
    }

    /**
     * Check if a date exception is blocking on a given date and time during a given duration.
     * So it checks if there is any overlap between the date exception and the given date and time and duration.
     * A date exception can also be a meeting so it can also have a starting time and ending time.
     * @param string $date The date in 'YYYY-MM-DD' format
     * @param string $time The start time in 'HH:MM' format
     * @param int $duration The duration in number of minutes
     * @return array
     */
    public static function isBlockingOLD($date, $time, $duration=60)
    {
        Log::info("checking if a blocking date exception is present on $date from $time for $duration minutes");

        $blockingDEs = [];
        $untilTime = date('H:i', strtotime($time) + $duration * 60);
        $dateExceptions = DateException::where("domain_id", "=", Auth::user()->domain->id)
            ->where(function($query) use ($date, $time, $untilTime) {
                $query->where([
                    ["datetime_start", "<=", $date . " " . $untilTime],
                    ["datetime_end", ">=", $date . " " . $time]
                ])->orWhere(function($query) use ($date, $time, $untilTime) {
                    $query->where([
                        ["datetime_start", "<=", $date . " " . $time],
                        ["datetime_end", ">=", $date . " " . $untilTime]
                    ]);
                });
            })->get();
        if(count($dateExceptions) > 0) {
            // list the blocking date exception's names (field=reason)
            foreach ($dateExceptions as $de) {
                $blockingDEs[] = $de->reason;
            }
        }
        return $blockingDEs;
    }

    /**
     * Check if a date exception is blocking on a given date and time during a given duration.
     * So it checks if there is any overlap between the date exception and the given date and time and duration.
     * A date exception can also be a meeting, so it can also have a starting time and ending time.
     * @param string $date The date in 'YYYY-MM-DD' format
     * @param string $time The start time in 'HH:MM' format
     * @param int $duration The duration in number of minutes
     * @param int $locationId The location's ID
     * @param int $tutorId The tutor's ID
     * @return array
     */
    public static function isBlocking($date, $time, $locationId, $tutorId, $duration = 60) {

        Log::info("checking if a blocking date exception is present on $date from $time for $duration minutes." .
            "Location: $locationId, Tutor: $tutorId");

        $blockingDEs = [];
        // first, get all date exceptions that are present on the $date and are blocking;
        $dateExceptions = DateException::query()
            ->where("plan_blocking", "=", 1)
            ->where(function($qo) use ($date, $time, $duration) {
                $qo->whereDate("datetime_start", "=", $date)
                    ->orWhere($query = function($query) use ($date, $time, $duration) {
                        $query->whereDate("datetime_start", "<", $date)
                            ->whereDate("datetime_end", ">", $date);
                    });
            })
            ->get();
        $startTime = $date . " " . $time;
        $untilTime = date('H:i', strtotime($time) + $duration * 60);
        Log::info("Found " . $dateExceptions->count() . " relevant date exceptions");
        foreach ($dateExceptions as $dateException) {
            $continue = true;
            // is the DE a wholeday event or between $startTime and $untilTime?
            $isWholeDay = str_contains($dateException->datetime_start, "00:00:00") && str_contains($dateException->datetime_end, "23:59:59");
            if (!$isWholeDay) {
                // Check if the DE is between $startTime and $untilTime or within the range.
                // If so, it is still relevant, so no break
                if (($dateException->datetime_start <= $startTime && $dateException->datetime_end >= $startTime) ||
                    ($dateException->datetime_start <= $untilTime && $dateException->datetime_end >= $startTime)) {
                    $continue = false;
                }
                // else: continue stays true
            } else {
                // whole-day event, still relevant, no break
                $continue = false;
            }
            if ($continue) {
                continue;
            }

            // same location?
            if ($dateException->location_id == $locationId) {
                $blockingDEs[] = $dateException->reason;
            } else {
                // check if they are whole school or tutor specific
                $wholeSchool = $dateException->tutors->count() == 0;
                if ($wholeSchool) {
                    $blockingDEs[] = $dateException->reason;
                } else {
                    // tutor specific event
                    $tutors = $dateException->tutors;
                    // is tutor_id one of the involved tutors?
                    foreach ($tutors as $tutor) {
                        if ($tutor->id == $tutorId) {
                            $blockingDEs[] = $dateException->reason;
                        }
                    }
                }
            }
        }
        Log::info("found " . count($blockingDEs) . " possibly blocking events due to date exceptions");
        return $blockingDEs;
    }
}
