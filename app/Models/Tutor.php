<?php

namespace App\Models;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\Mydat2Ndat;

class Tu<PERSON> extends User {

    protected $table = "users";


    /**
     * Set standard filtering on queries:
     * makes sure only tutors belonging to the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query
            ->leftJoin("role_user", "role_user.user_id", "=", "users.id")
            ->where([
                ['role_id', "=", config('app.TUTORROLEID')],
                ['users.domain_id', "=", Auth::user()->domain->id]
            ]);
        return $query;
    }

    public function dateexceptions() {
        return $this->belongsToMany(DateException::class, "dateexception_tutor", 'user_id', 'date_exception_id')->withPivot(["confirmed", "mandatory"]);
    }
    /**
     * Courses the tutor is qualified to teach
     */
    public function coursegroups() {
        return $this->belongsToMany(Coursegroup::class)->withPivot("age_group");
    }

    public function scheduleproposals() {
        return $this->hasMany(Scheduleproposal::class);
    }

    /**
     * List all students and studentgroups that this tutor teaches
     * It's your pupil as soon as you have an event for this pupil
     * but futur events
     * @return array
     */
    public function getMyStudentsAttribute() {
        $studentIdArray = Event::select("s.id")
            ->leftJoin("timetables as tt", 'timetable_id', '=', 'tt.id')
            ->leftJoin("course_student as cs", "cs.id", "=", "tt.course_student_id")
            ->leftJoin("students as s", "cs.student_id", "=", "s.id")
            ->where('tutor_id', '=', $this->id)
            ->whereDate('datetime', '>=', Carbon::now())
            ->distinct()
            ->get();
        // for some reason i get a complex array returned
        // that will break the next statement. cleanup first
        $studentIds = [];
        foreach ($studentIdArray as $studentIdRecord) {
            $studentIds[] = $studentIdRecord->id;
        }
        Log::info('getting students for tutor');
        $students = Student::with('courses')
            ->orderBy('lastname')
            ->whereIn('id', $studentIds)
            ->get();
        Log::debug("# students found: " . $students->count());
        $studentGroups = Studentgroup::with('courses')
            ->orderBy('lastname')
            ->whereIn('id', $studentIds)
            ->get();
        Log::debug("# studentgroups found: " . $studentGroups->count());
        $allLearners = $students->merge($studentGroups)->sortBy('lastname');
        Log::debug("total # learners: " . $allLearners->count());

        // get student data, sort resulted collection, add learner type, add participants if its a group
        foreach ($allLearners as $index => $student) {
            if ($student->firstname === '-' && $student->date_of_birth === '1800-01-01') {
                $allLearners[$index]["type"] = "studentgroup";
                // get id's of participating students
                $studentsInGroup = $student->students;
                $studentsInGroupIds = [];
                foreach ($studentsInGroup as $studentObj) {
                    $studentsInGroupIds[] = $studentObj->id;
                }
                $allLearners[$index]["participants"] = $studentsInGroupIds;
            } else {
                $allLearners[$index]["type"] = "individual";
                $allLearners[$index]["participants"] = [];
            }
        }
        return $allLearners;
    }

    /**
     * this includes 'concierge'
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function getActiveTutors()
    {
        return User::query()
            ->leftJoin("role_user", "role_user.user_id", "=", "users.id")
            ->where([
                ['role_id', "=", config('app.TUTORROLEID')],
                ['users.domain_id', "=", Auth::user()->domain->id]
            ])->where(function ($query){
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>', Carbon::now());
            })->get();
    }

    /**
     * Check if the tutor is available at the given date and time according to their schedule
     *
     * @param string $date The date in 'YYYY-MM-DD' format
     * @param string $time The start time in 'HH:MM' format
     * @param int $duration The duration in minutes
     * @return bool Returns true if the tutor is available at the given date and time, false otherwise
     */
    public function isAvailableAccodingToTutorSchedule($date, $time, $duration)
    {
        $available = false;
        $untilTime = date('H:i', strtotime($time) + $duration * 60);
        Log::info("checking availability for tutor $this->id on $date from $time until $untilTime according to working schedule");
        // get the daynumber of the date in which 1=monday, 6=saturday, 7=sunday
        $dayNumber = date('N', strtotime($date));
        $availability = Availability::where([
            ['tutor_id', '=', $this->id],
            ['day_number', '=', $dayNumber],
            ['from_time', '<=', $time],
            ['to_time', '>=', $untilTime]
        ])->first();
        if ($availability) {
            $available = true;
        }
        return $available;
    }

    /**
     * Check if an event is blocking on a given date and time during a given duration
     * So it checks if there is any overlap between the event and the given date and time and duration
     * @param int $tutorId The tutor's ID
     * @param string $date The date in 'YYYY-MM-DD' format
     * @param string $time The start time in 'HH:MM' format
     * @param int $duration The duration in number of minutes
     * @param int $skipEventId The event to be skipped (e.g. because it is itself)
     * @return array
     * @throws \Exception
     */
    public static function isOccupiedOn(int $tutorId, string $date, string $time, int $duration, int $skipEventId = 0)
    {
        Log::info("checking if tutor $tutorId is occupied on $date from $time for $duration minutes");
        $blockingEvents = [];
        $untilTime = date('H:i', strtotime($time) + $duration * 60);
        // Select only events of a specific tutor on a specific day
        $events = Event::where('tutor_id', $tutorId)
            ->where('id', '<>', $skipEventId)
            ->whereDate('datetime', '=', $date)
            ->get();

        $requestedStart = new \DateTime($date . " " . $time);
        $requestedEnd = new \DateTime($date . " " . $untilTime);

        $blockingEventObjects = $events->filter(function($event) use ($requestedStart, $requestedEnd) {
            $eventStart = new \DateTime($event->datetime);
            $eventEnd = clone $eventStart;
            $duration = $event->timespan; // can e.g. be '10 minutes' or '10 minuten'
            $duration = str_replace(" minutes", '',
                        str_replace(" minuten", '', $duration));
            $eventEnd->add(new \DateInterval('PT' . $duration . 'M'));
            return ($eventStart > $requestedStart && $eventStart < $requestedEnd) ||
                ($eventEnd > $requestedStart && $eventEnd < $requestedEnd) ||
                ($eventStart < $requestedStart && $eventEnd > $requestedEnd);
        });

        if (count($blockingEventObjects) > 0) {
            // list the blocking event's names (field=title)
            foreach ($blockingEventObjects as $fev) {
                $blockingEvents[] = $fev->course->name . " " . $fev->student->name . " " . Mydat2Ndat::getNdat($fev->datetime);
            }
        }
        Log::info("found " . count($blockingEvents) . " possibly blocking events due to tutor occupation");
        return $blockingEvents;
     }

}
