<?php
/**
 * note: a registration is implemented by means of the table course_student
 */
namespace App\Models;

use App\Http\Resources\StudentCourseResponse;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Registration extends Model {
    use hasFactory;

	protected $table = "course_student";
	protected $fillable = [
		"id", "student_id", "course_id", "start_date", "end_date",
		"signed", "status", "sign_code", "sign_request_send", "signed_at",
		"signed_user_agent", "planninggroup", "created_at", "update_at"
	];

	/**
	 * Get student accociated with this registration
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function student() {
	    $s = DB::table('students')->where('id', '=', $this->student_id)->first();
        if(!empty($s)) {
            // if its a studentgroup, return the studentgroup
            if (($s->firstname === '-') && ($s->date_of_birth === '1800-01-01')) {
                return $this->belongsTo(Studentgroup::class);
            }
        }
        return $this->belongsTo(Student::class);
	}

	/**
	 * Get course accociated with this registration
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function course()
    {
		return $this->belongsTo(Course::class);
	}

	/**
	 * Get related timetables (for all registrations)
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function timetables()
    {
		return $this->hasMany(Timetable::class, 'course_student_id','id');
	}

	/**
	 * Get checklists accociated with this registration
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function checklists()
    {
		return $this->hasMany(Checklist::class, 'registration_id', 'id');
	}

    /**
     * Registration may belong to a planninggroup
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function studentgroup()
    {
        return $this->belongsTo(Studentgroup::class);
	}

    public function scheduleproposals()
    {
        return $this->hasMany(Scheduleproposal::class);
    }

    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    public function events()
    {
        return $this->hasManyThrough(
            Event::class,
            Timetable::class,
            'course_student_id', // Foreign key on timetables table...
            'timetable_id', // Foreign key on posts table...
            'id', // Local key on countries table...
            'id' // Local key on users table...
        );
    }

    /**
     * Determine the full name description of the registration as a virtual attribute
     * @return string
     */
    public function getFullnameAttribute() {
        $courseName = $this->course->name;
        $recurrenceOptionDescription = (isset($this->course->recurrenceoption)
            ? ": " . $this->course->recurrenceoption->description
            : ''
        );
        return $courseName . $recurrenceOptionDescription;
    }

	/**
	 * Check if there is a timetable for every schoolyear for this registration
	 * if not: create them
	 * If new timetables were created, a reinit maybe needed on the calling side of this method
	 * @return boolean
	 */
	public function checkTimetables() {
		$reinitNeeded = false;
		$schoolyears = Schoolyear::all();
		foreach ($schoolyears as $schoolyear) {
			$schoolyearId = $schoolyear->id;
			$timetable = Timetable::where(['course_student_id' => $this->id, 'schoolyear_id' => $schoolyearId])->first();
			if (!isset($timetable->id)) {
				$reinitNeeded = true;
				// create a timetable for this registration and schoolyear
				$tt = new Timetable();
				$tt->course_student_id = $this->id;
				$tt->schoolyear_id = $schoolyearId;
				$tt->save();
			}
		}
		return $reinitNeeded;
	}

	/**
	 * Find all registrations that have a checklist.
	 * Find out whether the checklist has open items
     * @param boolean onlyIncomplete
	 * @return mixed
	 */
	public static function getRegistrationWithChecklists($onlyIncomplete = false) {
        // DB::enableQueryLog();
		$registrations = DB::table('course_student')
			->select(
				'course_student.id',
				'course_student.student_id',
				'course_student.course_id',
				'course_student.signed',
				'course_student.status',
				'course_student.start_date',
				'course_student.end_date',
				'students.name',
				'students.city',
				'courses.name as course',
				'courses.price_ex_tax as courseprice',
				'courses.is_trial_course',
				'checklists.name as checklistname',
				'checklists.id as checklistid',
				'recurrenceoptions.description as recoption')
			->leftJoin('students', 'course_student.student_id', '=', 'students.id')
			->leftJoin('courses', 'course_student.course_id', '=', 'courses.id')
			->leftJoin('checklists', 'course_student.id', '=', 'checklists.registration_id')
			->leftJoin('recurrenceoptions', 'courses.recurrenceoption_id', '=', 'recurrenceoptions.id')
			->where(DB::raw('(course_student.end_date IS NOT NULL OR course_student.end_date > NOW())'))
			->whereNotNull('checklists.name')
            ->where("students.domain_id", "=", Auth::user()->domain->id)
            ->where("courses.domain_id", "=", Auth::user()->domain->id)
			->get();
        /*
        $queryLog = DB::getQueryLog();
        $last_query = end($queryLog);
        Log::debug($last_query);
        DB::disableQueryLog();
        */
		// find out per registration if the checklist is complete
        Log::info("count active regs with a checklists: " . count($registrations));
		foreach ($registrations as $index => $registration) {
			$checklist = Checklist::findOrFail($registration->checklistid);
			$registrations[$index]->complete = $checklist->isComplete();
			$registrations[$index]->incompleteItems = $checklist->getIncompleteItems();
            // remove if we only want the incomplete ones
            if ($onlyIncomplete) {
                if ($registrations[$index]->complete) {
                    unset($registrations[$index]);
                }
            }
		}
        if ($onlyIncomplete) {
            Log::info("Returning only registrations with incomplete checklists");
            Log::info("count regs to return: " . count($registrations));
        }

		return $registrations;
	}

	public function updateme() {
		DB::table('course_student')
			->where(['id'=>$this->id])
			->update([
				'start_date' 	=> $this->start_date,
				'end_date' 		=> $this->end_date,
				'signed'		=> $this->signed,
				'status'		=> $this->status
			]);
	}

	public static function destroyForStudent($student_id) {
		DB::table('course_student')
			->where('student_id', $student_id)
			->delete();
	}

	public static function getStudentsForCourse($course_id) {
		$students = DB::table('course_student')
			->where('course_id', $course_id)
			->get();
		return $students;
	}

	public static function getCoursesForStudent($student_id) {
		$courses = DB::table('course_student')
			->where('student_id', $student_id)
			->get();
		return $courses;
	}

    public function getIsActiveNowOrFutureAttribute() {
        $now = new \DateTime();
        $endDate = new \DateTime($this.end_date);
        return (($this->end_date == null) || ($endDate > $now));
	}

	/**
	 * get all students that have no active course
	 * @param string $order sortorder
	 * @return mixed
	 */
	public static function getStudentsNoCourse($order='lastname') {
		// these are student objects! this means we can use the 'email' magic function in the student model
		// which would not be possible if I use a complex query, which would yield a collection of stdObject's
        $students = Student::query()->orderBy($order)->get();
		// filter students
		$filtered = $students->filter(function ($value) {
			// initially: this student has no active course: result = true
			$result = true;
			// filter this if it's the wrong domain_id
			if ($value->domain_id !== Auth::user()->domain->id) return true;
			$courses = $value->courses;
			foreach ( $courses as $course ) {
				// however: see if i can find at least 1 active course, then: result = false
				if ( (empty($course->pivot->end_date)) || date_parse($course->pivot->end_date) > date_parse(date("Y-m-d"))) {
                    $result = false;
                }
			}
			// return the closure: apply filter or not
			return $result;
		});
		return $filtered;
	}

	/**
	 * find all student accounts
	 * @return \Illuminate\Database\Eloquent\Collection|static[]
	 */
	public static function getAllStudents() {
		// all students
		$allStudents = Student::all();
		return $allStudents;
	}

	/**
	 * find all students that have at least one active course registration
	 *
	 * @return Student[]|\Illuminate\Database\Eloquent\Collection
	 */
	public static function getStudentsWithAtLeastOneCourse() {
		// these are student objects! this means we can use the 'email' magic function in the student model
		// which would not be possible if I use a complex query, which would yield a collection of stdObject's
		$students = Student::all();
		// filter students
		$filtered = $students->filter(function ($value) {
			// initially: this student has no active course
			$result = false;
			$courses = $value->courses;
			foreach ( $courses as $course ) {
				// However, if we find at least one active course, this student should be in the result
				if ( (empty($course->pivot->end_date)) || date_parse($course->pivot->end_date) > date_parse(date("Y-m-d"))) $result = true;
			}
			// return the closure: apply filter or not
			return $result;
		});
		return $filtered;
	}

	/**
	 * get all students with an active course
     * @notice this function is also available in the Student model.
     * @todo find out if we can merge these functions
	 * @return mixed
	 */
	public static function getStudentsActive() {
		$students = DB::table('course_student')
			->select('student_id as id',
				'students.firstname',
				'students.lastname',
				'students.preposition',
				'students.name',
				'students.city',
				'students.lastname')
			->leftJoin('students', 'course_student.student_id', '=', 'students.id')
			->distinct()
            ->where([
                ["students.domain_id", "=", Auth::user()->domain->id],
                ["students.firstname", "<>", "-"],
                ["students.date_of_birth", "<>", "1800-01-01"]
            ])
            ->where(function($query) {
                $query
                    ->whereNull('end_date')
                    ->orWhere('end_date', '>=',DB::raw('NOW()'));
            })
			->orderBy('students.lastname')
			->get();

		return $students;
	}

	/**
     * Get all registrations for this student that are 'active' or will be in future
     */
    public static function getActiveRegistrationsForStudent($student_id = 0) {
        Log::info("Getting active course registrations for student $student_id");
        // dont lookup domain id: this function is used by students who are not logged in.
        // the only way to lookup the domain is by student_id, making it redundant as a security measure
        $registrations = Registration::select(['course_student.*', 'course_student.id as regid'])
            ->leftJoin('students', 'course_student.student_id', '=', 'students.id')
            ->where('student_id', '=', $student_id)
            ->where(function($query) {
                $query
                    ->whereNull('end_date')
                    ->orWhere('end_date', '>=',DB::raw('NOW()'));
            })->with("studentgroup")->get();

        // toggle the recurrenceoption to include it in the response
        foreach ($registrations as $registration) {
            $a = $registration->course->recurrenceoption;
        }
        return $registrations;
	}

	/**
     * Get all registration that are active now plus in future (not started yet)
     */
    public static function getActiveNowAndFuture() {
        return Registration::leftJoin('students', 'course_student.student_id', '=', 'students.id')
            ->where("students.domain_id", "=", Auth::user()->domain->id)
            ->where(function($query) {
                $query
                    ->whereNull('end_date')
                    ->orWhere('end_date', '>=',DB::raw('NOW()'));
            })
            ->with(array('course' => function($q) {
                $q->where('is_trial_course', '0');
            }))
            ->with(['course','student'])
            ->get();
	}

	/**
	 * Find the registration by means of its signcode
	 * @param $signCode
	 *
	 * @return \Illuminate\Database\Eloquent\Collection|Model|null
	 */
	public static function getRegistrationBySignCode( $signCode ) {
		$reg = Registration::select('id')->where(['sign_code'=>$signCode])->first();
		if(!empty($reg->id)) {
			return Registration::findOrFail($reg->id);
		} else {
			return null;
		}
	}

    /**
     * @param string $schoolyear
     * @return array|null
     * @throws \Exception
     */
    public static function getRegistrationForYear($schoolyear='currentorfuture' ) {
		$result = null;
		if (($schoolyear === 'currentorfuture') || (intval($schoolyear) == 0)) {
			$theSchoolyear = Schoolyear::getCurrentOrFuture();
		} else {
			// find the requested schoolyear
			$theSchoolyear = Schoolyear::getByStartyear($schoolyear);
		}

		if (!empty($theSchoolyear)) {
			$registrations = Registration::select(
				'course_student.*',
				'students.id as studentid',
				'students.name as studentname', 
				'courses.id as courseid',
				'courses.name as coursename',
				DB::raw('count(events.id) as nrOfEvents')
			)
			->leftJoin('timetables', 'course_student.id', '=', 'timetables.course_student_id')
			->leftJoin('schoolyears', 'timetables.schoolyear_id', '=', 'schoolyears.id')
			->leftJoin('events', 'events.timetable_id', '=', 'timetables.id')
			->leftJoin('students', 'course_student.student_id', '=', 'students.id')
			->leftJoin('courses', 'course_student.course_id', '=', 'courses.id')
			->with('course.recurrenceoption')
			->where(function($query) {
				$query->whereNull('course_student.end_date')
					  ->orWhere('course_student.end_date', '>', DB::raw('NOW()'));
			})
			->where('courses.domain_id', Auth::user()->domain->id)
			->where('students.domain_id', Auth::user()->domain->id)
			->groupBy('course_student.id')
			->orderBy('students.lastname')
			->get();

			$result = [
				"registrations" => $registrations,
				"schoolyearlabel" => $theSchoolyear->label
			];
		}

		return $result;
	}

    /**
     * Count the number of events (appointments) planned for
     * this registration in a requested schoolyear
     * @param bool $onlyFuture - only count future events
     * @param string $schoolyear - ['currentorfuture': use the current or next (in case of not found) schoolyear] -OR-
     *                             [numberstring (int)" the schoolyear that has this string as start year] -OR-
     *                             ['all'] all events ever
     *
     * @return int
     * @throws \Exception
     */
	public function getNrOfAppointments( $onlyFuture=false, $schoolyear='currentorfuture' ) {
		$numberOfEvents = 0;
		// determine which school year == which timetable

        // if $schoolyear is an object of the type schoolyear, use it
        if (is_object($schoolyear) && get_class($schoolyear) === 'App\Models\Schoolyear') {
            $theSchoolyear = $schoolyear;
        } elseif ( ($schoolyear === 'currentorfuture') || (intval($schoolyear) == 0)) {
            $theSchoolyear = Schoolyear::getCurrentOrFuture();
        } else {
            // find the requested school year
            $theSchoolyear = Schoolyear::getByStartyear($schoolyear);
        }

        // for all timetables
        if ($schoolyear === 'all') {
            Log::info("getting all events (all time rather than current schoolyear)");
            // get all timetables for this registration
            $timetables = $this->timetables;
            foreach ($timetables as $timetable) {
                $numberOfEvents += Event::where('timetable_id', '=', $timetable->id)->count();
            }
            Log::info("Found $numberOfEvents in all timetables for registration $this->id");
        }
		// found school year, look for timetable
		else if (!empty($theSchoolyear)) {
			// find the timetable
			$timeTable = Timetable::where([
				["schoolyear_id", "=", $theSchoolyear->id],
				["course_student_id", "=", $this->id]
			])->first();
			if ($timeTable) {
				// count the events on this timetable
				if ( $onlyFuture ) {
					$now = new \DateTime();
					$numberOfEvents = Event::where([
						['datetime', '>=', $now->format('Y-m-d')],
						['timetable_id', '=', $timeTable->id]
					])->count();
				} else {
					$numberOfEvents = Event::where('timetable_id', '=', $timeTable->id)->count();
				}
			}
		} else {
			// school year not found
			Log::error("Requested school year not found");
		}

		return $numberOfEvents;
	}

    /**
     * Get last max 5 events to see standard day, time, tutor and location
     * @throws \Exception
     */
    public function getStandardEventDataAttribute() {
        $retArr = ["tutor" => 'unknown', "location" => 'unknown', "day" => 'unknown', "time" => 'unknown'];
        $theSchoolyear = Schoolyear::getLastSchoolyearForPlanning();
        if(!empty($theSchoolyear)) {
            // find the timetable, should only return 1: the one for this registration
            $timeTable = Timetable::where([
                ["schoolyear_id", "=", $theSchoolyear->id],
                ["course_student_id", "=", $this->id]
            ])->first();

            if(empty($timeTable)) {
                Log::error("couldn't find timetable for registration $this->id.");
            } else {
                $events = Event::where('timetable_id', '=', $timeTable->id)
                    ->orderBy("datetime", "desc")
                    ->limit(5)
                    ->get();

                // if we find events
                $tutors = $locations = $days = $times = [];
                if (!empty($events)) {
                    // common day / time / location / tutor?
                    foreach ($events as $event) {
                        $tutors[$event->tutor_id] = !isset($tutors[$event->tutor_id]) ? 0 : $tutors[$event->tutor_id] + 1;
                        $locations[$event->location_id] = !isset($locations[$event->location_id]) ? 0 : $locations[$event->location_id] + 1;
                        // day and time
                        $dt = new \DateTime($event->datetime);
                        $day = $dt->format("w");
                        $time = $dt->format("Hi"); // no ':' to make it easier to sort
                        $days[$day] = !isset($days[$day]) ? 0 : $days[$day] + 1;
                        $times[$time] = !isset($times[$time]) ? 0 : $times[$time] + 1;
                    }

                    // get the biggest, most likely to be standard
                    $stdTutor =  getIndexOfBiggestValueInArray($tutors);
                    $stdLocation =  getIndexOfBiggestValueInArray($locations);
                    $retArr = [
                        "id"        => $this->id,
                        "tutor"     => $stdTutor === -1 ? 'unknown' : Tutor::findOrFail($stdTutor),
                        "location"  => $stdLocation === -1 ? 'unknown' : Location::findOrFail($stdLocation),
                        "day"       => getIndexOfBiggestValueInArray($days),
                        "time"      => getIndexOfBiggestValueInArray($times)
                    ];

                }
            }
        }
        return $retArr;
    }

    public function getRegIsForStudentGroupAttribute() {
        // is deze student met deze cursus deel van een student group?
        $groups = $this->student->studentgroups;
        foreach ($groups as $group) {
            $courses = $group->courses;
            foreach ($courses as $course) {
                if($course->id === $this->course_id) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Returns active registrations now plus in future (not started yet)
     * If students are in a group, then those registrations are only present as a group,
     * not the individual registrations as well. This is because we need to know what to schedule.
     * @param int $minimumRecurrence
     * @return \Illuminate\Support\Collection
     */
    public static function getCurrentlyActiveLearners($minimumRecurrence = 1)
    {
        Log::info("get currently active students, now plus future.");
        $registrations = DB::table('course_student AS cs')
            ->select('cs.id', 's.name', 's.city', 's.date_of_birth', 's.firstname',
                'ro.per_interval', 'ro.nr_of_times', 'ro.timeunit', 'ro.description AS recurrenceoption',
                'cs.course_id', 'cs.student_id', 'c.name AS coursename',
                'sp.updated_at as lastSchedulePrefChanged'
            )
            ->leftJoin('students AS s', 'cs.student_id', '=', 's.id')
            ->leftJoin('courses AS c', 'cs.course_id', '=', 'c.id')
            ->leftJoin('recurrenceoptions AS ro', 'c.recurrenceoption_id', '=', 'ro.id')
            ->leftJoin('schedule_prefs AS sp', 's.id', '=', 'sp.student_id')
            // domain
            ->where("s.domain_id", "=", Auth::user()->domain_id)
            // recurrence
            ->where(function($query) use ($minimumRecurrence) {
                $query->whereNull('ro.ends_after_nr_of_occurrences')
                    ->orWhere('ro.ends_after_nr_of_occurrences', '>=', $minimumRecurrence);
            })
            // end date
            ->where(function($query) {
                $query->whereNull('cs.end_date')
                    ->orWhere('cs.end_date', '>', DB::raw("now()"));
            })
            // studentgroup
            ->whereNotIn(DB::raw('(cs.course_id, cs.student_id)'), function($query){
                // exclude these students, they are part of a group
                // only the group will be part of the result
                $query->select(['cs_inner.course_id', 'ssg.student_id'])
                    ->from('student_studentgroup AS ssg')
                    ->leftJoin('students AS s_inner', 'ssg.studentgroup_id',  '=', 's_inner.id')
                    ->leftJoin('course_student AS cs_inner', 's_inner.id', '=', 'cs_inner.student_id');
            })
            ->orderBy('s.lastname')
            ->get();

        // remove student groups that have no active students
        foreach ($registrations as $index => $registration) {
            if ($registration->date_of_birth === '1800-01-01' && $registration->firstname === '-') {
                Log::debug("Trying to find studentgroup: $registration->student_id.");
                $sg = Studentgroup::findOrFail($registration->student_id);
                if(count($sg->students) === 0) {
                    unset($registrations[$index]);
                } else {
                    unset($registrations[$index]->lastSchedulePrefChanged);
                    $myStudents = $sg->students;
                    foreach ($myStudents as $index2 => $sg_student) {
                        // trigger date added to response
                        $myStudents[$index2]->lastSchedulePrefChanged = $sg_student->lastDatePrefsFilledIn;
                    }
                    $registrations[$index]->isAStudentgroup = true;
                    $registrations[$index]->students = $myStudents;
                }
            } else {
                $registrations[$index]->isAStudentgroup = false;
            }
        }
        return $registrations;
    }

    /**
     * Get attendance for this registration as an array of values.
     * Returns the array with '0' as values if we are between school years
     * - If teaching is in a student group, Make sure to call with the
     *   correct reg-id (the group reg-id). In this case, the student's id is also needed
     * - If it's individual teaching, call with the individual reg-id
     * See registration trait for an example of how to call this method.
     * @param int $studentId    if we are looking for a student group,
     *                          we need the student id of the participating student
     *                          that we are looking to get the attendance count for
     * @return int[]|string[]
     * @throws \Exception
     */
    public function getAttendanceCount($studentId = 0)
    {
        Log::info('Getting attendance count for registration ' . $this->id);
        $returnArray = [
            "absentNoShow" => '0',
            "absentWithNotification" => '0',
            "absentWithNotificationTooLate" => '0',
            "present" => '0',
            "unknown" => '0',
            "total" => '0'
        ];
        // GET current schoolyear
        $schoolyear = Schoolyear::getCurrentOrFuture(0, 'fail');
        if (empty($schoolyear)) {
            Log::info("No current schoolyear found, returning empty dataset");
            return $returnArray;
        }
        // get events in this schoolyear
        $events = Event::query()
            ->select('events.id')
            ->leftJoin('timetables as tt', 'tt.id', '=', 'events.timetable_id')
            ->leftJoin('schoolyears as sy', 'sy.id', '=', 'tt.schoolyear_id')
            ->leftJoin('course_student as reg', 'reg.id', '=', 'tt.course_student_id')
            ->where('reg.id', '=', $this->id)
            ->where('tt.schoolyear_id', '=', $schoolyear->id)
            ->get();

        Log::info("Found " . count($events) . " events for this registration in this schoolyear");
        // determine for every of these events in which category they belong
        // initialize on 0
        $returnArray["total"] = count($events);
        foreach ($events as $event) {
            $attendanceNote = $studentId > 0
                ? $event->attendancenotes->where('student_id', '=', $studentId)
                : $event->attendancenotes;
            if (count($attendanceNote) === 0) {
                $returnArray["unknown"] += 1; // this maybe future or not have been entered yet
            } else {
                // Always use the index 0.
                // There should be only one, but it's always an array because of how it's designed:
                // i.e. there can be more students in the same event if it is a group
                $firstAttNote = $attendanceNote[0] ?? null;
                $presence = $firstAttNote?->attendanceoption_id ?? 0;
                Log::debug($event->id . ", presence: $presence");
                switch ($presence) {
                    case config('app.PRESENT'):
                        $returnArray["present"] += 1;
                        break;
                    case config('app.ABSENT_WITH_NOTIFICATION'):
                        $returnArray["absentWithNotification"] += 1;
                        break;
                    case config('app.ABSENT_WITH_NOTIFICATION_TOO_LATE'):
                        $returnArray["absentWithNotificationTooLate"] += 1;
                        break;
                    case config('app.ABSENT_NO_SHOW'):
                        $returnArray["absentNoShow"] += 1;
                        break;
                    default:
                        break;
                }
            }
        }

        return $returnArray;
    }

}
