<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Trialrequeststatus extends Model
{
    use HasFactory;

    public function newQuery()
    {
        $query = parent::newQuery();
        $query->where([
            ['trialrequeststatuses.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    public function trialrequest()
    {
        return $this->hasMany(Trialstudent::class);
    }
}
