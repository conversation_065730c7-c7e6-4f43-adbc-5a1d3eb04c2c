<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Studentlist extends Model {

    /**
     * Set standard filtering on queries:
     * makes sure only studentlists from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['studentlists.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }


    public function students() {
        return $this->belongsToMany(Student::class);
    }

    /**
     * Check if a given student is a member of a list
     * @param $studentId
     * @return bool
     */
    public function isMemberOf($studentId)
    {
        return $this->students->find($studentId) !== null;
    }

}
