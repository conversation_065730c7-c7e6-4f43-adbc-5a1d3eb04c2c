<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Trialstudent extends Model {

    /**
     * Set standard filtering on queries:
     * makes sure only trialstudents from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['trialstudents.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

    public function course() {
		return $this->belongsTo(Course::class);
	}

    public function trialrequeststatus()
    {
        return $this->belongsTo(Trialrequeststatus::class);
    }

    public static function trialrequestsWithoutStudentAccount() {
        $openTrialStudents = self::whereNull("generated_student_id")->get();
        return $openTrialStudents;
	}

	public function hasRegistration() {
        return !empty($this->generated_registration_id);
    }

    public function hasStudentAccount() {
        return !empty($this->generated_student_id);
    }

}
