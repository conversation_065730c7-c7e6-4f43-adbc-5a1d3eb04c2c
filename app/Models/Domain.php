<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Domain extends Model {

    use HasFactory;
    protected $MAX_STUDENTS_FOR_TRIAL   = 5;
    protected $MAX_TUTORS_FOR_TRIAL     = 1;
    protected $MAX_COURSES_FOR_TRIAL    = 3;

    /**
     * add https to websiteurl if no protocol was given
     * @attribute: $this->fullwebsiteurl
     * @return mixed|string
     */
    public function getFullwebsiteurlAttribute() {
        return substr($this->website_url, 0, 4) == 'http' ?
            $this->website_url :
            "https://" . $this->website_url;
    }

    /**
     * Check if the limit for # students has been reached for a CLASS trial account
     * @return bool
     */
    public function getTrialLimitReachedStudentAttribute() {
        if(Auth::user()->domain->status === 'trial') {
            $nrOfStudents = Student::count();
            Log::info("Checking trial status max value for students. Fount $nrOfStudents student(s).");
            return ($nrOfStudents >= $this->MAX_STUDENTS_FOR_TRIAL);
        } else {
            Log::info("this is a PRO account, no limitations");
            return false;
        }
    }

    /**
     * Check if the limit for # tutors has been reached for a CLASS trial account
     * @return bool
     */
    public function getTrialLimitReachedTutorAttribute() {
        if(Auth::user()->domain->status === 'trial') {
            $nrOfTutors = Tutor::count();
            Log::info("Checking trial status max value for tutors. Found $nrOfTutors tutor(s).");
            return ($nrOfTutors >= $this->MAX_TUTORS_FOR_TRIAL);
        } else {
            Log::info("this is a PRO account, no limitations");
            return false;
        }
    }

    /**
     * Check if the limit for # courses has been reached for a CLASS trial account
     * @return bool
     */
    public function getTrialLimitReachedCourseAttribute() {
        if(Auth::user()->domain->status === 'trial') {
            $nrOfCourses = Course::count();
            Log::info("Checking trial status max value for courses. Found $nrOfCourses course(s).");
            return ($nrOfCourses >= $this->MAX_COURSES_FOR_TRIAL);
        } else {
            Log::info("this is a PRO account, no limitations");
            return false;
        }
    }

    /**
     * Check if this domain has a completed setup
     */
    public function setupComplete() {
        $result = [];

        // 2FA active?
        $security = Auth::user()->loginsecurity;

        // check coursegroups
        $courseGroups = Coursegroup::all();

        // check schoolyears
        $schoolyears = Schoolyear::all();

        // Check locations
        $locations = Location::all();

        // Tutors
        $tutors = User::leftJoin("role_user as ru", "users.id", "=", "ru.user_id")
            ->where([
                ['users.domain_id', "=", Auth::user()->domain->id],
                ['ru.role_id', '=', config('app.TUTORROLEID')]
            ])->get();
        // Students
        $students = Student::all();

        // assemble result
        if (empty($security) || !$security->google2fa_enable) {
            Log::debug("2FA missing");
            $result['trialoption'] = ['message' => trans('generic.twofactorismissing'), "link" => '/2fa'];
        }
        if (count($courseGroups) === 0) {
            Log::debug("coursegroups missing");
            $result['coursegroups'] = ['message' => trans('generic.coursegroupsmissing'), "link" => '/coursegroups/create'];
        }
        if (count($schoolyears) === 0) {
            Log::debug("schoolyears missing");
            $result['schoolyears'] = ['message' => trans('generic.schoolyearmissing'), "link" => '/schoolyears/create'];
        }
        if (count($locations) === 0) {
            Log::debug("locations missing");
            $result['locations'] = ['message' => trans('generic.locationsmissing'), "link" => '/locations/create'];
        }
        if (count($tutors) === 0) {
            Log::debug("tutors missing");
            $result['tutors'] = ['message' => trans('generic.tutorsmissing'), "link" => '/tutors/create'];
        }
        if (count($students) === 0) {
            Log::debug("students missing");
            $result['students'] = ['message' => trans('generic.studentsmissing'), "link" => '/students/create'];
        }

        return $result;
    }
}
