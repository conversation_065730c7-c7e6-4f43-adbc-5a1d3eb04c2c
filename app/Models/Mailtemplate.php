<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Mailtemplate extends Model {
    /**
     * Set standard filtering on queries:
     * makes sure only mailtemplates from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['mailtemplates.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

}
