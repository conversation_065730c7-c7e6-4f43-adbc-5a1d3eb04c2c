<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Scheduleproposal extends Model {
    protected $table = "scheduleproposals";

    /**
     * Set standard filtering on queries:
     * makes sure only scheduleproposals from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if(!Auth::guest()) {
            $query->where([
                ['scheduleproposals.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

    public function tutor() {
        return $this->belongsTo(Tutor::class);
    }

    public function location() {
        return $this->belongsTo(Location::class);
    }

    public function registration() {
        return $this->belongsTo(Registration::class);
    }


}
