<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Studentcontact extends Model {

	protected $fillable = [
	    'contacttype',
        'label',
        'value',
        'apply_for_finance',
        'apply_for_planning',
        'apply_for_promotions',
        'use_salutation'
    ];

	/**
	 * get associated student for this contact
	 * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
	 */
	public function student() {
		return $this->belongsTo( Student::class );
	}

}
