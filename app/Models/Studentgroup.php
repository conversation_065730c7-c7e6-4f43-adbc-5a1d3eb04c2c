<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Studentgroup extends Model {

    use HasFactory;
    protected $table = "students";


    /**
     * Set standard filtering on queries:
     * provides a standard separation between students and studentgroups
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if(!Auth::guest()) {
            $query->where([
                ['firstname', '=', '-'],
                ['date_of_birth', '=', '1800-01-01'],
                ['domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

    /**
     * check if any students need to be unsubscribed from a group
     * because they nog longer have an active subscription
     * todo needs a re-write. disable for now.
     */
    private function cleanupStudentSubscriptions() {
        return;
        Log::info("cleaning up studentgroups");
        // which course is attached to the studentgroup
        $course = $this->courses[0];

        // which students are attached
        $allStudents = DB::table("student_studentgroup")
            ->select("student_id")
            ->where("studentgroup_id", "=", $this->id)
            ->get();

        $serial = "";
        foreach ($allStudents as $student) {
            $serial .= $student->student_id . ",";
        }
        // remove last komma
        $serial = substr($serial, 0, -1);

        // no students, no party
        if(strlen($serial) < 2) return;

        // any students that shouldn't be there?
        $q = "select id
            from students
            where id in($serial)
                and id not in (
                    select student_id
                    from course_student
                    where student_id in ($serial)
                    and course_id = $course->id
                    and (end_date is NULL or end_date > now())
            )";
        $studentsToDetach = DB::select($q);
        Log::debug( count($studentsToDetach) . " found to remove");
        // detach if the students does not follow this course currently
        $s = [];
        foreach ($studentsToDetach as $studentToDetach) {
            $s[] = $studentToDetach->id;
        }
        log:info("detaching studentid's " . implode(",", $s) . " from studentgroup $this->lastname");

        DB::table("student_studentgroup")
            ->whereIn("student_id", $s)
            ->where("studentgroup_id", "=", $this->id)
            ->delete();
    }

    /**
     * Gets students associated with a student group.
     * This only returns students that have an active subscription!
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     * @throws \Exception
     */
    public function students() {
        $this->cleanupStudentSubscriptions();
        return $this->belongsToMany(Student::class)
            ->with("courses")
            ->withPivot(["as_trial_student", "start_date", "end_date"])
            ->where("start_date", "<=", Carbon::now())
            ->where(function($query) {
                $query->where("end_date", ">=", Carbon::now())
                    ->orWhereNull("end_date");
            });
    }

    public function getActiveStudentIds() {
        $studentIds = [];
        foreach ($this->students as $student) {
            if (!in_array($student->id, $studentIds)) {
                $studentIds[] = $student->id;
            }
        }
        return $studentIds;
    }

    /**
     * get courses associated with a student group. (that's only one course for a student group)
     * needs specs for the relation to make sure it uses course_student
     * instead of "course_studentgroup" which doesn't exist
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function courses() {
        return $this->belongsToMany( Course::class, 'course_student', 'student_id')
            ->withPivot( 'id', 'start_date', 'end_date', 'signed', 'sign_request_send', 'checklist_id', 'status', 'please_keep_scheduled_time')
            ->withTimestamps();
    }

    /**
     * get associated logentries
     * needs specs for the relation to make sure it uses student_id
     * instead of "studentgroup_id" which doesn't exist
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function logentries() {
        return $this->hasMany( Logentry::class, 'student_id' );
    }

    public function getCurrentCoursesAttribute() {
        return $this->courses->filter(function($course) {
            return $course->pivot->end_date == null ||
                strtotime($course->pivot->end_date) > time();
        })->map(function($item) {
            return $item->name;
        })->all();
    }

    public function getCurrentCoursesDetailsAttribute() {
        return $this->courses->filter(function($course) {
            return $course->pivot->end_date == null ||
                strtotime($course->pivot->end_date) > time();
        })->map(function($item) {
            return ["name" => $item->name, "please_keep_scheduled_time" => $item->pivot->please_keep_scheduled_time];
        })->all();
    }

    public function libraries() {
        return $this->belongsToMany(Library::class, "library_student", "student_id", "library_id");
    }

    /**
     * quick way to find out if the student is actually a studentgroup
     * @return bool
     */
    public function getIsAStudentgroupAttribute() {
        return (($this->date_of_birth === "1800-01-01") && ($this->firstname === "-"));
    }


    /**
     * retrieve all emailadresses with the apply_for_planning marker        return $this->belongsToMany(Studentgroup::class);
     * usage: $student->planning_email
     * @return bool|string
     */
    public function getPlanningEmailAttribute() {
        $allEmails = [];
        $students = $this->students;
        foreach ($students as $student):
            $email = $student->planningEmail;
            $allEmails = array_merge($allEmails, explode(",", $email));
        endforeach;
        return implode(",", $allEmails);
    }

    /**
     * retrieve all emailadresses with the apply_for_finance marker
     * usage: $student->financial_email
     * @return bool|string
     */
    public function getFinancialEmailAttribute() {
        $emailAddress = '';
        foreach ( $this->contacts as $contact ) {
            if ( ( $contact->contacttype === "email" ) && ( $contact->apply_for_finance == 1) ) {
                $emailAddress .= $contact->value . ',';
            }
        }
        // remove last ,
        return $emailAddress === '' ? $this->email : substr($emailAddress, 0, -1);
    }

    /**
     * retrieve all emailadresses with the apply_for_promotions marker
     * usage: $student->promotions_email
     * @return bool|string
     */
    public function getPromotionsEmailAttribute() {
        $emailAddress = '';
        foreach ( $this->contacts as $contact ) {
            if ( ( $contact->contacttype === "email" ) && ( $contact->apply_for_promotions == 1) ) {
                $emailAddress .= $contact->value . ',';
            }
        }
        // remove last ,
        return $emailAddress === '' ? $this->email : substr($emailAddress, 0, -1);
    }

    public function getActiveRegistrations() {
        return Registration::getActiveRegistrationsForStudent($this->id);
    }

    /**
     * Check if there is an incomplete checklist at all
     * only checks active courses (also future active courses)
     */
    public function getHasIncompleteChecklistAttribute() {
        $result = false;
        $regs = $this->getActiveRegistrations();
        foreach ($regs as $reg) {
            // get checklists for this registration
            foreach ($reg->checklists as $checklist) {
                if(!$checklist->isComplete()) {
                    $result = true;
                    break;
                }
            }
            if($result) break;
        }
        return $result;
    }

    /**
     * Get students that were part of a student group at a particular date
     * @return array
     */
    public static function getStudentsInGroupAtDT($groupId, $dt) {
        $q = "select  s.id, s.name, ssg.start_date, ssg.end_date
            from student_studentgroup ssg
            left join students s on s.id = ssg.student_id        
            where studentgroup_id = $groupId
            and start_date <= '$dt'
            and (end_date >= '$dt' or end_date is null)";
        return DB::select($q);
    }
}
