<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class DefaultChecklist extends Model {
    /**
     * Set standard filtering on queries:
     * makes sure only checklists from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['default_checklists.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }

}
