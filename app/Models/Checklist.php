<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Checklist extends Model {


	public function registration(  ) {
		return $this->belongsTo(Registration::class, 'registration_id', 'id');
	}

	/**
	 * Find out if this checklist is completely checked off
	 * @return bool
	 */
	public function isComplete() {
		$completed = true;
		// loop through checklist fields
		for ($count=1; $count < 13; $count++) {
			$field = "item" . $count;
			$fieldCompleted = $field . "_checked";
			if (($this->$field != '') && ($this->$fieldCompleted != '1')) {
				$completed = false;
			}
		}
		return $completed;
	}

    /**
     * Get all incomplete items as an array of strings.
     * @return array
     */
    public function getIncompleteItems()
    {
        $items = array();
        for ($count=1; $count < 13; $count++) {
            $field = "item" . $count;
            $fieldCompleted = $field . "_checked";
            if (($this->$field != '') && ($this->$fieldCompleted != '1')) {
                $items[] = $this->$field;
            }
        }
        return $items;
	}

	/**
	 * Create a checklist based on a default checklist (template) and couple it onto registration
	 * @param $defaultChecklistId
	 * @return Checklist the checklist id
	 */
	public static function createNewChecklist( $defaultChecklistId, $registrationId ) {
		// copy the fields in the defaultchecklist to a new checklist
		$defChecklist = DefaultChecklist::findOrFail($defaultChecklistId);
		$checklist = new Checklist();
		$checklist->name = $defChecklist->name;
		$checklist->registration_id = $registrationId;
		for ($i =1; $i<13; $i++) {
			$checklist['item'.$i] = $defChecklist["item".$i];
		}
		$checklist->save();
		return $checklist;
	}

}
