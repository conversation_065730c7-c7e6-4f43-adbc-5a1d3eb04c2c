<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Attendanceoption extends Model
{
    use HasFactory;
    protected $table = "attendanceoptions";

    /**
     * Set standard filtering on queries:
     * makes sure only attendance options from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if (!Auth::guest()) {
            $query->where([
                ['attendanceoptions.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

    /**
     * 'attendancenotes' is a three-way relation between a student, an event and an attendance option
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function attendancenotes()
    {
        return $this->hasMany(Attendancenote::class);
    }

}
