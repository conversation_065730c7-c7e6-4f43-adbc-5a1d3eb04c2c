<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Coursegroup extends Model {

    /**
     * Set standard filtering on queries:
     * makes sure only coursegroup from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if (!Auth::guest()) {
            $query->where([
                ['coursegroups.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

    /**
	 * 1:n relation
	 * @return \Illuminate\Database\Eloquent\Relations\HasMany
	 */
	public function courses() {
		return $this->hasMany(Course::class);
	}

    public function libraries() {
        return $this->belongsToMany(Library::class, "library_coursegroup", "coursegroup_id", "library_id");
    }

    /**
     * Active tutors that are qualified to teach courses in this course group
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function tutors() {
        return $this
            ->belongsToMany(Tutor::class)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere("end_date", ">", DB::raw("NOW()"));
            })
            ->withPivot(["age_group_child", "age_group_adolescent", "age_group_adult"]);
    }

    /**
     * get coursegroups the currently have courses attached
     * @param int $domainId if we have a logged in user, we use that users domain id
     *                      if not we use the parameter (wordpress request)
     * @return mixed
     */
    public static function getActiveCoursegroups($domainId = 0)
    {
        $domainIdForLookup = Auth::guest() ? $domainId : Auth::user()->domain_id;
        Log::info("Getting active coursegroups for domain $domainIdForLookup");

        // get all course groups that have courses attached that are not archived
        $courseGroups = Coursegroup::with([
            'courses',
            'courses.recurrenceoption'
        ])->where([
            ['coursegroups.domain_id', '=', $domainIdForLookup]
        ])->get();

        foreach ($courseGroups as $cgIndex => $courseGroup) {
            // check if there are courses in this coursegroup that are not archived
            // if not, remove the course group from the list
            foreach ($courseGroup->courses as $cIndex => $course) {
                // remove it from the list if it is archived
                if ($course->archive) {
                    unset($courseGroups[$cgIndex]->courses[$cIndex]);
                }
            }
            // if the course group has no courses left, remove it from the list
            if (count($courseGroups[$cgIndex]->courses) == 0) {
                unset($courseGroups[$cgIndex]);
            }
        }
        return $courseGroups;
    }

    public function getActiveStudentIds() {
        $studentIds = [];
        foreach ($this->courses as $course) {
            foreach ($course->students as $student) {
                if (!in_array($student->id, $studentIds)) {
                    $studentIds[] = $student->id;
                }
            }
        }
        return $studentIds;
    }
}
