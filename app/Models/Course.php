<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Course extends Model {

    use HasFactory;

    /**
     * Set standard filtering on queries:
     * makes sure only courses from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        // some interfaces don't need logging in, but they
        // do need a course associated with a registration
        if(!(Auth::guest())) {
            $query->where([
                ['courses.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }

    /**
	 * get students associated with a course
	 */
	public function students() {
		return $this->belongsToMany(Student::class)
            ->withPivot('id', 'start_date', 'end_date', 'signed', 'sign_request_send', 'checklist_id','status','please_keep_scheduled_time')
            ->withTimestamps();
	}

    public function getCurrentStudentsAttribute() {
        return $this->students->filter(function($student) {
            return $student->pivot->end_date == null ||
                strtotime($student->pivot->end_date) > time();
        })->map(function($item) {
            return $item->name;
        })->all();
    }

    public function getGroupSizeAttribute() {
        $groupSize = $this->group_size_min . "-" . $this->group_size_max;
        if ($this->group_size_min === 1 && $this->group_size_max === 1) {
            $groupSize = substr(ucfirst(trans('generic.individual')), 0, 3) . ".";
        } else if ($this->group_size_min === 2 && $this->group_size_max === 2) {
            $groupSize = substr(ucfirst(trans('generic.duolesson')), 0, 3) . ".";
        }
        return $groupSize;
    }

    /**
     * Course participants get access to this library (through Classy)
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
	public function libraries() {
	    return $this->belongsToMany(Library::class, "library_course", "course_id", "library_id");
    }

    public function currentStudents() {
        return $this->belongsToMany('App\Models\Student')
            ->where(function($query) {
                $query->whereNull('end_date')
                      ->orWhere("end_date", ">", DB::raw("NOW()"));
            })
            ->withPivot('id', 'start_date', 'end_date', 'signed', 'sign_request_send', 'checklist_id','status','please_keep_scheduled_time')
            ->withTimestamps();
    }

	public function coursegroup() {
		return $this->belongsTo(Coursegroup::class);
	}

	public function recurrenceoption() {
		return $this->belongsTo(RecurrenceOption::class);
	}

	public function registrations() {
	    return $this->hasMany(Registration::class, 'course_id', 'id');
    }

    public function studentgroups() {
        return $this->belongsToMany(Studentgroup::class, 'course_student', 'course_id', 'student_id')
            ->withPivot('id', 'start_date', 'end_date', 'signed', 'sign_request_send', 'checklist_id','status','please_keep_scheduled_time')
            ->with('students')
            ->withTimestamps();
    }

    /**
     * assemble the full name of the course
     * @return string
     */
    public function getFullnameAttribute() {
	    $recOption = isset($this->recurrenceoption) ? " " . $this->recurrenceoption->description : "";
        return $this->name . $recOption;
    }

    /**
     * get unique date/times on which a lesson is schedules for any student
     * uses current schoolyear and future events
     */
    public function getLessonTableAttribute() {
        $weekdays = [
            1 => trans('localisation.monday'),
            2 => trans('localisation.tuesday'),
            3 => trans('localisation.wednesday'),
            4 => trans('localisation.thursday'),
            5 => trans('localisation.friday'),
            6 => trans('localisation.saturday'),
            7 => trans('localisation.sunday'),
        ];

        $now = date('Ymd');
        $retArr = [];
        $registrations = $this->registrations;
        $registrationIds = [];
        // make sure the registrations are still valid
        foreach ($registrations as $registration) {
            if(!empty($registration->end_date)) {
                $enddate = new \DateTime($registration->end_date);
                $enddate = $enddate->format('Ymd');
            }
            if(empty($registration->end_date) || ($enddate >= $now) ) {
                $registrationIds[] = $registration->id;
            }
        }
        Log::info("registration IDs: ". implode(",", $registrationIds));

        // current schoolyear, be sure there's only one response
        $schoolyear = Schoolyear::getCurrentOrFuture();
        // get timetables for this course in current schoolyear
        $timetables = Timetable::where('schoolyear_id', '=', $schoolyear->id)
                            ->whereIn('course_student_id', $registrationIds)->get();
        Log::info("found " . count($timetables) . " timetables for course $this->id in current schoolyear ($schoolyear->id).");

        // get events for these timetables
        $ttIds = [];
        foreach ($timetables as $ttloop) {
            // $tt->events werkt niet. if los object voor maken
            $ttIds[] = $ttloop->id;
        }
        $allEvents = Event::whereIn('timetable_id', $ttIds)->get();

        // filter and make unique
        $saveEvents = [];
        foreach ($allEvents as $index => $event) {
            $eventStart = new \DateTime($event->datetime);
            $now = new \DateTime();
            if ($eventStart >= $now) {
                Log::info("dt: $event->datetime");
                $dow = $eventStart->format('N');
                $time = $eventStart->format('H:i');
                $saveEvents[$dow . '-' . $time]['index'] = $index;
                if(isset($saveEvents[$dow . '-' . $time]['datetimes'])) {
                    if(!isset($saveEvents[$dow . '-' . $time]['datetimes'][$event->datetime])) {
                        $saveEvents[$dow . '-' . $time]['datetimes'][$event->datetime] = 1;
                    }
                } else {
                    $saveEvents[$dow . '-' . $time]['datetimes'] = [$event->datetime => 1];
                }
            }
        }

        foreach ($saveEvents as $key => $saveEvent) {
            $event = $allEvents[$saveEvent['index']];
            $parts = explode('-', $key);
            $retArr[] = [
                'dow'           => $parts[0],
                'dow_name'      => $weekdays[$parts[0]],
                'time'          => $parts[1],
               // 'basedOnEvent'  => $event, ff weg, clutter
                'schoolyear'    => $schoolyear->id,
                'course'        => $this->id,
                'nrOfEvents'    => count($saveEvent['datetimes'])
            ];
        }

        return $retArr;
	}



}
