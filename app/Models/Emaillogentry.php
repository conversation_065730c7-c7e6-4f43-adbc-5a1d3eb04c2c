<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Emaillogentry extends Model {
    /**
     * Set standard filtering on queries:
     * makes sure only logentries from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        if (!Auth::guest()) {
            $query->where([
                ['emaillogentries.domain_id', "=", Auth::user()->domain->id]
            ]);
        }
        return $query;
    }
}
