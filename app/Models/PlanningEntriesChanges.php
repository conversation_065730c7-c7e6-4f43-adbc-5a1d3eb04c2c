<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class PlanningEntriesChanges extends Model
{
    use HasFactory;

    /**
     * Set standard filtering on queries:
     * makes sure only locations from the logged-in domain are returned
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where([
            ['planning_entries_changes.domain_id', "=", Auth::user()->domain->id]
        ]);
        return $query;
    }
}
