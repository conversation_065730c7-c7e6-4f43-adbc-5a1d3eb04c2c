import { defineConfig } from "cypress";

export default defineConfig({
    e2e: {
        specPattern: "tests/e2e/**/*.cy.{js,jsx,ts,tsx}",
        supportFile: "tests/e2e/cypress/support/e2e.js",
        baseUrl: "http://localhost:8000",
        defaultCommandTimeout: 30000,
        retries: {
            runMode: 2,
            openMode: 0
        },
        setupNodeEvents(on, config) {
            on('before:browser:launch', (browser = {}, launchOptions) => {
                // Controleer of het een Chromium-browser is (zoals Chrome)
                if (browser.family === 'chromium' && browser.name !== 'electron') {
                    // Voeg de gewenste argumenten toe
                    launchOptions.args.push('--enable-logging');
                    launchOptions.args.push('--v=1');
                    launchOptions.args.push('--log-file=chrome.log');
                }
                // Retourneer de aangepaste launchOptions
                return launchOptions;
            });
        },
    },
    screenshotsFolder: "tests/e2e/screenshots",
    video: false
});
