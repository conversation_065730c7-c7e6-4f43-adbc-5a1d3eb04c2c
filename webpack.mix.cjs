// This is a CommonJS wrapper for the ES Module webpack.mix.js
// It's used by Laravel Mix which expects a CommonJS module

const mix = require('laravel-mix');
const webpack = require('webpack');
const path = require('path');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */
mix.webpackConfig({
    resolve: {
        extensions: ['.ts', '.vue', '.js'],
        alias: {
            jQuery: path.resolve(__dirname, 'node_modules/jquery/dist/jquery.js')
        }
    },
    plugins: [
        // ProvidePlugin helps to recognize $ and jQuery words in code
        // And replace it with require('jquery')
        new webpack.ProvidePlugin({
            $: 'jquery',
            jQuery: 'jquery'
        })
    ],
    output: {
        hashFunction: "sha256"
    },
    stats: {
        children: false,
    }
});

/* remove console messages in production builds */
if (mix.inProduction()) {
    mix.version();
    mix.options({
        terser: {
            terserOptions: {
                compress: {
                    drop_console: true
                }
            }
        }
    });
} else {
    mix.sourceMaps()
}

// Only compile the new Vue 3 application
mix.js('resources/js/class3/app.js', 'public/js/tmpl3')
    .vue({ version: 3 }) // Specify Vue 3
    .sass('resources/sass/tmpl3/styles.scss', 'public/css/tmpl3')

// Legacy files - to be removed once migration is complete
// Commented out to prevent compilation during migration
// mix.js('resources/js/app.js', 'public/js')
//     .vue()
//     .sass('resources/sass/app.scss', 'public/css')
//     .js('resources/js/preferedschedule.js', 'public/js')
