<nav class="sb-topnav navbar navbar-dark bg-blue" id="mainNav">
    <a class="navbar-brand" href="/">
        <img src="/images/class3-logo-layers.svg" class="class3logo" alt="Class 3 logo"/>
    </a>

    <!-- toggler/hamburger -->
    <button
        class="navbar-toggler"
        type="button"
        data-toggle="collapse"
        data-target="#navbarCollapsableContent"
        aria-controls="navbarSupportedContent"
        aria-expanded="false"
        aria-label="Toggle navigation"
    >
        <span class="navbar-toggler-icon"></span>
    </button>

    @auth
        <div class="collapse navbar-collapse" id="navbarCollapsableContent">
            <ul class="navbar-nav mr-auto ml-2">

                {{-- Logged in School --}}
                <li class="nav-item px-2 hide-llg-and-down" style="border:1px solid white;">
                    <div class="mt-2 text-light">{{Auth::user()->domain->name}}</div>
                </li>

                {{-- DASHBOARD --}}
                <li class="nav-item ml-4">
                    <a class="nav-link" href="{{url('/home')}}">
                        <i class="fas fa-tachometer-alt hide-llg-and-down"></i>
                        {{ ucfirst(trans('generic.dashboard')) }}
                    </a>
                </li>
                @if(Auth::user()->userIsA('admin'))
                    {{-- NEW --}}
                    <x-menu-new></x-menu-new>
                    {{-- BASIC DATA --}}
                    <x-menu-basic-data></x-menu-basic-data>
                    {{-- REPORTS --}}
                    <x-menu-reports></x-menu-reports>
                @endif

                {{-- TIMETABLES/PLANNING --}}
                <x-menu-course-schedule></x-menu-course-schedule>

                {{-- CLASSY --}}
                {{-- <x-menu-classy></x-menu-classy> --}}

                @if(Auth::user()->userIsA('admin'))
                    {{-- SETTINGS --}}
                    <x-menu-tools></x-menu-tools>
                @endif
                @if(Auth::user()->userIsA('admin'))
                    {{-- SETTINGS --}}
                    <x-menu-settings></x-menu-settings>
                @endif

            </ul>

            {{-- USER / Profile --}}
            <x-menu-logout></x-menu-logout>
        </div>
    @else
        <x-menu-login></x-menu-login>
    @endauth

</nav>
