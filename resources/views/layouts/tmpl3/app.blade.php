<!DOCTYPE html>
<html lang="{{App::getLocale()}}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {{-- CSRF Token --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <meta name="description" content="program to help small schools to administrate students, courses, tutors and their planning" />
    <meta name="author" content="Scolavisa" />
    <title>Class 3</title>
    <link rel="icon" type="image/x-icon" href="{{ url('/favicon.ico') }}?v={{ time() }}" />
    <link href="{{ url('/css/tmpl3/styles.css') }}" rel="stylesheet" />
    <link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" rel="stylesheet" crossorigin="anonymous" />

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/js/all.min.js" crossorigin="anonymous"></script>
    <script src="{{ url('/js/v-tooltip.min.js') }}"></script>

    {{-- Scripts --}}
    <script>
        window.Laravel = <?php echo json_encode([
            'csrfToken' => csrf_token(),
        ]); ?>;
    </script>

    <script>
        window.trans = <?php
        // copy all translations from /resources/lang/CURRENT_LOCALE/* to global JS variable
        // $lang_files = File::files(resource_path() . '/lang/' . App::getLocale() . '/generic.php');
        $lang_files = [
            resource_path() . '/lang/' . App::getLocale() . '/email.php',
            resource_path() . '/lang/' . App::getLocale() . '/generic.php',
            resource_path() . '/lang/' . App::getLocale() . '/localisation.php',
            resource_path() . '/lang/' . App::getLocale() . '/mailtargets.php',
            resource_path() . '/lang/' . App::getLocale() . '/priceinterval.php',
            resource_path() . '/lang/' . App::getLocale() . '/reporting.php',
            resource_path() . '/lang/' . App::getLocale() . '/templates.php',
        ];
        $trans = [];
        foreach ($lang_files as $f) {
            $filename = pathinfo($f)['filename'];
            $trans[$filename] = trans($filename);
        }
        echo json_encode($trans);
        ?>;
        window.logedinuserid = '<?php
            $user = \Illuminate\Support\Facades\Auth::user();
            echo isset($user) ? $user->id : 0
            ?>';
    </script>

    @yield("extra_style")

    {{-- no bugsnag during development --}}
    @if(strtolower(config('app.env')) !== 'development' && strtolower(config('app.env')) !== 'local')
        {{-- https://app.bugsnag.com --}}
        <script src="//d2wy8f7a9ursnm.cloudfront.net/bugsnag-3.min.js"
                data-apikey="3fbaaa870d7340cf45f104d0cee2f656"
                data-releasestage="{{config('app.env')}}"></script>
    @endif

</head>
<body class="sb-nav-fixed">
<div id="vuecontext">
    @include('layouts.tmpl3._navbar')

    <div id="layoutSidenav">

        <div id="layoutSidenav_content">
            <main>
                <div class="container-fluid pt-sm-2 pt-lg-5">
{{--                     @include('layouts.tmpl3._showbreakpoint')--}}
                    @yield('content')
                </div>
            </main>
            @include('layouts.tmpl3._footer')
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
<script src="/js/tmpl3/app.js"></script>

</body>
</html>
