<nav class="sb-topnav navbar navbar-dark bg-dark-blue" id="mainNav">
    <a class="navbar-brand" href="/">
        <img src="/images/class-4.png" class="class4logo" alt="Class 4 logo"/>
    </a>

    <!-- toggler/hamburger -->
    <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarCollapsableContent"
        aria-controls="navbarSupportedContent"
        aria-expanded="false"
        aria-label="Toggle navigation"
    >
        <span class="navbar-toggler-icon"></span>
    </button>

    @auth
        <div class="collapse navbar-collapse" id="navbarCollapsableContent">
            <ul class="navbar-nav me-auto">

                {{-- Logged in School --}}
                <li class="nav-item px-2 hide-llg-and-down">
                    <div class="text-light opacity-75 fst-italic small">
                        {{Auth::user()->domain->name}}
                    </div>
                </li>

                {{-- DASHBOARD --}}
                <li class="nav-item ms-4">
                    <a class="nav-link" href="{{url('/home')}}">
                        <i class="fas fa-tachometer-alt hide-llg-and-down"></i>
                        {{ ucfirst(trans('generic.dashboard')) }}
                    </a>
                </li>
                @if(Auth::user()->userIsA('admin'))
                    {{-- NEW --}}
                    <x-menu-new></x-menu-new>
                    {{-- BASIC DATA --}}
                    <x-menu-basic-data></x-menu-basic-data>
                    {{-- REPORTS --}}
                    <x-menu-reports></x-menu-reports>
                @endif

                {{-- TIMETABLES/PLANNING --}}
                <x-menu-course-schedule></x-menu-course-schedule>

                {{-- CLASSY --}}
                {{-- <x-menu-classy></x-menu-classy> --}}

                @if(Auth::user()->userIsA('admin'))
                    {{-- SETTINGS --}}
                    <x-menu-tools></x-menu-tools>
                @endif
                @if(Auth::user()->userIsA('admin'))
                    {{-- SETTINGS --}}
                    <x-menu-settings></x-menu-settings>
                @endif

            </ul>

            {{-- USER / Profile --}}
            <x-menu-logout-bs5></x-menu-logout-bs5>
        </div>
    @else
        <x-menu-login></x-menu-login>
    @endauth

</nav>
