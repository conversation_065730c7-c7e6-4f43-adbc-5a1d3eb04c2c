@extends('layouts.tmpl3.app')

@section('content')
    <div class="container" id="email_list">
        <div class="card mb-2">
            <div class="card-header" id="headingOne">
                Export for maillist
            </div>
            <div class="card-body">
                <a :href="'/students/exportmaillist/lastname'" class="btn btn-success">
                    <i class="fa fa-file-excel"></i>
                    <div>{{ ucfirst(trans('generic.exporttoexcel')) }}</div>
                </a>
            </div>
        </div>
        <div id="accordion">
            <div class="card">
                <div class="card-header" id="headingOne">
                    <h5 class="mb-0">
                        <button class="btn btn-link" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            {{ucfirst(trans('generic.emaillistallstudents'))}}
                        </button>
                    </h5>
                </div>

                <div id="collapseOne" class="collapse" aria-labelledby="headingOne" data-parent="#accordion">
                    <div class="card-body">
                        @foreach($allAddresses as $address)
                            {{$address->email}},
                        @endforeach
                        <br/>
                        <!-- Copy -->
                        <button class="btn btn-primary clipboardTarget" data-clipboard-target="#emaillist1">
                            <i class="fa fa-clipboard" aria-hidden="true"></i>&nbsp;{{ucfirst(trans('generic.copy'))}}
                        </button>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header" id="headingTwo">
                    <h5 class="mb-0">
                        <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                            {{ucfirst(trans('generic.emaillistallstudentsminimalone'))}}
                        </button>
                    </h5>
                </div>
                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
                    <div class="card-body">
                        @foreach($atleastone as $address)
                            {{$address->email}},
                        @endforeach
                        <br/>
                        <!-- Copy -->
                        <button class="btn btn-primary clipboardTarget" data-clipboard-target="#emaillist2">
                            <i class="fa fa-clipboard" aria-hidden="true"></i>&nbsp;{{ucfirst(trans('generic.copy'))}}
                        </button>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header" id="headingThree">
                    <h5 class="mb-0">
                        <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                            {{ucfirst(trans('generic.emaillistallstudentsnocourse'))}}
                        </button>
                    </h5>
                </div>
                <div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-parent="#accordion">
                    <div class="card-body">
                        @foreach($nocourses as $address)
                            {{$address->email}},
                        @endforeach
                        <br/>
                        <!-- Copy -->
                        <button class="btn btn-primary clipboardTarget" data-clipboard-target="#emaillist3">
                            <i class="fa fa-clipboard" aria-hidden="true"></i>&nbsp;{{ucfirst(trans('generic.copy'))}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection