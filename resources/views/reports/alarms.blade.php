@extends('layouts.tmpl3.app')

@section('content')
    <h1>{{ ucfirst(trans('generic.overviewalarms'))  }}</h1>
    <div class="card-deck">
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.conflictscalendar'))}}"
                subject="timetableconflicts"
                icon="fa-calendar"
        ></show-alert-status>
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.conflictsstudentsnoregistration'))}}"
                subject="conflictsstudentsnoregistration"
                icon="fa-user-tag"
        ></show-alert-status>
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.conflictsstudentsnoemail'))}}"
                subject="conflictsstudentsnoemail"
                icon="fa-user-edit"
        ></show-alert-status>
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans_choice('generic.tasks', 2))}}"
                subject="activetasks"
                icon="fa-tasks"
        ></show-alert-status>
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.triallessons'))}}"
                subject="triallessons"
                icon="fa-vial"
        ></show-alert-status>
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.activeschoolyearavailable'))}}"
                subject="activeschoolyearavailable"
                icon="fa-vial"
        ></show-alert-status>
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.signrequestbacklog'))}}"
                subject="signrequestbacklog"
                icon="fa-signature"
        ></show-alert-status>
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.checklistincomplete'))}}"
                subject="incompletechecklist"
                icon="fa-clipboard-list"
        ></show-alert-status>
        <!-- this one is very slow to execute, keep it last for now -->
        <show-alert-status
                token="{{csrf_token()}}"
                paneltitle="{{ucfirst(trans('generic.conflictstutorsandlocations'))}}"
                subject="tutorlocationconflicts"
                icon="fa-user-cog"
        ></show-alert-status>
    </div>
@endsection
