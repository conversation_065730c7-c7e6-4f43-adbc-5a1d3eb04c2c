{{--popup confirm delete event --}}
<div class="modal fade" id="confirm-delete-event" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <input type="hidden" id="eventId"/>
        <div class="modal-content">
            <div class="modal-header">
                <h4>{{ucfirst(trans('generic.areyousure'))}}?</h4>
            </div>
            <div class="modal-body">
                <h5>{{ucfirst(trans('generic.areyousuredeleteevent'))}}</h5>
                {{ucfirst(trans('generic.thiscannotbeundone'))}}!
                <hr/>
                <div class="radio">
                    <label><input type="radio" name="optradio" value="0" v-model="deleteRangeOption">{{trans('generic.thisevent')}}</label>
                </div>
                <div class="radio">
                    <label><input type="radio" name="optradio" value="1" v-model="deleteRangeOption">{{trans('generic.allevents')}}</label>
                </div>
                <div class="radio">
                    <label><input type="radio" name="optradio" value="2" v-model="deleteRangeOption">{{trans('generic.thisandfutureevent')}}</label>
                </div>

                <div class="form-inline" role="form">
                    <div class="form-group">
                        <div class="radio">
                            <label>
                                <input type="radio" id="untildate"  value="3" v-model="deleteRangeOption" name="optradio" >
                                {{trans('generic.everyappointmentuntilandincluding')}}:&nbsp;
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        {{-- v-model won't work here because of datepicker / virtual DOM conflict --}}
                        <input type="text" class="form-control datepicker" id="deleteUntilDate" @click="setfocusonmyradio('#untildate')"><br/>
                        <small>{{trans('generic.timeisalways235959')}}</small>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ucfirst(trans('generic.close'))}}</button>
                <a @click="doDelete" class="btn btn-danger btn-ok" data-dismiss="modal">{{ucfirst(trans('generic.delete'))}}</a>
            </div>
        </div>
    </div>
</div>
