@extends('layouts.tmpl3.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle mr-1"></i>
                    Bootstrap 5 Migration Example
                </div>
                <div class="card-body">
                    <h2>Incremental Migration Example</h2>
                    <p class="lead">
                        This page demonstrates how to incrementally migrate from Bootstrap 4 to Bootstrap 5 using the scoped CSS approach.
                        Elements with the <code>.bs5</code> class use Bootstrap 5 styles, while other elements continue to use Bootstrap 4.
                    </p>
                    
                    <div class="alert alert-info">
                        <strong>Migration Strategy:</strong> Add the <code>.bs5</code> class to any element you want to migrate to Bootstrap 5.
                        This allows you to migrate components one by one, page by page, without affecting the rest of the application.
                    </div>
                </div>
            </div>
            
            <!-- Bootstrap 4 Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-code mr-1"></i>
                    Bootstrap 4 Component
                </div>
                <div class="card-body">
                    <h4>This is a Bootstrap 4 Card</h4>
                    <p>This card uses Bootstrap 4 styles and components.</p>
                    
                    <form>
                        <div class="form-group">
                            <label for="bs4Input">Email address</label>
                            <input type="email" class="form-control" id="bs4Input" placeholder="<EMAIL>">
                            <small class="form-text text-muted">We'll never share your email with anyone else.</small>
                        </div>
                        <div class="form-group">
                            <label for="bs4Select">Example select</label>
                            <select class="form-control" id="bs4Select">
                                <option>1</option>
                                <option>2</option>
                                <option>3</option>
                            </select>
                        </div>
                        <div class="form-group form-check">
                            <input type="checkbox" class="form-check-input" id="bs4Check">
                            <label class="form-check-label" for="bs4Check">Check me out</label>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>
            
            <!-- Bootstrap 5 Card -->
            <div class="bs5-container">
                <div class="bs5">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-code-branch me-1"></i>
                            Bootstrap 5 Component
                        </div>
                        <div class="card-body">
                            <h4>This is a Bootstrap 5 Card</h4>
                            <p>This card uses Bootstrap 5 styles and components.</p>
                            
                            <form>
                                <div class="mb-3">
                                    <label for="bs5Input" class="form-label">Email address</label>
                                    <input type="email" class="form-control" id="bs5Input" placeholder="<EMAIL>">
                                    <div class="form-text">We'll never share your email with anyone else.</div>
                                </div>
                                <div class="mb-3">
                                    <label for="bs5Select" class="form-label">Example select</label>
                                    <select class="form-select" id="bs5Select">
                                        <option>1</option>
                                        <option>2</option>
                                        <option>3</option>
                                    </select>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="bs5Check">
                                    <label class="form-check-label" for="bs5Check">Check me out</label>
                                </div>
                                <button type="submit" class="btn btn-primary">Submit</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mixed Example -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-random mr-1"></i>
                    Mixed Bootstrap 4 and 5 Example
                </div>
                <div class="card-body">
                    <h4>This card contains both Bootstrap 4 and 5 elements</h4>
                    <p>The outer card uses Bootstrap 4, while the inner alert uses Bootstrap 5.</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Bootstrap 4 Alert -->
                            <div class="alert alert-warning">
                                This is a Bootstrap 4 alert.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Bootstrap 5 Alert -->
                            <div class="bs5">
                                <div class="alert alert-warning">
                                    This is a Bootstrap 5 alert.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <!-- Bootstrap 4 Buttons -->
                            <h5>Bootstrap 4 Buttons</h5>
                            <button type="button" class="btn btn-primary">Primary</button>
                            <button type="button" class="btn btn-secondary">Secondary</button>
                            <button type="button" class="btn btn-success">Success</button>
                            <button type="button" class="btn btn-danger">Danger</button>
                        </div>
                        <div class="col-md-6">
                            <!-- Bootstrap 5 Buttons -->
                            <h5>Bootstrap 5 Buttons</h5>
                            <div class="bs5">
                                <button type="button" class="btn btn-primary">Primary</button>
                                <button type="button" class="btn btn-secondary">Secondary</button>
                                <button type="button" class="btn btn-success">Success</button>
                                <button type="button" class="btn btn-danger">Danger</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Migration Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-lightbulb mr-1"></i>
                    Migration Tips
                </div>
                <div class="card-body">
                    <h4>Tips for Incremental Migration</h4>
                    <ol>
                        <li>Start with non-interactive components (typography, cards, etc.)</li>
                        <li>Move on to form controls, which have significant changes in Bootstrap 5</li>
                        <li>Finally, migrate interactive components (modals, dropdowns, etc.)</li>
                    </ol>
                    
                    <h5 class="mt-4">Common Changes to Watch For:</h5>
                    <ul>
                        <li><code>.form-group</code> → <code>.mb-3</code></li>
                        <li><code>.form-control</code> for selects → <code>.form-select</code></li>
                        <li><code>data-toggle</code> → <code>data-bs-toggle</code></li>
                        <li><code>data-target</code> → <code>data-bs-target</code></li>
                        <li><code>data-dismiss</code> → <code>data-bs-dismiss</code></li>
                        <li><code>.close</code> button → <code>.btn-close</code></li>
                        <li><code>.mr-*</code>, <code>.ml-*</code> → <code>.me-*</code>, <code>.ms-*</code></li>
                    </ul>
                    
                    <div class="bs5">
                        <div class="alert alert-info mt-4">
                            <strong>Pro Tip:</strong> Use the <code>bs5-highlight</code> class on the body to highlight all Bootstrap 5 elements during development.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Example of how to initialize Bootstrap 5 components
    document.addEventListener('DOMContentLoaded', function() {
        // You can add specific Bootstrap 5 initialization code here if needed
        console.log('Bootstrap 5 components initialized');
    });
</script>
@endsection
