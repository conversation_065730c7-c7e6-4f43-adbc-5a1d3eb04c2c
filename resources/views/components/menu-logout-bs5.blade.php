<div class="nav-item dropdown">
    <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButtonProfile"
            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
    >
        <user-avatar avatar="{{ Auth::user()->avatar }}"></user-avatar>
        {{ Auth::user()->name }}
    </button>
    <div class="dropdown-menu" aria-labelledby="dropdownMenuButtonProfile">

        @if(Auth::user()->domain->status === 'pro')
            <label class="dropdown-item dropdown-item-nc my-2">
                <i class="fa fa-bolt"></i>
                <strong class="ms-2">Pro account</strong>
            </label>
        @else
            <label class="dropdown-item dropdown-item-nc my-2">
                <i class="fa fa-eye"></i>
                <strong class="ms-2">Trial account</strong>
            </label>
        @endif

        <div class="dropdown-divider"></div>
        <a class="dropdown-item" href="/users/profile">
            <i class="fas fa-id-card"></i>
            {{ucfirst(trans('generic.profile'))}}
        </a>

        @if(Auth::user()->userIsA('scolavisa'))
            <a class="dropdown-item" href="/class3_monitor">
                <i class="fas fa-wave-square"></i>
                Monitor
            </a>
        @endif

        <div class="dropdown-divider"></div>
        <a class="dropdown-item" id="logoutButton" href="javascript:noop()">
            <i class="fas fa-sign-out-alt"></i>
            {{ucfirst(trans('auth.logout'))}}
        </a>
    </div>
</div>

<form id="logout-form" action="{{ url('/logout') }}" method="POST">
    {{ csrf_field() }}
</form>
