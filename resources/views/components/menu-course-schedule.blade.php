<li class="nav-item dropdown">
    <a class="nav-link dropdown-toggle"
       href="#" id="navbarCourseSchedule" role="button" data-toggle="dropdown"
       aria-haspopup="true" aria-expanded="false"
    >
        <i class="fas fa-calendar hide-llg-and-down"></i>
        {{ ucfirst(trans_choice('generic.plantables',1)) }}
    </a>
    <div class="dropdown-menu" aria-labelledby="navbarCourseSchedule">
        {{-- show all events --}}
        <a class="dropdown-item" href="{{ url('/calendar') }}">{{ ucfirst(trans('generic.full')) }} {{ trans_choice('generic.calendar',1) }}</a>
        <a class="dropdown-item" href="{{ url('/planning/lesson') }}">{{ ucfirst(trans('generic.plancard')) }}</a>
        <a class="dropdown-item" href="{{ url('/dateexceptions') }}">{{ ucfirst(trans('generic.eventsholidaysleave')) }}</a>
        <a class="dropdown-item" href="{{ url('/trialstudents') }}">{{ ucfirst(trans('generic.processteachingrequests')) }}</a>

        {{-- currently not active --}}
        {{-- <a class="dropdown-item" href="{{ url('/planning/table') }}">{{ ucfirst(trans_choice('generic.planningtable',2)) }}</a>--}}
        {{-- <a class="dropdown-item" href="{{ url('/conflicts') }}">{{ ucfirst(trans_choice('generic.conflicts',2)) }}</a>--}}
        {{-- <a class="dropdown-item" href="{{ url('/planning/assistant') }}">{{ ucfirst(trans_choice('generic.scheduleassistant',2)) }}</a>--}}
        <a class="dropdown-item" href="{{ url('/timetables/showstudentprefs') }}">{{ ucfirst(trans('generic.studentprefs')) }}</a>
    </div>
</li>
