@extends("layouts.tmpl3.app")

@section("content")
    <div class="container" id="setupwizard">

        <div class="card">
            <div class="card-header">
                {{ ucfirst(trans('generic.setupwizard')) }}
                <div>
                    <small>{{ trans('generic.explainsetuppage') }}</small>
                </div>
            </div>
            <div class="card-body">
                <h5>{{ ucfirst(trans('generic.missingdata')) }}:</h5>
                <div class="row">
                    <div class="col-md-12">
                        @foreach($incompleteSetupOptions as $key => $option)
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <div>{{ucfirst($option["message"])}}</div>
                                    <div>
                                        <a href="{{$option["link"]}}?setupfix=true" class="btn btn-sm btn-primary">
                                            <i class="fa fa-wrench"></i>
                                            {{ trans('generic.fixthishere') }}
                                        </a>
                                        @if($key == 'tutors')
                                            {{trans('generic.mayalsomakeadminatutor')}}:
                                            <a href="/users/profile?setupfix=true" class="btn btn-sm btn-primary">
                                                <i class="fa fa-wrench"></i>&nbsp;{{ trans('generic.fixthishere') }}
                                            </a>
                                        @endif
                                    </div>
                                </li>
                            </ul>
                        @endforeach
                    </div>{{--/col--}}
                </div>{{--/row--}}

            </div>
        </div>
    </div> {{--/container--}}

@endsection