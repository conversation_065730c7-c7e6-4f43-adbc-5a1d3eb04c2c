@extends("layouts.app_public")

@section("script")
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.19.1/axios.min.js"></script>

    <!-- Minified version of `es6-promise-auto` below. Needed for IE11 -->
    <script src="https://cdn.jsdelivr.net/npm/es6-promise@4/dist/es6-promise.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/es6-promise@4/dist/es6-promise.auto.min.js"></script>

    {{-- lodash--}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.15/lodash.min.js"></script>

    {{--jQuery--}}
    <script
            src="https://code.jquery.com/jquery-3.4.1.min.js"
            integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo="
            crossorigin="anonymous"></script>

    <script>


    </script>

    <script src="/js/preferedschedule.js"></script>
@endsection

@section("style")
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/css/font-awesome.min.css">
@endsection

@section("content")
    {{-- don't add the student id. having only an access token makes the code use that as auth mechanism --}}
    <preferedschedule
        accesstoken='{{ $accesstoken }}'
        student-name='{{ $student->name }}'
        :student-courses='{{ json_encode($activeCourses) }}'
        :planning-details='{!! json_encode($planningDetails) !!}'
        show-remark-keep-planning='{{ $showRemarkKeepPlanning }}'
        :generic='{name: "{{trans('generic.name')}}",
            currentlessontime: "{{trans('generic.currentlessontime')}}",
            iprefertokeepcurrenttime: "{{trans('generic.iprefertokeepcurrenttime')}}",
            newcoursetobeplanned: "{{trans('generic.newcoursetobeplanned')}}",
            course: "{{trans('generic.course')}}",
            availability: "{{trans('generic.availability')}}",
            saved: "{{trans('generic.saved')}}",
            dayname: "{{trans('generic.dayname')}}",
            notavailable: "{{trans('generic.notavailable')}}",
            from: "{{trans('generic.from')}}",
            until: "{{trans('generic.until')}}",
            add: "{{trans('generic.add')}}",
            hhmm: "{{trans('generic.hhmm')}}",
            savepreference: "{{trans('generic.savepreference')}}",
            preferencesaved: "{{trans('generic.preferencesaved')}}",
            preferedtime: "{{trans('generic.preferedtime')}}",
            error: "{{trans('generic.error')}}",
            notice: "{{trans('generic.notice')}}",
            youravailability: "{{trans('generic.youravailability')}}",
            notavalidtime: "{{trans('generic.notavalidtime')}}",
            explainarrowtoaddtoschedule: "{{trans('generic.explainarrowtoaddtoschedule')}}",
            explainpreferedtime: "{{trans('generic.explainpreferedtime')}}",
            didnotenterresponseforeveryday: "{{trans('generic.didnotenterresponseforeveryday')}}",
            totaltimetoolow: "{{trans('generic.totaltimetoolow')}}",
            noinputfor: "{{trans('generic.noinputfor')}}",
            youhavenocourses: "{{trans('generic.youhavenocourses')}}",
            allreadyyoumayclosethiswindow: "{{trans('generic.allreadyyoumayclosethiswindow') }}",
            totimeshouldbegreaterthanfromtime: "{{trans('generic.totimeshouldbegreaterthanfromtime')}}",
            explainkeepscheduletimerestriction: "{!! trans('generic.explainkeepscheduletimerestriction') !!}",
            youravailability: "{{ucfirst(trans('generic.youravailability'))}}",
            pleasemarkyourpreferedtimesegments: "{{ucfirst(trans('generic.pleasemarkyourpreferedtimesegments'))}}"
            }'

        :daynames='{monday: "{{trans('localisation.monday')}}",
            tuesday: "{{trans('localisation.tuesday')}}",
            wednesday: "{{trans('localisation.wednesday')}}",
            thursday: "{{trans('localisation.thursday')}}",
            friday: "{{trans('localisation.friday')}}",
            saturday: "{{trans('localisation.saturday')}}",
            sunday: "{{trans('localisation.sunday')}}"}'
    ></preferedschedule>

@endsection

@section('domainheader')
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <img src="{!! $domain->logo_url !!}" height="120px">
        </div>
        <div class="col-sm-6 col-md-6">
            <div class="col-sm-12 col-md-12"><h4 class="pull-right">{{$domain->name}}</h4></div>
            <div class="col-sm-12 col-md-12"><span class="pull-right">{{$domain->address1}}</span></div>
            <div class="col-sm-12 col-md-12"><span class="pull-right">{{$domain->address2}}</span></div>
            <div class="col-sm-12 col-md-12"><span class="pull-right">tel: {{$domain->telephone}}</span></div>
            <div class="col-sd-12 col-md-12"><span class="pull-right">email: {{$domain->email}}</span></div>
        </div>
    </div>
@endsection
