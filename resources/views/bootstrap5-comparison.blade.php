<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Bootstrap 4 vs 5 Comparison</title>

    <!-- Main application styles (Bootstrap 4) -->
    <link href="{{ url('/css/tmpl3/styles.css') }}" rel="stylesheet">

    <style>
        /* Custom styles for the comparison page */
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .comparison-container {
            margin-bottom: 40px;
        }

        .comparison-title {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }

        .bs4-col, .bs5-col {
            padding: 20px;
            border-radius: 8px;
        }

        .bs4-col {
            background-color: rgba(0, 123, 255, 0.05);
            border: 1px solid rgba(0, 123, 255, 0.2);
        }

        .bs5-col {
            background-color: rgba(13, 110, 253, 0.05);
            border: 1px solid rgba(13, 110, 253, 0.2);
        }

        .component-label {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 4px;
            display: inline-block;
        }

        .bs4-label {
            background-color: rgba(0, 123, 255, 0.2);
        }

        .bs5-label {
            background-color: rgba(13, 110, 253, 0.2);
        }

        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }

        code {
            color: #d63384;
        }
    </style>

    <!-- Bootstrap 5 CSS with .bs5 prefix -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap5-scoped@1.0.0/css/bootstrap-scoped.min.css" />
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1>Bootstrap 4 vs 5 Comparison</h1>
                <a href="/home" class="btn btn-primary">Return to Dashboard</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    This page shows Bootstrap 4 and Bootstrap 5 components side by side for comparison.
                    Bootstrap 5 components are scoped with the <code>.bs5</code> class to prevent conflicts.
                </div>

                <!-- Buttons Comparison -->
                <div class="comparison-container">
                    <h2 class="comparison-title">Buttons</h2>
                    <div class="row">
                        <div class="col-md-6 bs4-col">
                            <div class="component-label bs4-label">Bootstrap 4</div>
                            <button type="button" class="btn btn-primary">Primary</button>
                            <button type="button" class="btn btn-secondary">Secondary</button>
                            <button type="button" class="btn btn-success">Success</button>
                            <button type="button" class="btn btn-danger">Danger</button>
                            <button type="button" class="btn btn-warning">Warning</button>
                            <button type="button" class="btn btn-info">Info</button>
                            <button type="button" class="btn btn-light">Light</button>
                            <button type="button" class="btn btn-dark">Dark</button>
                            <button type="button" class="btn btn-link">Link</button>

                            <pre><code>&lt;button type="button" class="btn btn-primary"&gt;Primary&lt;/button&gt;</code></pre>
                        </div>
                        <div class="col-md-6 bs5-col">
                            <div class="component-label bs5-label">Bootstrap 5</div>
                            <div class="bs5">
                                <button type="button" class="btn btn-primary">Primary</button>
                                <button type="button" class="btn btn-secondary">Secondary</button>
                                <button type="button" class="btn btn-success">Success</button>
                                <button type="button" class="btn btn-danger">Danger</button>
                                <button type="button" class="btn btn-warning">Warning</button>
                                <button type="button" class="btn btn-info">Info</button>
                                <button type="button" class="btn btn-light">Light</button>
                                <button type="button" class="btn btn-dark">Dark</button>
                                <button type="button" class="btn btn-link">Link</button>
                            </div>

                            <pre><code>&lt;button type="button" class="btn btn-primary"&gt;Primary&lt;/button&gt;</code></pre>
                        </div>
                    </div>
                </div>

                <!-- Cards Comparison -->
                <div class="comparison-container">
                    <h2 class="comparison-title">Cards</h2>
                    <div class="row">
                        <div class="col-md-6 bs4-col">
                            <div class="component-label bs4-label">Bootstrap 4</div>
                            <div class="card">
                                <div class="card-header">Card Header</div>
                                <div class="card-body">
                                    <h5 class="card-title">Card Title</h5>
                                    <p class="card-text">Some quick example text to build on the card title.</p>
                                    <button class="btn btn-primary">Go somewhere</button>
                                </div>
                                <div class="card-footer">Card Footer</div>
                            </div>

                            <pre><code>&lt;div class="card"&gt;
  &lt;div class="card-header"&gt;Card Header&lt;/div&gt;
  &lt;div class="card-body"&gt;
    &lt;h5 class="card-title"&gt;Card Title&lt;/h5&gt;
    &lt;p class="card-text"&gt;Some text&lt;/p&gt;
    &lt;button class="btn btn-primary"&gt;Button&lt;/button&gt;
  &lt;/div&gt;
  &lt;div class="card-footer"&gt;Card Footer&lt;/div&gt;
&lt;/div&gt;</code></pre>
                        </div>
                        <div class="col-md-6 bs5-col">
                            <div class="component-label bs5-label">Bootstrap 5</div>
                            <div class="bs5">
                                <div class="card">
                                    <div class="card-header">Card Header</div>
                                    <div class="card-body">
                                        <h5 class="card-title">Card Title</h5>
                                        <p class="card-text">Some quick example text to build on the card title.</p>
                                        <button class="btn btn-primary">Go somewhere</button>
                                    </div>
                                    <div class="card-footer">Card Footer</div>
                                </div>
                            </div>

                            <pre><code>&lt;div class="card"&gt;
  &lt;div class="card-header"&gt;Card Header&lt;/div&gt;
  &lt;div class="card-body"&gt;
    &lt;h5 class="card-title"&gt;Card Title&lt;/h5&gt;
    &lt;p class="card-text"&gt;Some text&lt;/p&gt;
    &lt;button class="btn btn-primary"&gt;Button&lt;/button&gt;
  &lt;/div&gt;
  &lt;div class="card-footer"&gt;Card Footer&lt;/div&gt;
&lt;/div&gt;</code></pre>
                        </div>
                    </div>
                </div>

                <!-- Forms Comparison -->
                <div class="comparison-container">
                    <h2 class="comparison-title">Forms</h2>
                    <div class="row">
                        <div class="col-md-6 bs4-col">
                            <div class="component-label bs4-label">Bootstrap 4</div>
                            <form>
                                <div class="form-group">
                                    <label for="bs4Email">Email address</label>
                                    <input type="email" class="form-control" id="bs4Email" placeholder="<EMAIL>">
                                    <small class="form-text text-muted">We'll never share your email.</small>
                                </div>
                                <div class="form-group">
                                    <label for="bs4Select">Example select</label>
                                    <select class="form-control" id="bs4Select">
                                        <option>1</option>
                                        <option>2</option>
                                        <option>3</option>
                                    </select>
                                </div>
                                <div class="form-group form-check">
                                    <input type="checkbox" class="form-check-input" id="bs4Check">
                                    <label class="form-check-label" for="bs4Check">Check me out</label>
                                </div>
                                <button type="submit" class="btn btn-primary">Submit</button>
                            </form>

                            <pre><code>&lt;div class="form-group"&gt;
  &lt;label for="email"&gt;Email address&lt;/label&gt;
  &lt;input type="email" class="form-control" id="email"&gt;
&lt;/div&gt;</code></pre>
                        </div>
                        <div class="col-md-6 bs5-col">
                            <div class="component-label bs5-label">Bootstrap 5</div>
                            <div class="bs5">
                                <form>
                                    <div class="mb-3">
                                        <label for="bs5Email" class="form-label">Email address</label>
                                        <input type="email" class="form-control" id="bs5Email" placeholder="<EMAIL>">
                                        <div class="form-text">We'll never share your email.</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="bs5Select" class="form-label">Example select</label>
                                        <select class="form-select" id="bs5Select">
                                            <option>1</option>
                                            <option>2</option>
                                            <option>3</option>
                                        </select>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="bs5Check">
                                        <label class="form-check-label" for="bs5Check">Check me out</label>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </form>
                            </div>

                            <pre><code>&lt;div class="mb-3"&gt;
  &lt;label for="email" class="form-label"&gt;Email address&lt;/label&gt;
  &lt;input type="email" class="form-control" id="email"&gt;
&lt;/div&gt;</code></pre>
                        </div>
                    </div>
                </div>

                <!-- Alerts Comparison -->
                <div class="comparison-container">
                    <h2 class="comparison-title">Alerts</h2>
                    <div class="row">
                        <div class="col-md-6 bs4-col">
                            <div class="component-label bs4-label">Bootstrap 4</div>
                            <div class="alert alert-primary" role="alert">
                                This is a primary alert—check it out!
                            </div>
                            <div class="alert alert-secondary" role="alert">
                                This is a secondary alert—check it out!
                            </div>
                            <div class="alert alert-success" role="alert">
                                This is a success alert—check it out!
                            </div>
                            <div class="alert alert-danger" role="alert">
                                This is a danger alert—check it out!
                            </div>

                            <pre><code>&lt;div class="alert alert-primary" role="alert"&gt;
  This is a primary alert—check it out!
&lt;/div&gt;</code></pre>
                        </div>
                        <div class="col-md-6 bs5-col">
                            <div class="component-label bs5-label">Bootstrap 5</div>
                            <div class="bs5">
                                <div class="alert alert-primary" role="alert">
                                    This is a primary alert—check it out!
                                </div>
                                <div class="alert alert-secondary" role="alert">
                                    This is a secondary alert—check it out!
                                </div>
                                <div class="alert alert-success" role="alert">
                                    This is a success alert—check it out!
                                </div>
                                <div class="alert alert-danger" role="alert">
                                    This is a danger alert—check it out!
                                </div>
                            </div>

                            <pre><code>&lt;div class="alert alert-primary" role="alert"&gt;
  This is a primary alert—check it out!
&lt;/div&gt;</code></pre>
                        </div>
                    </div>
                </div>

                <!-- Modal Comparison -->
                <div class="comparison-container">
                    <h2 class="comparison-title">Modals</h2>
                    <div class="row">
                        <div class="col-md-6 bs4-col">
                            <div class="component-label bs4-label">Bootstrap 4</div>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#bs4Modal">
                                Launch Bootstrap 4 modal
                            </button>

                            <div class="modal fade" id="bs4Modal" tabindex="-1" aria-labelledby="bs4ModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="bs4ModalLabel">Bootstrap 4 Modal</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            Modal body text goes here.
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                            <button type="button" class="btn btn-primary">Save changes</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <pre><code>&lt;button data-toggle="modal" data-target="#modal"&gt;
  Launch modal
&lt;/button&gt;

&lt;div class="modal" id="modal"&gt;
  &lt;div class="modal-dialog"&gt;
    &lt;div class="modal-content"&gt;
      &lt;div class="modal-header"&gt;
        &lt;h5 class="modal-title"&gt;Modal title&lt;/h5&gt;
        &lt;button class="close" data-dismiss="modal"&gt;
          &lt;span aria-hidden="true"&gt;&times;&lt;/span&gt;
        &lt;/button&gt;
      &lt;/div&gt;
      &lt;div class="modal-body"&gt;...&lt;/div&gt;
      &lt;div class="modal-footer"&gt;...&lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
                        </div>
                        <div class="col-md-6 bs5-col">
                            <div class="component-label bs5-label">Bootstrap 5</div>
                            <div class="bs5">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bs5Modal">
                                    Launch Bootstrap 5 modal
                                </button>

                                <div class="modal fade" id="bs5Modal" tabindex="-1" aria-labelledby="bs5ModalLabel" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="bs5ModalLabel">Bootstrap 5 Modal</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Modal body text goes here.
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                <button type="button" class="btn btn-primary">Save changes</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <pre><code>&lt;button data-bs-toggle="modal" data-bs-target="#modal"&gt;
  Launch modal
&lt;/button&gt;

&lt;div class="modal" id="modal"&gt;
  &lt;div class="modal-dialog"&gt;
    &lt;div class="modal-content"&gt;
      &lt;div class="modal-header"&gt;
        &lt;h5 class="modal-title"&gt;Modal title&lt;/h5&gt;
        &lt;button class="btn-close" data-bs-dismiss="modal"&gt;&lt;/button&gt;
      &lt;/div&gt;
      &lt;div class="modal-body"&gt;...&lt;/div&gt;
      &lt;div class="modal-footer"&gt;...&lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery (required for Bootstrap 4) -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>

    <!-- Bootstrap 4 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
            crossorigin="anonymous"></script>
</body>
</html>
