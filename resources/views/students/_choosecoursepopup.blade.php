<!-- Modal (popup) -->
<div id="chooseCourseModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{{ucfirst(trans('generic.chooseacourse'))}}</h4>
            </div>
            <div class="modal-body pre-scrollable">
                <input type="text" class="form-control" placeholder="Filter" v-model="searchCourseFilterKey"/>
                <hr/>
                <table class="table table-striped">
                    <tr v-for="course in filteredCourses" v-if="course.is_trial_course">
                        <td>
                            <button
                                    class="btn btn-success btn-sm"
                                    @click.prevent="addNewCourse(course.id, course.coursegroup.name, course.fullname, course.name)"
                                    data-dismiss="modal">
                                <i class="fa fa-plus"></i>
                            </button>
                        </td>
                        <td>
                            @{{course.name}} ({{trans('generic.triallesson')}})
                        </td>
                    </tr>
                    <tr v-else>
                        <td>
                            <button
                                    class="btn btn-success btn-sm"
                                    @click.prevent="addNewCourse(course.id, course.coursegroup.name, course.fullname, course.name)"
                                    data-dismiss="modal">
                                <i class="fa fa-plus"></i>
                            </button>
                        </td>
                        <td>
                            @{{course.fullname}} @{{course.recurrenceoption.description}}
                        </td>
                    </tr>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ucfirst(trans('generic.close'))}}</button>
            </div>
        </div>
    </div>
</div>