@extends('layouts.tmpl3.app')
@section('content')
    <div class="container">
        <div class="row justify-content-md-center">
            <div class="col-md-8 ">
                <div class="card">
                    <div class="card-header"><strong>{{ ucfirst(trans('generic.twofactorauthentication')) }}</strong></div>
                    <div class="card-body">
                        <p>{{trans('generic.twofactorexplain')}}</p>
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        {{ trans('generic.twofactorstep2') }}:<br/><br/>
                        <form class="form-horizontal" action="{{ route('2faVerify') }}" method="POST">
                            {{ csrf_field() }}
                            <div class="form-group{{ $errors->has('one_time_password-code') ? ' has-error' : '' }}">
                                <label for="one_time_password" class="control-label">
                                    {{ ucfirst(trans('generic.twofactorauthenticatorcode')) }}
                                </label>
                                <input
                                        id="one_time_password"
                                        name="one_time_password"
                                        class="form-control col-md-4"
                                        type="text"
                                        autofocus
                                        required
                                >
                            </div>
                            <button class="btn btn-primary" data-testid="authenticate-btn" type="submit">{{ ucfirst(trans('generic.twofactorauthenticate')) }}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
