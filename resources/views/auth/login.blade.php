@extends('layouts.tmpl3.app')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header">
                <h3 class="text-center font-weight-light my-4">{{ucfirst(trans('auth.login'))}}</h3>
            </div>
            <div class="card-body">
                <form class="form-horizontal" role="form" method="POST" action="{{ url('/login') }}">

                    {{ csrf_field() }}

                    <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }}">
                        <label class="small mb-1" for="inputEmailAddress">
                            {{ucfirst(trans('auth.emailaddress'))}}
                        </label>
                        <input class="form-control py-4" id="inputEmailAddress" name="email"
                               type="email" placeholder="Enter email address" autocomplete="off" required>
                    </div>

                    <div class="form-group{{ $errors->has('password') ? ' has-error' : '' }}">
                        <label for="inputPassword">{{ucfirst(trans('auth.password'))}}</label>
                        <input class="form-control py-4" id="inputPassword" name="password"
                               type="password" placeholder="Enter password" autocomplete="off" required>
                    </div>

                    <div class="form-group d-flex align-items-center justify-content-between mt-4 mb-0">
                        <button type="submit" class="btn btn-primary" data-testid="login-button">{{ucfirst(trans('auth.login'))}}</button>
                    </div>
                </form>
                @if (session('login_error'))
                    <div class="alert alert-danger mt-3">
                        {{ session('login_error') }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
