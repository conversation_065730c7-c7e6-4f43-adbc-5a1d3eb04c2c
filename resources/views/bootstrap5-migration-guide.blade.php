<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Bootstrap 5 Migration Guide</title>
    
    <!-- Main application styles (Bootstrap 4) -->
    <link href="{{ url('/css/tmpl3/styles.css') }}" rel="stylesheet">
    
    <style>
        /* Custom styles for the migration guide */
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .guide-section {
            margin-bottom: 40px;
        }
        
        .guide-title {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .change-item {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .change-title {
            font-weight: bold;
            color: #0d6efd;
            margin-bottom: 10px;
        }
        
        .code-block {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .bs4-code {
            background-color: rgba(0, 123, 255, 0.1);
        }
        
        .bs5-code {
            background-color: rgba(13, 110, 253, 0.1);
        }
        
        .change-label {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        
        .breaking-change {
            background-color: #dc3545;
            color: white;
        }
        
        .minor-change {
            background-color: #ffc107;
            color: #212529;
        }
        
        .new-feature {
            background-color: #198754;
            color: white;
        }
        
        .migration-steps {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .migration-steps ol {
            margin-bottom: 0;
        }
        
        .migration-steps li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1>Bootstrap 5 Migration Guide</h1>
                <div>
                    <a href="/bootstrap5-comparison" class="btn btn-info mr-2">View Component Comparison</a>
                    <a href="/home" class="btn btn-primary">Return to Dashboard</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    This guide documents the key differences between Bootstrap 4 and Bootstrap 5, 
                    and provides a step-by-step migration plan for the Class application.
                </div>
                
                <div class="migration-steps">
                    <h3>Migration Strategy</h3>
                    <ol>
                        <li>Use scoped Bootstrap 5 CSS for testing and comparison</li>
                        <li>Identify and document all breaking changes</li>
                        <li>Create a component-by-component migration plan</li>
                        <li>Implement changes incrementally, testing at each step</li>
                        <li>Update JavaScript initialization for interactive components</li>
                        <li>Complete full migration after thorough testing</li>
                    </ol>
                </div>
                
                <!-- Major Changes Section -->
                <div class="guide-section">
                    <h2 class="guide-title">Major Changes in Bootstrap 5</h2>
                    
                    <div class="change-item">
                        <span class="change-label breaking-change">Breaking Change</span>
                        <div class="change-title">jQuery Dependency Removed</div>
                        <p>Bootstrap 5 no longer requires jQuery as a dependency. All JavaScript components have been rewritten in vanilla JavaScript.</p>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block bs4-code">
                                    <!-- Bootstrap 4 -->
                                    $('#myModal').modal('show');<br>
                                    $('.tooltip').tooltip();
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="code-block bs5-code">
                                    <!-- Bootstrap 5 -->
                                    const myModal = new bootstrap.Modal(document.getElementById('myModal'));<br>
                                    myModal.show();<br><br>
                                    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');<br>
                                    [...tooltipTriggerList].map(el => new bootstrap.Tooltip(el));
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="change-item">
                        <span class="change-label breaking-change">Breaking Change</span>
                        <div class="change-title">Data Attribute Changes</div>
                        <p>All data attributes have been renamed with the <code>bs</code> prefix.</p>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block bs4-code">
                                    <!-- Bootstrap 4 -->
                                    &lt;button data-toggle="modal" data-target="#myModal"&gt;<br>
                                    &lt;button data-dismiss="modal"&gt;
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="code-block bs5-code">
                                    <!-- Bootstrap 5 -->
                                    &lt;button data-bs-toggle="modal" data-bs-target="#myModal"&gt;<br>
                                    &lt;button data-bs-dismiss="modal"&gt;
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="change-item">
                        <span class="change-label breaking-change">Breaking Change</span>
                        <div class="change-title">Form Controls</div>
                        <p>Form controls have been redesigned with a simpler class structure.</p>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block bs4-code">
                                    <!-- Bootstrap 4 -->
                                    &lt;div class="form-group"&gt;<br>
                                    &nbsp;&nbsp;&lt;label for="exampleInput"&gt;Example&lt;/label&gt;<br>
                                    &nbsp;&nbsp;&lt;input type="text" class="form-control" id="exampleInput"&gt;<br>
                                    &nbsp;&nbsp;&lt;small class="form-text text-muted"&gt;Helper text&lt;/small&gt;<br>
                                    &lt;/div&gt;
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="code-block bs5-code">
                                    <!-- Bootstrap 5 -->
                                    &lt;div class="mb-3"&gt;<br>
                                    &nbsp;&nbsp;&lt;label for="exampleInput" class="form-label"&gt;Example&lt;/label&gt;<br>
                                    &nbsp;&nbsp;&lt;input type="text" class="form-control" id="exampleInput"&gt;<br>
                                    &nbsp;&nbsp;&lt;div class="form-text"&gt;Helper text&lt;/div&gt;<br>
                                    &lt;/div&gt;
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="change-item">
                        <span class="change-label breaking-change">Breaking Change</span>
                        <div class="change-title">Close Button</div>
                        <p>The close button markup has changed from <code>&times;</code> to a new <code>.btn-close</code> class.</p>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block bs4-code">
                                    <!-- Bootstrap 4 -->
                                    &lt;button type="button" class="close" data-dismiss="modal" aria-label="Close"&gt;<br>
                                    &nbsp;&nbsp;&lt;span aria-hidden="true"&gt;&times;&lt;/span&gt;<br>
                                    &lt;/button&gt;
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="code-block bs5-code">
                                    <!-- Bootstrap 5 -->
                                    &lt;button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"&gt;&lt;/button&gt;
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Component-Specific Changes -->
                <div class="guide-section">
                    <h2 class="guide-title">Component-Specific Changes</h2>
                    
                    <div class="change-item">
                        <span class="change-label minor-change">Minor Change</span>
                        <div class="change-title">Cards</div>
                        <p>Card markup remains mostly the same, with minor styling changes.</p>
                    </div>
                    
                    <div class="change-item">
                        <span class="change-label breaking-change">Breaking Change</span>
                        <div class="change-title">Dropdowns</div>
                        <p>Dropdown markup has changed, and the JavaScript initialization is different.</p>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block bs4-code">
                                    <!-- Bootstrap 4 -->
                                    $('.dropdown-toggle').dropdown();
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="code-block bs5-code">
                                    <!-- Bootstrap 5 -->
                                    const dropdownElementList = document.querySelectorAll('.dropdown-toggle');<br>
                                    const dropdownList = [...dropdownElementList].map(el => new bootstrap.Dropdown(el));
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="change-item">
                        <span class="change-label breaking-change">Breaking Change</span>
                        <div class="change-title">Select Inputs</div>
                        <p>Select inputs now use <code>.form-select</code> instead of <code>.form-control</code>.</p>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="code-block bs4-code">
                                    <!-- Bootstrap 4 -->
                                    &lt;select class="form-control" id="exampleSelect"&gt;
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="code-block bs5-code">
                                    <!-- Bootstrap 5 -->
                                    &lt;select class="form-select" id="exampleSelect"&gt;
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="change-item">
                        <span class="change-label new-feature">New Feature</span>
                        <div class="change-title">Utilities API</div>
                        <p>Bootstrap 5 introduces a new Utilities API for creating custom utility classes.</p>
                    </div>
                </div>
                
                <!-- Migration Plan -->
                <div class="guide-section">
                    <h2 class="guide-title">Migration Plan for Class Application</h2>
                    
                    <div class="change-item">
                        <div class="change-title">Phase 1: Preparation</div>
                        <ol>
                            <li>Document all Bootstrap 4 components used in the application</li>
                            <li>Identify custom Bootstrap overrides and variables</li>
                            <li>Create test pages for comparing Bootstrap 4 and 5 components</li>
                        </ol>
                    </div>
                    
                    <div class="change-item">
                        <div class="change-title">Phase 2: Component Migration</div>
                        <ol>
                            <li>Start with non-interactive components (typography, cards, etc.)</li>
                            <li>Update form controls to use new Bootstrap 5 classes</li>
                            <li>Migrate interactive components (modals, dropdowns, etc.)</li>
                            <li>Update JavaScript initialization for all components</li>
                        </ol>
                    </div>
                    
                    <div class="change-item">
                        <div class="change-title">Phase 3: Testing and Refinement</div>
                        <ol>
                            <li>Test all components thoroughly</li>
                            <li>Fix any visual or functional issues</li>
                            <li>Optimize CSS and JavaScript</li>
                            <li>Remove jQuery dependency if possible</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
