@extends('layouts.tmpl3.app-bs5')

@section('content')
<!-- Bootstrap 5 Page Example -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Bootstrap 5 Page Example
                </div>
                <div class="card-body">
                    <h2>Bootstrap 5 Page Example</h2>
                    <p class="lead">
                        This page uses the Bootstrap 5 layout (<code>app-bs5.blade.php</code>) instead of the Bootstrap 4 layout.
                        Notice that you don't need to add any special classes - all elements automatically use Bootstrap 5 styles.
                    </p>

                    <div class="alert alert-info">
                        <strong>Migration Strategy:</strong> Use <code>@extends('layouts.tmpl3.app-bs5')</code> for pages you want to migrate to Bootstrap 5.
                        This allows you to migrate pages one by one without affecting the rest of the application.
                    </div>
                </div>
            </div>

            <!-- Bootstrap 5 Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-code me-1"></i>
                    Bootstrap 5 Components
                </div>
                <div class="card-body">
                    <h4>This is a Bootstrap 5 Card</h4>
                    <p>All components on this page use Bootstrap 5 styles.</p>

                    <form>
                        <div class="mb-3">
                            <label for="bs5Input" class="form-label">Email address</label>
                            <input type="email" class="form-control" id="bs5Input" placeholder="<EMAIL>">
                            <div class="form-text">We'll never share your email with anyone else.</div>
                        </div>
                        <div class="mb-3">
                            <label for="bs5Select" class="form-label">Example select</label>
                            <select class="form-select" id="bs5Select">
                                <option>1</option>
                                <option>2</option>
                                <option>3</option>
                            </select>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="bs5Check">
                            <label class="form-check-label" for="bs5Check">Check me out</label>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>

            <!-- Bootstrap 5 Components -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-bell me-1"></i>
                            Alerts
                        </div>
                        <div class="card-body">
                            <div class="alert alert-primary" role="alert">
                                This is a primary alert—check it out!
                            </div>
                            <div class="alert alert-secondary" role="alert">
                                This is a secondary alert—check it out!
                            </div>
                            <div class="alert alert-success" role="alert">
                                This is a success alert—check it out!
                            </div>
                            <div class="alert alert-danger" role="alert">
                                This is a danger alert—check it out!
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-square me-1"></i>
                            Buttons
                        </div>
                        <div class="card-body">
                            <h5>Bootstrap 5 Buttons</h5>
                            <button type="button" class="btn btn-primary">Primary</button>
                            <button type="button" class="btn btn-secondary">Secondary</button>
                            <button type="button" class="btn btn-success">Success</button>
                            <button type="button" class="btn btn-danger">Danger</button>
                            <button type="button" class="btn btn-warning">Warning</button>
                            <button type="button" class="btn btn-info">Info</button>
                            <button type="button" class="btn btn-light">Light</button>
                            <button type="button" class="btn btn-dark">Dark</button>
                            <button type="button" class="btn btn-link">Link</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bootstrap 5 Modal -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-window-maximize me-1"></i>
                    Modal
                </div>
                <div class="card-body">
                    <h5>Bootstrap 5 Modal</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">
                        Launch demo modal
                    </button>

                    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    Modal body text goes here.
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    <button type="button" class="btn btn-primary">Save changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Migration Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-lightbulb me-1"></i>
                    Migration Tips
                </div>
                <div class="card-body">
                    <h4>Tips for Page-by-Page Migration</h4>
                    <ol>
                        <li>Change the layout from <code>@extends('layouts.tmpl3.app')</code> to <code>@extends('layouts.tmpl3.app-bs5')</code></li>
                        <li>Update spacing classes: <code>mr-*</code> to <code>me-*</code>, <code>ml-*</code> to <code>ms-*</code></li>
                        <li>Update form structure: <code>form-group</code> to <code>mb-3</code></li>
                        <li>Update select inputs: <code>form-control</code> to <code>form-select</code></li>
                        <li>Update data attributes: <code>data-toggle</code> to <code>data-bs-toggle</code>, etc.</li>
                        <li>Update close buttons: <code>&times;</code> to <code>btn-close</code></li>
                    </ol>

                    <div class="alert alert-warning mt-4">
                        <strong>Note:</strong> Some JavaScript components might need to be reinitialized for Bootstrap 5.
                        Check the Bootstrap 5 documentation for details.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Example of how to initialize Bootstrap 5 components
    document.addEventListener('DOMContentLoaded', function() {
        // You can add specific Bootstrap 5 initialization code here if needed
        console.log('Bootstrap 5 page loaded');
    });
</script>
@endsection
