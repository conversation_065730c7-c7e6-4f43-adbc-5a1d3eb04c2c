{{-- ----------------------------- --}}
{{-- specs of registration for the --}}
{{-- page to ask student to sign   --}}
{{-- (sign_registration.blade.php) --}}
{{-- the registration - preferably --}}
{{-- before courses start          --}}
{{-- ----------------------------- --}}
{{-- this page is NOT behind UN/PW --}}
{{-- ----------------------------- --}}

{{-- Inject Scolavisa library --}}
@inject('Mydat2Ndat', 'Scolavisa\scolib\Mydat2Ndat')

{{-- NAW --}}
<h4>{{ucfirst(trans('generic.student'))}} {{ucfirst(trans('generic.registration'))}}</h4>
<dl class="dl-horizontal">
    <dt>{{ucfirst(trans('generic.name'))}}</dt>
    <dd>{{$registration->student->name}}</dd>
    <dt>{{ucfirst(trans('generic.address'))}}</dt>
    <dd>{{$registration->student->address}}</dd>
    <dd>{{$registration->student->zipcode}} {{$registration->student->city}}</dd>
    <dt>{{ucfirst(trans('generic.email'))}}</dt>
    <dd>{{$registration->student->email}}</dd>
</dl>
{{--course info--}}
<h4>{{ucfirst(trans('generic.course'))}}</h4>
<dl class="dl-horizontal">
    <dt>{{ucfirst(trans('generic.coursename'))}}</dt>
    <dd>
        {{$registration->course->name}}
        {{(isset($registration->course->recurrenceoption) ? ": " . $registration->course->recurrenceoption->description : '')}}
    </dd>
    <dt>{{ucfirst(trans('generic.price'))}}</dt>
    <dd>
        @if(empty($registration->incidental_price_ex_tax))
            &euro;
            @if($registration->student->age < 21)
                {{-- no tax for subadult --}}
                {{sprintf("%01.2f", round($registration->course->price_ex_tax_sub_adult,2)) . ' / ' . trans('priceinterval.'.$registration->course->price_is_per) }}
            @else
                {{sprintf("%01.2f", round($registration->course->price_invoice,2)) . ' / ' . trans('priceinterval.'.$registration->course->price_is_per) }}
                ({{ trans('generic.including') }} {{  $courseTaxRate }}% {{ trans('generic.tax') }})
            @endif
        @else
            &euro; {{sprintf("%01.2f", round($registration->incidental_price_ex_tax * (1+($courseTaxRate/100)),2)) . ' / ' . trans('priceinterval.'.$registration->course->price_is_per) }}
            ({{ trans('generic.including') }} {{  $courseTaxRate }}% {{ trans('generic.tax') }})
        @endif
        <br><small>{{ucfirst(trans('generic.explaincoursechange'))}}</small>
    </dd>
    <dt>{{ucfirst(trans('generic.registrationdate'))}}</dt>
    <dd>{{$Mydat2Ndat::getNdat($registration->start_date)}}</dd>
</dl>
<h4>{{ucfirst(trans('generic.permissionautobanktransfer'))}}</h4>
<dl class="dl-horizontal">
    @if($registration->student->permission_auto_banktransfer == 'Ja')
        <dt>{{ucfirst(trans('generic.bankaccountname'))}}</dt>
        <dd>{{$registration->student->bankaccount_name}}</dd>
        <dt>{{ucfirst(trans('generic.bankaccountnumber'))}}</dt>
        <dd>{{$registration->student->bankaccount_number}}</dd>
        <dt>{{ucfirst(trans('generic.mandatenumber'))}}</dt>
        <dd>{{$registration->student->mandate_number}}</dd>
    @else
        {{ucfirst(trans('generic.no'))}}
    @endif
</dl>
<hr>
<ul>
    @if(strlen($termsUrl) > 0)
        <li>
            <a
                    href="{{$termsUrl}}"
                    target="_blank">
                {{ucfirst(trans('generic.download'))}} {{ucfirst(trans('generic.termsandconditions'))}}
            </a>
        </li>
    @endif
    @if(strlen($privacyUrl) > 0)
        <li>
            <a
                    href="{{$privacyUrl}}"
                    target="_blank">
                {{ucfirst(trans('generic.download'))}} {{ucfirst(trans('generic.socialmediaprivacyetiquette'))}}
            </a>
        </li>
    @endif
</ul>
