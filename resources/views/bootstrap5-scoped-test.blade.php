<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Bootstrap 5 Scoped Test</title>

    <!-- Main application styles (Bootstrap 4) -->
    <link href="{{ url('/css/tmpl3/styles.css') }}" rel="stylesheet">

    <style>
        /* Custom styles for the test page */
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        /* This ensures that Bootstrap 5 styles only apply within the .bs5-container */
        .bs5-container {
            padding: 20px;
            border: 2px solid #6c757d;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>

    <!-- Bootstrap 5 CSS with .bs5 prefix -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap5-scoped@1.0.0/css/bootstrap-scoped.min.css" />
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1>Bootstrap 5 Scoped Test</h1>
                <a href="/home" class="btn btn-primary">Return to Dashboard</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <strong>Bootstrap 4 Section:</strong> This part of the page uses the main application's Bootstrap 4 styles.
                </div>

                <div class="card">
                    <div class="card-header">Bootstrap 4 Card</div>
                    <div class="card-body">
                        <h5 class="card-title">Card Title (Bootstrap 4)</h5>
                        <p class="card-text">This card is styled with Bootstrap 4 from the main application.</p>
                        <button class="btn btn-primary">Bootstrap 4 Button</button>
                    </div>
                </div>

                <hr class="my-4">

                <h2>Bootstrap 5 Section (Scoped)</h2>
                <p>The content below is styled with Bootstrap 5, but the styles are scoped to only apply within elements with the .bs5 class.</p>

                <!-- Bootstrap 5 scoped container -->
                <div class="bs5-container">
                    <div class="bs5">
                        <div class="alert alert-warning">
                            <strong>Bootstrap 5 Section:</strong> This part of the page uses Bootstrap 5 styles, scoped to elements with the .bs5 class.
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">Bootstrap 5 Card</div>
                                    <div class="card-body">
                                        <h5 class="card-title">Card Title (Bootstrap 5)</h5>
                                        <p class="card-text">This card is styled with Bootstrap 5.</p>
                                        <button class="btn btn-primary">Bootstrap 5 Button</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <form>
                                    <div class="mb-3">
                                        <label for="exampleInput" class="form-label">Example Input</label>
                                        <input type="text" class="form-control" id="exampleInput">
                                    </div>
                                    <div class="mb-3">
                                        <label for="exampleSelect" class="form-label">Example Select</label>
                                        <select class="form-select" id="exampleSelect">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-success">Submit</button>
                                </form>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h3>Bootstrap 5 Buttons</h3>
                                <button type="button" class="btn btn-primary">Primary</button>
                                <button type="button" class="btn btn-secondary">Secondary</button>
                                <button type="button" class="btn btn-success">Success</button>
                                <button type="button" class="btn btn-danger">Danger</button>
                                <button type="button" class="btn btn-warning">Warning</button>
                                <button type="button" class="btn btn-info">Info</button>
                                <button type="button" class="btn btn-light">Light</button>
                                <button type="button" class="btn btn-dark">Dark</button>
                                <button type="button" class="btn btn-link">Link</button>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h3>Bootstrap 5 Alerts</h3>
                                <div class="alert alert-primary" role="alert">
                                    This is a primary alert—check it out!
                                </div>
                                <div class="alert alert-secondary" role="alert">
                                    This is a secondary alert—check it out!
                                </div>
                                <div class="alert alert-success" role="alert">
                                    This is a success alert—check it out!
                                </div>
                                <div class="alert alert-danger" role="alert">
                                    This is a danger alert—check it out!
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <h3>Bootstrap 5 Form Controls</h3>
                                <form>
                                    <div class="mb-3">
                                        <label for="formEmail" class="form-label">Email address</label>
                                        <input type="email" class="form-control" id="formEmail" placeholder="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="formTextarea" class="form-label">Example textarea</label>
                                        <textarea class="form-control" id="formTextarea" rows="3"></textarea>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="formCheck">
                                        <label class="form-check-label" for="formCheck">Check me out</label>
                                    </div>
                                    <div class="mb-3">
                                        <label for="formRange" class="form-label">Example range</label>
                                        <input type="range" class="form-range" id="formRange">
                                    </div>
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
            crossorigin="anonymous"></script>
</body>
</html>
