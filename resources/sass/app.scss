// Fonts
@import url("https://fonts.googleapis.com/css?family=Raleway:300,400,600");
// Variables
@import "variables";
// Bootstrap (v.3): https://getbootstrap.com/docs/3.3/
@import "~bootstrap-sass/assets/stylesheets/bootstrap";
// vue-bootstrap-datetimepicker
@import "~pc-bootstrap4-datetimepicker/src/sass/bootstrap-datetimepicker-build.css";

// font awesome: http://fontawesome.io/
// ~ will resolve to the node_modules path. tells webpack this is not a relative path
$fa-font-path: "~@fortawesome/fontawesome-free/webfonts";
@import "~@fortawesome/fontawesome-free/scss/fontawesome.scss";
@import "~@fortawesome/fontawesome-free/scss/regular";

// Gentelella: https://colorlib.com/polygon/gentelella/
@import "~gentelella/src/scss/custom";
// material radio button
@import "material_radio";

@media(device-width: 768px) and (device-height: 1024px){
  ::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 7px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: rgba(0,0,0,.5);
    -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
  }
}

%environment-indicator-position {
  width: 120px;
  height: 40px;
  z-index: 99999;
  position: fixed;
  top:0;
  left:0;
}

.env-development > .environment {
  @extend %environment-indicator-position;
  // background-image: url(/css/images/header_environment_devel.png);
}

.env-test > .environment {
  @extend %environment-indicator-position;
  // background-image: url(/css/images/header_environment_test.png);
}

.title {
  font-size: 6rem;
}

.title-small {
  font-size: 4rem;
}

.subtitle {
  font-size: 2rem;
}

.m-b-md {
  margin-bottom: 30px;
}
.domain_info {
  color: white;
  margin-left: .5em;
}

// END homepage

.panel-heading h3 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: normal;
  width: 75%;
  padding-top: 8px;
}

.alertmessage {
  color: $brand-danger !important;
}

.alert-small {
  margin-bottom: 1px;
  height: 30px;
  line-height:30px;
  padding:0 15px;
  border-radius: 0.3em !important;
}
.alert-warning {
  color:black !important;
}

table {
  .compacttable {
    td, th {
      padding: .3em .5em !important;
    }
  }
  a:not(.no_underline) {
    text-decoration: underline !important;
  }
  a.no-margin-bottom {
    margin-bottom: 0;
  }
}

.markcurrent, a.markcurrent {
  color: $brand-danger !important;
}

#studentFilterbox, #CourseFilterbox {
  margin-bottom: 1em;
}

div.unregistered {
  background-color: $brand-success-light;
}

.incomplete {
  color: $brand-danger;
}

.complete {
  color: $brand-success;
  display: inline-block;
}

.nochecklist {
  color: $text-gray;
}

.searchbox {
  margin-bottom: 5px;
}

.test-warning {
  width: 85%;
  text-align: left;
  color: $brand-high-danger;
  border-top: $brand-high-danger;
  border-bottom: solid 1px $brand-high-danger;
  margin-left: 15px;
  margin-bottom: 2em;
  font-weight: bold;
  font-size: 120%
}

.pre-scrollable {
  max-height: 500px !important;
  overflow-x: hidden;
}

.container {
  padding-bottom: 30px !important;
}

footer {
  margin-top: -22px;
}

// checkbox switch
.material-switch {
  > input[type="checkbox"] {
    display: none;
    &:checked {
      + label::before {
        background: inherit;
        opacity: 0.5;
      }
      + label::after {
        background: inherit;
        left: 20px;
      }
    }
  }
  > label {
    cursor: pointer;
    height: 0;
    position: relative;
    width: 40px;
    &::before {
      background: rgb(0, 0, 0);
      box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
      border-radius: 8px;
      content: '';
      height: 16px;
      margin-top: -8px;
      position: absolute;
      opacity: 0.3;
      transition: all 0.4s ease-in-out;
      width: 40px;
    }
    &::after {
      background: rgb(255, 255, 255);
      border-radius: 16px;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
      content: '';
      height: 24px;
      left: -4px;
      margin-top: -8px;
      position: absolute;
      top: -4px;
      transition: all 0.3s ease-in-out;
      width: 24px;
    }
  }
}

.extra-vpadd {
  padding-top: 2.2em;
}
.reset-vpadd {
  margin-top: -2.2em;
}
.extra-vpadd1 {
  padding-top: 1.8em;
}
.extra-vpadd2 {
  padding-top: 0.8em;
}
.extra-vpadd3 {
  padding-top: 0.4em;
}

.ml-1 {
  margin-left: 1em;
}
.mt-1 {
  margin-top: 1em;
}
.mr-1 {
  margin-right: 1em;
}
.mr-05 {
  margin-right: .5em;
}
.mb-1 {
  margin-bottom: 1em;
}
.mb-05 {
  margin-bottom: .5em;
}
.mt-2 {
  margin-top: 1em;
}
.pr-1 {
  padding-right: 1em;
}
.reposition-4 {
  position: relative;
  top: -4px;
}
.bb {
  border-bottom: solid #aaa 1px;
}

// inline buttons need very little margin
.btn-course-row {
  margin-right: inherit;
}

// end checkbox switch
// checkbox active
*[type="checkbox"].regular-checkbox {
  -webkit-appearance: none;
  padding: 8px;
  border: solid $class-red 1px;
  border-radius: 3px;
  display: inline-block;
  top: 3px;
  &:active, &:checked:active {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  &:checked {
    background-color: #e9ecee;
    color: $class-red;
    &:after {
      content: '\2714';
      font-size: 14px;
      position: absolute;
      top: 0;
      left: 3px;
      color: $class-red;
    }
  }
  &:focus {
    outline: 0;
  }
  &:hover {
    cursor: pointer;
  }
}

li {
  *[type="checkbox"].regular-checkbox {
    top: -3px;
  }
}

// end checkbox active

.panel_scroll_280 {
  max-height: 280px;
  overflow-y: auto;
  overflow-x: hidden;
}

.panel_scroll_260 {
  max-height: 260px;
  overflow-y: auto;
  overflow-x: hidden;
}

.panel_scroll_220 {
  max-height: 220px;
  overflow-y: auto;
  overflow-x: hidden;
}

*[v-cloak] {
  display: none;
}

.newCourseRow, .needsToBeSaved, .signRequestLabelAfter {
  display: none;
}

.red {
  color: $brand-danger;
}

.appointment {
  td.editable {
    padding-bottom: 2px;
    &:hover {
      cursor: zoom-in;
      border: 1px solid $text-color;
    }
  }
}

// Full Calendar
.fc-event-container {
  cursor: pointer;
}
.fc-list-empty {
  vertical-align: top;
  padding-top: 3em;
}
// glyphicon empty placeholder
// e.g. <i class="glyphicon glyphicon-none"></i>
.glyphicon-none:before {
  content: "\E045";
  color: transparent !important;
}
.highlight_danger {
  &:hover {
    color: $brand-danger;
  }
}
.highlight_success {
  &:hover {
    color: $brand-success;
  }
}
// this is used to compensate the somewhat smaller fa icons compared to glyph icons
// usefull if used next to one another
.fa-compensate {
  font-size: 1.5rem !important;
}


div.zebra > div:nth-of-type(odd) {
  background: #f7f7f7;
}

.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
  background: $text-gray;
  color: $text-color;
  opacity: 0.45;
  border: $text-color solid 1px;
}

.btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled].focus, fieldset[disabled] .btn-success:hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success.focus {
  background-color: $text-gray;
  border-color: $text-color;
}
.bg-success {
  background-color: $brand-success;
}
.bg-success-semi {
  background-color: rgba($brand-success, 0.3);
  z-index: -1;
}
.badge {
  &:hover {
    background-color: $brand-danger;
  }

}
.badge-active {
  background-color: $brand-success;
}

.text-success {
  color: $brand-success;
}

.text-danger {
  color: $brand-danger;
}

@media print {

  .left_col, #individualDropZone, #newGroupDropZone, .nav_menu, .noDisplayForPrint {
    display: none;
  }

}

.tooltip-inner {
  white-space:pre-wrap;
  max-width: 400px !important;
}
.tooltip a {
  color: white !important;
}

.extra-sizing {
  display:inline-block;
  position:relative;
  width:200px;
  height:2em;
  top:-5px
}
.flex-block {
  display:inline-flex
}
.attach-btn {
  margin-right: -4px;
}
.btn-single-line {
  margin-bottom: -3px;
  line-height: 1.2;
}

#setupwizard {
  li {
    list-style: none;
  }
}
/** used in planning page to move the badges inline with the delete buttons */
.badge-move-up {
  margin-bottom: 6px;
}
/* calendar icons*/
.cal-loc-icon {
  float:right;
  margin-top:4px
}

/** Vue SFC local scss no longer compiles */
/** possible bug in laravel-mix */
/** For now - transfer styles for SFC's using lang="scss" to this global location */
/** hopefully without naming clashes... */

/** resources/js/components/Preference.vue */
.btn-weekplanner {
  margin-bottom: 1.1rem;
}
.day {
  margin-right: 1rem;
  margin-bottom: 1rem;
  border: solid black 2px;
  display: inline-block;
  width: 10rem;
  text-align: center;

  .dayname {
    background-color: #26BB9C;
    border-bottom: solid black 2px;
    color: white;
  }

  .weekend {
    background-color: #ededed;
    color: black;
  }

  .hour {
    /* not selectable */
    /* because we do this programmatically */
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
    user-select: none;
    border-bottom: solid #2A3F54 1px;

    &:last-child {
      border-bottom: none;
    }
  }

  .markMe {
    background-color: #3E5367;
    color: white;
  }

}

.lang-img {
  height: 2rem;
  padding-right: 1rem;
}
