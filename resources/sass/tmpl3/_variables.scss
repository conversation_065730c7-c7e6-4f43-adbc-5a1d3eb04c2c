// CLASS Brand colors
$classred: #C75454;
$classblue: #6A6E8F ;
$classdarkblue: #425668;
$classgreen: #4C9B5E;
$classyellow: #EDA707;
$classbgblue: #6A6E8F;
$classlightblue: #66B6C3;
$classlight: #f8f9fa;
$classdark:#343a40;


// Import Bootstrap variables for use within theme
@import "~bootstrap/scss/functions";
@import "~bootstrap/scss/variables";

// Import spacing variables
@import "./variables/spacing.scss";

// Import navigation variables
@import "./variables/navigation.scss";

// --------------------------------------------------------------
// Bootstrap 4 color overrides
// --------------------------------------------------------------
// These should go before bootstrap include in styles.scss
// Bootstrap will use it's own definition if it's not present
// here because of the !default flag for all styles in BS4

// BS override
$primary: $classblue;
$success: $classgreen;
$danger: $classred;
$warning: $classyellow;

// Site Theme
$theme-colors: (
    "primary": $primary,
    "success": $success,
    "danger": $danger,
    "warning": $warning
);

