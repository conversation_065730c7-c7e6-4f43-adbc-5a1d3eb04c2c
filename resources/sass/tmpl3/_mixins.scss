// scss maps for breakpoints
// at these points and bigger: 640px, 1024px, 1400px
$breakpoints-up: ("medium": "40em", "large": "64em", "xlarge": "87.5em");
// at these points and smaller: 639px, 1023px, 1399px (no overlapping points)
$breakpoints-down: ("small": "39.9375em", "medium": "63.9375em", "large": "87.4375em");

// $size: one of medium, large, xlarge
@mixin breakpoint-up($size) {
  @media (min-width: map-get($map: $breakpoints-up, $key: $size)) {
    @content;
  }
}

// $size: one of small, medium, large
@mixin breakpoint-down($size) {
  @media (max-width: map-get($map: $breakpoints-down, $key: $size)) {
    @content;
  }
}
