<?php
return [
    "accepted" => "The :attribute must be accepted.",
    "active_url" => "The :attribute is not a valid URL.",
    "after" => "The :attribute must be a date after :date.",
    "after_or_equal" => "The :attribute must be a date after or equal to :date",
    "alpha" => "The :attribute may only contain letters.",
    "alpha_dash" => "The :attribute may only contain letters, numbers, and dashes.",
    "alpha_num" => "The :attribute may only contain letters and numbers.",
    "array" => "The :attribute must be an array.",
    "before" => "The :attribute must be a date before :date.",
    "before_or_equal" => "The :attribute must be a date before or equal to :date",
    "between" => [
        "array" => "The :attribute must have between :min and :max items.",
        "file" => "The :attribute must be between :min and :max kilobytes.",
        "numeric" => "The :attribute must be between :min and :max.",
        "string" => "The :attribute must be between :min and :max characters."
    ],
    "boolean" => "The :attribute field must be true or false.",
    "confirmed" => "The :attribute confirmation does not match.",
    "custom" => [
        "ndat_greater_equal" => ":field1 must be greater than or equal to :field2",
        "ndat_year_apart" => ":field1 must be 1 year bigger or equal to :field2"
    ],
    "date" => "The :attribute is not a valid date.",
    "date_format" => "The :attribute does not match the format :format.",
    "different" => "The :attribute and :other must be different.",
    "digits" => "The :attribute must be :digits digits.",
    "digits_between" => "The :attribute must be between :min and :max digits.",
    "dimensions" => "The :attribute has invalid image dimensions.",
    "distinct" => "The :attribute field has a duplicate value.",
    "email" => "The :attribute must be a valid email address.",
    "exists" => "The selected :attribute is invalid.",
    "file" => "The :attribute must be a file.",
    "filled" => "The :attribute field is required.",
    "gte" => ["numeric" => ":attribute must be greater of equal to :value"],
    "image" => "The :attribute must be an image.",
    "in" => "The selected :attribute is invalid.",
    "in_array" => "The :attribute field does not exist in :other.",
    "integer" => "The :attribute must be an integer.",
    "ip" => "The :attribute must be a valid IP address.",
    "ipv4" => "The :attribute must be a valid IPv4 address.",
    "ipv6" => "The :attribute must be a valid IPv6 address.",
    "json" => "The :attribute must be a valid JSON string.",
    "max" => [
        "array" => "The :attribute may not have more than :max items.",
        "file" => "The :attribute may not be greater than :max kilobytes.",
        "numeric" => "The :attribute may not be greater than :max.",
        "string" => "The :attribute may not be greater than :max characters."
    ],
    "mimes" => "The :attribute must be a file of type: :values.",
    "mimetypes" => "The :attribute must be a file of type: :values.",
    "min" => [
        "array" => "The :attribute must have at least :min items.",
        "file" => "The :attribute must be at least :min kilobytes.",
        "numeric" => "The :attribute must be at least :min.",
        "string" => "The :attribute must be at least :min characters."
    ],
    "not_in" => "The selected :attribute is invalid.",
    "numeric" => "The :attribute must be a number.",
    "present" => "The :attribute field must be present.",
    "pw_check" => "This password is not correct",
    "pw_strong" => "Your password lacks some strength. Please provide a stronger password.",
    "regex" => "The :attribute format is invalid.",
    "required" => "The :attribute field is required.",
    "required_if" => "The :attribute field is required when :other is :value.",
    "required_unless" => "The :attribute field is required unless :other is in :values.",
    "required_with" => "The :attribute field is required when :values is present.",
    "required_with_all" => "The :attribute field is required when :values is present.",
    "required_without" => "The :attribute field is required when :values is not present.",
    "required_without_all" => "The :attribute field is required when none of :values are present.",
    "same" => "The :attribute and :other must match.",
    "size" => [
        "array" => "The :attribute must contain :size items.",
        "file" => "The :attribute must be :size kilobytes.",
        "numeric" => "The :attribute must be :size.",
        "string" => "The :attribute must be :size characters."
    ],
    "string" => "The :attribute must be a string.",
    "timezone" => "The :attribute must be a valid zone.",
    "unique" => "The :attribute has already been taken.",
    "uploaded" => "The :attribute failed to upload.",
    "url" => "The :attribute format is invalid."
];
