<?php
return [
    "accepted" => ":attribute dient te worden geaccepteerd.",
    "active_url" => ":attribute is geen geldige URL.",
    "after" => ":attribute dient een datum te zijn na :date.",
    "after_or_equal" => "De :attribute moet groter of gelijk zijn aan :date",
    "alpha" => ":attribute mag alleen letters bevatten.",
    "alpha_dash" => ":attribute mag alleen letters, nummers, and strepen bevatten.",
    "alpha_num" => ":attribute mag alleen letters en nummers bevatten.",
    "array" => ":attribute moet een array zijn.",
    "before" => ":attribute moet een datum zijn eerder dan :date.",
    "before_or_equal" => "De :attribute moet kleiner of gelijk zijn aan :date",
    "between" => [
        "array" => ":attribute moet tussen :min en :max items bevatten.",
        "file" => ":attribute moet tussen :min en :max kilobytes zijn.",
        "numeric" => ":attribute moet tussen :min en :max liggen.",
        "string" => ":attribute moet tussen :min en :max karakters lang zijn."
    ],
    "boolean" => ":attribute kan enkel true of false zijn.",
    "confirmed" => ":attribute bevestiging komt niet overeen.",
    "custom" => [
        "ndat_greater_equal" => ":field1 moet groter zijn dan of gelijk zijn aan :field2",
        "ndat_year_apart" => ":field1 moet 1 jaar groter zijn of gelijk zijn aan :field2"
    ],
    "date" => ":attribute is geen geldige datum.",
    "date_format" => ":attribute komt niet overeen met het formaat :format.",
    "different" => ":attribute en :other dienen verschillend te zijn.",
    "digits" => ":attribute moet :digits cijfers zijn.",
    "digits_between" => ":attribute moet tussen :min en :max cijfers zijn.",
    "dimensions" => ":attribute heeft een ongeldige grootte.",
    "distinct" => ":attribute heeft een dubbele waarde.",
    "email" => ":attribute dient een geldig emailadres te zijn.",
    "exists" => "Het geselecteerde :attribute is ongeldig.",
    "file" => ":attribute moet een bestand zijn.",
    "filled" => ":attribute veld is verplicht.",
    "gte" => ["numeric" => ":attribute moet groter of gelijk zijn aan :value"],
    "image" => ":attribute dient een afbeelding te zijn.",
    "in" => "Het geselecteerde :attribute is ongeldig.",
    "in_array" => ":attribute komt niet voor in :other.",
    "integer" => ":attribute dient een geheel getal te zijn.",
    "ip" => ":attribute moet een geldig IP adres zijn.",
    "ipv4" => ":attribute moet een geldig IPv4 adres zijn.",
    "ipv6" => ":attribute moet een geldig IPv6 adres zijn.",
    "json" => ":attribute moet een geldige JSON string zijn.",
    "max" => [
        "array" => ":attribute mag niet meer dan :max items bevatten.",
        "file" => ":attribute mag niet groter zijn dan :max kilobytes.",
        "numeric" => ":attribute mag niet groter zijn dan :max.",
        "string" => ":attribute mag niet groter zijn dan :max karakters."
    ],
    "mimes" => ":attribute dient een bestand te zijn van het type: :values.",
    "mimetypes" => ":attribute dient een bestand te zijn van het type: :values.",
    "min" => [
        "array" => ":attribute dient minimaal :min items te bevatten.",
        "file" => ":attribute dient minimaal :min kilobytes te zijn.",
        "numeric" => ":attribute dient minimaal :min te zijn.",
        "string" => ":attribute dient minimaal :min karakters te bevatten."
    ],
    "not_in" => "Het geselecteerde :attribute is ongeldig.",
    "numeric" => "Het :attribute dient een nummer te zijn.",
    "present" => "Het :attribute dient aanwezig te zijn.",
    "pw_check" => "Het wachtwoord is niet correct",
    "pw_strong" => "Het wachtwoord is niet sterk genoeg. Vul a.u.b. een sterker wachtwoord in.",
    "regex" => "Het :attribute formaat is ongeldig.",
    "required" => "Het :attribute veld is verplicht.",
    "required_if" => "Het :attribute veld is verplicht wanneer :other is :value.",
    "required_unless" => "Het :attribute veld is verplicht, tenzij :other is in :values.",
    "required_with" => "Het :attribute veld is verplicht wanneer :values aanwezig is.",
    "required_with_all" => "Het :attribute veld is verplicht wanneer :values aanwezig is.",
    "required_without" => "Het :attribute veld is verplicht wanneer :values niet aanwezig is.",
    "required_without_all" => "Het :attribute veld is verplicht wanneer geen van :values aanwezig is.",
    "same" => "Het :attribute en :other moeten hetzelfde zijn.",
    "size" => [
        "array" => ":attribute moet :size items bevatten.",
        "file" => ":attribute moet :size kilobytes groot zijn.",
        "numeric" => ":attribute moet :size zijn.",
        "string" => ":attribute moet :size karakters lang zijn."
    ],
    "string" => ":attribute moet een string zijn.",
    "timezone" => ":attribute moet een geldige tijdszone zijn.",
    "unique" => "deze waarde is al in gebruik.",
    "uploaded" => "Het uploaden van :attribute is mislukt.",
    "url" => ":attribute formaat is ongeldig."
];
