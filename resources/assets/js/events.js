import {EventBus} from "./app";
import { Autocomplete } from 'element-ui';

export function importics() {

    Vue.component('fileupload', require('./components/FileUpload'));
    Vue.component('Autocomplete', require('vuejs-auto-complete'));
    Vue.component(Autocomplete.name, Autocomplete);

    new Vue({
        el: "#importics_page",

        data: {
            fileContents: [],
            events: [],
            analysing: false
        },

        methods: {
            fileReceived() {
                // send file to backend
                const fileList = document.querySelector('#fileitem').files;
                const theFile = fileList[0];
                let reader = new FileReader();
                // capture the file information.
                reader.onload = (() => {
                    EventBus.$emit("clearHelptext");
                    return (e) => {
                        this.fileContents = e.target.result.split('\n');
                    };
                })(theFile);
                // Read in the image file as a data URL.
                EventBus.$emit("changeHelptext", "Reading");
                reader.readAsText(theFile);
            },

            analyseICSFile() {
                let indexCounter = -1;
                let localEvents = [];
                let parts = [];
                EventBus.$emit("changeHelptext", "Analysing");
                this.analysing = true;
                EventBus.$emit("changeHelptext", " ");

                // if line starts with a space, it's a line wrap of a long line, and should be handled with the previous line
                // also remove all trailing spaces
                this.fileContents.forEach((e, index, me) => {
                    if (e.substr(0, 1) === " ") {
                        me[(index - 1)] = me[(index - 1)] + e.trim();
                    } else {
                        me[index] = e.trim();
                    }
                });
                // remove indexes that we no longer need because of fixed line wrap
                this.fileContents.forEach((e, index, me) => {
                    if (e.substr(0, 1) === " ") {
                        this.fileContents.splice(index, 1);
                    }
                });

                // search for "BEGIN:VEVENT" entries
                this.fileContents.forEach((entry, index) => {
                    let delimiter = this.getDelimiter(entry);
                    if (entry === "BEGIN:VEVENT") {
                        if (delimiter !== "*") {
                            parts = entry.split(delimiter);
                            let parts_zero = parts.shift();
                            this.$set(this.events, ++indexCounter, {});
                            // use $set to make these added fields reactive, they are user input
                            this.$set(this.events[indexCounter], 'student', '');
                            this.$set(this.events[indexCounter], 'tutor', '');
                            this.$set(this.events[indexCounter], 'location', '');
                            this.$set(this.events[indexCounter], 'registration', '');
                            this.events[indexCounter][parts_zero] = parts.join(delimiter);
                        } else {
                            console.log(`Error: Unhandled case (1)!`);
                        }
                    } else {
                        // consecutive entries (after begin:vevent)
                        if ((indexCounter > -1) && (entry !== "END:VCALENDAR")) {
                            if (delimiter !== "*") {
                                parts = entry.split(delimiter);
                                let parts_zero = parts.shift();
                                this.events[indexCounter][parts_zero] = parts.join(delimiter);
                            } else {
                                console.log(`Error: Unhandled case (2)!`);
                            }
                        }
                    }
                });

                this.analysing = false;
            },
            /**
             * get the first candidate for a delimiter,
             * meaning: which comes first? colon or semicolon
             * if none found, return *
             * @param input
             * @returns {string} :, ; or *
             */
            getDelimiter(input) {
                let colon = input.indexOf(":");
                let semicolon = input.indexOf(";");
                let hasColon = colon !== -1;
                let hasSemicolon = semicolon !== -1;

                if ((!hasColon) && (!hasSemicolon)) {
                    // meaning: don't try to split
                    return "*";
                } else if ((!hasColon) && (hasSemicolon)) {
                    return ";";
                } else if ((hasColon) && (!hasSemicolon)) {
                    return ":";
                }
                // if both, which is the first occurrence?
                return (colon < semicolon) ? ":" : ";";
            },

            translateDT(rawDT) {
                // FORMAT: TZID=Europe/Amsterdam:20190904T134000
                if (rawDT.substr(0, 4) === "TZID") {
                    // remove TZID=
                    let test2 = rawDT.substr(5);
                    // split into date and timezone
                    let parts = test2.split(":");
                    let dt = moment(parts[1]).tz(parts[0]);
                    return dt.format("DD-MM-YYYY HH:mm");
                }
                // FORMAT: 20191016T143000Z
                else if (rawDT.length === 16 && rawDT.indexOf("T") !== -1 && rawDT.indexOf("Z") !== -1) {
                    let dt = moment(rawDT);
                    return dt.format("DD-MM-YYYY HH:mm");
                }
                // FORMAT: VALUE=DATE:20191117
                else if (rawDT.length === 19 && (rawDT.substr(0, 11) === "VALUE=DATE:")) {
                    let dt = moment(rawDT.substr(11));
                    return dt.format("DD-MM-YYYY HH:mm");
                }
                // Not yet implemented
                else {
                    return "(2) unable to convert $input - format not recognized";
                }
            },

            /**
             * lookup url for autocomplete
             * @param input
             * @returns {string}
             */
            searchStudentEndpoint(input) {
                const sanitized = input.replace(/[^a-zA-Z0-9]+/g, "");
                return `/api/searchstudents?searchkey=${sanitized}`;
            },

            /**
             * find active registrations for this student, after choosing in de autocomplete box
             * @param rowIndex integer row number that initiated the selected student
             * @param student object
             */
            checkRegistration(rowIndex, student) {
                if (_.isNumber(student.value)) {
                    axios.get(`/api/getreg/${student.value}`)
                        .then(({data}) => {
                            let htmlString = "";
                            // todo: check if start date is on or before start date of this event/ics entry
                            // todo: maybe that leaves only one possible registration
                            if (_.size(data) === 1) {
                                let reg = _.first(data);
                                htmlString = "<input type='checkbox' value='" + reg.regid + "' checked @click='handleme'/><span>" + reg.course.name + "</span>";
                            } else {
                                data.forEach(reg => {
                                    htmlString += "<input type='checkbox' value='" + reg.regid + "' @click='handleme'/><span>" + reg.course.name + " " + reg.course.recurrenceoption.description + "</span><br/>";
                                });
                            }
                            $("#target_" + rowIndex).html(htmlString);
                        })
                        .catch(err => {
                            console.log(`error getting registrations for student ${student.value}`);
                        });
                }
            },
            handleme() {
                console.log(`HANDLE@!`);
            }

        },
        computed: {

            detailsOK() {
                return true;
            },

            // token to make API request from the autocomplete component
            xrcfToken() {
                let token = document.head.querySelector('meta[name="csrf-token"]');
                return {'X-CSRF-TOKEN': token.content};
            }
        }

    });
}
