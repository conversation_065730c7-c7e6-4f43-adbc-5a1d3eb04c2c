<template>
    <div>
        <h3>
            {{ucFirst(translateChoice('generic.students', 1))}} {{studentName}}
            <img v-if="saving" src="/images/class-loader-small.gif" alt="loading">
            <span class="badge badge-success">{{saveresult}}</span>
        </h3>
        <div class="day" v-for="(dayName, index) in dayNames" :key="index">

            <div v-if="dayName !== 'saturday' && dayName !== 'sunday'" class="dayname">{{ucFirst(translate(`localisation.${dayName}`))}}</div>
            <div v-else class="dayname weekend">{{ucFirst(translate(`localisation.${dayName}`))}}</div>
            <div class="hourSequence">
                <!-- Don't make the class reactive. We would have to wait for re-render to see te tile turning on or off -->
                <div @mouseover="markMe" @mousedown="startDragSelect" @mouseup="endDragSelect" :data-entry="`${dayName}_${Object.keys(hourName)[0]}`"
                     class="hour" v-for="(hourName, index) in dayMarkers[dayName]" :key="dayName+index">{{Object.keys(hourName)[0]}}</div>
            </div>
        </div>
        <button @click="resetTimeSheet" class="btn btn-sm btn-danger">{{translate('generic.reset')}}</button>
    </div>
</template>

<script>
import useLang from "@/composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();
export default {
    name: "Preference",
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data() {
        return {
            saving: false,
            saveresult: '',
            dayNames: [
                'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
            ],
            dragging: false,
            dayMarkers: {
                'monday': [], 'tuesday': [], 'wednesday': [], 'thursday': [], 'friday': [], 'saturday': [], 'sunday': []
            }
        }
    },
    props: {
        isAdmin: {
            default: false,
            type: Boolean
        },
        accesstoken: {
            default: 'none',
            type: String
        },
        studentId: {
            default: '0',
            type: String
        },
        studentName: {
            required: true,
            type: String
        },
        studentCourses: {
            required: true,
            type: Array
        },
        planningDetails: {
            required: false
        },
        showRemarkKeepPlanning: {
            required: true,
            type: String
        },
        generic: {
            required: true,
            type: Object
        },
        daynames: Object
    },

    mounted() {
        this.fillHourNames();
        this.getStudentPrefs(this.studentId, this.accesstoken, this.dayNames)
            .then(resp => {

            })
            .catch(err => {
                console.log(`Error loading prefs: ${err}`);
            })
            .finally(() => {
                this.saving = false;
            });
    },

    methods: {
        /**
         *
         */
        fillHourNames() {
            const MINTIME = 9;
            const MAXTIME = 23;
            for(let i = MINTIME; i < MAXTIME; i++) {
                let name1 = i + ":00";
                let name2 = i + ":30";
                // prepend 0
                if(i < 10) { name1 = "0" + name1; name2 = "0" + name2 }
                // enter timeslot into dataset
                for (let dayName in this.dayMarkers) {
                    if (this.dayMarkers.hasOwnProperty(dayName)) {
                        this.dayMarkers[dayName].push({[name1]: false});
                        this.dayMarkers[dayName].push({[name2]: false});
                    }
                }
            }
        },

        /**
         * start drag-select
         * @param ev
         */
        startDragSelect(ev) {
            const LEFTCLICK = 1;
            // only if left click!
            if (ev.which === LEFTCLICK) {
                // only if we are not already dragging (failsafe, should never be the case)
                if (!this.dragging) {
                    this.dragging = true;
                    this.markMe(ev);
                    setTimeout(() => {
                        console.log("dragging");
                    }, 500);
                }
            }
        },

        /**
         * end drag-select
         */
        endDragSelect() {
            if (this.dragging) {
                this.dragging = false;
                this.saving = true;
                // find all currently selected timeslots
                this.saveAvailability(this.studentId, this.accesstoken)
                    .then(resp => {
                        this.saveresult = "Opslaan gelukt";
                        setTimeout(() => {
                            this.saveresult = "";
                        }, 5000)
                    })
                    .catch(err => {
                        console.log(`Error saving prefs: ${err}`);
                    })
                    .finally(() => {
                        this.saving = false;
                    });
            }
        },

        /**
         * Mark a div as selected or unselected depending on the current state
         * @param ev
         * @param orgClass string
         */
        markMe(ev, orgClass="hour") {
            if (this.dragging) {
                // highlight if off, reset highlight if on
                const currentClass = ev.target.getAttribute('class');
                const entry = ev.target.getAttribute('data-entry');
                if(currentClass.indexOf('markMe') === -1) {
                    // set "on"
                    ev.target.setAttribute("class", `${orgClass} markMe`);
                } else {
                    ev.target.setAttribute("class", orgClass);
                }
            }
        },


        /**
         * Save new set of preferred schedule time slots
         * @returns {Promise<*>}
         */
        saveAvailability: async (studentId, accesstoken) => {
            let data = {
                student: studentId, // in case of admin
                token: accesstoken, // in case of student (w.o. login)
                availability:{
                    'monday': [], 'tuesday': [], 'wednesday': [],
                    'thursday': [], 'friday': [], 'saturday': [], 'sunday': []
                }
            };
            // get all id's as data
            $(".markMe").map((key, val) => {
                const parts = val.getAttribute("data-entry").split('_');
                data.availability[parts[0]].push(parts[1]);
            });
            // send to backend
            return axios.post('/api/updateschedulepreferences', data);
        },

        /**
         * Get current set from database
         * @returns {Promise<*>}
         */
        getStudentPrefs: async (studentId, accesstoken, dayNames) => {
            return axios.get(`/api/getuserprefs?student=${studentId}&token=${accesstoken}`)
                .then(resp => {
                    $(".hour").attr("class", "hour");
                    dayNames.forEach((dayName) =>  {
                        console.log(dayName);
                        resp.data[dayName].forEach(timeSegment => {
                            $(`[data-entry='${dayName}_${timeSegment}']`).attr('class', 'hour markMe');
                        })
                    });
                })
                .catch(err => {
                    console.log(`Error fetching current prefs: ${err}`);
                })

        },

        /**
         * unmark all timeslots
         */
        resetTimeSheet() {
            // use jQuery, we don't want to wait for re-render
            $(".hour").attr("class", "hour");
        },
    }

}
</script>

<style scoped lang="scss">
    .day {
        margin-right: 1rem;
        border: solid black 2px;
        display: inline-block;
        width: 10rem;
        text-align: center;
        .dayname {
            background-color: #26BB9C;
            border-bottom: solid black 2px;
            color:white;
        }
        .weekend {
            background-color: #ededed;
            color:black;
        }
        .hour {
            /* not selectable */
            /* because we do this programmatically */
            -moz-user-select: none;
            -khtml-user-select: none;
            -webkit-user-select: none;
            -o-user-select: none;
            user-select: none;
            border-bottom: solid #2A3F54 1px;
            &:last-child {
                border-bottom: none;
            }
        }
        .markMe {
            background-color: #3E5367;
            color: white;
        }

    }
</style>
