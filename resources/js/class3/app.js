/**
 * Load all of this project's JavaScript dependencies,
 * which includes Vue and other libraries.
 */
import mitt from 'mitt';
import { createApp } from 'vue';
import jQuery from 'jquery';

// Create Vuex store
import { createStore } from 'vuex';
import storeData from './store/index.js';
import useLang from "./composables/useLang.js";

import FloatingVue from 'floating-vue';
import 'floating-vue/dist/style.css';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import './bootstrap.js';

window.emitter = mitt();

// Create a Vuex store
const store = createStore(storeData);

/**
 * Component Registration
 *
 * Previously, this used require.context to automatically register all Vue components.
 * We now use a build-time script to generate imports for all Vue components.
 *
 * The script (scripts/generate-component-imports.js) scans the components directory
 * and generates a file with imports for all .vue files. This file is then imported here.
 *
 * This approach maintains the convenience of auto-registration while being compatible
 * with ES Modules ("type": "module" in package.json).
 */

// Import the auto-generated components
import { components } from './generated/component-imports.js';

/**
 * Create a fresh Vue application instance and attach it to the page.
 */
// if we have signregistration in the URL: don't load Vue!
// It uses only vanilla JavaScript, which is being wrecked by Vue
if (window.location.href.includes('signregistration')) {
    console.log('Vue not loaded, signregistration URL detected');
} else {
    const app = createApp({
        mounted() {
            console.log('Application loaded');
        }
    });

    // Register components
    Object.entries(components).forEach(([name, component]) => {
        app.component(name, component);
    });

    // Use plugins
    app.use(store);
    app.use(FloatingVue, {
        // Default options for tooltips
        themes: {
            'tooltip': {
                placement: 'top',
                delay: { show: 500, hide: 0 }
            }
        }
    });
    app.component('VueDatepicker', VueDatepicker);

    // Mount the app
    app.mount('#vuecontext');
}

/**
 * Custom navbar behavior. Because the menu is too big for XL 1200-1400 px,
 * we can't simply use the bootstrap navbar-expand-xl class.
 * This function shows the hamburger menu on screens below 1400 px width
 * and shows the full menu on screens 1400 px and above
 */
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.getElementById('mainNav');
    const navbarCollapse = document.getElementById('navbarCollapsableContent');

    function adjustNavbar() {
        if (window.innerWidth < 1400) {
            // Below 1400px - show hamburger menu
            navbar.classList.remove('navbar-expand-xl');
            navbarCollapse.classList.add('collapse');
        } else {
            // 1400px and above - show full menu
            navbar.classList.add('navbar-expand-xl');
            navbarCollapse.classList.remove('collapse');
        }
    }

    // Initial adjustment
    adjustNavbar();

    // Adjust on window resize
    window.addEventListener('resize', adjustNavbar);
});

// on Document load, jQuery stuff
(function ($) {
    'use strict'; // Start of use strict

    // check if the current route needs an initial filling of the Vuex store
    const loc = window.location.href;
    const route =
        loc.includes('planning/assistant')
            ? 'planningAssistant'
            : loc.includes('planning/table')
                ? 'planningTable'
                : '';

    store.dispatch('initForRoute', { route });

    /* Prevent navigation on the Dirty-flag  */
    window.dirty = false; // initial reset after a page load
    window.onbeforeunload = function () {
        // Any not-null return value will toggle the popup.
        // The string itself will not be shown!
        return typeof dirty !== 'undefined' && window.dirty
            ? useLang().translate('generic.warnunsavedchanges')
            : null;
    };
    /* END Prevent navigation on the Dirty-flag  */

    /** ******* Bootstrap dropdown toggle */
    $('.dropdown-toggle').dropdown();
    /** ****END Bootstrap dropdown toggle */
    /** ******* Bootstrap collapse mobile menu */
    document.addEventListener('click', () => $('#navbarCollapsableContent').collapse('hide'));
    /** ****END Bootstrap collapse toggle */

    const logoutBtn = document.querySelector('#logoutButton');
    if (logoutBtn != null) {
        logoutBtn.addEventListener('click', event => {
            event.preventDefault();
            document.querySelector('#logout-form').submit();
        });
    }
})(jQuery);
