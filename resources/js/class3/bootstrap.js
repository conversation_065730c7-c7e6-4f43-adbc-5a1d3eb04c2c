import _ from 'lodash';
import Popper from 'popper.js';
import jQuery from 'jquery';
import 'jquery-ui';
// Import specific jQuery UI widgets
import 'jquery-ui/ui/widgets/draggable.js';
import 'bootstrap';
import Clipboard from 'clipboard';

// Assign lodash to window object for global access
// todo: get rid of this global!
window._ = _;

/**
 * We'll load jQuery and the Bootstrap jQuery plugin which provides support
 * for JavaScript based Bootstrap features such as modals and tabs. This
 * code may be modified to fit the specific needs of your application.
 * todo: get rid of this global! as soon as we finished used BS4
 */
try {
    window.Popper = Popper;
    window.$ = window.jQuery = jQuery;
} catch (e) {
    console.log('Unable to load JS libraries');
}

/* Clipboard */
window.Clipboard = Clipboard;

// Note: We've removed the global axios instance.
// All API calls should now use the useApi composable.
