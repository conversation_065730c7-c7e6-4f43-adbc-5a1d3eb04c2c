import { ref } from 'vue';
import useApi from './useApi.js';
import useToast from './useToast.js';
import useLang from "./useLang.js";

const { apiGet, apiPost, apiPut, apiDel } = useApi();
const { failToast, successToast } = useToast();
const { ucFirst, translate, translateChoice } = useLang();

const templates = ref([]);
const useFor = ref(''); // one of {a, b, c} meaning 'concept', 'request signature', 'request schedule preference'
const busy = ref(false);
const selectedTemplate = ref(null);
const selectedTemplateId = ref(0);

export default function useMailTemplates() {
    /**
     * Get mail templates, optionally filtered by target
     * @returns {Promise<void>}
     */
    const getTemplates = async() => {
        busy.value = true;
        try {
            const endpoint = useFor.value
                ? `/api/getMailTemplates/${useFor.value}`
                : '/api/getMailTemplates';

            const response = await apiGet(endpoint);
            templates.value = response.data;
        } catch (error) {
            failToast(
                ucFirst(translate('generic.errorretrievingtemplates')) + ': ' + error.message,
                ucFirst(translate('generic.error'))
            );
        } finally {
            busy.value = false;
        }
    };

    /**
     * Save changes to an existing mail template
     * @param {Object} template - The template to update
     * @returns {Promise<boolean>} - Success status
     */
    const saveTemplate = async (template) => {
        busy.value = true;
        try {
            const data = {
                id: template.id,
                mcontent: template.content,
                mtarget: template.targets
            };

            await apiPut('/api/updateMailTemplate', data);
            successToast(ucFirst(translate('generic.changessaved')));
            return true;
        } catch (error) {
            failToast(
                ucFirst(translate('generic.savingfailed')) + '. ' +
                ucFirst(translateChoice('generic.messages', 1)) + ': ' + error.message,
                ucFirst(translate('generic.error'))
            );
            return false;
        } finally {
            busy.value = false;
        }
    };

    /**
     * Create a new mail template
     * @param {Object} templateData - The template data
     * @returns {Promise<number|null>} - The new template ID or null on failure
     */
    const createTemplate = async (templateData) => {
        busy.value = true;
        try {
            const data = {
                mlabel: templateData.label,
                mcontent: templateData.content || ucFirst(translate('generic.pleaseaddcontent')),
                mtarget: templateData.target
            };

            const response = await apiPost('/api/createMailTemplate', data);
            successToast(ucFirst(translate('generic.savesuccess')));
            return response.data.newTemplateId;
        } catch (error) {
            failToast(
                ucFirst(translate('generic.savingfailed')) + '. ' +
                ucFirst(translateChoice('generic.messages', 1)) + ': ' + error.message,
                ucFirst(translate('generic.error'))
            );
            return null;
        } finally {
            busy.value = false;
        }
    };

    /**
     * Delete a mail template
     * @param {number} templateId - The ID of the template to delete
     * @returns {Promise<boolean>} - Success status
     */
    const deleteTemplate = async (templateId) => {
        busy.value = true;
        try {
            await apiDel(`/api/deleteMailTemplate/${templateId}`);
            successToast(ucFirst(translate('generic.deletesuccessful')));
            return true;
        } catch (error) {
            failToast(
                ucFirst(translate('generic.deletefailed')) + '. ' +
                ucFirst(translateChoice('generic.messages', 1)) + ': ' + error.message,
                ucFirst(translate('generic.error'))
            );
            return false;
        } finally {
            busy.value = false;
        }
    };

    return {
        busy,
        createTemplate,
        deleteTemplate,
        getTemplates,
        saveTemplate,
        selectedTemplate,
        selectedTemplateId,
        templates,
        useFor
    };
}
