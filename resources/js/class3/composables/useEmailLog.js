import { computed, ref } from 'vue';
import useApi from './useApi.js';
import useToast from './useToast.js';

const emailLog = ref([]);
const fetchTimer = ref(null);

export default function useEmailLog () {
    const { apiGet, apiDel } = useApi();
    const { failToast, successToast } = useToast();

    const fetchEmailLog = async () => {
        try {
            const response = await apiGet('/api/emaillogentries');
            emailLog.value = response.data;
        } catch (error) {
            failToast(error.message);
        }
    };

    const listHasQueuedEmails = computed(() => {
        return emailLog.value.some((entry) => entry.status === 'queued');
    });

    const getEmailLog = async () => {
        await fetchEmailLog();
        // as long as we have queued emails, keep fetching the email log to auto update the list
        if (listHasQueuedEmails.value) {
            fetchTimer.value = setInterval(async () => {
                await fetchEmailLog();
                if (!listHasQueuedEmails.value) {
                    clearInterval(fetchTimer.value);
                }
            }, 5000);
        } else {
            if (fetchTimer.value) {
                clearInterval(fetchTimer.value);
            }
        }
    };

    const deleteFromEmailLog = async (id) => {
        try {
            await apiDel(`/api/emaillogentries/${id}`);
            successToast('Email log entry deleted successfully');
            await fetchEmailLog();
        } catch (error) {
            failToast(error.message);
        }
    };

    return {
        deleteFromEmailLog,
        emailLog,
        getEmailLog
    };
}
