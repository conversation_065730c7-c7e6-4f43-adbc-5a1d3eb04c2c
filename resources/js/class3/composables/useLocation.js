import { ref } from "vue";
import axios from "axios";
import useLang from "./useLang.js";
import useNoty from "./useNoty.js";
import useBaseData from "./useBaseData.js";

const { ucFirst, translate} = useLang();
const { failNoty, successNoty } = useNoty();
const { initBaseData } = useBaseData();

const locationToEdit = ref({});
const locationIdToDelete = ref(null);

export default function useLocation() {
    const saveLocationData = async  () => {
        let url = '';
        let method = 'post';
        let data = { name: locationToEdit.value.name };
        if(locationToEdit.value.id) {
            url = `/locations/${locationToEdit.value.id}`;
            method = 'put';
        } else {
            url = '/locations';
            method = 'post';
        }
        try {
            await axios[method](url, data);
            successNoty(ucFirst(translate('generic.datasaved')));
            updateLocations();
        } catch (error) {
            failNoty(ucFirst(translate('generic.error')) + ' ' + error.message);
        }
    };

    const createNewLocation = () => {
        locationToEdit.value = {};
        showEdit.value = true;
    }

    const deleteLocation = async () => {
        try {
            await axios.delete(`/locations/${locationIdToDelete.value}`);
            updateLocations()
            successNoty(ucFirst(translate('generic.locationdeleted')));
        } catch (error) {
            failNoty(ucFirst(translate('generic.error')) + ' ' + error.message);
        }
    }

    const showEdit = ref(false);

    const editLocation = (location) => {
        locationToEdit.value = location;
        showEdit.value = true;
    }

    const updateLocations = async () => {
        locationToEdit.value = {};
        await initBaseData({locations: true}, true);
        showEdit.value = false;
    }

    return {
        createNewLocation,
        deleteLocation,
        editLocation,
        locationIdToDelete,
        locationToEdit,
        saveLocationData,
        showEdit,
        updateLocations
    }
}
