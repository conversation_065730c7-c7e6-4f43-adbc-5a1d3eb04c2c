import { computed, ref } from "vue";
import axios from "axios";
import useNoty from "./useNoty.js";
import useLang from "./useLang.js";
import useBaseData from "./useBaseData.js";

const { failNoty, successNoty } = useNoty();
const { ucFirst, translate } = useLang();
const { initBaseData } = useBaseData();

const schoolYearIDToDelete = ref(null);
const schoolYearIDToEdit = ref(-1);
const schoolYearToEdit = ref(null);
const busy = ref(false);

export default function useSchoolYear() {
    const getSchoolYear = async () => {
        busy.value = true;
        if (schoolYearIDToEdit.value > 0) {
            // Logic to fetch a school year by ID: GET /api/schoolyears/{schoolYearId}
            try {
                const response = await axios.get(`/api/schoolyears/${ schoolYearIDToEdit.value }`);
                schoolYearToEdit.value = response.data;
            } catch (err) {
                // Handle error
                failNoty(err, ucFirst(translate("generic.errorloadingdata")))
            } finally {
                busy.value = false;
            }
        } else {
            // Logic to create a new school year
            schoolYearToEdit.value = {
                id: null,
                label: "",
                start_year: 0,
                start_date: "",
                end_year: 0,
                end_date: "",
            };
            busy.value = false;
        }
    }

    const deleteSchoolYear = async () => {
        // Logic to delete a school year
        busy.value = true;
        try {
            await axios.delete(`/api/schoolyears/${ schoolYearIDToDelete.value }`);
            successNoty(ucFirst(translate('generic.datasaved')));
            schoolYearIDToDelete.value = null;
            // retrieve the list of school years again
            await initBaseData({schoolYears: true}, true);
        } catch (err) {
            failNoty(err, ucFirst(translate("generic.errorloadingdata")))
        } finally {
            busy.value = false;
        }

        schoolYearIDToDelete.value = null;
    };

    const isSaveable = computed(() => {
        return schoolYearToEdit.value?.label &&
               schoolYearToEdit.value.label.length > 2 &&
               schoolYearToEdit.value.start_date.length > 0 &&
               schoolYearToEdit.value.end_date.length > 0;
    });

    const saveSchoolyear = async () => {
        const busy = ref(true);
        let url = '';
        let method = 'post';
        let data = schoolYearToEdit.value;
        if(schoolYearToEdit.value.id) {
            url = `/api/schoolyears/${schoolYearToEdit.value.id}`;
            method = 'put';
        } else {
            url = '/api/schoolyears';
            method = 'post';
        }
        try {
            await axios[method](url, data);
            successNoty(ucFirst(translate('generic.datasaved')));
            schoolYearToEdit.value = {};
            schoolYearIDToEdit.value = schoolYearToEdit.value.id;
            window.dirty = false;
            // retrieve the list of school years again
            await initBaseData({schoolYears: true}, true);
        } catch (error) {
            failNoty(ucFirst(translate('generic.error')) + ' ' + error.message);
        } finally {
            busy.value = false;
        }
    }

    return {
        busy,
        deleteSchoolYear,
        getSchoolYear,
        isSaveable,
        saveSchoolyear,
        schoolYearIDToDelete,
        schoolYearIDToEdit,
        schoolYearToEdit
    }
}
