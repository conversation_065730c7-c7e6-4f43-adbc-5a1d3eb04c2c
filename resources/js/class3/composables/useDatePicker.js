import { computed } from 'vue';
import useLang from './useLang.js';

/**
 * Composable for date picker options compatible with @vuepic/vue-datepicker
 * @param {boolean} isWholeDay - Whether the date picker should only show date (true) or date and time (false)
 * @param {string} [forceLang] - Force a specific language ('nl' or 'en'), otherwise use the user's language
 * @param {boolean} [allowUserInput] - Whether the user can input dates manually (true) or not (false)
 * @param {boolean} [showClear=false] - Whether to show the clear button
 * @returns {Object} - Date picker options
 */
export default function useDatePicker(
    isWholeDay,
    forceLang = null,
    allowUserInput = true,
    showClear = false
) {
    const { lang } = useLang();

    // Options for date and time picker in Dutch
    const dpOptionsDateTimeNl = {
        format: 'yyyy-MM-dd HH:mm',
        formatDatePart: 'dd-MM-yyyy', // use when datetime picker is split in two: date and time
        locale: 'nl',
        enableTimePicker: true,
        label: 'datum / tijd',
        placeholder: 'datum / tijd',
        modelType: 'format'
    };

    // Options for date and time picker in English
    const dpOptionsDateTimeEn = {
        format: 'yyyy-MM-dd HH:mm',
        formatDatePart: 'yyyy-MM-dd',
        locale: 'en',
        enableTimePicker: true,
        label: 'date / time',
        placeholder: 'date / time',
        modelType: 'format'
    };

    // Options for date-only picker in Dutch
    const dpOptionsDateNl = {
        format: 'dd-MM-yyyy',
        locale: 'nl',
        enableTimePicker: false,
        label: 'datum',
        placeholder: 'datum',
        modelType: 'format'
    };

    // Options for date-only picker in English
    const dpOptionsDateEn = {
        format: 'yyyy-MM-dd',
        locale: 'en',
        enableTimePicker: false,
        label: 'date',
        placeholder: 'date',
        modelType: 'format'
    };

    /**
     * Computed property that returns the appropriate date picker options
     * based on the user's language and whether it's a whole day or not
     */
    const dpOptions = computed(() => {
        // Determine which language to use (forced or from user settings)
        const currentLang = forceLang || lang.value;

        // Get the base options based on language and whether it's a whole day
        let options;
        if (isWholeDay) {
            options = (currentLang === 'nl') ? { ...dpOptionsDateNl } : { ...dpOptionsDateEn };
        } else {
            options = (currentLang === 'nl') ? { ...dpOptionsDateTimeNl } : { ...dpOptionsDateTimeEn };
        }

        // Add clearable property if showClear is true
        if (showClear) {
            options.clearable = true;
        }
        if (allowUserInput) {
            options.textInput = true;
        }

        return options;
    });


    return {
        dpOptions
    };
}
