import { ref, watch } from 'vue';
import useNoty from './useNoty.js';
import useLang from './useLang.js';
import axios from 'axios';

const idToEdit = ref(0);
const tutor = ref(null);
const busy = ref(false);

export default function useEditTutor () {
    const { failNoty, successNoty } = useNoty();
    const { ucFirst, translate } = useLang();

    watch(idToEdit, async () => {
        if (!busy.value) {
            if (idToEdit.value === -1) {
                console.log('NEW TUTOR');
                const today = new Date();
                const dd = ('00' + today.getDate()).slice(-2);
                const mm = ('00' + (today.getMonth() + 1)).slice(-2);
                const yyyy = today.getFullYear();
                busy.value = true;
                tutor.value = {
                    id: -1,
                    inUse: false,
                    is_blocked: 0,
                    has_future_events: false,
                    name: '',
                    hexcolor: '',
                    email: '',
                    telephone: '',
                    telephone_extra: null,
                    preferred_language: null,
                    last_active_at: null,
                    start_date: `${yyyy}-${mm}-${dd}`,
                    end_date: null
                };
                busy.value = false;
            } else if (idToEdit.value !== tutor.value?.id) {
                busy.value = true;
                try {
                    const response = await axios.get(`/api/tutors/${idToEdit.value}`);
                    tutor.value = response.data.data;
                } catch (err) {
                    console.log(err);
                } finally {
                    busy.value = false;
                }
            }
        }
    });

    /**
     * save current Tutor data
     * @returns {Promise<void>}
     */
    const saveTutor = () => {
        if (tutor.value.id === -1) {
            // POST / NEW
            axios.post('/api/tutor', tutor.value)
                .then(response => {
                    successNoty(ucFirst(translate('generic.savesuccess')),
                        ucFirst(translate('generic.success')));
                    window.emitter.emit('updatetutors');
                    window.dirty = false;
                    tutor.value.id = response.data.newid;
                })
                .catch(err => {
                    failNoty(ucFirst(translate('generic.savingfailed')) + err,
                        ucFirst(translate('generic.fail')));
                });
        } else {
            // PUT / UPDATE
            axios.put(`/api/tutor/${tutor.value?.id}`, tutor.value)
                .then(response => {
                    successNoty(ucFirst(translate('generic.savesuccess')),
                        ucFirst(translate('generic.success')));
                    window.emitter.emit('updatetutors');
                    window.dirty = false;
                })
                .catch(err => {
                    failNoty(ucFirst(translate('generic.savingfailed')) + err,
                        ucFirst(translate('generic.fail')));
                });
        }
    };

    const deleteTutor = (id) => {
        axios.delete(`/api/tutor/${id}`)
            .then(() => {
                successNoty(
                    ucFirst(translate('generic.tutordeleted')),
                    ucFirst(translate('generic.success'))
                );
                // on success: refresh list
                window.emitter.emit('updatetutors');
                // if we have this tutor in the edit section: close the edit section
                if (id === tutor.value.id) {
                    tutor.value = null;
                }
            })
            .catch(err => {
                failNoty(
                    ucFirst(translate('generic.unabletodeleteturtor')),
                    ucFirst(translate('generic.deletefailed')) + err
                );
            });
    };

    return {
        busy,
        deleteTutor,
        idToEdit,
        saveTutor,
        tutor
    };
};
