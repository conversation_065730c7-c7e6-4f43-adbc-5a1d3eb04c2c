import { ref } from 'vue';
import useApi from './useApi.js';
import useToast from './useToast.js';
import useLang from './useLang.js';

// file that we show or edit in a dialog
const fileToUpload = ref(null);
const responseMessage = ref("");
const { translate, ucFirst } = useLang();
const { failToast, successToast } = useToast();
const { apiPost } = useApi();

export default function useUploadForm() {

    /**
     * Get the download url for a file
     * TODO: implement this
     * @param {string} fileName - The name of the file
     * @returns {string} The download url for the file
     */
    const getDownloadUrl = (fileName) => {
        return `${ fileName }`;
    }

    const uploadFile = async (uploadSuccess) => {
        if (!fileToUpload.value) {
            failToast(`${ ucFirst(translate('generic.error')) }: No file selected`);
            return null;
        }
        const formData = new FormData();
        formData.append('fileitem', fileToUpload.value);
        try {
            const response = await apiPost(`/api/uploadfile`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            responseMessage.value = response.data.message;
            successToast(response.data.message);
            uploadSuccess(response.data.documentId);
        } catch (e) {
            failToast(`${ ucFirst(translate('generic.error')) }: ${ e.message }, ${ e?.response?.data?.message }`);
            return null;
        }
    }

    const setFileToUpload = (ev) => {
        const files = ev.target.files || ev.dataTransfer.files;
        if (files.length > 0) fileToUpload.value = files[0]
        else fileToUpload.value = null;
    }

    return {
        fileToUpload,
        getDownloadUrl,
        responseMessage,
        setFileToUpload,
        uploadFile
    }
}
