export default function useColor () {
    /**
     * return possible the most contrasting color for a given hex color
     * @param hexColor
     * @returns {string}
     */
    const getContrastColor = (hexColor) => {
        if (hexColor == null || hexColor === '') return false;
        // if input is not a hex string, return false
        if (!hexColor.startsWith('#')) {
            hexColor = hex2rgb(hexColor);
            if (!hexColor) {
                return false;
            }
            hexColor = rgb2hex(hexColor);
        }

        /// ///////// hexColor RGB (convert hex parts to decimal)
        const R1 = parseInt(hexColor.substring(1, 3), 16);
        const G1 = parseInt(hexColor.substring(3, 5), 16);
        const B1 = parseInt(hexColor.substring(5, 7), 16);

        /// ///////// Black RGB
        const blackColor = '#000000'; // tipping point, the amount of black
        const R2BlackColor = parseInt(blackColor.substring(1, 3), 16);
        const G2BlackColor = parseInt(blackColor.substring(3, 5), 16);
        const B2BlackColor = parseInt(blackColor.substring(5, 7), 16);

        /// ///////// Calculate contrast ratio
        const L1 = 0.2126 * Math.pow(R1 / 255, 2.2) +
            0.7152 * Math.pow(G1 / 255, 2.2) +
            0.0722 * Math.pow(B1 / 255, 2.2);

        const L2 = 0.2126 * Math.pow(R2BlackColor / 255, 2.2) +
            0.7152 * Math.pow(G2BlackColor / 255, 2.2) +
            0.0722 * Math.pow(B2BlackColor / 255, 2.2);

        const contrastRatio = L1 > L2
            ? parseInt((L1 + 0.05) / (L2 + 0.05))
            : parseInt((L2 + 0.05) / (L1 + 0.05));

        return contrastRatio > 5 ? 'black' : 'white';
    };

    /**
     * Converts an RGB color value to its corresponding hexadecimal color code.
     *
     * @param {string|object} rgb - The RGB color value to convert. Can be either a string in 'rgb(r, g, b)' format or 'r, g, b' format,
     *                             or an object with 'r', 'g', and 'b' properties.
     * @returns {string|boolean} - The corresponding hexadecimal color code if successful, or false if the conversion fails.
     */
    const rgb2hex = (rgb) => {
        if (rgb == null || rgb === '') return false;
        if (typeof rgb === 'object') {
            if (rgb.r == null || rgb.g == null || rgb.b == null) return false;
            rgb = `rgb(${rgb.r},${rgb.g},${rgb.b})`;
        }
        // if the input starts with rgb(, remove it
        if (rgb.startsWith('rgb(')) {
            rgb = rgb.substring(4);
            // remove last ) if it exists
            if (rgb.endsWith(')')) {
                rgb = rgb.substring(0, rgb.length - 1);
            }
        }
        const parts = rgb.split(',');
        const r = parts[0];
        const g = parts[1];
        const b = parts[2];
        if (r == null || g == null || b == null) return false;
        const rHex = parseInt(r).toString(16).padStart(2, '0');
        const gHex = parseInt(g).toString(16).padStart(2, '0');
        const bHex = parseInt(b).toString(16).padStart(2, '0');
        return '#' + rHex + gHex + bHex;
    };

    const hex2rgb = (hex) => {
        if (hex == null || hex === '') return false;
        // if hex string is really already rgb in the form of rgb(10,15,12), convert it to rgb object
        if (hex.startsWith('rgb(')) {
            hex = rgb2hex(hex);
        }
        const validHEXInput = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (!validHEXInput) {
            return false;
        }
        return {
            r: parseInt(validHEXInput[1], 16),
            g: parseInt(validHEXInput[2], 16),
            b: parseInt(validHEXInput[3], 16)
        };
    };

    return {
        getContrastColor,
        hex2rgb,
        rgb2hex
    };
}
