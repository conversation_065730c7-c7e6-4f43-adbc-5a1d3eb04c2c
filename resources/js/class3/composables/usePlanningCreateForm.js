// New Planning form variables
import { computed, ref } from 'vue';
import moment from 'moment';
import useBaseData from './useBaseData.js';

const date = ref(moment().format('DD-MM-YYYY'));
const time = ref(moment().format('HH:mm'));
const repeats = ref(0);
const locationId = ref(0);
const locationIdAlt = ref(0);
const tutorId = ref(0);

const { allLocations } = useBaseData();

export default function usePlanningCreateForm () {
    const locationsFirstChoice = computed(() => {
        return allLocations.value;
    });

    const locationsSecondChoice = computed(() => {
        return allLocations.value.filter((location) => {
            return location.id !== locationId.value;
        });
    });

    return {
        date,
        locationId,
        locationIdAlt,
        locationsFirstChoice,
        locationsSecondChoice,
        repeats,
        time,
        tutorId
    };
}
