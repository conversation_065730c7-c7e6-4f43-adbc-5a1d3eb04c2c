import { computed, ref } from 'vue';
import useLang from "./useLang.js";
import useNoty from "./useNoty.js";
import axios from "axios";

const { translate, ucFirst } = useLang();
const { successNoty, failNoty } = useNoty();

const studentsOnCourse = ref([]);
const studentGroupSpecsOfCourse = ref([]);
const lessonTable = ref([]);

const courseId = ref(0);
const courseName = ref('');
const courseGroup = ref('');
const isTrialCourse = ref(false);
const archiveCourse = ref(false);
const maxStudentGroupSize = ref(1); // defaults to 1:1 teaching
const minStudentGroupSize = ref(1); // defaults to 1:1 teaching
const recurrenceOption = ref('');
const pricePreAdult = ref(0);
const priceExTaxCalculated = ref(0);
const priceIncTax = ref(0);
const priceIsPer = ref('time');
const priceIsPerOptions = [
    { value: 'time', label: translate('generic.time') },
    { value: 'week', label: translate('generic.week') },
    { value: 'twoweeks', label: translate('generic.twoweeks') },
    { value: 'month', label: translate('generic.month') },
    { value: 'year', label: translate('generic.year') },
    { value: 'sequence', label: translate('generic.sequence') },
];

export default function useCourse() {

    const initCourseData = async () => {
        if (courseId.value > 0) {
            // load the course data
            try {
                const response = await axios.get('/api/courses/' + courseId.value);
                const course = response.data.data;
                courseName.value = course.name;
                courseGroup.value = course.coursegroup_id;
                isTrialCourse.value = course.is_trial_course === 1;
                archiveCourse.value = course.archive === 1;
                maxStudentGroupSize.value = course.group_size_max;
                minStudentGroupSize.value = course.group_size_min;
                pricePreAdult.value = course.price_ex_tax_sub_adult;
                priceIncTax.value = course.price_invoice;
                recurrenceOption.value = course.recurrenceoption_id;
                priceIsPer.value = course.price_is_per;
                studentsOnCourse.value = course.students;
                lessonTable.value = course.lesson_table;
            } catch(error) {
                failNoty(ucFirst(translate("generic.loadfailed")) + ": " + error);
            }
        }
    };

    const saveCourseData = async () => {
        // save the course data
        const data = {
            archive: archiveCourse.value ? 1 : 0,
            coursegroup: courseGroup.value,             // = ID of the course group
            group_size_min: minStudentGroupSize.value,
            group_size_max: maxStudentGroupSize.value,
            is_trial_course: isTrialCourse.value ? 1 : 0,
            name: courseName.value,
            price_ex_tax: priceExTaxCalculated.value, // calculated from pricePreAdult and VAT from domain settings
            price_ex_tax_sub_adult: pricePreAdult.value,
            price_invoice: priceIncTax.value,
            price_is_per: priceIsPer.value,
            recurrenceoption_id: recurrenceOption.value,
            variant_code: null // currently not in use
        };
        let url, method;
        if (newMode.value) {
            url = '/api/courses';
            method = 'post';
        } else {
            url = '/api/courses/' + courseId.value;
            method = 'put';
        }
        try {
            const response = await axios[method](url, data);
            successNoty(ucFirst(translate("generic.savesuccess")));
            window.dirty = false; // reset the dirty flag so we can navigate away without warning
            // now redirect to the course edit page if we are in new mode
            if (newMode.value) {
                setTimeout(() => {
                    window.location.href = '/courses/' + response.data.course.id + '/edit';
                }, 1000);
            }
        } catch (error) {
            failNoty(ucFirst(translate("generic.savingfailed")) + ": " + error);
        }
    };

    const newMode = computed(() => {
       return courseId.value === 0;
    });

    const isSaveable = computed(() => {
        return courseName.value !== '' &&
            courseGroup.value > 0 &&
            minStudentGroupSize.value > 0 &&
            maxStudentGroupSize.value > 0 &&
            pricePreAdult.value >= 0 &&
            priceIncTax.value >= 0 &&
            recurrenceOption.value > 0;
    });

    /**
     * Get the student group specs for the selected course
     * We use this to see if the selected course is being taught in groups or 1:1
     * @returns {Promise<void>}
     */
    const getStudentgroupSpecsForCourse = async () => {
        if (courseId.value > 0) {
            try {
                const response = await axios.get('/api/getstudentgroupsofcourse/' + courseId.value);
                studentGroupSpecsOfCourse.value = response.data;
            } catch (error) {
                failNoty(ucFirst(translate("generic.loadfailed")) + ": " + error.response.data.message);
            }
        }
        // Check for match with student group specs. 
        // Making sure the added student fields keep reactivity
        const updatedStudents = [...studentsOnCourse.value];
        for (const student of updatedStudents) {
            student.group_id = 0;
            student.groupName = "";
            for (const spec of studentGroupSpecsOfCourse.value) {
                const studentIds = spec.students.map(s => s.id);
                if (studentIds.includes(student.id)) {
                    student.group_id = spec.id;
                    student.groupName = spec.lastname;
                    break;
                }
            }
        }
        studentsOnCourse.value = updatedStudents;
    }

    return {
        archiveCourse,
        courseGroup,
        courseId,
        courseName,
        getStudentgroupSpecsForCourse,
        // getStudentgroupsOfCourse,
        isSaveable,
        isTrialCourse,
        initCourseData,
        lessonTable,
        maxStudentGroupSize,
        minStudentGroupSize,
        newMode,
        priceExTaxCalculated,
        priceIncTax,
        priceIsPer,
        priceIsPerOptions,
        pricePreAdult,
        recurrenceOption,
        saveCourseData,
        studentGroupSpecsOfCourse,
        studentsOnCourse
    }
}
