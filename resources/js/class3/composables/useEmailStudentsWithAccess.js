import { ref } from 'vue';
import useLang from "./useLang.js";
import useToast from "./useToast.js";
import useApi from './useApi.js';

const { ucFirst, translate } = useLang();
const { failToast, successToast } = useToast();
const { apiGet, apiPost } = useApi();

//const chosenEmail = ref('');
const students = ref([]);
const busy = ref(false);


export default function useEmailStudentsWithAccess() {
    const chosenTemplate = ref(-1);
    const subject = ref('');
    const from = ref('');
    const mailBody = ref('');
    const getStudentsWithAccess = async () => {
        try {
            busy.value = true;
            const resp = await apiGet('/api/studentswithaccess');
            students.value = resp.data;
        } catch (error) {
            failToast(ucFirst(translate('generic.errorretrievingstudents')) + ":" + error);
        } finally {
            busy.value = false;
        }
    }

    const sendmailmulti = async () => {
        busy.value = true;
        const data = {
            to: students.value.map(s => {
                return { id: s.id, email: s.email }
            }),
            from: from.value,
            subject: subject.value,
            body: mailBody.value,
        };
        try {
            const resp = await apiPost('/api/sendmultiemail', data);
            successToast(translate('email.emailsent'));
        } catch(error) {
            failToast(ucFirst(translate('email.errorsendingemail')) + ":" + error);
        } finally {
            busy.value = false;
        }
    };

    const sendmailsingle = async ( student ) => {
        busy.value = true;
        const data = {
            to: student.studentId,
            from: from.value,
            subject: subject.value,
            body: mailBody.value,
        };
        try {
            const resp = await apiPost('/api/sendsingleemail', data);
            successToast(translate('email.emailsent'));
        } catch(error) {
            failToast(ucFirst(translate('email.errorsendingemail')) + ":" + error);
        } finally {
            busy.value = false;
        }
    }

    return {
        busy,
        chosenTemplate,
        // chosenEmail,
        from,
        getStudentsWithAccess,
        mailBody,
        sendmailmulti,
        sendmailsingle,
        students,
        subject
    }
}
