import { ref } from 'vue';
import useNoty from "./useNoty.js";
import useLang from "./useLang.js";
import useApi from "./useApi.js";

const { failNoty, successNoty } = useNoty();
const { translate, translateChoice, ucFirst } = useLang();
const { apiGet, apiPost, apiPut, apiDelete } = useApi();

const busy = ref(false);
const defaultChecklists = ref([]);
const defaultChecklistIdToDelete = ref(0);
const defaultChecklistToEdit = ref(null);

export default function useDefaultChecklists() {

    const getDefaultChecklists = async () => {
        busy.value = true;
        try {
            const response = await apiGet('/api/defaultchecklists');
            defaultChecklists.value = response.data;
        } catch (error) {
            failNoty(error);
        } finally {
            busy.value = false;
        }
    }

    const removeDefaultChecklist = async () => {
        try {
            busy.value = true;
            await apiDelete(`/api/defaultchecklists/${defaultChecklistIdToDelete.value}`);
            await getDefaultChecklists();
            successNoty(ucFirst(translateChoice('generic.defaultchecklists', 1)) + ' ' + translate('generic.deleted'));
        } catch (error) {
            failNoty(error);
        } finally {
            busy.value = false;
        }
    };

    const saveDefaultChecklist = async () => {
        try {
            busy.value = true;
            if (defaultChecklistToEdit.value.id > 0) {
                // update record
                await apiPut(`/api/defaultchecklists/${defaultChecklistToEdit.value.id}`, defaultChecklistToEdit.value);
            } else {
                // new record
                await apiPost('/api/defaultchecklists', defaultChecklistToEdit.value);
            }
            // retrieve the new list from the backend
            await getDefaultChecklists();
        } catch (error) {
            throw error;
        } finally {
            busy.value = false;
        }
    }

    const getNrOfItemsFilled = (defaultChecklist) => {
        let count = 0;
        for (let i = 1; i <= 12; i++) {
            if (defaultChecklist['item' + i] !== "" && defaultChecklist['item' + i] !== null) {
                count++;
            }
        }
        return count;
    }

    const emptyDefaultChecklist = () => {
        defaultChecklistToEdit.value = {
            id: 0,
            name: "",
            auto_add: 0,
            item1: "",
            item2: "",
            item3: "",
            item4: "",
            item5: "",
            item6: "",
            item7: "",
            item8: "",
            item9: "",
            item10: "",
            item11: "",
            item12: "",
        };
    }

    return {
        busy,
        emptyDefaultChecklist,
        getDefaultChecklists,
        getNrOfItemsFilled,
        defaultChecklists,
        defaultChecklistIdToDelete,
        defaultChecklistToEdit,
        removeDefaultChecklist,
        saveDefaultChecklist,
    };
}
