import { ref } from 'vue';
import axios from "axios";
import useNoty from "./useNoty.js";
import useLang from "./useLang.js";

const studentGroups = ref([]);
const { failNoty } = useNoty();
const { ucFirst, translate } = useLang();
const busy = ref(false);
const studentGroupToDelete = ref(0);

export default function useStudentGroups() {
    const getStudentGroups = async () => {
        busy.value = true;
        try {
            const response = await axios.get('/api/studentgroups');
            studentGroups.value = response.data.data;
            busy.value = false;
        } catch (err) {
            failNoty(
                ucFirst(translate('generic.errorloadingstudentgroups')) + ' ' + err,
                ucFirst(translate('generic.error'))
            );
            busy.value = false;
        }
    };
    const deleteStudentGroup = async () => {
        busy.value = true;
        try {
            await axios.delete(`/api/studentgroups/${studentGroupToDelete.value}`);
            // reload
            await getStudentGroups();
            busy.value = false;
        } catch (err) {
            failNoty(
                ucFirst(translate('generic.errordeletingstudentgroup')) + ' ' + err,
                ucFirst(translate('generic.error'))
            );
            busy.value = false;
        }
    };
    return {
        busy,
        deleteStudentGroup,
        getStudentGroups,
        studentGroups,
        studentGroupToDelete
    };
}
