import { computed, ref } from "vue";
import useApi from './useApi.js';
import useToast from './useToast.js';
import useLang from "./useLang.js";
import useLibraries from "./useLibraries.js";

const documents = ref([]);
const documentSearchKey = ref("");
const { translate, ucFirst } = useLang();
const { failToast } = useToast();
const { getLibraries } = useLibraries();
const { apiGet, apiPut } = useApi();

export default function useDocuments() {
    const getDocuments = async (libraryId) => {
        const response = await apiGet(`/api/documents/${libraryId}`);
        documents.value = response.data;
    };

    const filteredDocuments = computed(() => {
        return documents.value.filter(doc => doc.label.toLowerCase().includes(documentSearchKey.value.toLowerCase()));
    });

    const addDocumentToLibrary = async (libraryId, docId) => {
        try {
            await apiPut(`/api/attachdoctolib/${libraryId}/${docId}`);
            documentSearchKey.value = '';
            await getLibraries();
        } catch (error) {
            failToast(`${ucFirst(translate('generic.attachingdocumentfailed'))}: ${error}`);
        }
    }

    const removeDocumentFromLibrary = async (libraryId, docId) => {
        try {
            await apiPut(`/api/detachdocfromlib/${libraryId}/${docId}`);
            documentSearchKey.value = '';
            await getLibraries();
        } catch (error) {
            failToast(`${ucFirst(translate('generic.detachingdocumentfailed'))}: ${error}`);
        }
    }

    return {
        addDocumentToLibrary,
        documentSearchKey,
        filteredDocuments,
        getDocuments,
        removeDocumentFromLibrary
    };
}
