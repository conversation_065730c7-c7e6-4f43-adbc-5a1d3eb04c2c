import { ref } from 'vue';
import useApi from './useApi.js';
import useToast from './useToast.js';
import useLang from './useLang.js';

const { translate, ucFirst } = useLang();
const { apiGet } = useApi();
const { failToast } = useToast();

// Shared state
const templateVariables = ref([]);
const templateVariablesData = ref({});
const busy = ref(false);


// Ik denk dat deze niet (meer) nodig zijn, kan niet vinden waar ze gebruikt worden.
// Oorspronkelijk is dit de mixin Datastructures.js
// even laten staan tot we zeker weten dat het weg mag. 2025-05-21

// // Calendar icons and tooltips from Datastructures mixin
// const calendarIcons = {
//     time: 'fas fa-clock',
//     date: 'fas fa-calendar',
//     up: 'fa fa-chevron-up',
//     down: 'fa fa-chevron-down',
//     previous: 'fa fa-chevron-left',
//     next: 'fa fa-chevron-right',
//     today: 'fas fa-calendar-day',
//     clear: 'fas fa-trash',
//     close: 'fas fa-times'
// };
//
// // We need to use a computed property for tooltips since they depend on translations
// const getCalendarTooltips = computed(() => {
//     return {
//         today: translate('localisation.today'),
//         clear: translate('localisation.clear'),
//         close: translate('localisation.close'),
//         selectMonth: translate('localisation.selectmonth'),
//         prevMonth: translate('localisation.prevmonth'),
//         nextMonth: translate('localisation.nextmonth'),
//         selectYear: translate('localisation.selectyear'),
//         prevYear: translate('localisation.prevyear'),
//         nextYear: translate('localisation.nextyear'),
//         selectDecade: translate('localisation.selectdecade'),
//         prevDecade: translate('localisation.prevdecade'),
//         nextDecade: translate('localisation.nextdecade'),
//         prevCentury: translate('localisation.prevcentury'),
//         nextCentury: translate('localisation.nextcentury')
//     };
// });

export default function useMailTemplateVariables() {
    /**
     * Get all available template variables
     * @param {number} registrationId - Optional registration ID to prefill variables with data
     * @returns {Promise<void>}
     */
    const getTemplateVariables = async (registrationId = 0) => {
        busy.value = true;
        try {
            const endpoint = registrationId > 0
                ? `/api/templateVariables/${registrationId}`
                : '/api/templateVariables';

            const response = await apiGet(endpoint);
            templateVariables.value = response.data.variables || [];
            templateVariablesData.value = response.data.data || {};
        } catch (error) {
            failToast(
                ucFirst(translate('generic.couldnotgettemplatevariables')) + ': ' + error.message,
                ucFirst(translate('generic.error'))
            );
        } finally {
            busy.value = false;
        }
    };

    return {
        busy,
        // calendarIcons, - zie hierboven
        // calendarTooltips: getCalendarTooltips, - zie hierboven
        getTemplateVariables,
        templateVariables,
        templateVariablesData
    };
}
