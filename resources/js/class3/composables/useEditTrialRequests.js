import { computed, ref, watch } from "vue";
import axios from 'axios';
import useDateTime from "./useDateTime.js";
import useNoty from "./useNoty.js";
import useLang from "./useLang.js";

const trialRequests = ref([]);
const trialRequest = ref({});
const idToView = ref(0);
const idToDelete = ref(0);
const orderBy = ref("created_at");
const dirAsc = ref(true);
const allStatuses = ref([]);

const { displayAge } = useDateTime();
const { failNoty, successNoty } = useNoty();
const { translate, ucFirst } = useLang();

export default function useEditTrialRequests() {

    /**
     * lookup the requested trial request by the trial student id
     */
    watch(idToView, async () => {
        if (idToView.value === 0) {
            console.log('RESET');
            trialRequest.value = {};
        } else if (idToView.value > 0) {
            console.log('VIEW TRIALREQUEST');
            trialRequest.value = trialRequests.value.find(entry => entry.trialstudent.id === idToView.value);
        }
    });

    /**
     * refresh or initiate from backend
     * @returns {Promise<void>}
     */
    const getAllTrialRequests = async () => {
        const resp = await axios.get('/api/gettrialrequests');
        trialRequests.value = resp.data;
    }

    const getAllStatuses = async () => {
        const resp = await axios.get("/api/trialrequeststatuses");
        allStatuses.value = resp.data;
    }

    const orderedTrialRequests = computed(() => {
        if (dirAsc.value) {
            return trialRequests.value.sort((a, b) => {
                if (orderBy.value === "created_at") {
                    return a.created_at > b.created_at ? 1 : -1;
                } else if (orderBy.value === "status") {
                    return a.trialstudent.trialrequeststatus.id > b.trialstudent.trialrequeststatus.id ? 1 : -1;
                } else {
                    return a.trialstudent.lastname > b.trialstudent.lastname ? 1 : -1;
                }
            });
        } else {
            return trialRequests.value.sort((a, b) => {
                if (orderBy.value === "created_at") {
                    return a.created_at < b.created_at ? 1 : -1;
                } else if (orderBy.value === "status") {
                    return a.trialstudent.trialrequeststatus.id < b.trialstudent.trialrequeststatus.id ? 1 : -1;
                } else {
                    return a.trialstudent.lastname < b.trialstudent.lastname ? -1 : 1;
                }
            });
        }
    });

    /**
     * form 1 string based on firstname, lastname and optionally preposition
     * prevents having two spaces if preposition is empty
     r* @param trialStudent
     * @returns {string}
     */
    const getFullName = (trialStudent) => {
        const fullName = trialStudent.preposition != null && trialStudent.preposition !== ''
            ? `${trialStudent.firstname} ${trialStudent.preposition} ${trialStudent.lastname}`
            : `${trialStudent.firstname} ${trialStudent.lastname}`;
        return `${fullName} (${displayAge(trialStudent.date_of_birth)})`;
    };

    const deleteTrialRequest = async () => {
        try {
            await axios.delete(`/api/trialstudents/${idToDelete.value}`);
            // refresh list from backend
            await getAllTrialRequests();
            successNoty(ucFirst(translate('generic.deletesuccessful')));
        } catch (e) {
            failNoty(ucFirst((translate('generic.deletefailed'))) + ': ' + e.message);
        }
    };

    const createStudentFromTrialRequest = async (trialStudentId) => {
        try {
            const data = {
                trialStudentId
            };
            const response = await axios.post('/api/studentfromtrialstudents', data);
            const generatedStudentId = response.data.generated_student_id;
            successNoty(ucFirst(translate('generic.studentcreatedfromtrialrequest')));
            // redirect after 2 seconds (read success message first)
            setTimeout(() => {
                window.location.href = `/students/${generatedStudentId}/edit`;
            }, 1000);
        } catch (e) {
            failNoty(ucFirst(translate('generic.error')) + ': ' + e.message);
        }
    };

    const updateTrialRequest = async (trialStudent) => {
        try {
            const response = await axios.put(`/api/trialstudents/${trialStudent.id}`, trialStudent);
            successNoty(ucFirst(translate('generic.updatesuccess')), ucFirst(translate('generic.updatesuccess')), 2000);
            // refresh list from backend
            await getAllTrialRequests();
        } catch (e) {
            failNoty(ucFirst(translate('generic.updatefailed')) + ": " + e.message);
        }
    }

    return {
        allStatuses,
        createStudentFromTrialRequest,
        deleteTrialRequest,
        dirAsc,
        getAllStatuses,
        getAllTrialRequests,
        getFullName,
        idToDelete,
        idToView,
        orderBy,
        orderedTrialRequests,
        trialRequest,
        trialRequests,
        updateTrialRequest
    };
}
