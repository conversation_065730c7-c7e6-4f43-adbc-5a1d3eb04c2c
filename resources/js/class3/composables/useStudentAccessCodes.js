import { computed, ref } from "vue";
import useApi from './useApi.js';
import useLang from './useLang.js';
import useToast from "./useToast.js";

const { ucFirst, translate, translateChoice } = useLang();
const { successToast, failToast } = useToast();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

const searchstudentsfilterkey = ref('');
const students = ref([]);
const emailArchive = ref([]);   // list of last time the student was emailed to request fill in preferences
                                                            // key is the access token
                                                            // value is the date
const revokeFilter = ref('');   // choose whether to REVOKE access for all students
                                                            // or just the students that have already filled in their preferences (last 60 days)
                                                            // ['all', 'filter'] are the possible values
const grantFilter = ref('');    // choose whether to GRANT access for all students
                                                            // or just the students that are yet to fill in their preferences (last 60 days)
                                                            // ['all', 'filter'] are the possible values
const domainThreshold = ref(60);// days since last email to consider a student as 'not yet filled in'
const busy = ref(false);        // during API calls

export default function useStudentAccessCodes() {

    /**
     * get all active students
     * @returns {Promise<void>}
     */
    const getStudents = async () => {
        try {
            const response = await apiGet(`/api/getActiveStudents`);
            students.value = response.data.students;
            emailArchive.value = response.data.emailArchive;
            domainThreshold.value = response.data.domain_schedule_threshold;
        } catch (error) {
            failToast(error);
        }
    }

    /**
     * see revoke filter for the students that are actually hit by this action
     * @returns {Promise<void>}
     */
    const revokeAccessToAll = async () => {
        if (revokeFilter.value === '') {
            return;
        }
        try {
            const resp = await apiDel(`/api/revokeAccessFromAll/${revokeFilter.value}`);
            successToast(ucFirst(translateChoice('generic.accessrevokedallstudents', {'affected': resp.data.affected})));
            clearPopupFieldsDelete();
            await getStudents();
        } catch (error) {
            failToast(ucFirst(translate('generic.errorrevokingaccess')) + error);
        }
    }

    /**
     * grant access to all active student
     * grantFilter (radio buttons) determines whether only to target
     * students that have not filled in at all or longer than 60 days ago (configurable)
     * @returns {Promise<void>}
     */
    const grantAccessToAll = async () => {
        if (grantFilter.value === '') {
            return;
        }
        try {
           const resp = await apiPost(`/api/grantAccessToAllActive/${grantFilter.value}`);
            successToast(ucFirst(translateChoice('generic.accessgrantedallstudents', {'affected': resp.data.affected})));
            clearPopupFieldsGrant();
            await getStudents();
        } catch (error) {
            failToast(ucFirst(translate('generic.errorgrantingaccess')) + error);
        }
    }

    /**
     * Resets the radio buttons in the popup for a next action
     */
    const clearPopupFieldsDelete = () => {
        revokeFilter.value = '';
    }

    /**
     * Resets the radio buttons in the popup for a next action
     */
    const clearPopupFieldsGrant = () => {
        grantFilter.value = '';
    }

    /**
     * remove access for a single student
     * (only executed after are-you-sure => yes)
     * @param student
     * @returns {Promise<void>}
     */
    const removeAccess = async (student) => {
        try {
            await apiPut(`/api/removeAccess/${ student.studentId }`);
            await getStudents();
            successToast(ucFirst(translate('generic.accessrevokedforstudent', {'studentname':student.name})));
        } catch (error) {
            failToast(ucFirst(translate('generic.errorrevokingaccess')) + error);
        }
    }

    const addAccess = async (student) => {
        try {
            await apiPut(`/api/addAccess/${ student.studentId }`);
            await getStudents();
            successToast(ucFirst(translate('generic.accessgrantedforstudent', {'studentname':student.name})));
        } catch (error) {
            failToast(ucFirst(translate('generic.errorgrantingaccess')) + error);
        }
    }

    const filteredStudents = computed(() => {
        return students.value.filter(student => {
            return student.name.toLowerCase().includes(searchstudentsfilterkey.value.toLowerCase());
        });
    });

    return {
        addAccess,
        busy,
        clearPopupFieldsDelete,
        clearPopupFieldsGrant,
        domainThreshold,
        emailArchive,
        filteredStudents,
        getStudents,
        grantAccessToAll,
        grantFilter,
        searchstudentsfilterkey,
        students,
        removeAccess,
        revokeAccessToAll,
        revokeFilter
    }

}
