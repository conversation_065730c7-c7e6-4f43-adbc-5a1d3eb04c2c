import moment from 'moment';
import useLang from './useLang.js';

const { translate } = useLang();
export default function useDateTime () {
    /**
     * Show locale specific datetime
     * - refactored to use Intl.DateTimeFormat
     * @param input
     * @param showSeconds
     * @param showDayName
     * @returns {string}
     */
    const displayDateTime = (input = '', showSeconds = false, showDayName = false) => {
        if (input == null || input?.length < 10) return '-';
        let format = '';
        const isoLang = translate('generic.language') === 'nl' ? 'nl-NL' : 'en-GB';
        // don't set a timezone here: it wil modify the date to the local timezone
        // and we want to show the date as it is stored in the database
        let options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        };
        if (showDayName) {
            options = {
                weekday: 'short',
                ...options
            };
        }
        if (showSeconds) {
            options = {
                second: '2-digit',
                ...options
            };
        }
        const date = new Date(input);
        format = new Intl.DateTimeFormat(isoLang, options).format(date);
        return format;
    };

    /**
     * Show locale specific date
     * @param input
     * @param suppressYearDisplay
     * @returns {string}
     */
    const displayDate = (input = '', suppressYearDisplay= false) => {
        if (input == null || input?.length < 10) return "-";
        if (input.substring(2, 3) === '-') {
            return input;
        }        
        if (translate('generic.language') === "nl") {
            if (suppressYearDisplay) {
                return moment(input).format('DD-MM');
            }
            return moment(input).format('DD-MM-YYYY');
        } else {
            if (suppressYearDisplay) {
                return moment(input).format('MM-DD');
            }
            return moment(input).format('MM-DD-YYYY');
        }
    };

    /**
     * Show locale specific time
     * @param input
     * @param suppressSeconds
     * @returns {string}
     */
    const displayTime = (input = '', suppressSeconds = true) => {
        if (input == null || input?.length < 5) return "-";
        // there may be a date before the actual time
        input = input.substring(input.length - 8);
        if (suppressSeconds) {
            return moment(input, 'HH:mm:ss').format('HH:mm');
        }
        return moment(input, 'HH:mm:ss').format('HH:mm:ss');
    };

    /**
     * Show age in years
     * @param input
     * @returns {number|string}
     */
    const displayAge = (input = '') => {
        if (input == null || input === '') return '-';
        // Dutch or mysql notation?
        if (input.substring(4, 5) === '-') {
            // getting age for mysql notation date
            return moment().diff(input, 'years');
        } else {
            // check patter for mysql notation, needs to be dddd-dd-dd
            if (!/^[0-9]{2}-[0-9]{2}-[0-9]{4}$/.test(input)) return '-';
            // getting age for Dutch notation date
            const inputMysql = moment(input, 'DD-MM-YYYY').format('YYYY-MM-DD');
            return moment().diff(inputMysql, 'years');
        }
    };

    /**
     * Check if date is in the future
     * @param qDate
     * @returns {boolean}
     */
    const isFutureDate = (qDate) => {
        if (qDate == null || qDate === '') return false;
        // pattern check, MySql or NL format?
        if (qDate.substring(4, 5) === '-') {
            // MySql format
            qDate = moment(qDate, 'YYYY-MM-DD');
        } else {
            // NL format?
            if (!/^[0-9]{2}-[0-9]{2}-[0-9]{4}$/.test(qDate)) return false;
            qDate = moment(qDate, 'DD-MM-YYYY');
        }
        return (parseInt(moment().diff(qDate, 'days')) <= 0);
    };

    const isFutureDateTime = (qDateTime) => {
        if (qDateTime == null || qDateTime === '') return false;
        // check pattern, MySql or NL format?
        if (qDateTime.substring(4, 5) === '-') {
            // MySql format
            qDateTime = moment(qDateTime, 'YYYY-MM-DD HH:mm:ss');
        } else {
            // NL format?
            if (!/^[0-9]{2}-[0-9]{2}-[0-9]{4}$/.test(qDateTime)) return false;
            qDateTime = moment(qDateTime, 'DD-MM-YYYY HH:mm:ss');
        }
        return (parseInt(moment().diff(qDateTime, 'seconds')) <= 0);
    };

    /**
     * Get end time based on start time and duration
     * @param startTime 5 or 8 digits (including seconds)
     * @param duration in minutes
     */
    const getEndTime = (startTime, duration) => {
        return (startTime != null && startTime.length === 5)
            ? moment(startTime, 'HH:mm').add(duration, 'm').format('HH:mm')
            : moment(startTime, 'HH:mm:ss').add(duration, 'm').format('HH:mm');
    };

    /**
     * Convert input to MySql date time format
     * @param input
     * @returns {string|null}
     */
    const convertToMysqlDateTime = (input) => {
        if (input == null || input === '') return null;
        // get the last 10 characters, there may be a day name before the actual data
        input = input.substring(input.length - 10);

        // pattern check, MySql or NL format?
        if (input.substring(4, 5) === '-') {
            // MySql format
            return input;
        } else {
            // NL format?
            if (!/^[0-9]{2}-[0-9]{2}-[0-9]{4}$/.test(input)) return null;
            return moment(input, 'DD-MM-YYYY').format('YYYY-MM-DD');
        }
    };

    /**
     * Get week number based on the given date.
     * @param inputDate
     * @returns {number|string}
     */
    const getWeekNumber = (inputDate)    => {
        if (inputDate == null || inputDate === '') return '-';
        const date = inputDate.substring(0, 10);
        // pattern check, MySql or NL format?
        if (date.substring(4, 5) === '-') {
            // MySql format
            return moment(date).isoWeek();
        } else {
            // NL format?
            if (!/^[0-9]{2}-[0-9]{2}-[0-9]{4}$/.test(date)) return '-';
            return moment(date, 'DD-MM-YYYY').isoWeek();
        }
    };

    /**
     * Get the date of the week based on the week number, year and day name.
     * So "give me the date of the monday in week 12 of year 2021"
     * would be getDateOfWeekByDayName(12, 2021, 'Monday')
     * @param weeknumber
     * @param year
     * @param dayname one of the days of the week, in English, capitalized
     * @returns {string} the requested date or 'Invalid date' if the input is invalid
     */
    const getDateOfWeekByDayName = (weeknumber, year, dayname) => {
        const days = ['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'];
        
        // Input validation
        if (
            !Number.isInteger(weeknumber) ||
            weeknumber == null ||
            !Number.isInteger(year)||
            year == null ||
            year < 1900 ||
            dayname == null ||
            weeknumber < 1 ||
            weeknumber > 53 ||
            !days.includes(dayname)
        ) return 'Invalid date';

        // Find the first Thursday of the year (according to ISO 8601)
        const jan1 = new Date(year, 0, 1);
        const dayOffset = ((11 - jan1.getDay()) % 7);
        const firstThursday = new Date(year, 0, 1 + dayOffset);
        
        // Calculate the Monday of the desired week
        const targetDate = new Date(firstThursday);
        targetDate.setDate(firstThursday.getDate() + (weeknumber - 1) * 7 - 3); // -3 to get to Monday
        
        // Add days based on the desired day name
        const dayNumber = days.indexOf(dayname);
        targetDate.setDate(targetDate.getDate() + dayNumber);
        
        // Format the date to YYYY-MM-DD
        const resultYear = targetDate.getFullYear(); // Use the actual year of the resulting date
        const month = String(targetDate.getMonth() + 1).padStart(2, '0');
        const day = String(targetDate.getDate()).padStart(2, '0');
        
        return `${resultYear}-${month}-${day}`;
    }

    /**
     * Returns the name of the month based on the given date.
     * @param {string} inputDate - The date in either MySql ('YYYY-MM-DD') or NL ('DD-MM-YYYY') format.
     * @param {boolean} [short=true] - Optional parameter to specify whether to return the short month name (e.g. 'Jan') or the full month name (e.g. 'January'). Default is true.
     * @returns {string} - The name of the month based on the given date.
     * If the date is null, empty or does not match the specified format, '-' is returned.
     */
    const getMonthName = (inputDate, short = true) => {
        if (inputDate == null || inputDate === '') return '-';
        const date = inputDate.substring(0, 10);
        // pattern check, MySql or NL format?
        if (date.substring(4, 5) === '-') {
            // MySql format
            return moment(date).format(short ? 'MMM' : 'MMMM');
        } else {
            // NL format?
            if (!/^[0-9]{2}-[0-9]{2}-[0-9]{4}$/.test(date)) return '-';
            return moment(date, 'DD-MM-YYYY').format(short ? 'MMM' : 'MMMM');
        }
    }
    /**
     * Returns the name of the month based on the given year and week number.
     * Looks for the monday of the given week number in case the week contains a month change.
     * @param year
     * @param weekNr
     * @param short
     */
    const getMonthNameOfMondayOfWeekNr = (year, weekNr, short = true) => {
        if (year == null || weekNr == null) return '-';
        const date = moment().day("Monday").year(year).week(weekNr);
        return moment(date, 'DD-MM-YYYY').format(short ? 'MMM' : 'MMMM');
    }


    /**
     * Get the highest week number for the given year.
     * According to ISO 8601
     * @param year - The year for which to get the highest week number.
     * @returns {number}
     */
    const getHighestWeekNumber = (year) => {
        const date = new Date(year, 11, 31);
        let weekNumber = moment().year(year).isoWeeksInYear();
        return Number(weekNumber);
    }

    return {
        convertToMysqlDateTime,
        displayAge,
        displayDate,
        displayDateTime,
        displayTime,
        getDateOfWeekByDayName,
        getEndTime,
        getHighestWeekNumber,
        getMonthName,
        getMonthNameOfMondayOfWeekNr,
        getWeekNumber,
        isFutureDate,
        isFutureDateTime
    };
}
