import { computed, ref } from 'vue';
import axios from "axios";
import useNoty from "./useNoty.js";

const { failNoty } = useNoty();
const registrationData = ref([]);
const busy = ref(false);

export default function useRegistration () {
    const getRegistrationData = (async () => {
        // Fetch data from API getregistrationdata
        try {
            busy.value = true;
            const response = await axios.get('/api/getregistrationdata');
            registrationData.value = response.data;
        } catch (error) {
            failNoty(error.response.data.message);
        } finally {
            busy.value = false;
        }
    });

    const registrationsForThisYear = computed(() => {
        return registrationData.value?.registrationsThisYear;
    });

    const registrationsForCourses = computed(() => {
        return registrationData.value?.registrations;
    });

    return {
        busy,
        getRegistrationData,
        registrationData,
        registrationsForCourses,
        registrationsForThisYear
    }
}
