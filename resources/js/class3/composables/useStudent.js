import { ref, watch } from 'vue';
import useToast from './useToast.js';
import useLang from './useLang.js';
import useApi from './useApi.js';
const { failToast, successToast } = useToast();
const { translate } = useLang();

const studentId = ref(0);
const validStudentId = ref(false);
const studentDataById = ref({firstname: '', lastname: ''});
const delStudentLastname = ref('');
const validLastnameEntered = ref(false);
const { apiGet, apiDel } = useApi();

export default function useStudent() {
    const getStudentData = async () => {
        const response = await apiGet(`/api/students/${studentId.value}`);
        studentDataById.value = response.data;
    };

    const deleteStudent = async () => {
        if (validLastnameEntered.value) {
            try {
                await apiDel(`/api/students/full/${studentId.value}`);
                successToast(translate('generic.studentdataremoved'));
            } catch (error) {
                failToast(translate('generic.studentdatacouldnotberemoved'));
            }
        } else {
                failToast(`${translate('generic.lastnameincorrect')} ${delStudentLastname.value} vs ${studentDataById.value.lastname}`);
        }
    };

    const resetStudentId = () => {
        studentId.value = 0;
        studentDataById.value = {firstname: '', lastname: ''};
        delStudentLastname.value = '';
        validStudentId.value = false;
        validLastnameEntered.value = false;
    };

    watch(studentId, () => {
        validStudentId.value = !isNaN(studentId.value) && studentId.value > 0;
    });

    watch(delStudentLastname, () => {
        validLastnameEntered.value = delStudentLastname.value.toLowerCase() === studentDataById.value.lastname.toLowerCase();
    });

    return {
        deleteStudent,
        getStudentData,
        resetStudentId,
        studentId,
        validStudentId,
        studentDataById,
        delStudentLastname,
        validLastnameEntered
    };
}
