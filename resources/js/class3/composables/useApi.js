import axios from 'axios';
import useLang from "./useLang.js";

const { translate, ucFirst } = useLang();

// Create a centralized axios instance
const api = axios.create({
    timeout: 120000, // 2 minutes
    withCredentials: true,
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

// Add CSRF token to all requests
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    api.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error(ucFirst(translate('api.noCSRFToken')));
}

// Set default retry count
api.defaults.retry = 3;

export default function useApi() {
    // Add response interceptor for error handling
    api.interceptors.response.use(
        response => response,
        error => {
            if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                if (error.response.status === 401) {
                    // Handle unauthorized access
                    window.location.href = '/login';
                } else if (error.response.status === 419) {
                    // CSRF token mismatch - just throw the error
                    error.message = ucFirst(translate('api.sessionExpired'));
                } else if (error.response.status >= 500) {
                    // Server error - just throw the error
                    error.message = ucFirst(translate('api.serverError'));
                }
            } else if (error.request) {
                // The request was made but no response was received
                error.message = ucFirst(translate('api.noResponseFromServer'));
            } else {
                // Something happened in setting up the request
                error.message = `${ucFirst(translate('api.errorSettingUpRequest'))}: ${error.message}`;
            }
            return Promise.reject(error);
        }
    );

    // Helper methods for common API operations
    const apiGet = async (url, config = {}) => {
        return await api.get(url, config);
    };

    const apiPost = async (url, data = {}, config = {}) => {
        return await api.post(url, data, config);
    };

    const apiPut = async (url, data = {}, config = {}) => {
        return await api.put(url, data, config);
    };

    const apiDel = async (url, config = {}) => {
        return await api.delete(url, config);
    };

    return {
        api,
        apiGet,
        apiPost,
        apiPut,
        apiDel
    };
}

// Export the api instance directly for simpler imports
export { api };
