import useLang from './useLang.js';
import Noty from 'noty';

export default function useNoty () {
    const { ucFirst, translate } = useLang();

    /**
     * Show the user a message of a successful action
     * Click anywhere to close it
     * @param bodyText
     * @param title
     * @param timeout // [integer|boolean] delay for closing event in milliseconds. Set false for sticky notifications
     */
    const successNoty = (bodyText, title = '', timeout = 3000) => {
        const useTitle = title === ''
            ? `<h3>${ucFirst(translate('generic.notice'))}</h3>`
            : `<h3>${title}</h3>`;
        new Noty({
            type: 'success',
            theme: 'bootstrap-v4',
            text: useTitle + bodyText,
            timeout,
            closeWith: ['click', 'backdrop']
        }).show();
    };

    /**
     * Show the user a message of a failed action
     * @param bodyText
     * @param title
     * @param timeout
     */
    const failNoty = (bodyText, title = '', timeout = 0) => {
        const useTitle = title === ''
            ? `<h3>${ucFirst(translate('generic.warn'))}</h3>`
            : `<h3>${title}</h3>`;
        new Noty({
            type: 'error',
            theme: 'bootstrap-v4',
            text: useTitle + bodyText,
            timeout,
            closeWith: ['click', 'button']
        }).show();
    };

    return {
        failNoty,
        successNoty
    };
}
