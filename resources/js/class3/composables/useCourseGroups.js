import { computed, ref } from "vue";
import axios from "axios";
import useLang from "./useLang.js";
import useNoty from "./useNoty.js";

const newMode = ref(true);
const courseGroupId = ref(0);
const name = ref('');
const isTrialGroup = ref(false);
const webDescription = ref('');
const coursesOnCourseGroup = ref([]);

const { ucFirst, translate } = useLang();
const { successNoty, failNoty } = useNoty();

export default function useCourseGroups() {

    const initCourseGroupData = async () => {
        if (courseGroupId.value > 0) {
            // load the coursegroup data
            try {
                const response = await axios.get('/api/coursegroups/' + courseGroupId.value);
                const coursegroup = response.data;
                name.value = coursegroup.name;
                isTrialGroup.value = coursegroup.is_trial_group;
                webDescription.value = coursegroup.webdescription;
                coursesOnCourseGroup.value = coursegroup.courses; // also has recurrence options and students
                newMode.value = false;
            } catch(error) {
                failNoty(ucFirst(translate("generic.loadfailed")) + ": " + error);
            }
        }
    };

    const saveCourseGroupData = async () => {
        const data = {
            name: name.value,
            isTrialGroup: isTrialGroup.value,
            webDescription: webDescription.value || '',
        }
        let url, method;
        if (newMode.value) {
            url = '/api/coursegroups';
            method = 'post';
        } else {
            url = '/api/coursegroups/' + courseGroupId.value;
            method = 'put';
        }
        try {
            const response = await axios[method](url, data);
            successNoty(ucFirst(translate("generic.saved")));
            // Reset the dirty flag. We can navigate away without warning
            window.dirty = false;
            // Only if we are in new mode navigate: to edit mode.
            if (newMode.value) {
                setTimeout(() => {
                    window.location.href = '/coursegroups/' + response.data.id + '/edit';
                }, 1000);
            }
        } catch (error) {
            failNoty(ucFirst(translate("generic.savefailed")) + ": " + error);
        }
    };

    const isSaveable = computed(() => {
        return name.value?.length > 0;
    })

    return {
        courseGroupId,
        coursesOnCourseGroup,
        initCourseGroupData,
        isSaveable,
        isTrialGroup,
        name,
        newMode,
        saveCourseGroupData,
        webDescription
    }

}
