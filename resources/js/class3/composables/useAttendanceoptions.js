import { ref, watch } from 'vue';
import useApi from './useApi.js';
import useToast from './useToast.js';

const attendanceoptions = ref([]); // everything currently in the database
const attendanceoptionIdToEdit = ref(0); // when editing, use this ID
const attendanceoptionIdToDelete = ref(0); // when deleting, use this ID
const attendanceoptionToEdit = ref(null); // when editing, this is the full record
const errorMessage = ref(''); // to be displayed anywhere on the screen
const busy = ref(false); // during API operations
export default function useAttendanceoptions () {
    const { apiGet, apiPost, apiPut, apiDel } = useApi();
    const { successToast, failToast } = useToast();
    const clearEditObject = () => {
        attendanceoptionToEdit.value = { id: 0, label: '', action_tutor: '' };
    };
    const getAll = async () => {
        try {
            busy.value = true;
            const response = await apiGet('/api/attendanceoptions');
            attendanceoptions.value = response.data;
        } catch (e) {
            errorMessage.value = e.message;
            failToast(e.message);
        } finally {
            busy.value = false;
        }
    };

    const saveAttendanceoption = async () => {
        try {
            busy.value = true;
            if (attendanceoptionIdToEdit.value > 0) {
                // update record
                await apiPut(`/api/attendanceoptions/${attendanceoptionIdToEdit.value}`, attendanceoptionToEdit.value);
            } else {
                // new record
                await apiPost('/api/attendanceoptions', attendanceoptionToEdit.value);
            }
            successToast('Attendance option saved successfully');
        } catch (e) {
            errorMessage.value = e.message;
            failToast(e.message);
        } finally {
            busy.value = false;
        }
        // retrieve the new list from the backend
        await getAll();
    };

    const deleteAttendanceoption = async () => {
        console.log('delete?');
        try {
            busy.value = true;
            if (attendanceoptionIdToDelete.value > 0) {
                console.log('yes!');
                // delete record
                await apiDel(`/api/attendanceoptions/${attendanceoptionIdToDelete.value}`);
                successToast('Attendance option deleted successfully');
            } else {
                console.log('no.');
            }
        } catch (e) {
            errorMessage.value = e.message;
            failToast(e.message);
        } finally {
            busy.value = false;
        }
        // retrieve new list from DB
        await getAll();
    };

    /**
     * if the id changes, get the corresponding attendance option from the DB
     * {immediate:true} initializes the object, so we only need one place to set the fields (clearEditObject)
     */
    watch(attendanceoptionIdToEdit, async () => {
        if (attendanceoptionIdToEdit.value > 0) {
            busy.value = true;
            try {
                const response = await apiGet(`/api/attendanceoptions/${attendanceoptionIdToEdit.value}`);
                attendanceoptionToEdit.value = response.data;
            } catch (e) {
                errorMessage.value = e.message;
                failToast(e.message);
            } finally {
                busy.value = false;
            }
        } else {
            // initialize for new attendanceoption
            clearEditObject();
        }
    }, { immediate: true });
    return {
        attendanceoptionIdToDelete,
        attendanceoptionIdToEdit,
        attendanceoptions,
        attendanceoptionToEdit,
        deleteAttendanceoption,
        errorMessage,
        getAll,
        saveAttendanceoption
    };
}
