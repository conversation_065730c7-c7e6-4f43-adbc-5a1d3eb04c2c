import { computed, ref } from "vue";
import useNoty from "./useNoty.js";
import useLang from "./useLang.js";
import axios from "axios";

const { ucFirst, translate } = useLang();
const { failNoty, successNoty } = useNoty();
const profile = ref(null);
const busy = ref(false);

export default function useProfile() {
    const getProfile = async () => {    
        busy.value = true;
        try {
            const response = await axios.get('/api/profile');
            profile.value = response.data;
        } catch (error) {
            throw error;
        } finally {
            busy.value = false;
        }
    };
    
    const saveProfile = async () => {
        busy.value = true;
        try {
            await axios.put('/api/profile', profile.value);
            successNoty(ucFirst(translate('generic.profileupdatedsuccessfully')));
        } catch (error) {
            failNoty(error.response.data.message);
        } finally {
            busy.value = false;
        }
    };

    const saveable = computed(() => {
        return profile.value?.name !== '' && profile.value?.email !== '';
    });

    return {
        busy,
        profile,
        getProfile,
        saveable,
        saveProfile,
    };
}
