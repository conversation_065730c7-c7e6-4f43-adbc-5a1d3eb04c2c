import { computed, ref } from 'vue';
import useNoty from '../composables/useNoty.js';
import useLang from './useLang.js';

import axios from 'axios';
import { toSvg } from 'jdenticon';

const { ucFirst, translate } = useLang();

const domain = ref({});
const allTutors = ref([]);
const allActiveUsers = ref([]); // this includes admins and tutors
const allLocations = ref([]);
const allCourses = ref([]);
const allCourseGroups = ref([]);
const allStudentGroups = ref([]);
const allSchoolYears = ref([]);
const allAttendanceoptions = ref([]);
const allDateExceptions = ref([]);
const allStudents = ref([]);
const allRecurrenceOptions = ref([]);

const loadingBaseData = ref(false)
let activePromise = null;  // keep track of the current active promise, in case we call initBaseData multiple times

export default function useBaseData () {
    /**
     * Initialize or update base data
     * @param {Object} options - Specify which data to fetch
     * @param {boolean} options.activeUsers - Fetch active users
     * @param {boolean} options.attendanceOptions - Fetch attendance options
     * @param {boolean} options.courseGroups - Fetch course groups
     * @param {boolean} options.courses - Fetch courses
     * @param {boolean} options.dateExceptions - Fetch courses
     * @param {boolean} options.domain - Fetch domain info
     * @param {boolean} options.locations - Fetch locations
     * @param {boolean} options.recurrenceOptions - Fetch locations
     * @param {boolean} options.schoolYears - Fetch school years
     * @param {boolean} options.studentGroups - Fetch student groups
     * @param {boolean} options.students - Fetch students
     * @param {boolean} options.tutors - Fetch tutors
     *
     * @param {boolean} forceUpdate - Force update for the selected data
     */
    const initBaseData = async (options = {}, forceUpdate = false) => {
        const defaultOptions = {
            activeUsers: false,
            attendanceOptions: false,
            courseGroups: false,
            courses: false,
            dateExceptions: false,
            domain: false,
            locations: false,
            recurrenceOptions: false,
            schoolYears: false,
            studentGroups: false,
            students: false,
            tutors: false
        };

        const finalOptions = { ...defaultOptions, ...options };

        // if there's already an active promise and we don't force an update, return that
        if (loadingBaseData.value && !forceUpdate && activePromise) {
            return activePromise;
        }

        // start a new data fetch
        loadingBaseData.value = true;
        
        const promises = [];

        if (finalOptions.activeUsers) promises.push(getAllActiveUser(forceUpdate));
        if (finalOptions.attendanceOptions) promises.push(getAttendanceoptions());
        if (finalOptions.courseGroups) promises.push(getCourseGroups(forceUpdate));
        if (finalOptions.courses) promises.push(getCourses(forceUpdate));
        if (finalOptions.dateExceptions) promises.push(getDateExceptions(forceUpdate));
        if (finalOptions.domain) promises.push(getDomainInfo());
        if (finalOptions.locations) promises.push(getAllLocations(forceUpdate));
        if (finalOptions.recurrenceOptions) promises.push(getAllRecurrenceOptions(forceUpdate));
        if (finalOptions.schoolYears) promises.push(getSchoolYears(forceUpdate));
        if (finalOptions.studentGroups) promises.push(getStudentGroups());
        if (finalOptions.students) promises.push(getStudents());
        if (finalOptions.tutors) promises.push(getAllTutors(forceUpdate));

        activePromise = Promise.all(promises).finally(() => {
            loadingBaseData.value = false;
            activePromise = null;
        });

        return activePromise;
    };

    const { failNoty } = useNoty();

    const getAllActiveUser = async (forceUpdate = false) => {
        if (allActiveUsers.value.length === 0 || forceUpdate) {
            try {
                const resp = await axios.get('/api/getactiveusers');
                allActiveUsers.value = resp.data;
            } catch (err) {
                failNoty(ucFirst(translate('generic.errorloadingactiveusers')) + ' ' + err);
            }
        }
    };

    const getAllTutors = async (forceUpdate = false) => {
        if (allTutors.value.length === 0 || forceUpdate) {
            try {
                const resp = await axios.get('/api/gettutors');
                allTutors.value = resp.data;
            } catch (err) {
                failNoty(
                    ucFirst(translate('generic.errorloadingtutors')) + ' ' + err
                );
            }
        }
    };

    const getAllLocations = async (forceUpdate = false) => {
        if (allLocations.value.length === 0 || forceUpdate) {
            try {
                const resp = await axios.get('/api/locations');
                allLocations.value = resp.data.data;
                // generate icons
                allLocations.value.forEach((location, idx) => {
                    allLocations.value[idx].icon = toSvg(`location_${location.domain_id}_${location.id}`, 35);
                    allLocations.value[idx].iconSmall = toSvg(`location_${location.domain_id}_${location.id}`, 20);
                });
            } catch (err) {
                failNoty(ucFirst(translate('generic.errorloadinglocations')) + ' ' + err);
            }
        }
    };

    const getAllRecurrenceOptions = async (forceUpdate = false) => {
        if (allRecurrenceOptions.value.length === 0 || forceUpdate) {
            try {
                const response = await axios.get('/api/recoptions');
                allRecurrenceOptions.value = response.data;
            } catch (err) {
                failNoty(
                    ucFirst(translate('generic.errorloadingrecurrenceoptions')) + ' ' + err
                );
            }
        }
    }

    const getCourses = async (forceUpdate = false) => {
        if (allCourses.value.length === 0 || forceUpdate) {
            try {
                const response = await axios.get('/api/courses?excludeArchive=0');
                allCourses.value = response.data.data;
            } catch (err) {
                failNoty(ucFirst(translate('generic.errorloadingcourses')) + ' ' + err);
            }
        }
    };

    const getSchoolYears = async (forceUpdate = false) => {
        if (allSchoolYears.value.length === 0 || forceUpdate) {
            try {
                const response = await axios.get('/api/getschoolyears');
                allSchoolYears.value = response.data;
            } catch (err) {
                failNoty(
                    ucFirst(translate('generic.errorloadingschoolyears')) + ' ' + err
                );
            }
        }
    };

    const getDomainInfo = async () => {
        try {
            const response = await axios.get('/api/getdomaininfo');
            domain.value = response.data.data;
        } catch (err) {
            failNoty(
                ucFirst(translate('generic.errorloadingdomaininfo')) + ' ' + err
            );
        }
    };

    /**
     * get the current school year or the first future school year
     * return null if no current or future school year is found
     */
    const currentOrFutureSchoolYear = computed(() => {
        const now = new Date();
        const retSchoolYears = allSchoolYears.value.filter(schoolYear => {
            const end = new Date(schoolYear.end_date);
            return (end >= now);
        });
        return retSchoolYears.length > 0 ? retSchoolYears[0] : null;
    });

    const getCourseGroups = async (forceUpdate = false) => {
        if (allCourseGroups.value.length === 0 || forceUpdate) {
            try {
                const response = await axios.get('/api/coursegroups');
                allCourseGroups.value = response.data;
            } catch (err) {
                failNoty(
                    ucFirst(translate('generic.errorloadingcoursegroups')) + ' ' + err
                );
            }
        }
    };

    const getStudentGroups = async () => {
        if (allStudentGroups.value.length === 0) {
            try {
                const response = await axios.get('/api/studentgroups');
                allStudentGroups.value = response.data.data;
            } catch (err) {
                failNoty(
                    ucFirst(translate('generic.errorloadingstudentgroups')) + ' ' + err
                );
            }
        }
    };

    const getStudents = async () => {
        if (allStudents.value.length === 0) {
            try {
                const response = await axios.get('/api/students?onlystudents=true&onlyactive=true');
                allStudents.value = response.data.data;
            } catch (err) {
                failNoty(
                    ucFirst(translate('generic.errorloadingstudents')) + ' ' + err
                );
            }
        }
    };

    const getAttendanceoptions = async () => {
        if (allAttendanceoptions.value.length === 0) {
            try {
                const response = await axios.get('/api/attendanceoptions');
                allAttendanceoptions.value = response.data;
            } catch (e) {
                failNoty(
                    ucFirst(translate('generic.errorloadingattendanceoptions')) + ' ' + e
                );
            }
        }
    };

    const getDateExceptions = async (forceUpdate = false) => {
        if (allDateExceptions.value.length === 0 || forceUpdate) {
            try {
                const response = await axios.get('/api/dateexceptions');
                allDateExceptions.value = response.data.data;
            } catch (e) {
                failNoty(
                    ucFirst(translate('generic.errorloadingdateexceptions')) + ' ' + e
                );
            }
        }
    }

    /**
     * get only course groups that have at least one course that is not archived
     * AND only return the not-archived courses in the course group
     * @type {ComputedRef<*[]>}
     */
    const filteredCourseGroups = computed(() => {
        const returnCourseGroups = [];
        allCourseGroups.value.forEach((cg) => {
            const theCoursesOfThisCourseGroup = cg.courses.filter(course => !(parseInt(course.archive) === 1));
            // if there's no course without the ARCHIEF label, don't include the course group in the result
            if (theCoursesOfThisCourseGroup.length > 0) {
                // sort the courses
                theCoursesOfThisCourseGroup.sort((courseEntryA, courseEntryB) => {
                    if (courseEntryA.name < courseEntryB.name) {
                        return -1;
                    }
                    if (courseEntryA.name > courseEntryB.name) {
                        return 1;
                    }
                    return 0;
                });
                returnCourseGroups.push(cg);
                returnCourseGroups[returnCourseGroups.length - 1].courses = theCoursesOfThisCourseGroup;
            }
        });
        return returnCourseGroups;
    });

    /**
     * get all courses that have no courses with the ARCHIEF label
     */
    const emptyCourseGroups = computed(() => {
        return allCourseGroups.value.filter(cg => cg.courses.length === 0);
    });

    /**
     * get only courses that are not archived
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const filteredCourses = computed(() => {
        return allCourses.value.filter(course => !(parseInt(course.archive) === 1));
    });

    /**
     * get only duo and group courses
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const duoAndGroupCourses = computed(() => {
        return allCourses.value.filter(course => course.group_size_min > 1);
    });

    /**
     * get only individual courses
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const individualCourses = computed(() => {
        return allCourses.value.filter(course => course.group_size_max === 1);
    });

    /**
     * fixme: better solution for hardcoded "HUUR" term, should that be a flag in the database?
     * @type {ComputedRef<UnwrapRefSimple<*>[]>}
     */
    const filteredCoursesNoRental = computed(() => {
        return allCourses.value.filter(course => {
            return !(
                parseInt(course.archive) === 1 ||
                course.name.includes('HUUR') ||
                course.name.includes('huur')
            );
        });
    });

    const allArchivedCourses = computed(() => {
        return allCourses.value.filter(course => course.archive !== 0);
    });

    return {
        allActiveUsers,
        allArchivedCourses,
        allAttendanceoptions,
        allCourses,
        allCourseGroups,
        allDateExceptions,
        allLocations,
        allRecurrenceOptions,
        allSchoolYears,
        allStudentGroups,
        allStudents,
        allTutors,
        domain,
        duoAndGroupCourses,
        individualCourses,
        loadingBaseData,
        currentOrFutureSchoolYear,
        emptyCourseGroups,
        filteredCourseGroups,
        filteredCourses,
        filteredCoursesNoRental,
        initBaseData
    };
}
