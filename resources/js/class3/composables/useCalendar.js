import useLang from "./useLang.js";
import useDateTime from "./useDateTime.js";
import useApi from "./useApi.js";
import useColor from "./useColor.js";

const { translate } = useLang();
const { convertToMysqlDateTime } = useDateTime();
const { apiGet } = useApi();
const { getContrastColor } = useColor();

// which colors to use for the locations
// for now, use a hard-coded list
const locationColorMap = {
    "1": "#1E88E5", // Vivid blue
    "2": "#43A047", // Green
    "3": "#E53935", // Red
    "4": "#FF9800", // Orange
    "5": "#5E35B1", // Purple
    "6": "#00ACC1", // Teal
    "7": "#F4511E", // Deep orange
    "8": "#8E24AA", // Deep purple
    "9": "#3949AB", // Indigo
    "10": "#00897B", // Teal green
    "11": "#D81B60", // Pink
    "12": "#6D4C41", // <PERSON>
    "13": "#546E7A", // Blue grey
    "14": "#C0CA33", // Lime
    "15": "#7CB342" // Light green
};

export default function useCalendar() {

    const calendarIcons = {
        time: 'fas fa-clock',
        date: 'fas fa-calendar',
        up: 'fa fa-chevron-up',
        down: 'fa fa-chevron-down',
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right',
        today: 'fas fa-calendar-day',
        clear: 'fas fa-trash',
        close: 'fas fa-times'
    };
    const calendarTooltips = {
        today: translate('localisation.today'),
        clear: translate('localisation.clear'),
        close: translate('localisation.close'),
        selectMonth: translate('localisation.selectmonth'),
        prevMonth: translate('localisation.prevmonth'),
        nextMonth: translate('localisation.nextmonth'),
        selectYear: translate('localisation.selectyear'),
        prevYear: translate('localisation.prevyear'),
        nextYear: translate('localisation.nextyear'),
        selectDecade: translate('localisation.selectdecade'),
        prevDecade: translate('localisation.prevdecade'),
        nextDecade: translate('localisation.nextdecade'),
        prevCentury: translate('localisation.prevcentury'),
        nextCentury: translate('localisation.nextcentury')
    };


    /** DAY SCHEDULE SECTION */
    const getPlanningOfDay = async (date) => {
        if (!date) return;
        try {
            const response = await apiGet("/api/planningforday?date=" + convertToMysqlDateTime(date));
            return response.data.data;
        } catch (error) {
            throw error;
        }
    }

    const transformSchedule = (input) => {
        // Create a map to group events by tutor_id
        const tutorsMap = new Map();

        // Process each event in the input array
        input.forEach(event => {
            const tutorId = event.tutor_id;

            // If tutor not in map, add them
            if (!tutorsMap.has(tutorId)) {
                tutorsMap.set(tutorId, {
                    id: tutorId,
                    name: event.tutor_name,
                    color: event.tutor_color,
                    events: []
                });
            }

            // Add the event to the tutor's events array (without date field)
            tutorsMap.get(tutorId).events.push({
                id: event.event_id,
                location_name: event.location_name,
                course_name: event.course_name,
                student_name: event.student_name,
                from: event.from,
                to: event.to,
                highlightOnHover: true,
                ganttBarConfig: {
                    id: event.event_id,
                    label: event.student_name,
                    immobile: true, // important! although the interface doesn't allow moving, the API does!
                    style: {
                        background: locationColorMap[event.location_id],
                        borderRadius: "2px",
                        color: getContrastColor(locationColorMap[event.location_id])
                    }
                }
            });
        });

        // Convert the map to an array of tutors and sort them by id
        const tutorsArray = Array.from(tutorsMap.values()).sort((a, b) => a.id - b.id);

        // Sort events for each tutor by start time
        tutorsArray.forEach(tutor => {
            tutor.events.sort((a, b) => new Date(a.from) - new Date(b.from));
        });

        // Create the final output structure
        return {
            schedule: {
                tutors: tutorsArray
            }
        };
    }

    /** END DAY SCHEDULE SECTION */

    return {
        calendarIcons,
        calendarTooltips,
        getPlanningOfDay,
        locationColorMap,
        transformSchedule
    };
}
