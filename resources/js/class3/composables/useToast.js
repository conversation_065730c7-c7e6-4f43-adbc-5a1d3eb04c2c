import useLang from './useLang.js';

export default function useToast() {
    const { ucFirst, translate } = useLang();

    /**
     * Show a success toast message
     * @param bodyText - Message to display
     * @param title - Optional title (defaults to 'Notice')
     * @param timeout - Milliseconds before auto-hide (default 3000, 0 for no auto-hide)
     */
    const successToast = (bodyText, title = '', timeout = 3000) => {
        const useTitle = title === ''
            ? "CLASS - " + ucFirst(translate('generic.notice'))
            : title;
        
        showToast(bodyText, useTitle, 'success', timeout);
    };

    /**
     * Show an error toast message
     * @param bodyText - Message to display
     * @param title - Optional title (defaults to 'Warning')
     * @param timeout - Milliseconds before auto-hide (default 0 - stays until dismissed)
     */
    const failToast = (bodyText, title = '', timeout = 0) => {
        const useTitle = title === ''
            ? "CLASS - " + ucFirst(translate('generic.warn'))
            : title;
        
        showToast(bodyText, useTitle, 'danger', timeout);
    };

    /**
     * Create and show a Bootstrap 5 toast
     * @param bodyText - Message content
     * @param title - Toast header title
     * @param type - 'success', 'danger', etc.
     * @param timeout - Auto-hide delay in ms (0 for no auto-hide)
     */
    const showToast = (bodyText, title, type, timeout) => {
        // Create container if it doesn't exist
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toastEl = document.createElement('div');
        toastEl.className = `toast border-0`;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        // Add header with title
        const toastHeader = document.createElement('div');
        toastHeader.className = `toast-header bg-${type} text-white`;
        
        const titleEl = document.createElement('strong');
        titleEl.className = 'me-auto';
        titleEl.textContent = title;
        
        const closeButton = document.createElement('button');
        closeButton.className = 'btn-close btn-close-white';
        closeButton.setAttribute('data-bs-dismiss', 'toast');
        closeButton.setAttribute('aria-label', 'Close');
        
        toastHeader.appendChild(titleEl);
        toastHeader.appendChild(closeButton);
        
        // Add body with message
        const toastBody = document.createElement('div');
        toastBody.className = 'toast-body';
        toastBody.textContent = bodyText;
        
        // Assemble toast
        toastEl.appendChild(toastHeader);
        toastEl.appendChild(toastBody);
        toastContainer.appendChild(toastEl);
        
        // Initialize and show toast
        const toastOptions = {
            autohide: timeout > 0,
            delay: timeout
        };
        
        const toast = new bootstrap.Toast(toastEl, toastOptions);
        toast.show();
        
        // Remove toast from DOM after it's hidden
        toastEl.addEventListener('hidden.bs.toast', () => {
            toastEl.remove();
        });
    };

    return {
        failToast,
        successToast
    };
}
