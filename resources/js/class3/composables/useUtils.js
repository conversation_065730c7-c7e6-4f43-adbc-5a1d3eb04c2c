import $ from 'jquery';

export default function useUtils() {
    /**
     * No operation - to be used to catch @click on a button that is supposed to NOT submit a form
     * Useful if the button doesn't have a click event, so we can't use @click.prevent.
     * This function is used in those cases: @click.prevent="noop"
     */
    const noop = () => {};

    const uniqueId = () => {
        return '_' + Math.random().toString(36).substring(2, 9);
    };

    const scrollToSection = (sectionId, duration) => {
        $('html, body').animate({
            scrollTop: $(`#${sectionId}`).offset().top
        }, duration);
    };

    /**
     * Remove HTML tags from a text string.
     * Uses jQuery text() function.
     * see https://regex101.com/r/4Z6cvM/1
     */
    const stripHtml = (input) => {
        // add <p> just in case we didn't receive HTML in the string.
        // $.text() would fail in that case, and the added <p> will be removed anyway
        const result = $(`<p>${input}</p>`).text();
        // replace ,C and .C with , C and . C resp. caused by a removed <br/> at the end of a line
        const regex = /\.|,([^ ])/gm;
        const subst = '. $1';
        return result.replace(regex, subst);
    };

    return {
        noop,
        scrollToSection,
        stripHtml,
        uniqueId
    };

}
