import { ref, computed } from 'vue';
import useApi from './useApi.js';
import useNoty from './useNoty.js';
import useLang from './useLang.js';

const { api, apiPut, apiGet, apiDel } = useApi();
const busy = ref(false);
const studentlists = ref([]);
const studentlistIdToEdit = ref(-1);
const studentlistToEdit = ref({
    id: null,
    name: '',
    hexcolor: '000000',
    remarks: '',
    students: []
});
const selectedStudents = ref([]);
const studentlistIdToDelete = ref(0);
const dirty = ref(false);

export default function useStudentLists() {
    const { failNoty, successNoty } = useNoty();
    const { translate, ucFirst } = useLang();

    /**
     * Get all student lists
     */
    const getStudentLists = async () => {
        try {
            busy.value = true;
            const response = await apiGet('/api/studentlists');
            studentlists.value = response.data;

            // Sort students by lastname
            studentlists.value.forEach(list => {
                if (list.students && list.students.length > 0) {
                    list.students.sort((a, b) => {
                        return (a.lastname > b.lastname) ? 1 : ((b.lastname > a.lastname) ? -1 : 0);
                    });
                }
            });
        } catch (error) {
            console.error('Error fetching student lists:', error);
            failNoty(translate('generic.errorloadingstudentlists'));
        } finally {
            busy.value = false;
        }
    };

    /**
     * Get a specific student list for editing
     */
    const getStudentList = async () => {
        busy.value = true;
        if (studentlistIdToEdit.value > 0) {
            // Edit existing student list
            try {
                const list = studentlists.value.find(list => list.id === studentlistIdToEdit.value);
                if (list) {
                    studentlistToEdit.value = { ...list };
                    // Make sure hexcolor has a # prefix for the color input
                    if (studentlistToEdit.value.hexcolor && !studentlistToEdit.value.hexcolor.startsWith('#')) {
                        studentlistToEdit.value.hexcolor = '#' + studentlistToEdit.value.hexcolor;
                    }
                } else {
                    // If not found in local state, fetch from API
                    const response = await apiGet(`/api/studentlists`);
                    const fetchedList = response.data.find(list => list.id === studentlistIdToEdit.value);
                    if (fetchedList) {
                        studentlistToEdit.value = { ...fetchedList };
                        // Make sure hexcolor has a # prefix for the color input
                        if (studentlistToEdit.value.hexcolor && !studentlistToEdit.value.hexcolor.startsWith('#')) {
                            studentlistToEdit.value.hexcolor = '#' + studentlistToEdit.value.hexcolor;
                        }
                    }
                }
            } catch (error) {
                console.error('Error fetching student list:', error);
                failNoty(translate('generic.errorloadingstudentlist'));
            }
        } else {
            // Create new student list
            studentlistToEdit.value = {
                id: null,
                name: '',
                hexcolor: '#000000',
                remarks: '',
                students: []
            };
        }
        selectedStudents.value = [];
        busy.value = false;
    };

    /**
     * Delete a student list
     */
    const deleteStudentList = async () => {
        if (!studentlistIdToDelete.value) return;

        try {
            busy.value = true;
            await apiDel(`/api/studentlists/${studentlistIdToDelete.value}`);
            await getStudentLists();
            successNoty(ucFirst(translate('generic.studentlistdeleted')));
        } catch (error) {
            console.error('Error deleting student list:', error);
            failNoty(ucFirst(translate('generic.deletefailed')));
        } finally {
            busy.value = false;
            studentlistIdToDelete.value = 0;
        }
    };

    /**
     * Add all students with active registrations to the current student list
     */
    const addAllActiveRegistration = async () => {
        if (!studentlistToEdit.value.id) return;

        try {
            busy.value = true;
            await apiPut(`/api/addAllActiveRegistrationToStudentlist/${studentlistToEdit.value.id}`);
            await getStudentLists();
            // Refresh the current student list
            const response = await apiGet('/api/studentlists');
            const updatedList = response.data.find(list => list.id === studentlistToEdit.value.id);
            if (updatedList) {
                studentlistToEdit.value = { ...updatedList };
                // Make sure hexcolor has a # prefix for the color input
                if (studentlistToEdit.value.hexcolor && !studentlistToEdit.value.hexcolor.startsWith('#')) {
                    studentlistToEdit.value.hexcolor = '#' + studentlistToEdit.value.hexcolor;
                }
            }
            successNoty(ucFirst(translate('generic.studentsadded')));
        } catch (error) {
            console.error('Error adding students to list:', error);
            failNoty(ucFirst(translate('generic.erroraddingstudents')));
        } finally {
            busy.value = false;
        }
    };

    /**
     * Remove all students from the current student list
     */
    const removeAllStudentsFromList = async () => {
        if (!studentlistToEdit.value.id) return;

        try {
            busy.value = true;
            await apiPut(`/api/removeAllStudentsFromList/${studentlistToEdit.value.id}`);
            await getStudentLists();
            // Refresh the current student list
            const response = await apiGet('/api/studentlists');
            const updatedList = response.data.find(list => list.id === studentlistToEdit.value.id);
            if (updatedList) {
                studentlistToEdit.value = { ...updatedList };
                // Make sure hexcolor has a # prefix for the color input
                if (studentlistToEdit.value.hexcolor && !studentlistToEdit.value.hexcolor.startsWith('#')) {
                    studentlistToEdit.value.hexcolor = '#' + studentlistToEdit.value.hexcolor;
                }
            }
            successNoty(ucFirst(translate('generic.studentsremoved')));
        } catch (error) {
            console.error('Error removing students from list:', error);
            failNoty(ucFirst(translate('generic.errorremovingstudents')));
        } finally {
            busy.value = false;
        }
    };

    /**
     * Remove selected students from the current student list
     */
    const removeSelectedStudentsFromList = async () => {
        if (!studentlistToEdit.value.id || selectedStudents.value.length === 0) return;

        try {
            busy.value = true;
            await apiPut(`/api/removeSelectedStudentsFromList/${studentlistToEdit.value.id}`, {
                studentids: selectedStudents.value
            });
            await getStudentLists();
            // Refresh the current student list
            const response = await apiGet('/api/studentlists');
            const updatedList = response.data.find(list => list.id === studentlistToEdit.value.id);
            if (updatedList) {
                studentlistToEdit.value = { ...updatedList };
                // Make sure hexcolor has a # prefix for the color input
                if (studentlistToEdit.value.hexcolor && !studentlistToEdit.value.hexcolor.startsWith('#')) {
                    studentlistToEdit.value.hexcolor = '#' + studentlistToEdit.value.hexcolor;
                }
            }
            successNoty(ucFirst(translate('generic.studentsremoved')));
        } catch (error) {
            console.error('Error removing selected students from list:', error);
            failNoty(ucFirst(translate('generic.errorremovingstudents')));
        } finally {
            busy.value = false;
            selectedStudents.value = [];
        }
    };

    /**
     * Toggle student selection
     * @param {number} studentId - The ID of the student to toggle
     */
    const toggleStudentSelection = (studentId) => {
        const index = selectedStudents.value.indexOf(studentId);
        if (index === -1) {
            selectedStudents.value.push(studentId);
        } else {
            selectedStudents.value.splice(index, 1);
        }
    };

    /**
     * Get a formatted list of students for a popover
     * @param {Array} students - The list of students
     * @returns {string} - HTML string for the popover
     */
    const getStudentsPopoverContent = (students) => {
        if (!students || students.length === 0) {
            return translate('generic.nostudentsfound');
        }

        let content = '<ul class="list-group list-group-flush">';
        students.forEach(student => {
            content += `<li class="list-group-item"><a href="/students/${student.id}/edit">${student.name}</a></li>`;
        });
        content += '</ul>';

        return content;
    };

    /**
     * Save the current student list
     */
    const saveStudentList = async () => {
        busy.value = true;
        let url = '';
        let method = 'post';

        const data = {
            name: studentlistToEdit.value.name,
            hexcolor: studentlistToEdit.value.hexcolor,
            remarks: studentlistToEdit.value.remarks || ''
        };

        if (studentlistToEdit.value.id) {
            // Update an existing student list
            url = `/api/studentlists/${studentlistToEdit.value.id}`;
            method = 'put';
        } else {
            // Create a new student list
            url = '/api/studentlists';
            method = 'post';
        }

        try {
            const response = await api[method](url, data);
            // If this was a new student list, set the ID to edit mode
            if (!studentlistToEdit.value.id && response.data && response.data.id) {
                studentlistIdToEdit.value = response.data.id;
            }
            await getStudentList();
            successNoty(ucFirst(translate('generic.savesuccess')));
            dirty.value = false;
            window.dirty = false;
        } catch (error) {
            console.error('Error saving student list:', error);
            failNoty(ucFirst(translate('generic.savingfailed')));
        } finally {
            busy.value = false;
        }
    };

    /**
     * Check if the form can be saved
     */
    const isSaveable = computed(() => {
        return studentlistToEdit.value.name &&
               studentlistToEdit.value.name.length > 0 &&
               studentlistToEdit.value.hexcolor &&
               studentlistToEdit.value.hexcolor.length > 0;
    });

    const isEditing = computed(() => studentlistIdToEdit.value > 0);
    const isNewRecord = computed(() => studentlistIdToEdit.value === 0);

    return {
        busy,
        dirty,
        studentlists,
        studentlistToEdit,
        studentlistIdToEdit,
        selectedStudents,
        studentlistIdToDelete,
        addAllActiveRegistration,
        deleteStudentList,
        getStudentList,
        getStudentLists,
        getStudentsPopoverContent,
        isEditing,
        isSaveable,
        isNewRecord,
        removeAllStudentsFromList,
        removeSelectedStudentsFromList,
        saveStudentList,
        toggleStudentSelection
    };
}
