import useLang from '../../composables/useLang';

const { translate, ucFirst } = useLang();

export default class ImageViaUrlPlugin {
    static get pluginName() {
        return 'CustomImageViaUrl';
    }

    constructor(editor) {
        this.editor = editor;
        this.init();
    }

    init() {
        const editor = this.editor;

        // Register schema if not already registered
        if (!editor.model.schema.isRegistered('customImage')) {
            editor.model.schema.register('customImage', {
                allowWhere: '$block',
                allowAttributes: ['src'],
                isObject: true
            });
        }

        // Separate conversion rules for data and editing
        editor.conversion.for('dataDowncast').elementToElement({
            model: 'customImage',
            view: (modelElement, { writer }) => {
                return writer.createEmptyElement('img', {
                    src: modelElement.getAttribute('src'),
                    style: 'max-width: 100%; height: auto;'
                });
            }
        });

        editor.conversion.for('editingDowncast').elementToElement({
            model: 'customImage',
            view: (modelElement, { writer }) => {
                return writer.createEmptyElement('img', {
                    src: modelElement.getAttribute('src'),
                    style: 'max-width: 100%; height: auto;'
                });
            }
        });

        editor.ui.componentFactory.add('imageViaUrl', () => {
            // Create a container for the dropdown
            const container = document.createElement('div');
            container.className = 'ck ck-dropdown';

            // Create the input container
            const inputContainer = document.createElement('div');
            inputContainer.className = 'ck ck-dropdown__panel';
            inputContainer.style.padding = '10px';
            inputContainer.style.display = 'none';

            // Create the input elements
            const inputWrapper = document.createElement('div');
            inputWrapper.className = 'ck-labeled-field-view';
            inputWrapper.style.display = 'flex';
            inputWrapper.style.gap = '8px';

            const urlInput = document.createElement('input');
            urlInput.type = 'text';
            urlInput.className = 'ck ck-input';
            urlInput.placeholder = ucFirst(translate('generic.enterimageurl'));

            const buttonWrapper = document.createElement('div');
            buttonWrapper.style.display = 'flex';
            buttonWrapper.style.gap = '4px';

            const confirmButton = document.createElement('button');
            confirmButton.className = 'ck ck-button';
            confirmButton.innerHTML = `
                <svg class="ck ck-icon" viewBox="0 0 20 20">
                    <path d="M8.315 13.859l-3.182-3.417a.506.506 0 0 1 0-.684l.643-.683a.437.437 0 0 1 .642 0l2.22 2.393 4.942-5.327a.436.436 0 0 1 .643 0l.643.684a.504.504 0 0 1 0 .683l-5.91 6.35a.437.437 0 0 1-.642 0"/>
                </svg>
            `;

            const cancelButton = document.createElement('button');
            cancelButton.className = 'ck ck-button';
            cancelButton.innerHTML = `
                <svg class="ck ck-icon" viewBox="0 0 20 20">
                    <path d="M11.591 10.177l4.243 4.242a1 1 0 01-1.415 1.415l-4.242-4.243-4.243 4.243a1 1 0 01-1.414-1.415l4.243-4.242L4.52 5.934a1 1 0 011.414-1.414l4.243 4.242 4.242-4.242a1 1 0 111.415 1.414l-4.243 4.243z"/>
                </svg>
            `;

            // Create the main button
            const button = document.createElement('button');
            button.className = 'ck ck-button';
            button.innerHTML = `
                <span class="ck ck-icon">
                    <svg class="ck ck-icon__svg" viewBox="0 0 20 20">
                        <path d="M6.91 10.54c.26-.23.64-.21.88.03l3.36 3.14 2.23-2.06a.64.64 0 0 1 .87 0l2.52 2.97V4.5H3.2v10.12l3.71-4.08zm10.27-7.51c.6 0 1.09.47 1.09 1.05v11.84c0 .59-.49 1.06-1.09 1.06H2.79c-.6 0-1.09-.47-1.09-1.06V4.08c0-.58.49-1.05 1.1-1.05h14.38zm-5.22 5.56a1.96 1.96 0 1 1 3.4-1.96 1.96 1.96 0 0 1-3.4 1.96z"/>
                    </svg>
                </span>
                <span class="ck ck-button__label">${ucFirst(translate('generic.imageurl'))}</span>
            `;

            // Event handlers
            button.addEventListener('click', () => {
                inputContainer.style.display = inputContainer.style.display === 'none' ? 'block' : 'none';
            });

            confirmButton.addEventListener('click', () => {
                const url = urlInput.value.trim();
                if (url) {
                    editor.model.change(writer => {
                        const imageElement = writer.createElement('customImage', {
                            src: url
                        });
                        editor.model.insertContent(imageElement);
                    });
                    
                    // Reset and close the dropdown
                    urlInput.value = '';
                    inputContainer.style.display = 'none';
                }
            });

            cancelButton.addEventListener('click', () => {
                urlInput.value = '';
                inputContainer.style.display = 'none';
            });

            // Enter key handler for the input
            urlInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    confirmButton.click();
                }
            });

            // Assemble all elements
            buttonWrapper.appendChild(confirmButton);
            buttonWrapper.appendChild(cancelButton);
            inputWrapper.appendChild(urlInput);
            inputWrapper.appendChild(buttonWrapper);
            inputContainer.appendChild(inputWrapper);
            container.appendChild(button);
            container.appendChild(inputContainer);

            return {
                element: container,
                render: () => {
                    return container;
                },
                destroy: () => {
                    container.remove();
                }
            };
        });
    }
} 