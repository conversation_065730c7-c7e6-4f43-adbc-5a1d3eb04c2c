<template>
  <div class="planning">
    <div class="row">
      <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column"><lesson-planning-base-data class="flex-fill" /></div>
      <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column"><lesson-planning-details class="flex-fill" /></div>
      <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column"><lesson-planning-create class="flex-fill" /></div>
    </div>
    <div>
      <lesson-planning-event-series />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import useBaseData from "../composables/useBaseData.js";
import LessonPlanningBaseData from '../components/Planning/LessonPlanningBaseData.vue';
import LessonPlanningDetails from '../components/Planning/LessonPlanningDetails.vue';
import LessonPlanningCreate from '../components/Planning/LessonPlanningCreate.vue';
import LessonPlanningEventSeries from '../components/Planning/LessonPlanningEventSeries.vue';

const { initBaseData } = useBaseData();

onMounted(async () => {
    await initBaseData({
        locations: true,
        tutors: true,
        schoolYears: true,
        courses: true,
    });
});
</script>

<style scoped>
.planning {
  margin-top: -20px;
}
</style>
