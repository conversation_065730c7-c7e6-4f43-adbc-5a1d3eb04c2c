<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('reporting.report_registrations')) }}</h3>
        </template>
        <div class="row">
            <div class="col-lg-12 col-xl-6 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <registrations-this-year />
                    </div>
                </div>
            </div>
            <div class="col-lg-12 col-xl-6 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <all-registrations-by-course />
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import useRegistration from "../composables/useRegistrations.js";
import useLang from "../composables/useLang.js";
import Panel from "../components/Layout/Panel.vue";
import AllRegistrationsByCourse from "../components/Reports/AllRegistrationsByCourse.vue";
import RegistrationsThisYear from "../components/Reports/RegistrationsThisYear.vue";
const { busy, getRegistrationData } = useRegistration();
const { ucFirst, translate } = useLang();

onMounted(() => {
    getRegistrationData();
});

</script>

<style scoped>

</style>
