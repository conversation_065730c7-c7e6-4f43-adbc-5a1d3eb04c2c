<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('generic.reportappointments')) }}</h3>
        </template>
        <div class="row">
            <div class="col-12">
                <individual-students-timetables />
            </div>
            <div class="col-12 mt-4">
                <group-timetables />
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import useLang from "../composables/useLang.js";
import useTimetableReport from "../composables/useTimetableReport.js";
import Panel from "../components/Layout/Panel.vue";
import IndividualStudentsTimetables from "../components/Timetables/IndividualStudentsTimetables.vue";
import GroupTimetables from "../components/Timetables/GroupTimetables.vue";

const { ucFirst, translate } = useLang();
const { busy, fetchTimetablesData } = useTimetableReport();

onMounted(() => {
    fetchTimetablesData();
});
</script>

<style scoped>
/* Any component-specific styles can go here */
</style>
