<template>
    <div>
        <panel
            :busy="busy"
        >
            <template v-slot:title>
                <i class="fas fa-users"></i>
                {{ ucFirst(translateChoice('generic.studentgroups', 2)) }}
            </template>
            <template v-slot:subtitle>
                <a href='/studentgroups/create' class="btn btn-sm btn-success">
                    {{ ucFirst(translate('generic.newstudentgroup')) }}
                </a>
            </template>
            <table class="table table-responsive table-sm">
                <thead>
                <tr>
                    <th class="text-center">&nbsp;</th>
                    <th>{{ ucFirst(translate('generic.studentgroupname')) }}</th>
                    <th>{{ ucFirst(translateChoice('generic.coupledcourses', 1)) }}</th>
                    <th>{{ ucFirst(translate('generic.nrofstudentsingroup')) }}</th>
                    <th>
                        {{ ucFirst(translate('generic.nrofappointments')) }}
                        <span v-tooltip="translate('generic.explainnrofappointmentsforyear')">
                            <i class="fa fa-info-circle"></i>
                        </span>
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="studentGroup in studentGroups" :key="studentGroup.id">
                    <td>
                        <a
                            v-tooltip="ucFirst(translate('generic.edit'))"
                            :href="'/studentgroups/'+studentGroup.id + '/edit'"
                            class="btn btn-sm btn-success"
                        >
                            <i class="fa fa-edit"></i>
                        </a>
                        <a
                            v-if="deleteable(studentGroup.id)"
                            v-tooltip="ucFirst(translate('generic.delete'))"
                            data-toggle="modal"
                            data-target="#deleteStudentGroupModal"
                            @click="studentGroupToDelete = studentGroup.id"
                            class="btn btn-sm btn-danger"
                        >
                            <i class="fa fa-trash"></i>
                        </a>
                    </td>
                    <td>
                        {{ studentGroup.name }}
                    </td>
                    <td>
                        <a v-if="studentGroup.course"
                           :href="'/courses/' + studentGroup.course.id + '/edit'">
                            {{ studentGroup.course.name }}
                        </a>
                    </td>
                    <td>
                        <h4 style="display: inline-block">
                        <span class="badge badge-pill badge-success" style="width:3em">
                            {{ studentGroup.students.length }}
                        </span>
                        </h4>
                        <a v-if="studentGroup.students.length > 0"
                           tabindex="0"
                           class="btn btn-sm btn-secondary"
                           role="dialog"
                           data-toggle="popover"
                           data-trigger="focus"
                           :data-content="listStudents(studentGroup.students)"
                           :title="ucFirst(translateChoice('generic.students', 2))"
                        >
                            {{ translate('generic.who') }}&nbsp;<i class="fa fa-question-circle"></i>
                        </a>
                    </td>
                    <td>
                        <h4>
                        <span class="badge badge-pill badge-secondary">
                            {{ studentGroup.appointments }} / {{ studentGroup.future_appointments || 0 }}
                        </span>
                        </h4>
                    </td>
                </tr>
                </tbody>
            </table>
        </panel>
        <are-you-sure
            :button-text="ucFirst(translate('generic.deletestudentgroup'))"
            @confirmclicked="deleteStudentGroup"
            modal-id="deleteStudentGroupModal"
        >
            {{ ucFirst(translate("generic.areyousuredeletestudentgroup")) }}
        </are-you-sure>
    </div>
</template>

<script setup>
import { onMounted, onUpdated } from 'vue';
import Panel from '../components/Layout/Panel.vue';
import useLang from '../composables/useLang.js';
import $ from 'jquery';
import useStudentGroups from "../composables/useStudentGroups.js";
import AreYouSure from "../components/Layout/AreYouSure.vue";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, deleteStudentGroup, getStudentGroups, studentGroups, studentGroupToDelete } = useStudentGroups();

onUpdated(() => {
    $('[data-toggle="popover"]').popover({ html: true });
});

onMounted(() => {
    getStudentGroups();
});

const deleteable = (stgid) => {
    const theSTG = studentGroups.value.find(stg => stg.id === stgid);
    // count appointments and coupled students. couplet course is not relevant
    return theSTG.appointments === 0 && theSTG.students.length === 0;
};

/**
 * for the popover: list students in the group
 * @param students
 * @returns {string}
 */
const listStudents = (students) => {
    let retString = '<ul class="list-group list-group-flush">';
    students.forEach(student => {
        if (student.pivot.as_trial_student > 0) {
            retString += `<li class="list-group-item"><a href="/students/${ student.id }/edit">${ student.name }</a>
            <i class="fa fa-flag text-info"></i></li>`;
        } else {
            retString += `<li class="list-group-item"><a href="/students/${ student.id }/edit">${ student.name }</a></li>`;
        }
    });
    return `${ retString }</ul>` +
        '<hr>' +
        "<i class='fa fa-flag text-info'></i> = " + ucFirst(translate('generic.triallesson'));
};

</script>

<style scoped>

</style>
