<template>
  <panel>
    <template #title>
      {{ ucFirst(translate("generic.emaillog")) }}
    </template>
    <div>
      <h5>
        {{ ucFirst(translate("generic.recentlogentries")) }}&nbsp;<small
          >(1 week,
          <a href="/report-email-log">{{ translate("generic.seefulllog") }}</a
          >)</small
        >
      </h5>
      <table class="table">
        <thead>
          <tr>
            <th>&nbsp;</th>
            <th>{{ ucFirst(translate("generic.recipient")) }}</th>
            <th>{{ ucFirst(translate("generic.subject")) }}</th>
            <th>{{ ucFirst(translate("generic.status")) }}</th>
            <th>{{ ucFirst(translate("generic.created")) }}</th>
            <th>{{ ucFirst(translate("generic.updated")) }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="entry in emailLog" :key="entry.id">
            <td>
              <button
                class="btn btn-sm btn-primary"
                data-bs-toggle="modal"
                data-bs-target="#log_entry_preview"
                @click="showEmailLog(entry.id)"
              >
                <i class="fas fa-eye"></i>
              </button>
              <button
                v-if="entry.status !== 'queued'"
                class="btn btn-sm btn-danger"
                data-bs-toggle="modal"
                data-bs-target="#del_areyousure"
                @click="emailLogEntryToDelete = entry.id"
              >
                <i class="fas fa-trash"></i>
              </button>
            </td>
            <td>{{ entry.to }}</td>
            <td>{{ entry.subject }}</td>
            <td
              :class="{
                'text-success': entry.status === 'sent',
                'text-danger': entry.status === 'failed',
                'text-warning': entry.status === 'queued',
              }"
            >
              {{ translate("generic." + entry.status) }}
              <span v-tooltip="entry.log">
                <i
                  :class="{
                    'fas fa-check': entry.status === 'sent',
                    'fas fa-clock': entry.status === 'queued',
                    'fas fa-times': entry.status === 'failed',
                  }"
                ></i>
              </span>
            </td>
            <td>{{ displayDateTime(entry.created_at, true) }}</td>
            <td>{{ displayDateTime(entry.updated_at, true) }}</td>
          </tr>
        </tbody>
      </table>
      <are-you-sure
        :button-text="ucFirst(translate('generic.deletelogentry'))"
        @confirmclicked="deleteFromEmailLog(emailLogEntryToDelete)"
        modal-id="del_areyousure"
      ></are-you-sure>
      <log-entry-preview
        :email-log="emailLogEntryToShow"
        modal-id="log_entry_preview"
      ></log-entry-preview>
    </div>
  </panel>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import useEmailLog from '../composables/useEmailLog.js';
import useDateTime from '../composables/useDateTime.js';
import AreYouSure from '../components/Layout/bs5/AreYouSure4.vue';
import LogEntryPreview from '../components/Email/LogEntryPreview.vue';
import Panel from '../components/Layout/bs5/Panel4.vue';
import useLang from "../composables/useLang.js";

const { translate, ucFirst } = useLang();
const { emailLog, getEmailLog, deleteFromEmailLog } = useEmailLog();
const { displayDateTime } = useDateTime();
const emailLogEntryToShow = ref({});
const emailLogEntryToDelete = ref(null);
onMounted(() => {
    getEmailLog();
});

const showEmailLog = (id) => {
    emailLogEntryToShow.value = emailLog.value.find((entry) => entry.id === id);
};
</script>

<style scoped></style>
