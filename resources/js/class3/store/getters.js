export default {
    courses (state) { return state.courses; },

    // planning assistant
    planTutors (state) { return state.planTutors; },
    planRegistrations (state) { return state.planRegistrations; },
    planSchoolyears (state) { return state.planSchoolyears; },
    planCourseGroups (state) { return state.planCourseGroups; },
    busyFlag1 (state) { return state.busyFlag1; },
    registrationSource (state) { return state.registrationSource; },
    registrationData (state) { return state.registrationData; },
    scheduleThreshold (state) { return state.scheduleThreshold; },
    regNoPrefs (state) { return state.regNoPrefs; },
    regWithPrefs (state) { return state.regWithPrefs; },
    choiceRegFilter (state) { return state.choiceRegFilter; },
    planningTable (state) { return state.planningTable; },

    // edit an event
    eventToEdit (state) { return state.eventToEdit; },
    updateOccurrence (state) { return state.updateOccurence; },
    chosenEventTutorId (state) { return state.chosenEventTutorId; },
    chosenEventLocationId (state) { return state.chosenEventLocationId; },
    startdate (state) { return state.startdate; },
    starttime (state) { return state.starttime; },
    enddate (state) { return state.enddate; },
    endtime (state) { return state.endtime; }
};
