export default {
    setCourses (state, data) { state.courses = data; },

    // planning assistant
    setPlanStudents (state, data) { state.planStudents = data; },
    setPlanCourseGroups (state, data) { state.planCourseGroups = data; },
    setPlanSchoolyears (state, data) { state.planSchoolyears = data; },
    setPlanTutors (state, data) { state.planTutors = data; },
    setPlanRegistrations (state, data) { state.planRegistrations = data; },
    setPlanningTable (state, data) { state.planningTable = data; },
    setBusyFlag1 (state, data) { state.busyFlag1 = data; },
    setRegistrationSource (state, data) { state.registrationSource = data; },
    setRegistrationData (state, data) { state.registrationData = data; },
    setScheduleThreshold (state, data) { state.scheduleThreshold = data; },
    setRegNoPrefs (state, data) { state.regNoPrefs = data; },
    setRegWithPrefs (state, data) { state.regWithPrefs = data; },
    setChoiceRegFilter (state, data) { state.choiceRegFilter = data; },

    // edit an event
    setEventToEdit (state, data) { state.eventToEdit = data; },
    setUpdateOccurrence (state, data) { state.updateOccurence = data; },
    setChosenEventTutorId (state, data) { state.chosenEventTutorId = data; },
    setChosenEventLocationId (state, data) { state.chosenEventLocationId = data; },
    setStartdate (state, data) { state.startdate = data; },
    setStarttime (state, data) { state.starttime = data; },
    setEnddate (state, data) { state.enddate = data; },
    setEndtime (state, data) { state.endtime = data; }
};
