import axios from 'axios';
import moment from 'moment';

export default {
    initForRoute (context, { route }) {
        console.log(`Init called for route ${route} in vuex store`);
        // here we need to establish which data is needed in the given route
        switch (route) {
        case 'planningAssistant':
            context.dispatch('getPreconditions');
            context.dispatch('getDomainInfo');
            context.dispatch('getPlanningTable');
            break;
        case 'planningTable':
            context.dispatch('getPlanningTable');
            break;
        default:
            break;
        }
    },

    initCourses ({ commit }) {
        axios.get('/api/courses')
            .then(response => {
                commit('setCourses', response.data.data);
            })
            .catch(err => {
                console.log(`Error: ${err}`);
            });
    },

    /**
     * Gets the preconditions to be able to start the planning assistant
     * @param commit
     */
    getPreconditions ({ commit }) {
        commit('setBusyFlag1', true);
        axios.get('/api/getpreconditionstatus')
            .then(({ data }) => {
                let regs = data.registrations;
                if (!Array.isArray(data.registrations)) {
                    // convert object to array
                    regs = Object.entries(data.registrations).map(entry => entry[1]);
                }
                commit('setPlanCourseGroups', data.coursegroups);
                commit('setPlanRegistrations', regs);
                commit('setPlanSchoolyears', data.schoolyears);
                // only tutors that have at least some availability
                commit('setPlanTutors', data.tutorAvailability.filter(tutor => tutor.available.length > 0));
            })
            .catch(err => {
                console.log(err);
            })
            .finally(() => {
                commit('setBusyFlag1', false);
            });
    },

    /**
     * retrieve the current planningtable from the database
     * @param commit
     * @returns {Promise<void>}
     */
    getPlanningTable ({ commit }) {
        return axios.get('/api/planningtable')
            .then(({ data }) => {
                commit('setPlanningTable', data.data);
            })
            .catch(err => {
                console.log(err);
            });
    },

    /**
     * Get all domain specific settings from the backend
     * and save them in the store
     * @param commit
     */
    getDomainInfo ({ commit }) {
        axios.get('/api/getdomaininfo')
            .then(({ data }) => {
                commit('setScheduleThreshold', data.data.scheduleThreshold);
            })
            .catch(err => {
                console.log(err);
            });
    },

    async saveEditedEvent ({ getters }) {
        const event = getters.eventToEdit;
        let startdate = getters.startdate;
        let enddate = getters.enddate;
        // dates must be in mysql format
        if (startdate.substr(2, 1) === '-') {
            startdate = moment(startdate, 'DD-MM-YYYY').format('YYYY-MM-DD');
        }
        if (enddate != null && enddate.substr(2, 1) === '-') {
            enddate = moment(enddate, 'DD-MM-YYYY').format('YYYY-MM-DD');
        }

        const data = {
            startdate,
            starttime: getters.starttime,
            enddate,
            endtime: getters.endtime,
            applyTo: getters.updateOccurence == null || getters.updateOccurence === ''
                ? 'thisEvent'
                : getters.updateOccurence, // how many to update: 'this', 'all' or 'only future events'
            eventId: event.id, // id's of related events todo: why 2x passing this id?
            tutorId: getters.chosenEventTutorId,
            locationId: getters.chosenEventLocationId
        };
        console.log(data);
        return axios.put(`/api/calendarevent/${event.id}`, data);
    }
};
