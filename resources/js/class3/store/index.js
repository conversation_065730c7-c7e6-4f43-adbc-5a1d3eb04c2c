import getters from './getters.js';
import mutations from './mutations.js';
import actions from './actions.js';

export default {
    state: {
        courses: [],
        planTutors: [],
        planRegistrations: [], // all data from the backend
        planSchoolyears: [],
        planCourseGroups: [],
        planningTable: [], // current planningtable
        busyFlag1: false, // there may be more simultaneously on the screen at some point, I'm starting with 1
        registrationSource: 0, // chosen index from the DDLB
        registrationData: [], // chosen data
        scheduleThreshold: 3, // saved in the domain, defaults to 3. nr of month a students schedule pref stays valid
        regNoPrefs: [], // regs from the list registrationData that have no or too old preferences
        regWithPrefs: [], // registrationData - regNoPrefs (regs that do have valid preferences)
        choiceRegFilter: 0, // choice continue with all students or those having valid prefs?
        // edit an event
        eventToEdit: null, // keeps track of changes in the event to edit
        updateOccurrence: '', // this, all or only future appointments to edit
        chosenEventTutorId: 0, // editable fields in event
        chosenEventLocationId: 0,
        startdate: '',
        starttime: '',
        enddate: '',
        endtime: '' // END editable fields in event
    },

    // handling of store state
    getters,
    mutations,
    actions
};
