<template>
    <div class="container">
        <div class="page-title">
            <div class="title_left">
                <h3>
                    {{ ucFirst(translateChoice('generic.studentlists', 1)) }}
                    ({{ isEditing ? ucFirst(translate('generic.edit')) : ucFirst(translate('generic.new')) }})
                </h3>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <panel :busy="busy">
                    <template #title>
                        <i class="fas fa-list"></i>
                        {{ ucFirst(translate('generic.details')) }}
                    </template>
                    
                    <form @submit.prevent="saveStudentList" class="form-horizontal">
                        <div class="form-group row">
                            <label class="col-md-2 col-form-label">{{ ucFirst(translate('generic.name')) }} *</label>
                            <div class="col-md-10">
                                <input 
                                    type="text" 
                                    v-model="form.name" 
                                    class="form-control" 
                                    required
                                    :class="{ 'is-invalid': errors.name }"
                                />
                                <div v-if="errors.name" class="invalid-feedback">
                                    {{ errors.name }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group row">
                            <label class="col-md-2 col-form-label">{{ ucFirst(translate('generic.color')) }} *</label>
                            <div class="col-md-10">
                                <input 
                                    type="color" 
                                    v-model="form.hexcolor" 
                                    class="form-control" 
                                    required
                                    :class="{ 'is-invalid': errors.hexcolor }"
                                />
                                <div v-if="errors.hexcolor" class="invalid-feedback">
                                    {{ errors.hexcolor }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group row">
                            <label class="col-md-2 col-form-label">{{ ucFirst(translate('generic.remarks')) }}</label>
                            <div class="col-md-10">
                                <textarea 
                                    v-model="form.remarks" 
                                    class="form-control" 
                                    rows="3"
                                ></textarea>
                            </div>
                        </div>
                        
                        <div class="form-group row">
                            <div class="col-md-10 offset-md-2">
                                <button type="submit" class="btn btn-primary">
                                    {{ ucFirst(translate('generic.save')) }}
                                </button>
                                <a href="/studentlists" class="btn btn-secondary ml-2">
                                    {{ ucFirst(translate('generic.cancel')) }}
                                </a>
                            </div>
                        </div>
                    </form>
                </panel>
            </div>
        </div>
        
        <div v-if="isEditing" class="row mt-4">
            <div class="col-12">
                <student-list-students :studentlist-id="studentlistId" />
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <student-lists-table />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import StudentListsTable from './StudentListsTable.vue';
import StudentListStudents from './StudentListStudents.vue';
import useLang from '../../composables/useLang';
import useStudentLists from '../../composables/useStudentLists';
import useNoty from '../../composables/useNoty';
import axios from 'axios';

const props = defineProps({
    studentlistId: {
        type: [Number, String],
        default: 0
    }
});

const { ucFirst, translate, translateChoice } = useLang();
const { busy, getStudentLists, setCurrentStudentList } = useStudentLists();
const { successNoty, errorNoty } = useNoty();

const form = ref({
    name: '',
    hexcolor: '#000000',
    remarks: ''
});

const errors = ref({});
const isSubmitting = ref(false);

const isEditing = computed(() => parseInt(props.studentlistId) > 0);

// Load student list data if editing
onMounted(async () => {
    if (isEditing.value) {
        try {
            busy.value = true;
            const response = await axios.get(`/api/studentlists`);
            const studentlist = response.data.find(list => list.id === parseInt(props.studentlistId));
            
            if (studentlist) {
                form.value.name = studentlist.name;
                form.value.hexcolor = '#' + studentlist.hexcolor;
                form.value.remarks = studentlist.remarks || '';
                setCurrentStudentList(studentlist.id);
            }
        } catch (error) {
            console.error('Error loading student list:', error);
            errorNoty(ucFirst(translate('generic.errorloadingstudentlist')));
        } finally {
            busy.value = false;
        }
    }
    
    // Load all student lists for the table
    getStudentLists();
});

// Save student list
const saveStudentList = async () => {
    if (isSubmitting.value) return;
    
    errors.value = {};
    isSubmitting.value = true;
    busy.value = true;
    
    try {
        // Validate form
        if (!form.value.name) {
            errors.value.name = ucFirst(translate('generic.fieldisrequired'));
        }
        
        if (!form.value.hexcolor) {
            errors.value.hexcolor = ucFirst(translate('generic.fieldisrequired'));
        }
        
        if (Object.keys(errors.value).length > 0) {
            isSubmitting.value = false;
            busy.value = false;
            return;
        }
        
        // Prepare form data
        const formData = new FormData();
        formData.append('name', form.value.name);
        formData.append('hexcolor', form.value.hexcolor);
        formData.append('remarks', form.value.remarks);
        
        if (isEditing.value) {
            // Update existing student list
            formData.append('_method', 'PUT');
            await axios.post(`/studentlists/${props.studentlistId}`, formData);
            successNoty(ucFirst(translate('generic.savesuccess')));
        } else {
            // Create new student list
            await axios.post('/studentlists', formData);
            successNoty(ucFirst(translate('generic.savesuccess')));
            
            // Reset form
            form.value = {
                name: '',
                hexcolor: '#000000',
                remarks: ''
            };
        }
        
        // Refresh student lists
        await getStudentLists();
    } catch (error) {
        console.error('Error saving student list:', error);
        errorNoty(ucFirst(translate('generic.savingfailed')));
        
        if (error.response && error.response.data && error.response.data.errors) {
            errors.value = error.response.data.errors;
        }
    } finally {
        isSubmitting.value = false;
        busy.value = false;
    }
};
</script>

<style scoped>
/* Any component-specific styles can go here */
</style>
