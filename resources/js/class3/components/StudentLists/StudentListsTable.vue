<template>
    <panel :busy="busy">
        <template #title>
            <i class="fas fa-list"></i>
            {{ ucFirst(translateChoice('generic.studentlists', 2)) }}
        </template>
        <template #subtitle>
            <a href="/studentlists/create" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i>
                {{ ucFirst(translate('generic.new')) }} {{ translate('generic.studentlist') }}
            </a>
        </template>
        
        <div class="row font-weight-bold mb-2">
            <div class="col-md-1">{{ ucFirst(translate('generic.functions')) }}</div>
            <div class="col-md-1">{{ ucFirst(translate('generic.color')) }}</div>
            <div class="col-md-4">{{ ucFirst(translate('generic.name')) }}</div>
            <div class="col-md-6">{{ ucFirst(translate('generic.remarks')) }}</div>
        </div>
        <hr/>
        
        <div v-if="studentlists.length === 0" class="alert alert-info">
            {{ ucFirst(translate('generic.nostudentlistsfound')) }}
        </div>
        
        <div v-else v-for="list in studentlists" :key="list.id" class="row mb-2">
            <div class="col-md-1">
                <div class="btn-group">
                    <a :href="'/studentlists/' + list.id + '/edit'" class="btn btn-sm btn-success">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button @click="confirmDelete(list.id)" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <span class="badge" :style="'background-color:#' + list.hexcolor">&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </div>
            <div class="col-md-4">
                {{ list.name }} ({{ list.students.length }})
                <span 
                    v-tooltip="translate('generic.totalnrofstudentsonthislist')"
                    data-toggle="popover"
                    data-trigger="hover"
                    :data-content="getStudentsPopoverContent(list.students)"
                    :title="ucFirst(translateChoice('generic.students', 2))"
                    class="fas fa-question-circle"
                ></span>
            </div>
            <div class="col-md-6 text-truncate" v-tooltip="list.remarks">
                {{ list.remarks }}
            </div>
        </div>
        
        <are-you-sure
            :button-text="ucFirst(translate('generic.delete'))"
            modal-id="confirm-delete-studentlist"
            @confirmclicked="deleteStudentList"
        >
            {{ ucFirst(translate('generic.areyousuredeletestudentlist')) }}
        </are-you-sure>
    </panel>
</template>

<script setup>
import { onMounted, onUpdated } from 'vue';
import Panel from '../Layout/Panel.vue';
import AreYouSure from '../Layout/AreYouSure.vue';
import useLang from '../../composables/useLang';
import useStudentLists from '../../composables/useStudentLists';
import $ from 'jquery';

const { ucFirst, translate, translateChoice } = useLang();
const { 
    busy, 
    studentlists, 
    studentlistIdToDelete,
    deleteStudentList,
    getStudentsPopoverContent
} = useStudentLists();

const confirmDelete = (id) => {
    studentlistIdToDelete.value = id;
    $('#confirm-delete-studentlist').modal('show');
};

onUpdated(() => {
    $('[data-toggle="popover"]').popover({ html: true });
});

onMounted(() => {
    $('[data-toggle="popover"]').popover({ html: true });
});
</script>

<style scoped>
/* Any component-specific styles can go here */
</style>
