<template>
    <div>
        <panel-4 :busy="busy">
            <template #title>
                <i class="fas fa-list"></i>
                {{ panelTitle }}
            </template>

            <div class="mb-3 row">
                <label class="col-md-2 col-form-label">{{ ucFirst(translate('generic.name')) }} *</label>
                <div class="col-md-7">
                    <input
                        type="text"
                        v-model="studentlistToEdit.name"
                        class="form-control"
                        required
                        @input="setDirty"
                    />
                </div>
                <label class="col-md-1 col-form-label text-end">{{ ucFirst(translate('generic.color')) }} *</label>
                <div class="col-md-2">
                    <input
                        type="color"
                        v-model="studentlistToEdit.hexcolor"
                        class="form-control color-input"
                        required
                        @input="setDirty"
                    />
                </div>
            </div>

            <div class="mb-3 row">
                <label class="col-md-2 col-form-label">{{ ucFirst(translate('generic.remarks')) }}</label>
                <div class="col-md-10">
                    <textarea
                        v-model="studentlistToEdit.remarks"
                        class="form-control"
                        rows="3"
                        @input="setDirty"
                    ></textarea>
                </div>
            </div>
        </panel-4>

        <student-list-students v-if="isEditing" />
    </div>
</template>

<script setup>
import { computed, watch } from 'vue';
import Panel4 from '../Layout/bs5/Panel4.vue';
import StudentListStudents from './StudentListStudents.vue';
import useLang from '../../composables/useLang';
import useStudentLists from '../../composables/useStudentLists';

const { ucFirst, translate } = useLang();
const {
    busy,
    dirty,
    isEditing,
    studentlistIdToEdit,
    studentlistToEdit,
    getStudentList
} = useStudentLists();

// Set dirty flag
const setDirty = () => {
    dirty.value = true;
};

const panelTitle = computed(() => {
    return isEditing.value
        ? ucFirst(translate('generic.editstudentlist'))
        : ucFirst(translate('generic.newstudentlist'));
});

// Watch for changes in the selected student list ID
watch(studentlistIdToEdit, () => {
    getStudentList();
}, { immediate: true });

// Watch for changes to propagate the dirty-flag
watch(dirty, () => {
    window.dirty = dirty.value;
});
</script>

<style scoped>
.color-input {
    height: 38px;
    padding: 0;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.text-end {
    text-align: right;
}
</style>
