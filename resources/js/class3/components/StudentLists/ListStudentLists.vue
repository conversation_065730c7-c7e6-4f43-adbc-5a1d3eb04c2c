<template>
    <panel-4 :busy="busy">
        <template #title>
            <i class="fas fa-list"></i>
            {{ ucFirst(translateChoice('generic.studentlists', 2)) }}
        </template>
        <template #subtitle>
            <button class="btn btn-primary btn-sm" @click="studentlistIdToEdit = 0">
                <i class="fas fa-plus"></i>
                {{ ucFirst(translate('generic.newstudentlist')) }}
            </button>
        </template>
        
        <div class="row font-weight-bold mb-2">
            <div class="col-md-1">{{ ucFirst(translate('generic.functions')) }}</div>
            <div class="col-md-1">{{ ucFirst(translate('generic.color')) }}</div>
            <div class="col-md-4">{{ ucFirst(translate('generic.name')) }}</div>
            <div class="col-md-6">{{ ucFirst(translate('generic.remarks')) }}</div>
        </div>
        <hr/>
        
        <div v-if="studentlists.length === 0" class="alert alert-info">
            {{ ucFirst(translate('generic.nostudentlistsfound')) }}
        </div>
        
        <div v-else v-for="list in studentlists" :key="list.id" class="row mb-2">
            <div class="col-md-1">
                <div class="btn-group" role="group" aria-label="functions">
                    <button
                        class="btn btn-primary btn-sm mr-1"
                        v-tooltip="translate('generic.edit')"
                        @click="studentlistIdToEdit = list.id"
                    >
                        <font-awesome-icon icon="edit" />
                    </button>
                    <button
                        class="btn btn-danger btn-sm"
                        v-tooltip="translate('generic.delete')"
                        @click="confirmDelete(list.id)"
                    >
                        <font-awesome-icon icon="trash" />
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <span class="badge border-1" :style="{ backgroundColor: `#${list.hexcolor}` }">&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </div>
            <div class="col-md-4">
                {{ list.name }} ({{ list.students.length }})
                <span 
                    v-tooltip="translate('generic.totalnrofstudentsonthislist')"
                    data-bs-toggle="popover"
                    data-bs-trigger="hover"
                    :data-bs-content="getStudentsPopoverContent(list.students)"
                >
                    <i class="fas fa-question-circle"></i>
                </span>
            </div>
            <div class="col-md-6 text-truncate" v-tooltip="list.remarks">
                {{ list.remarks }}
            </div>
        </div>
        
        <are-you-sure-4
            :button-text="ucFirst(translate('generic.delete'))"
            modal-id="confirm-delete-studentlist"
            @confirmclicked="deleteStudentList"
        >
            {{ ucFirst(translate('generic.areyousuredeletestudentlist')) }}
        </are-you-sure-4>
    </panel-4>
</template>

<script setup>
import { onMounted, onUpdated } from 'vue';
import Panel4 from '../Layout/bs5/Panel4.vue';
import AreYouSure4 from '../Layout/bs5/AreYouSure4.vue';
import useLang from '../../composables/useLang';
import useStudentLists from '../../composables/useStudentLists';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate, translateChoice } = useLang();
const { 
    busy, 
    studentlists, 
    studentlistIdToDelete,
    studentlistIdToEdit,
    deleteStudentList,
    getStudentsPopoverContent
} = useStudentLists();

const confirmDelete = (id) => {
    studentlistIdToDelete.value = id;
    // Replace jQuery modal show with BS5 approach
    const modal = document.getElementById('confirm-delete-studentlist');
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
};

onUpdated(() => {
    // Replace jQuery popover initialization with BS5 approach
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            html: true
        });
    });
});

onMounted(() => {
    // Replace jQuery popover initialization with BS5 approach
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            html: true
        });
    });
});
</script>

<style scoped>
.border-1 {
    border: 1px solid #ccc;
}
</style>
