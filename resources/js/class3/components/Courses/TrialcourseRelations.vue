<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-graduation-cap"></i>
            {{ucFirst(translate('generic.triallessons'))}} {{ translate('generic.followuprelations') }}
        </template>

        <div class="row">
            <span
                class="col"
                v-for="course in nonTrialCourses"
                :key="'nontrialcourse_' + course.id"
            >
                <span class="badge badge-pill badge-primary bigger"
                      draggable="true" @dragstart="drag"
                      :data-course-id="course.id"
                >
                    {{ course.name }}
                </span>
            </span>
        </div>
        <div class="row">
            <div class="col text-center">
                <small>{{ ucFirst(translate('generic.explaintrialcourserelation'))}}</small>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-1">
                <strong>{{ucFirst(translate('generic.edit'))}}</strong>
            </div>
            <div class="col-md-4">
                <strong>{{ucFirst(translate('generic.name'))}}</strong>
            </div>
            <div class="col-md-7">
                <strong>{{ucFirst(translate('generic.istrialcoursefor'))}}</strong>
                <small class="ml-2">({{translate('generic.clicktodelete')}})</small>
            </div>
        </div>
        <!-- List trial courses and their relations with other courses -->
        <div
            class="row mt-2"
            v-for="course in trialCourses"
            :key="course.id"
        >
            <div class="col-md-1">
                <a class="btn btn-sm btn-primary"
                   data-toggle="tooltip"
                   v-tooltip="translate('generic.edit')"
                   :href="'/courses/' + course.id + '/edit'"
                >
                    <i class="fa fa-edit"></i>
                </a>
            </div>
            <div class="col-md-4">
                <div class="btn btn-primary btn-block"
                     @drop="drop"
                     @dragover="allowDrop"
                     @dragleave="removeStyle"
                     :data-trialcourse-id="course.id"
                >
                    {{ course.name }}
                </div>
            </div>
            <div class="col-md-7">
                <div v-if="courseRelations[course.id] != null">
                    <span
                        v-for="assocCourseId in courseRelations[course.id]"
                        :key="course.id + '_' + assocCourseId"
                    >
                        <span class="mb-1 mr-1 delete-icon" @click="unlinkFromTrialCourse(course.id, assocCourseId)">
                            {{ showCourseName(assocCourseId) }}
                            <i class="fa fa-trash"></i>
                        </span>
                    </span>
                </div>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref } from "vue";
import Panel from "../Layout/Panel";
import useLang from "../../composables/useLang";
import useNoty from "../../composables/useNoty";
import useBaseData from "../../composables/useBaseData";

const { ucFirst, translate } = useLang();
const { filteredCoursesNoRental, initBaseData } = useBaseData();
const { failNoty } =useNoty();
const busy = ref(false);
const courseRelations = ref([]);

onMounted(async () => {
    await initBaseData({courses: true});
    getCourseTrialcourseRelations();
});

const trialCourses = computed(() => {
    return filteredCoursesNoRental.value.filter(course => course.is_trial_course);
});

const nonTrialCourses = computed(() => {
    const courses = filteredCoursesNoRental.value.filter(course => !course.is_trial_course);
    // only unique names (not variants, being other timespan or frequency)
    return courses.filter((course, index) => {
        return index === 0 ? true : course.name !== courses[index-1].name;
    });
});

const getCourseTrialcourseRelations = async () => {
    try {
        const response = await axios.get("/api/coursetrialcourse");
        courseRelations.value = response.data;
    } catch(err) {
        failNoty("error fetching trial course relations");
    }
};

const showCourseName = (courseId) => {
    const course = filteredCoursesNoRental.value.find(c => c.id === courseId);
    if (course != null) {
        return course.name;
    } else {
        return 'unknown';
    }
}

const drag = (ev) => {
    ev.dataTransfer.setData("text", ev.target.getAttribute('data-course-id'));
}
const drop = async (ev) => {
    ev.preventDefault();
    removeStyle(ev);
    let trialCourse = ev.target.getAttribute('data-trialcourse-id');
    let courseId = ev.dataTransfer.getData("text");
    console.log(`trialcourse: ${trialCourse}, course: ${courseId}`);
    try {
        await axios.post('/api/trialcourserelation', {trialCourse, courseId});
        getCourseTrialcourseRelations();
    } catch(err) {
        failNoty("error saving course relation");
        console.log(`Error saving new trialcourse-course-relation: ${err}`);
    }
}
const allowDrop = (ev) => {
    ev.preventDefault();
    $(ev.target).removeClass('btn-primary').addClass('btn-danger');
}
const removeStyle = (ev) => {
    $(ev.target).removeClass('btn-danger').addClass('btn-primary');
}
const unlinkFromTrialCourse = async (trialCourseId, courseId) => {
    console.log(`unlinking ${courseId} from ${trialCourseId}`)
    await axios.delete('/api/trialcourserelation', {data: {courseId, trialCourseId}});
    getCourseTrialcourseRelations();
}

</script>

<style scoped>
.delete-icon {
    display: inline-block;
    border: solid 1px black;
    padding-left: 0.4rem;
    padding-right: 0.4rem;
    cursor: url('/images/icons8-remove-32.png'), auto;
}
.bigger {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}
</style>
