<template>
    <panel :busy="busy" extraClass="min-with-cards-2"  panel-test-id="class-search-courses">
        <template v-slot:title>
            <i class="fas fa-search"></i> {{ucFirst(translate('generic.directsearch'))}} {{translateChoice('generic.courses',2)}}
        </template>
        <template v-slot:subtitle>
            <div class="form-group form-check">
                <input type="checkbox" class="form-check-input" id="onlyactive" v-model="onlyactive">
                <label class="form-check-label" for="onlyactive">{{ucFirst(translate('generic.onlyactive'))}}</label>
            </div>
        </template>
        <div class="form-group">
            <div class="input-group searchbox mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="search-addon"><i class="fas fa-search"></i></span>
                </div>
                <input type="text" class="form-control" :placeholder="translate('generic.coursename')"
                       aria-label="Searchbox" aria-describedby="search-addon"
                       v-model="courseSearchkey">
            </div>
        </div>

        <div class="table-fix-head">
            <table class="table table-striped table-sm table-responsive-md">
                <thead class="thead-light">
                <tr>
                    <th>{{ucFirst(translate('generic.name'))}}</th>
                    <th>{{ucFirst(translateChoice('generic.recurrenceoptions', 1))}}</th>
                    <th>{{ucFirst(translateChoice('generic.students', 2))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="course in filteredCourses" :key="course.id">
                    <td><a :href="'/courses/' + course.id + '/edit'">{{course.name}}</a></td>
                    <td class="text-truncate">{{ course.recurrenceoption }}</td>
                    <td>{{ course.currentstudentsarray.length }}</td>
                </tr>
                <tr v-if="filteredCourses.length === 0">
                    <td colspan="3">{{ucFirst(translate('generic.nocoursesfound'))}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </panel>
</template>

<script>
import Panel from '../Layout/Panel';
import axios from 'axios';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

export default {
    name: 'SearchCourses',
    components: { panel: Panel },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            busy: false,
            onlyactive: true,
            courseSearchkey: '',
            courses: []
        };
    },
    mounted () {
        this.getCourses();
    },
    methods: {
        getCourses () {
            this.busy = true;
            axios.get('/api/courses')
                .then((response) => {
                    this.courses = response.data.data;
                })
                .catch((err) => {
                    this.failNoty(ucFirst(translate('generic.errorloadingcourses')) + `: ${err}`);
                    console.log(`error retrieving courses from database ${err}`);
                })
                .finally(() => {
                    this.busy = false;
                });
        }
    },
    computed: {
        filteredCourses () {
            if (this.onlyactive) {
                return this.courses.filter(
                    course =>
                        course.name.toLowerCase().includes(this.courseSearchkey.toLowerCase()) &&
                            course.currentstudentsarray.length > 0
                );
            } else {
                return this.courses.filter(
                    course => course.name.toLowerCase().includes(this.courseSearchkey.toLowerCase())
                );
            }
        }
    }
};
</script>

<style scoped lang="scss">
@import '../../../../sass/tmpl3/variables';
.table-fix-head {
    overflow-y: auto;
    height: 20rem;
    & thead th {
        position: sticky;
        top: -3px;
    }
}
</style>
