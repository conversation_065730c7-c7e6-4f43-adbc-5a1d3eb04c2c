<template>
    <panel>
        <template v-slot:title>
            <i class="fas fa-users"></i>
            {{ucFirst(translateChoice('generic.studentgroups',2))}}
        </template>
        <template v-slot:subtitle>
            <a :href="`/studentgroups/create?course_id=${courseId}`"
               class="btn btn-success btn-sm">
                {{ucFirst(translate('generic.newstudentgroup'))}}
            </a>
        </template>
        <div v-if="studentGroupSpecsOfCourse.length > 0">
            <div class="row">
                <div class="col-md-1"><h5>{{ucFirst(translate('generic.functions'))}}</h5></div>
                <div class="col-md-2"><h5>{{ucFirst(translate('generic.name'))}}</h5></div>
                <div class="col-md-9"><h5>{{ucFirst(translate('generic.numberofstudents'))}}</h5></div>
            </div>
            <div class="row" v-for="studentGroup in studentGroupSpecsOfCourse" :key="studentGroup.id">
                <div class="col-md-1">
                    <a :href="'/studentgroups/' + studentGroup.id + '/edit'"
                       v-tooltip="translate('generic.edit')"
                       data-toggle="tooltip"
                       class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i>
                    </a>

                    <!-- planning link -->
                    <a v-if="studentGroup.students.length > 0"
                       :href="'/planning/lesson?studentgroupId=' + studentGroup.id"
                       class="edittimetable btn btn-sm btn-outline-secondary"
                       v-tooltip="ucFirst(translate('generic.opentimetable'))">
                        <span class='fas fa-calendar'></span>
                    </a>
                </div>
                <div class="col-md-2">{{ studentGroup.lastname }}</div>
                <div class="col-md-9">{{ studentGroup.students.length }}</div>
            </div>
        </div>
        <div v-else class="row">
            <div class="col">{{ucFirst(translate('generic.nostudentgroupsfound'))}}</div>
        </div>
    </panel>
</template>

<script setup>
import useLang from "../../composables/useLang";
import useCourse from "../../composables/useCourse";
import Panel from "../Layout/Panel.vue";

const { translate, translateChoice, ucFirst } = useLang();
const { courseId, studentGroupSpecsOfCourse } = useCourse();

</script>

<style scoped>

</style>
