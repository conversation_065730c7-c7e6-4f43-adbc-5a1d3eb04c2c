<template>
    <panel>
        <template v-slot:title>
            <i class="fas fa-user"></i>
            <span v-if="showType==='active'">{{ucFirst(translate('generic.studentsonthiscourse'))}}</span>
            <span v-else>{{ucFirst(translate('generic.unregistered'))}} {{translateChoice('generic.students',2)}}</span>
        </template>
        <div>
            <table class="table">
                <thead>
                    <tr>
                        <th v-if="showType==='active'">{{ucFirst(translate('generic.studentgroupname'))}}</th>
                        <th>{{ucFirst(translate('generic.name'))}}</th>
                        <th>{{ucFirst(translate('generic.age'))}}</th>
                        <th>{{ucFirst(translate('generic.registrationdate'))}}</th>
                        <th v-if="showType==='inactive'">{{ucFirst(translate('generic.unregisterdate'))}}</th>
                        <th v-else-if="isTrialCourse">{{ucFirst(translate('generic.status'))}}</th>
                        <th v-else>&nbsp;</th>
                    </tr>
                </thead>
                <tbody>
                    <!--button to create a group from selected students-->
                    <tr v-if="selectedStudents.length > 0">
                        <td colspan="5">
                            <button
                                data-toggle="modal"
                                data-target="#studentgroupEntryModal"
                                class="btn btn-sm btn-success"
                                @click.prevent="noop"
                            >
                                {{translate('generic.createnewstudentgroup')}}
                            </button>
                            <small class="ml-2">{{translate('generic.clicktochoosestudentstocreategroup')}}</small>
                        </td>
                    </tr>
                    <!--list students-->
                    <tr v-for="(student, index) in filteredStudents" :key="index">
                        <td v-if="showType==='active'">
                            <span v-if="showMyStudentGroupForThisCourse(student.studentgroups) === ''" class="text-muted">
                                <input
                                    type="checkbox"
                                    class="custom-checkbox"
                                    v-model="selectedStudents"
                                    :value="student.id"
                                />
                                <span aria-roledescription="label" class="adjust-cb-label ml-1">({{ translate('generic.newstudentgroup') }})</span>
                            </span>
                            <span v-else>{{ showMyStudentGroupForThisCourse(student.studentgroups) }}</span>
                        </td>
                        <td><a :href="'/students/' + student.id + '/edit'">{{ student.name }}</a></td>
                        <td>{{ displayDate(student.date_of_birth) }} ({{ displayAge(student.date_of_birth) }})</td>
                        <td>{{ displayDate(student.pivot.start_date) }}</td>
                        <td v-if="showType==='inactive'">{{ displayDate(student.pivot.end_date) }}</td>
                        <td v-else-if="!isTrialCourse && student.pivot.signed">{{translate('generic.signed')}}</td>
                        <td v-else-if="!isTrialCourse && !student.pivot.signed">{{translate('generic.notsigned')}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <modal
            v-if="showType==='active'"
            modal-id="studentgroupEntryModal"
            :popup-title="ucFirst(translateChoice('generic.studentgroups', 1))"
            :closetext="ucFirst(translate('generic.close'))"
        >
            <div>
                <h5>{{ucFirst(translate('generic.newgroupforthesestudents'))}}:</h5>
                <div v-for="(studentid, index) in selectedStudents" :key="index">{{ getStudentName(studentid) }}</div>
            </div>
            <hr/>
            <div class="form-group">
                <label for="newgroupname">{{ucFirst(translate('generic.groupname'))}}</label>
                <input id='newgroupname' type="text" class="form-control" v-model="newgroupname"/>
            </div>
            <template v-slot:okbutton>
                <button
                    type="button"
                    class="btn btn-danger"
                    data-dismiss="modal"
                    @click.prevent="createNewGroup"
                    :disabled="newgroupname.length < 2"
                >
                    {{ ucFirst(translate('generic.save')) }}
                </button>
            </template>
        </modal>
    </panel>
</template>

<script>
import Panel from '../Layout/Panel';
import Modal from '../Layout/Modal3';
import axios from 'axios';

import useLang from "../../composables/useLang";
import useDateTime from "../../composables/useDateTime";

const { translate, translateChoice, ucFirst } = useLang();
const { displayAge, displayDate, isFutureDate } = useDateTime();

export default {
    name: 'StudentsOnCourse',
    components: { Modal, Panel },
    setup() {
        return {
            displayAge,
            displayDate,
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            selectedStudents: [],
            newgroupname: ''
        };
    },
    props: {
        isTrialCourse: {
            type: Boolean,
            default: false
        },
        students: {
            type: Array,
            default: () => []
        },
        courseId: {
            type: Number,
            required: true
        },
        userIsAdmin: {
            type: Boolean,
            default: false
        },
        showType: {
            type: String,
            required: true,
            validator: (value) => {
                // in effect this is an enumeration
                return ['active', 'inactive'].indexOf(value) !== -1;
            }
        }
    },
    computed: {
        /**
         * Filter out 'active' or 'inactive' students, according to the property 'showType'
         * @returns {T[]}
         */
        filteredStudents () {
            if (this.showType === 'active') {
                return this.students.filter(student => {
                    return student.pivot.end_date === null ||
                        student.pivot.end_date === '' ||
                        (student.pivot.end_date !== '' && isFutureDate(student.pivot.end_date));
                });
            } else {
                return this.students.filter(student => {
                    return student.pivot.end_date !== null &&
                        student.pivot.end_date !== '' &&
                        !isFutureDate(student.pivot.end_date);
                });
            }
        }
    },
    methods: {
        /**
         * find out which of the student groups (if any)
         * belongs to this course and return its name
         * @param groups
         * @returns {*}
         */
        showMyStudentGroupForThisCourse (groups = []) {
            const retGroup = groups.filter(group => group.courses[0].id === this.courseId);
            return retGroup.length === 0
                ? ''
                : retGroup[0].lastname;
        },
        getStudentName (studentid) {
            const theStudent = this.students.filter(
                student => student.id === studentid
            );
            return theStudent[0].name;
        },
        /**
         * Creates a new student group (via API)
         * then refreshes the data
         */
        createNewGroup () {
            const groupdata = {
                courseid: this.courseId,
                students: this.selectedStudents,
                groupname: this.newgroupname
            };
            axios.post('/api/newstudentgroup', groupdata)
                .then(resp => {
                    this.successNoty(
                        ucFirst(translate('generic.newstudentgroupcreated')),
                        ucFirst(translate('generic.succeeded'))
                    );
                })
                .catch(err => {
                    this.failNoty(
                        ucFirst(translate('generic.errorcreatingnewstudentgroup')) + err,
                        ucFirst(translate('generic.failed'))
                    );
                    this.pnotify_danger('error', 'error creating new studentgroup!');
                })
                .finally(() => {
                    // fixme: bit ugly, we need to refresh the whole page.
                    // fixme: get data from store to prevent need for page refresh
                    window.location.reload();
                });
        }
    }
};
</script>

<style scoped>
    .adjust-cb-label {
        position: relative;
        top: -5px;
    }
</style>
