<template>
    <div>
        <strong>{{ form<PERSON>abel }}</strong><br><hr>
        <!-- if not initial state (edit or new) -->
        <template v-if="formLabel.length < 10">
            <div class="mb-3">
                <label for="nr_of_times" class="form-label">{{ ucFirst(translate('generic.nroftimes')) }}</label>
                <input
                    type="number"
                    step="0.5"
                    class="form-control"
                    id="nr_of_times"
                    v-model="recurrenceOptionToEdit.nr_of_times"
                >
            </div>
            <div class="mb-3">
                <label for="timeunit" class="form-label">{{ ucFirst(translate('generic.timeunit')) }}</label>
                <select
                    class="form-select"
                    id="timeunit"
                    v-model="recurrenceOptionToEdit.timeunit"
                >
                    <option
                        v-for="(timeunit, index) in timeunits"
                        :key="index"
                        :value="timeunit.value"
                    >
                        {{ ucFirst(translateChoice(`generic.${timeunit.label}`, 1)) }}
                    </option>
                </select>
            </div>
            <div class="mb-3">
                <label for="per_interval" class="form-label">{{ ucFirst(translate('generic.per')) }}</label>
                <select
                    class="form-select"
                    id="per_interval"
                    v-model="recurrenceOptionToEdit.per_interval"
                >
                    <option
                        v-for="(per_interval, index) in per_intervals"
                        :key="index"
                        :value="per_interval.value"
                    >
                        {{ ucFirst(translate(`generic.${per_interval.label}`)) }}
                    </option>
                </select>
            </div>
            <div class="mb-3">
                <label for="ends_after_nr_of_occurrences" class="form-label">{{ ucFirst(translate('generic.andendsafter')) }}</label>
                <input
                    type="number"
                    class="form-control"
                    id="ends_after_nr_of_occurrences"
                    v-model="recurrenceOptionToEdit.ends_after_nr_of_occurrences"
                >
                <span>{{ translateChoice('generic.repetitions', 2) }}</span>
                <small class="form-text text-muted ms-1">({{ ucFirst(translate('generic.leaveemptyforcontinuous')) }})</small>
            </div>
            <div v-if="recurrenceOptionToEdit?.courses?.length > 0">
                <!-- changes will affect the following courses: -->
                <div class="alert alert-warning">
                    {{ translateChoice('generic.changeswilleffectthefollowingcourses', recurrenceOptionToEdit.courses.length) }}:
                    <span v-for="(course, index) in recurrenceOptionToEdit.courses" :key="index" class="me-2">
                        <a :href="`/courses/${course.id}/edit`">{{ course.name }}</a>
                    </span>
                </div>
            </div>
            <div class="mb-3">
                <button
                    class="btn btn-primary"
                    @click.prevent="doSave"
                >
                    {{ ucFirst(translate('generic.save')) }}
                </button>
                <button
                    class="btn btn-secondary ms-2"
                    @click.prevent="recurrenceOptionToEdit = {}"
                >
                    {{ ucFirst(translate('generic.cancel')) }}
                </button>
            </div>
        </template>
    </div>
</template>

<script setup>
import { computed } from "vue";
import useLang from "../../composables/useLang";
import useRecurrenceOptions from "../../composables/useRecurrenceOptions";
import useToast from "../../composables/useToast.js";

const { ucFirst, translate, translateChoice } = useLang();
const { failToast, successToast } = useToast();
const { per_intervals, recurrenceOptionToEdit, saveRecurrenceOption, timeunits } = useRecurrenceOptions();

/**
 * initially there will be no object at all, then return 'please choose a recurrence option or create a new one'
 * if there is an object, check if it has an id, if it has an id, return 'Edit', if it doesn't have an id, return 'New'
 * @type {ComputedRef<any|string>}
 */
const formLabel = computed(() => {
    // check if this is an initial state, having no object at all (<> empty)
    if (!recurrenceOptionToEdit.value?.nr_of_times && !recurrenceOptionToEdit.value?.timeunit && !recurrenceOptionToEdit.value?.per_interval && !recurrenceOptionToEdit.value?.ends_after_nr_of_occurrences) {
        return translate('generic.pleasechoosearecurrenceoptionorcreateanewone');
    }
    return recurrenceOptionToEdit.value?.id ? ucFirst(translate('generic.edit')) : ucFirst(translate('generic.new'));
});

const doSave = async () => {
    try {
        await saveRecurrenceOption();
        recurrenceOptionToEdit.value = {};
        successToast(ucFirst(translateChoice('recurrenceoptions', 1)) + ' ' + translate('generic.saved'));
    } catch (error) {
        failToast(error);
    }
}

</script>

<style scoped>

</style>
