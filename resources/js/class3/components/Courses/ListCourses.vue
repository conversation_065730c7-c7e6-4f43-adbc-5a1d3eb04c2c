<template>
    <panel
        :busy="busy"
    >
        <template v-slot:title>
            <i class="fas fa-graduation-cap"></i>
            {{ ucFirst(translateChoice('generic.courses', 2)) }}
        </template>
        <template v-slot:subtitle>
            <a href='/courses/create' class="btn btn-sm btn-success">
                {{ ucFirst(translate('generic.newcourse')) }}
            </a>
        </template>

        <!-- based on coursegroups -->
        <table class="tableFixHead">
            <thead>
            <tr>
                <th>
                    {{ ucFirst(translate('generic.name')) }} {{ translate('generic.coursegroup') }}
                </th>
                <th>
                    <div class="row">
                        <div class="offset-sm-7 col-3 text-center bg-danger text-light">
                            {{ ucFirst(translate('generic.price')) }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-3">
                            {{ ucFirst(translate('generic.coursename'))}} <span class="muted">({{ translate('generic.wordpresscode') }})</span>
                        </div>
                        <div class="col-1 text-center">
                            {{ ucFirst(translateChoice('generic.groups', 1)) }}
                        </div>
                        <div class="col-3">
                            {{ ucFirst(translateChoice('generic.recurrenceoptions',1)) }}
                        </div>
                        <div class="col-1 text-right">
                            {{ ucFirst(translate('generic.priceExTaxSubAdult', {adultthreshold: domain.adultThreshold})) }}
                        </div>
                        <div class="col-1 text-right">
                            {{ ucFirst(translate('generic.priceExTax')) }}
                        </div>
                        <div class="col-1 text-right">
                            {{ ucFirst(translate('generic.priceinvoice')) }}
                        </div>
                        <div class="col-1">
                            {{ translate('generic.nrofstudents') }}
                        </div>
                        <div class="col-1">
                            {{ translate('generic.nrofactivestudents') }}
                        </div>
                    </div>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="courseGroup in filteredCourseGroups" :key="courseGroup.id" class="courseGroupSeparator">
                <td>{{ courseGroup.name }}</td>
                <td>
                    <div
                        v-for="(course, index) in courseGroup.courses"
                        :key="course.id"
                        :class="['hoverHighlight', {'zebraOn' : index % 2 === 0}, 'row']"
                        @click="navigateToCourse(course.id)"
                    >
                        <div class="col-3">
                            {{ course.name }} <span class="muted">({{ course.id }})</span>
                        </div>
                        <div class="col-1 text-center">
                            {{ course.groupSize }}
                        </div>
                        <div class="col-3">
                            {{ course.recurrenceoption.description }}
                        </div>
                        <div class="col-1 text-right">
                            {{ course.price_ex_tax_sub_adult }} / {{ translate(`generic.${course.price_is_per === 'time' ? 'times' : course.price_is_per}`) }}
                        </div>
                        <div class="col-1 text-right">
                            {{ (course.price_invoice / 1.21).toFixed(2) }} / {{ translate(`generic.${course.price_is_per === 'time' ? 'times' : course.price_is_per}`) }}
                        </div>
                        <div class="col-1 text-right">
                             {{ course.price_invoice }} / {{ translate(`generic.${course.price_is_per === 'time' ? 'times' : course.price_is_per}`) }}
                        </div>
                        <div class="col-1">
                            {{ course.students.length }}
                        </div>
                        <div class="col-1">
                            {{ course.current_students.length }}
                            <a v-if="course.current_students.length > 0"
                               tabindex="0"
                               class="btn btn-sm btn-outline-secondary ml-1"
                               role="dialog"
                               data-toggle="pop"
                               @click.stop="openPopover"
                               :data-content="listStudents(course.current_students)"
                               :title="ucFirst(translateChoice('generic.students', 2))"
                            >
                                {{ translate('generic.who') }}&nbsp;<i class="fa fa-question-circle"></i>
                            </a>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </panel>
</template>

<script setup>
import {onMounted, onUpdated, ref} from "vue";
import Panel from "../Layout/Panel";
import useLang from "../../composables/useLang";
import useBaseData from "../../composables/useBaseData";

onUpdated(() => {
    // activate popovers (dynamically added)
    $('[data-toggle="pop"]').popover({
        html:true,
        trigger: 'focus'
    });
});

const { ucFirst, translate, translateChoice } = useLang();

const busy = ref(false);
const { initBaseData, filteredCourseGroups, domain } = useBaseData();

onMounted(async () => {
    busy.value = true;
    await initBaseData({
        courseGroups: true,
        domain: true,   // for domain.adultThreshold in the template
        courses: true   // for traial course relation
    });
    busy.value = false;
});

const navigateToCourse = (courseId) => {
    window.location = `/courses/${courseId}/edit`;
}

/**
 * noop, this is just here to prevent propagation to the popovers underlying TR
 * @param ev
 */
const openPopover = (ev) => {};

/**
 * for the popover: list students in group
 * @param students
 * @returns {string}
 */
const listStudents = (students) => {
    let retString = '<ul class="list-group list-group-flush">';
    students.forEach(student => {
        retString += `<li class="list-group-item"><a href="/students/${student.id}/edit">${student.name}</a></li>`
    });
    return `${retString}</ul>`;
}

</script>

<style scoped lang="scss">
@import '../../../../sass/tmpl3/variables';
/* for grouping the same course name, skip the table TD border*/
.empty {
    border-top: none;
}
.hoverHighlight:hover {
    background-color: $classblue;
    color: white;
    cursor: pointer;
}
.zebraOn {
    background-color: $gray-200;
    border-bottom: solid $gray-400 1px;
}
.courseGroupSeparator {
    border-bottom: solid 1px $gray-700;
}
table.tableFixHead {
    border-collapse: collapse;
    width: 100%;
    overflow: auto;
    height: 100px;
    & thead {
        position: sticky;
        top: 3.2rem;
        z-index: 1;
        & th {
            background: $gray-100;
            border-bottom: solid $gray-500 2px;
            color: black;
            position: sticky;
            top: 0;
            z-index: 1;
        }
    }
}
.muted {
    color: $gray-500;
}
</style>
