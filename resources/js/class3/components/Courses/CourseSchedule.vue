<template>
    <panel>
        <template v-slot:title>
            <i class="fas fa-calendar"></i>
            <span v-if="lessonTable.length > 0">
                {{ucFirst(translate('generic.courseisscheduledon'))}}
            </span>
            <span v-else>
                {{ucFirst(translate('generic.coursehasnotbeenscheduled'))}}
            </span>
        </template>
        <div v-if="lessonTable.length > 0">
            <div class="row" v-for="(lesson, index) in lessonTable" :key="index">
                <div class="col">
                    {{ucFirst(lesson.dow_name)}} {{translate('generic.at')}} {{lesson.time}} ({{lesson.nrOfEvents}}&nbsp;
                    {{translateChoice('generic.futureappointments', lesson.nrOfEvents)}})
                </div>
            </div>
        </div>
        <div v-else>&nbsp;</div>
    </panel>
</template>

<script>
import Panel from '../Layout/Panel';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();
export default {
    name: 'CourseSchedule',
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    components: { Panel },
    props: {
        lessonTable: {
            type: Array,
            default: () => []
        }
    },
};
</script>

<style scoped>

</style>
