<template>
    <panel>
        <template #title>
            <i class="fas fa-book"></i>
            {{ ucFirst(translate('generic.coursedata')) }}
            <small class="ml-2">{{ newMode ? translate("generic.new") : translate("generic.edit") }}</small>
        </template>
        <template v-if="!newMode" #subtitle>
            <QuickJumpCourse
                :exclude-id="parseInt(courseId)"
                :popup-title="ucFirst(translate('generic.quickjumptocourse'))"
                display-fields="name, recurrenceoption"
                api-get-string="/api/courses"
                @record-chosen="jumpToCourse"
            />
        </template>
        <div class="d-flex justify-content-start">
            <div class="form-group mr-2">
                <label for="courseName">{{ ucFirst(translate('generic.coursename')) }}*</label>
                <input type="text" class="form-control" id="courseName" v-model="courseName" required @keyup="dirty=true">
            </div>
            <div class="form-group mr-2">
                <label for="courseGroup">{{ ucFirst(translate('generic.coursegroup')) }}*</label>
                <select class="form-control" id="courseGroup" v-model="courseGroup" @change="dirty=true">
                    <option value="">{{ ucFirst(translate('generic.select')) }}</option>
                    <option v-for="cg in props.courseGroups" :key="cg.id" :value="cg.id">{{ cg.name }}</option>
                </select>
            </div>
            <div class="form-group mr-2">
                <!-- is trial course switch -->
                <material-switch
                    switch-id="isTrialCourse"
                    :label-on="translate('generic.isatrialcourse')"
                    :label-off="translate('generic.isnotatrialcourse')"
                    color="success"
                    v-model="isTrialCourse"
                    value-type="boolean"
                    :tooltip="ucFirst(translate('generic.explainistrialcourse'))"
                    @change="dirty=true"
                ></material-switch>
            </div>
            <div class="form-group" v-if="!newMode">
                <!-- archive switch -->
                <material-switch
                    switch-id="archiveCourse"
                    :label-on="translate('generic.coursearchived')"
                    :label-off="translate('generic.coursenotarchived')"
                    color="danger"
                    v-model="archiveCourse"
                    value-type="boolean"
                    :tooltip="ucFirst(translate('generic.explainarchivecourse'))"
                    @change="dirty=true"
                ></material-switch>
            </div>
        </div>
        <div class="d-flex justify-content-start mb-3">
            <!-- recurrence option * -->
            <div class="form-group mr-2">
                <label for="recurrenceOption">
                    {{ucFirst(translateChoice('generic.recurrenceoptions', 1)) }}*
                </label>
                <select class="form-control" id="recurrenceOption" v-model="recurrenceOption" @change="dirty=true">
                    <option value="">{{ ucFirst(translate('generic.select')) }}</option>
                    <option v-for="ro in props.recurrenceOptions" :key="ro.id" :value="ro.id">{{ ro.name }}</option>
                </select>
            </div>

            <!-- min studentgroup size * -->
            <div class="form-group mr-2">
                <label for="minStudentGroupSize">
                    {{ ucFirst(translate('generic.minstudentgroupsize')) }}*
                    <span
                        class="badge badge-pill badge-primary ml-2"
                        v-tooltip="ucFirst(translate('generic.explainminmaxgroupsize'))"
                    >
                        <i class="fas fa-question"></i>
                    </span>
                </label>
                <input
                    type="number"
                    class="form-control"
                    id="minStudentGroupSize"
                    v-model="minStudentGroupSize"
                    @keyup="dirty=true"
                >
            </div>
            <!-- max studentgroup size * -->
            <div class="form-group">
                <label for="maxStudentGroupSize">{{ ucFirst(translate('generic.maxstudentgroupsize')) }}*</label>
                <input
                    type="number"
                    class="form-control"
                    id="maxStudentGroupSize"
                    v-model="maxStudentGroupSize"
                    @keyup="dirty=true"
                >
            </div>
        </div>
        <div class="d-flex justify-content-start">
            <!-- price incl tax * -->
            <div class="form-group mr-2">
                <label for="priceIncTax">{{ ucFirst(translate('generic.price')) }}
                    {{ translate('generic.priceinvoice') }}*</label>
                <input
                    type="number"
                    class="form-control"
                    id="priceIncTax"
                    v-model="priceIncTax"
                    required
                    @keyup="dirty=true"
                >
            </div>
            <!-- price ex tax (calculated, not stored) -->
            <div class="form-group mr-2">
                <label for="priceExTax">
                    {{ ucFirst(translate('generic.price')) }} {{ translate('generic.priceExTax') }}
                    <span
                        class="badge badge-pill badge-primary ml-2"
                        v-tooltip="ucFirst(translate('generic.calculated'))"
                    >
                        <i class="fas fa-question"></i>
                    </span>
                </label>
                <input
                    type="number"
                    class="form-control"
                    id="priceExTax"
                    :value="priceExTax"
                    disabled>
            </div>
            <!-- price sub-adult * -->
            <div class="form-group mr-2">
                <label for="pricePreAdult">
                    {{ ucFirst(translate('generic.price')) }} < {{ props.domain.adultThreshold }}*
                    <span
                        class="badge badge-pill badge-primary ml-2"
                        v-tooltip="ucFirst(translate('generic.explainpricesubadult'))"
                    >
                        <i class="fas fa-question"></i>
                    </span>
                </label>
                <input
                    type="number"
                    class="form-control"
                    id="pricePreAdult"
                    v-model="pricePreAdult"
                    required
                    @keyup="dirty=true"
                >
            </div>
            <!-- price is per * -->
            <div class="form-group">
                <label for="priceIsPer">{{ ucFirst(translate('generic.priceper')) }}*</label>
                <select class="form-control" id="priceIsPer" v-model="priceIsPer" @change="dirty=true">
                    <option v-for="option in priceIsPerOptions" :key="option.value" :value="option.value">
                        {{ option.label }}
                    </option>
                </select>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import useLang from "../../composables/useLang";
import useCourse from "../../composables/useCourse";

import Panel from '../Layout/Panel';
import MaterialSwitch from '../Layout/MaterialSwitch';
import QuickJumpCourse from '../Layout/QuickJump';
const dirty = ref(false);

const { ucFirst, translate, translateChoice } = useLang();
const {
    archiveCourse,
    courseGroup,
    courseId,
    courseName,
    isTrialCourse,
    maxStudentGroupSize,
    minStudentGroupSize,
    newMode,
    priceExTaxCalculated,
    priceIncTax,
    priceIsPer,
    priceIsPerOptions,
    pricePreAdult,
    recurrenceOption
} = useCourse();

const jumpToCourse = (courseId) => {
    window.location.href = `/courses/${courseId}/edit`;

};

const props = defineProps({
    courseGroups: {
        type: Array,
        required: true,
    },
    domain: {
        type: Object,
        required: true,
    },
    recurrenceOptions: {
        type: Array,
        required: true,
    },
});

const priceExTax = computed(() => {
    priceExTaxCalculated.value = priceIncTax.value > 0 ? (priceIncTax.value / (1 + (props.domain.courseTaxRate / 100))).toFixed(2) : 0;
    return priceExTaxCalculated.value;
});

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});

</script>
