<template>
    <div>
        <CourseGroupGeneric />
        <ListCourseGroupCourses v-if="!newMode" />
        <floating-bar>
            <a href='/home' class="btn btn-secondary">{{ ucFirst(translate('generic.dashboard')) }}</a>
            <a href='/coursegroups' class="btn btn-secondary">{{ ucFirst(translate('generic.list')) }}</a>
            <button class="btn btn-primary" @click="saveCourseGroupData" :disabled="!isSaveable">
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </floating-bar>
    </div>
</template>

<script setup>
import { onMounted } from "vue";
import CourseGroupGeneric from "./CourseGroupGeneric.vue";
import ListCourseGroupCourses from "./ListCourseGroupCourses.vue";
import useCourseGroups from "../../composables/useCourseGroups";
import FloatingBar from "../Layout/FloatingBar.vue";
import useLang from "../../composables/useLang";

const { ucFirst, translate } = useLang();
const { courseGroupId, initCourseGroupData, isSaveable, newMode, saveCourseGroupData } = useCourseGroups();
const props = defineProps({
    courseGroupId: {
        type: Number,
        default: 0
    },
});

onMounted(() => {
    courseGroupId.value = props.courseGroupId;
    initCourseGroupData();
})
</script>

<style scoped>

</style>
