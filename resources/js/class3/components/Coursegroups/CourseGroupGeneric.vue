<template>
    <div>
        <Panel>
            <template #title>
                <h3>{{ ucFirst(translate("generic.coursegroup")) }}</h3>
            </template>
            <template #subtitle>
                <small v-html="ucFirst(translate('generic.explaincoursegroup'))"/>
            </template>
            <div class="row">
                <div class="col-6">
                    <div class="form-group">
                        <label for="name">{{ ucFirst(translate("generic.name")) }}</label>
                        <input type="text" class="form-control" id="name" v-model="name" required @keyup="dirty=true">
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <material-switch
                            switch-id="isTrialGroup"
                            :label-on="`${ucFirst(translate('generic.istrialgroup'))} ➞ ${translate('generic.yes')}`"
                            :label-off="`${ucFirst(translate('generic.istrialgroup'))} ➞ ${translate('generic.no')}`"
                            color="success"
                            v-model="isTrialGroup"
                            value-type="boolean"
                            @change="dirty=true"
                        >
                            <template v-if="isTrialGroup" #after>
                                <small class="ml-1">
                                    {{ ucFirst(translate("generic.explaintrialsettrue")) }}
                                </small>
                            </template>
                        </material-switch>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    <div class="form-group">
                        <label for="description">{{ ucFirst(translate("generic.webdescription")) }}</label>
<!--                        <ckeditor-->
<!--                            :editor="editor"-->
<!--                            :config="editorConfigSimple"-->
<!--                            v-model="webDescription"-->
<!--                            @input="dirty=true"-->
<!--                        />-->
                    </div>
                </div>
                <div class="col-6">&nbsp;</div>
            </div>
        </Panel>
    </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import Panel from '../Layout/Panel.vue';
import MaterialSwitch from "../Layout/MaterialSwitch.vue";
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import CKEditor from '@ckeditor/ckeditor5-vue';
import useConfigItems from '../../composables/useConfigItems';
import FloatingBar from "../Layout/FloatingBar.vue";
import useLang from "../../composables/useLang";
import useCourseGroups from "../../composables/useCourseGroups";

const { editorConfigSimple } = useConfigItems();
const { ucFirst, translate } = useLang();
const { name, isTrialGroup, webDescription } = useCourseGroups();

const dirty = ref(false);
// const editor = ClassicEditor;
// const ckeditor = CKEditor.component;

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});
</script>

<style>
.ck-editor__editable:not(.ck-editor__nested-editable) {
    min-height: 200px;
}
</style>
