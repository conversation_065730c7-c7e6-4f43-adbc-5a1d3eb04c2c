<template>
    <div>
        <show-course-group-list
            :course-groups="filteredCourseGroups"
            :title-small="ucFirst(translate('generic.explaincoursegroup')) +
                '&nbsp;' + ucFirst(translate('generic.groupscanonlybedeletedif'))"
            @getTutorsForCourseGroup="getTutorsForCourseGroup"
        />
        <show-course-group-list
            v-if="emptyCourseGroups.length > 0"
            :course-groups="emptyCourseGroups"
            :title-small="ucFirst(translate('generic.nocoupledcourses'))"
            @setCGIDToDelete="setCGIDToDelete"
            @getTutorsForCourseGroup="getTutorsForCourseGroup"
        />

        <!-- -->
        <are-you-sure
            modal-id="confirm-delete-cg"
            @confirmclicked="deleteCourseGroup"
        />

        <!-- pick tutors who are able to teach lessons in this course group -->
        <modal
            closetext="close"
            size="large"
            :popup-title="
                ucFirst(translate('generic.choosetutor')) + ' ' +
                translate('generic.for') + ' ' +
                activeCourseGroupName
            "
            modal-id="choosetutorpopup"
        >
            <table class="table table-sm">
                <thead>
                <tr>
                    <th rowspan="2">{{ ucFirst(translateChoice('generic.tutors', 1)) }}</th>
                    <th colspan="3" class="text-center">{{ ucFirst(translate('generic.agegroup')) }}</th>
                </tr>
                <tr>
                    <td class="text-center mx-2">{{ translateChoice('generic.children', 2) }}</td>
                    <td class="text-center mx-2">{{ translateChoice('generic.adolescents', 2) }}</td>
                    <td class="text-center mx-2">{{ translateChoice('generic.adults', 2) }}</td>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(tutor, index) in assocTutors"
                    :key="tutor.id"
                >
                    <td>
                        <input :id="'cb'+tutor.id"
                               type="checkbox"
                               @click.prevent="toggleTutorForCourseGroup($event, tutor.id)"
                               v-model="tutor.assoc"
                        >
                        <span>{{ tutor.name }}</span>
                    </td>
                    <td class="text-center">
                        <input
                            type="checkbox"
                            v-model="tutor.child"
                            @click="setTutorCheckbox($event, index, tutor.id, 'child')"
                            :id="'tutor_child_' + tutor.id"
                        >
                    </td>
                    <td class="text-center">
                        <input
                        type="checkbox"
                        v-model="tutor.adolescent"
                        @click="setTutorCheckbox($event, index, tutor.id, 'adolescent')"
                        :id="'tutor_adolescent_' + tutor.id"
                    >
                    </td>
                    <td class="text-center">
                        <input
                            type="checkbox"
                            v-model="tutor.adult"
                            @click="setTutorCheckbox($event, index, tutor.id, 'adult')"
                            :id="'tutor_adult_' + tutor.id"
                        >
                    </td>
                </tr>
                </tbody>
            </table>
        </modal>
    </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import ShowCourseGroupList from './ShowCourseGroupList.vue';
import Modal from '../Layout/Modal3';
import useLang from '../../composables/useLang';
import useNoty from '../../composables/useNoty';
import AreYouSure from '../Layout/AreYouSure';
import useBaseData from '../../composables/useBaseData';
import axios from 'axios';

const { ucFirst, translate, translateChoice } = useLang();
const { failNoty, successNoty } = useNoty();
const assocTutors = ref([]);
const activeCourseGroupId = ref(0);
const CGIDToDelete = ref(0);
const { allCourseGroups, emptyCourseGroups, filteredCourseGroups, initBaseData } = useBaseData();

const activeCourseGroupName = computed(() => {
    return allCourseGroups.value.find(cg => cg.id === activeCourseGroupId.value)?.name;
});

onMounted(async () => {
    await initBaseData({
        courseGroups: true
    });
});

const setCGIDToDelete = (cgid) => {
    CGIDToDelete.value = cgid;
};

const deleteCourseGroup = () => {
    console.log('DELETE ' + CGIDToDelete.value);
    axios.delete('/api/coursegroups/' + CGIDToDelete.value)
        .then(resp => {
            successNoty(ucFirst(translate('generic.coursegroupdeleted')));
            // re-retrieve new list course groups from backend
            updateCourseGroups();
        })
        .catch(error => {
            failNoty(translate('generic.error') + ': ' + error);
        });
};

/**
 * Gets the tutors of the requested course group including the associated age groups
 * @param courseGroupId
 */
const getTutorsForCourseGroup = (courseGroupId) => {
    axios.get(`/api/gettutorsforcoursegroup/${courseGroupId}`)
        .then(resp => {
            assocTutors.value = resp.data;
            activeCourseGroupId.value = courseGroupId;
        })
        .catch(error => {
            failNoty(translate('generic.error') + ': ' + error);
        });
};

/**
 *
 * @param event
 * @param tutorId
 */
const toggleTutorForCourseGroup = (event, tutorId) => {
    const toggleVal = event.target.checked;
    axios.put('/api/toggletutorforcoursegroup/', {
        coursegroupid: activeCourseGroupId.value,
        tutorid: tutorId,
        toggleval: toggleVal
    })
        .then(resp => {
            updateCourseGroups(); // update main screen
            getTutorsForCourseGroup(activeCourseGroupId.value);
        })
        .catch(error => {
            failNoty(translate('generic.error') + ': ' + error);
        });
};

/**
 * attach a tutor because one of 'child', 'adolescent' or 'adult' was clicked
 * on a not-attached tutor
 *
 * @param tutorId int
 * @param ageGroup string {'child', 'adolescent', 'adult'}
 */
const toggleTutorForCourseGroupOn = (tutorId, ageGroup) => {
    axios.put('/api/toggletutorforcourseandcheck/', {
        coursegroupid: activeCourseGroupId.value,
        tutorid: tutorId,
        agegroup: ageGroup
    })
        .then(resp => {
            updateCourseGroups();
            getTutorsForCourseGroup(activeCourseGroupId.value);
        })
        .catch(error => {
            failNoty(translate('generic.error') + ': ' + error);
            console.log(
                `Error saving tutor-toggle: ON for coursegroup ${activeCourseGroupId.value}, for agegroup: ${ageGroup}: ${error}`
            );
        });
};

/**
 * Handle click on age group checkboxes
 * @param event
 * @param index int
 * @param tutorId int
 * @param ageGroup string {'child', 'adolescent', 'adult'}
 */
const setTutorCheckbox = (event, index, tutorId, ageGroup) => {
    const toggleVal = event.target.checked;
    // if toggleVal = 'on' and the tutor is not yet associated, trigger toggleTutorForCourseGroup
    if (toggleVal && !(assocTutors.value[index].assoc)) {
        toggleTutorForCourseGroupOn(tutorId, ageGroup);
    } else if (assocTutors.value[index].assoc) {
        // if tutor already assoc, toggle the age group on of off
        axios.put('/api/toggleagegroupfortutor', {
            coursegroupid: activeCourseGroupId.value,
            tutorid: tutorId,
            toggleval: toggleVal,
            agegroup: ageGroup
        })
            .then(resp => {
                updateCourseGroups(); // possibly update main screen
                getTutorsForCourseGroup(activeCourseGroupId.value);
            })
            .catch(error => {
                failNoty(`Error saving tutor-toggle: ON for coursegroup ${activeCourseGroupId.value}, for agegroup: ${ageGroup}: ${error}`,
                    translate('generic.error') + ': ' + error);
            });
    }
};

const updateCourseGroups = async () => {
    await initBaseData({
        courseGroups: true
    }, true);
}

</script>

<style scoped>

</style>
