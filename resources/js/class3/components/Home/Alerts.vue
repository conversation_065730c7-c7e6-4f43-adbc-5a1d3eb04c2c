<template>
    <div class="card shadow mb-4 min-with-cards-3" data-testid="class-alerts">
        <!-- Card Header and Dropdown -->
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fa fa-exclamation-circle"></i> {{ucFirst(translateChoice('generic.alerts', 2))}}
            </h6>
            <a
                href="/report-alarms"
                v-tooltip="translate('generic.gotoalertpage')"
                class="btn btn-success"
            >
                <i class="fa fa-angle-right"></i>
            </a>
        </div>

        <!-- Card Body -->
        <div class="card-body">
            <div class="chart-area">
                <span v-if="busy">
                    <spinner-s-v-g/>
                </span>
                <template v-else>
                    <table class="table table-responsive" v-if="messages.length > 0">
                        <thead>
                        <tr>
                            <th>{{ ucFirst(translateChoice('generic.type', 1)) }}</th>
                            <th>{{ ucFirst(translateChoice('generic.messages', 1)) }}</th>
                            <th>&nbsp;</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(msg, index) in messages" :key="index">
                            <td>
                                <span v-bind:class="{'text-danger': msg.showas==='alert'}">
                                    {{ucFirst(translate('generic.' + msg.result))}}
                                </span>
                            </td>
                            <td>
                                <span v-bind:class="{'text-danger': msg.showas==='alert'}" v-html="msg.message"></span>
                            </td>
                            <td><a :href="msg.link" v-if="msg.link">{{ ucFirst(translateChoice('generic.links', 1)) }}</a></td>
                        </tr>
                        </tbody>
                    </table>
                    <span v-else-if="hasConnectionError" class="text-danger">
                        {{ ucFirst(translate('generic.connectionerror')) }}
                    </span>
                    <span v-else>
                        {{ ucFirst(translate('generic.noalerts')) }}
                    </span>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
import SpinnerSVG from '../Layout/SpinnerSvg.vue';
import axios from 'axios';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

export default {
    name: 'Alerts',
    components: { SpinnerSVG },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            busy: false,
            hasConnectionError: false,
            messages: []
        };
    },
    mounted () {
        // this takes a long time,
        // start it after the rest of the interface shows a result
        // it blocks Vue's nexttick() for some reason
        setTimeout(() => {
            this.getCurrentAlerts();
        }, 1000);
    },
    methods: {
        getCurrentAlerts () {
            this.busy = true;
            this.hasConnectionError = false;
            axios.get('/api/alertstatus')
                .then((response) => {
                    this.busy = false;
                    this.messages = response.data.error === undefined
                        ? []
                        : response.data.error;
                })
                .catch((error) => {
                    this.busy = false;
                    this.hasConnectionError = true;
                    this.failNoty(
                        ucFirst(translate('generic.errorretrievingalerts')),
                        ucFirst(translate('generic.error')) + `: ${error}`
                    );
                });
        }
    }
};
</script>

<style lang="scss" scoped>
    .point {
        cursor: pointer;
    }
    .dropdown-item {
        color: black;
    }
</style>
