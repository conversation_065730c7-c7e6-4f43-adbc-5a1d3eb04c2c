<template>
    <div class="card shadow mb-4 min-with-cards-3" data-testid="personal-timetable">
        <!-- Card Header - Dropdown -->
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-calendar"></i> {{ucFirst(translateChoice('generic.timetables',1))}}
                <small v-if="filterAppointments">{{translate('generic.for')}} {{tutorName}}</small>
                <small v-else>{{translate('generic.fortheschool')}}</small>
            </h6>
            <div class="dropdown">
                <button
                    @click="toggleShowDropDownContent"
                    class="btn btn-success btn-sm"
                >
                    <!-- fa-fw is fixed width -->
                    <i class="fas fa-ellipsis-v fa-sm fa-fw"></i>
                </button>
                <div v-if="showDropdownContent" class="dropdown-content-section">
                    <button class="btn btn-link dropdown-item" @click="setFilterAppointments(true)">
                        {{ucFirst(translate('generic.myappointments'))}}
                    </button>
                    <button class="btn btn-link dropdown-item" @click="setFilterAppointments(false)">
                        {{ucFirst(translate('generic.allappointments'))}}
                    </button>
                </div>
            </div>
        </div>
        <!-- Card Body -->
        <div class="card-body">
            <div class="chart-area">
                <div class="container">
                    <div class="row" v-for="(appointment, index) in filteredAppointments" :key="index">
                        <div class="col">
                            <span :class="appointment.color">
                                {{displayTime(appointment.datetime)}}-{{displayTime(appointment.enddatetime)}}: {{appointment.courseName}} ({{appointment.name}}-{{appointment.studentName}})
                            </span>
                        </div>
                    </div>
                    <div v-if="filteredAppointments.length === 0">
                        {{ucFirst(translate('generic.nothingscheduled'))}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, ref, watchEffect } from 'vue';
import axios from 'axios';
import useLang from '../../composables/useLang';
import useDateTime from "../../composables/useDateTime";
import useNoty from "../../composables/useNoty";

const { ucFirst, translate, translateChoice } = useLang();
const { displayTime, isFutureDateTime } = useDateTime();
const { failNoty } = useNoty();

const filterAppointments = ref(true);
const allAppointments = ref([]);
const now = ref(new Date());
const showDropdownContent = ref(false);

const props = defineProps({
    tutorId: {
        type: Number,
        required: true
    },
    tutorName: {
        type: String,
        required: true
    }
})

onMounted(() => {
    getEventsOfToday();
    setInterval(() => {
        now.value = new Date();
    }, 10000);
});

const getEventsOfToday = async () => {
    try {
        const response = await axios.get('/api/eventsoftoday');
        allAppointments.value = response.data.data;
    } catch (error) {
        failNoty(ucFirst(translate('generic.errorfetchingappointmentsoftoday'))
            + `: ${error}`);
    }
};

const toggleShowDropDownContent = () => {
    showDropdownContent.value = !showDropdownContent.value;
};

const setFilterAppointments = (value) => {
    filterAppointments.value = value;
    showDropdownContent.value = false; // Close dropdown after selection
};

const filteredAppointments = computed(() => {
    return filterAppointments.value
        ? allAppointments.value.filter(appointment => {
            return appointment.tutor_id === parseInt(props.tutorId) || appointment.tutor_id === 0
          })
        : allAppointments.value;
});

watchEffect(() => {
    filteredAppointments.value.forEach((appointment) => {
        const isEndDateFuture = isFutureDateTime(appointment.enddatetime);
        const isStartDateFuture = isFutureDateTime(appointment.datetime);

        if (!isEndDateFuture) {
            appointment.color = 'past';
        } else if (isStartDateFuture) {
            appointment.color = 'future';
        } else {
            appointment.color = 'current';
        }
    });
});
</script>

<style scoped lang="scss">
    @import '../../../../sass/tmpl3/variables';
    a:focus {
        outline: none;
    }
    .future {
        color: $classdark;
    }
    .past {
        color: $classgreen;
    }
    .current {
        color: $classred;
    }
    .dropdown-content-section {
        position: absolute;
        background-color: #f1f1f1;
        min-width: 160px;
        box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
        z-index: 1000;
    }
    .dropdown-item {
        color: $classdark;
        cursor: pointer;
        &:hover {
            background-color: $classlight;
            color: $classdarkblue;
        }
    }
</style>
