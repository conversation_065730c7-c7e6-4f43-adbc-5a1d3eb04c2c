<template>
    <!-- Content Row 1 -->
    <div class="card-deck">
        <personal-calendar
            :tutor-id="tutorId"
            :tutor-name="tutorName"
        />
        <alerts />
        <birthdays />
    </div>
    <!-- Content Row 2 -->
    <div class="card-deck">
        <search-students />
        <search-courses />
    </div>
</template>

<script setup>
import PersonalCalendar from "./PersonalCalendar.vue";
import Alerts from "./Alerts.vue";
import Birthdays from "./Birthdays.vue";
import SearchStudents from "../Students/SearchStudents.vue";
import SearchCourses from "../Courses/SearchCourses.vue";

defineProps({
    tutorId: {
        type: Number,
        required: true
    },
    tutorName: {
        type: String,
        required: true
    }
});
</script>

<style scoped>

</style>
