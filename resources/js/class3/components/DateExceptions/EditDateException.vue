<template>
    <modal :popup-title="popupTitle"
           :closetext="ucFirst(translate('generic.cancel'))"
           :modal-id="modalId"
           size="large"
    >
        <div class="form-group row">
            <label for="reason" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.subject')) }}</label>
            <div class="col-sm-4">
                <input class="form-control" id="reason" type="text" data-testid="reason-input" v-model="dateException.reason">
            </div>
            <label for="allDay" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.allday')) }}?</label>
            <div class="col-sm-4 mt-2">
                <material-switch
                    label-on=""
                    switch-id="allDay"
                    v-model="dateException.isWholeDay"
                >
                    <template v-slot:after>
                        <span class="ml-2">{{ isAllDayLabel }}</span>
                    </template>
                </material-switch>
            </div>
        </div>
        <!-- -->
        <div :class="[
                'form-group',
                'row',
                { 'warn-incomplete': daySpanIncomplete }
            ]"
        >
            <label for="from" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.from')) }}</label>
            <div class="col-sm-4">
                <VueDatepicker
                    :id="'from-' + modalId"
                    v-model="dateException.datetime_start"
                    v-bind="dpOptionsDateTime"
                    data-testid="from-input"
                />
            </div>
            <label for="until" class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.untilincluding')) }}
            </label>
            <div class="col-sm-4">
                <VueDatepicker
                    :id="'until-' + modalId"
                    v-model="dateException.datetime_end"
                    v-bind="dpOptionsDateTime"
                    data-testid="until-input"
                    position="right"
                />
            </div>
        </div>
        <!-- DATE & TIME SPAN -->
        <div class="form-group row">
            <label class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.timespan')) }}</label>
            <div class="col-sm-4">
                <input class="form-control static" :value="timespan" readonly>
            </div>
            <label class="col-sm-2 col-form-label reposition-up1">
                {{ ucFirst(translate('generic.booklocation')) }}
            </label>
            <div class="col-sm-4">
                <select class="form-control" v-model="dateException.location_id">
                    <option value="0">{{ ucFirst(translate('generic.none')) }}</option>
                    <option v-for="location in allLocations" :key="location.id" :value="location.id">
                        {{ location.name }}
                    </option>
                </select>
            </div>
        </div>
        <hr v-if="!dateException.plan_blocking">
        <div class="form-group row" v-if="!dateException.plan_blocking">
            <label class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.hexcolor')) }}
                <span v-tooltip="translate('generic.explaincalendarcolor')">
                    <i class="fa fa-question-circle text-secondary"></i>
                </span>
            </label>
            <div class="col-md-4">
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                        <input class="input-group-text form-control add-width add-cursor"
                               type="color" v-model="dateException.calendar_color">
                    </div>
                    <input type="text" class="form-control" :value="dateException.calendar_color" readonly>
                </div>
            </div>
            <label class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.detailurl')) }}
                <span v-tooltip="translate('generic.explaindetailurl')">
                    <i class="fa fa-question-circle text-secondary"></i>
                </span>
            </label>
            <div class="col-md-4">
                <div class="input-group mb-3">
                    <input class="form-control" v-model="dateException.detail_url">
                </div>
            </div>
        </div>
        <hr>
        <div class="form-group row">
            <div class="col">
                <label class="form-label">
                    {{ ucFirst(translate('generic.wordpresscode')) }}
                    <span v-tooltip="translate('generic.explainwordpresscode')">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </label>
                : <span class="text-muted">{{  dateException.id }}</span>
            </div>
        </div>
        <div class="form-group row mt-3">
            <label for="from" class="col-sm-4 col-form-label">
                {{ ucFirst(translate('generic.ignoreforalerts')) }}?
            </label>
            <div class="col-sm-2 mt-2">
                <material-switch
                    label-on=""
                    switch-id="ignoreForAlerts"
                    v-model="dateException.exclude_from_alerts"
                    color="danger"
                >
                    <template v-slot:after>
                        <span v-tooltip="translate('generic.explainignoreforalerts')" class="ml-2">
                            <i class="fa fa-question-circle text-secondary"></i>
                        </span>
                    </template>
                </material-switch>
            </div>
        </div>
        <hr>
        <div class="form-group row mt-3">
            <label for="from" class="col-sm-2 col-form-label">{{ ucFirst(translate('generic.wholeschool')) }}?</label>
            <div class="col-sm-4 mt-2">
                <material-switch
                    label-on=""
                    switch-id="shareWholeSchool"
                    v-model="wholeSchool"
                />
            </div>
            <template v-if="!wholeSchool">
                <div class="form-check col-sm-6 mt-2">
                    <div class="row extra-margin-left">
                        <div class="col-12" v-for="tutor in allTutors" :key="tutor.id">
                            <input
                                type="checkbox"
                                class="form-check-input selectableTutor"
                                :value="tutor.id"
                                v-model="selectedTutors"
                            >{{ tutor.name }}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mt-2">
                            <div class="btn-group" role="group" aria-label="select all">
                                <button class="btn btn-outline-success" @click="toggleSelectAllTutors(true)">
                                    Select all
                                </button>
                                <button class="btn btn-outline-success" @click="toggleSelectAllTutors(false)">
                                    Select none
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
        <hr>
        <div class="form-group row mt-3">
            <label for="from" class="col-sm-2 col-form-label">
                {{ ucFirst(translate('generic.isrecurringappointment')) }}?</label>
            <div class="col-sm-4 mt-2">
                <material-switch
                    label-on=""
                    switch-id="isRecurring"
                    v-model="isRecurring"
                />
            </div>
            <div v-if="isRecurring" class="col-sm-6 mt-2">
                <div class="form-group">
                    <label>{{ translate('generic.startingfrom') }}: {{ dtStartFormatted }}</label>
                    <span v-if="dateException.datetime_start === ''">
                        <em>
                            {{ translate('generic.fillstartdattimeabove') }}
                        </em>
                    </span>
                </div>
                <div class="form-group">
                    <label><strong>{{ ucFirst(translate('generic.repeat')) }}</strong></label><br>
                    <input type="radio" v-model="whichRecurrence" value="0">&nbsp;{{ translate('generic.everyday') }}<br>
                    <input type="radio" v-model="whichRecurrence" value="1">&nbsp;{{ translate('generic.everyweek') }}<br>
                    <input type="radio" v-model="whichRecurrence" value="2">&nbsp;{{ translate('generic.otherweek') }}<br>
                </div>
                <div class="form-group">
                    <label>{{ translate('generic.andendsafter') }} {{ translate('generic.date') }}:</label><br>
                    <small>{{ translate('generic.emptyisendofschoolyear') }}</small>
                    <VueDatepicker
                        v-model="recurringEndAfterDate"
                        v-bind="dpOptionsDate"
                    />
                </div>
            </div>
        </div>

        <template #popupmessage v-if="errorMessage !== ''">
            <span class="text-danger" v-html="errorMessage"></span>
        </template>
        <template #okbutton>
            <button type="button"
                    class="btn btn-primary"
                    data-dismiss="modal"
                    :disabled="!isSavable"
                    @click.prevent="saveDateException"
                    data-testid="date-exception-save-button"
            >
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import Modal from '../Layout/Modal3.vue';
import MaterialSwitch from '../Layout/MaterialSwitch.vue';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import moment from 'moment';
import { computed, onMounted, ref, watch } from 'vue';
import useNoty from '../../composables/useNoty';
import useLang from '../../composables/useLang';
import useBaseData from '../../composables/useBaseData';
import axios from 'axios';

const { successNoty, failNoty } = useNoty();
const { translate, translateChoice, ucFirst } = useLang();
const { allTutors, allLocations, initBaseData } = useBaseData();
const emits = defineEmits(['triggerUpdateDEList']);
const props = defineProps({
    dateExceptionId: {
        type: Number,
        required: true
    },
    modalId: {
        type: String,
        required: true
    },
    schoolyearId: {
        type: Number,
        required: true
    }
});

const busy = ref(false);
const dateException = ref({
    id: 0,
    reason: '',
    exclude_from_alerts: false,
    plan_blocking: true, // defaults to true!
    ignore_for_alerts: false,
    isWholeDay: false,
    calendar_color: '',
    datetime_start: '',
    datetime_end: '',
    schoolyear_id: props.schoolyearId,
    tutors: []
});
const errorMessage = ref('');
const lang = ref('');
const selectedTutors = ref([]);
const shareWholeSchool = ref(false);
const isRecurring = ref(false);
const whichRecurrence = ref(1);
const recurringEndAfterDate = ref('');
import useDatePicker from '../../composables/useDatePicker';

// Get date picker options from the composable
const { dpOptions: dpOptionsDate } = useDatePicker(true);
const { dpOptions: dpOptionsDateTime } = useDatePicker(false);

onMounted(async () => {
    await initBaseData({
        locations: true,
        tutors: true
    });
});

/**
 * (de-)select all tutors
 * the list of tutors will always be empty if we select all on or all off
 * the only difference is the 'shareWholeSchool' parameter
 * @param selectOn boolean
 */
const toggleSelectAllTutors = (selectOn = true) => {
    selectedTutors.value = [];
    wholeSchool.value = selectOn;
};
const saveDateException = async () => {
    // upsert...
    const method = dateException.value.id > 0 ? 'put' : 'post';
    // only if 'shareWholeSchool' is false do we look at the selected tutors
    // there maybe old data in there, the 'shareWholeSchool' parameter overrides this
    dateException.value.tutors = wholeSchool.value ? [] : selectedTutors.value;
    const data = {
        ...dateException.value,
        repeatAppointment: {
            recurrenceOption: isRecurring.value ? whichRecurrence.value : -1,
            repeatEndsAfterDate: recurringEndAfterDate.value
        }
    };
    try {
        const response = await axios({
            method,
            url: '/api/dateexception',
            data
        });
        successNoty(response.data.message, translate('generic.success'));
        emits('triggerUpdateDEList');
    } catch (error) {
        failNoty(error.message, translate('generic.error'));
    }
};

watch(() => props.dateExceptionId, async () => {
    console.log('update dateexception data');
    // new DE or update existing one?
    // existing needs to be retrieved through api call
    if (props.dateExceptionId > 0) {
        busy.value = true;
        try {
            const response = await axios.get(`/api/dateexceptions/${props.dateExceptionId}`);
            dateException.value = response.data.data;
        } catch (error) {
            failNoty(error, translate('generic.error'));
        } finally {
            busy.value = false;
        }
    } else {
        // reset data fields
        dateException.value = {
            id: 0,
            reason: '',
            exclude_from_alerts: false,
            plan_blocking: true, // defaults to true!
            ignore_for_alerts: false,
            calendar_color: '',
            isWholeDay: false,
            datetime_start: '',
            datetime_end: '',
            schoolyear_id: props.schoolyearId,
            tutors: []
        };
    }
    if (dateException.value.tutors.length === 0) {
        // no tutors selected and shareWholeSchool is true
        toggleSelectAllTutors(true);
    } else {
        wholeSchool.value = false;
        dateException.value.tutors.forEach((tutor) => {
            selectedTutors.value.push(tutor.id);
        });
    }
    // reset recurrence fields
    recurringEndAfterDate.value = '';
    isRecurring.value = false;
    whichRecurrence.value = 1;
});

/**
 * initialize with school year id of the parent component (through property)
 */
watch(() => props.schoolyearId, async () => {
    console.log('update schoolyear id');
    dateException.value.schoolyear_id = props.schoolyearId;
});

watch(() => dateException.value.isWholeDay, (newValue) => {
    if (newValue) {
        // whole day, make time of DE 00:00:00 - 23:59:59
        dateException.value.datetime_start = dateException.value.datetime_start.substring(0, 10) + ' 00:00:00';
        dateException.value.datetime_end = dateException.value.datetime_end.substring(0, 10) + ' 23:59:59';
    }
});

/**
 * Check if a date exception is theoretically savable
 * This does not check if the date exception is valid in the context of the school year
 * checks:
 * - is the end date after the start date
 * - is the end date in the future
 * - if the DE is recurring:
 * -- is the repeat end date after the end date of the DE
 * @returns {boolean}
 * @type {ComputedRef<unknown>}
 */
const isSavable = computed(() => {
    let hasError = false;
    let localErrorMessage = '';

    if (isRecurring.value) {
        if (
            dateException.value.datetime_end.length >= 10 &&
            moment(dateException.value.datetime_end).diff(moment(recurringEndAfterDate.value)) > 0
        ) {
            hasError = true;
            localErrorMessage = ucFirst(translate('generic.repeatendisbeforeappointmentend'));
        }
    }

    if (
        dateException.value.datetime_start.length >= 10 &&
        dateException.value.datetime_end.length >= 10 &&
        moment(dateException.value.datetime_start).diff(moment(dateException.value.datetime_end)) > 0
    ) {
        hasError = true;
        localErrorMessage = ucFirst(translate('generic.endisbeforestart'));
    }

    // end date should be a future date
    if (
        dateException.value.datetime_end.length >= 10 &&
        moment(dateException.value.datetime_end).diff(moment()) < 0
    ) {
        hasError = true;
        localErrorMessage = ucFirst(translate('generic.endisnotinfuture'));
    }

    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    errorMessage.value = localErrorMessage;

    return dateException.value.reason.length > 1 &&
        dateException.value.datetime_start.length >= 10 &&
        dateException.value.datetime_end.length >= 10 &&
        !hasError;
});

const isAllDayLabel = computed(() => {
    return dateException.value.isWholeDay
        ? ucFirst(translate('generic.yes'))
        : ucFirst(translate('generic.no'));
});

const popupTitle = computed(() => {
    return dateException.value.id > 0
        ? ucFirst(translate('generic.editdateexception'))
        : ucFirst(translate('generic.newdateexception'));
});

const dtStartFormatted = computed(() => {
    const dt = moment(dateException.value.datetime_start);
    return dateException.value.isWholeDay
        ? dt.format('DD-MM-YYYY')
        : dt.format('DD-MM-YYYY HH:mm');
});

/**
 * config to use for datetimepicker, depending on language and whole day setting
 * @returns config object
 */
const dpOptions = computed(() => {
    if (dateException.value.isWholeDay) {
        return lang.value === 'nl'
            ? dpOptionsDateNl
            : dpOptionsDateEn;
    } else {
        return lang.value === 'nl'
            ? dpOptionsDateTimeNl
            : dpOptionsDateTimeEn;
    }
});

const timespan = computed(() => {
    const start = moment(dateException.value.datetime_start);
    const end = moment(dateException.value.datetime_end);
    if (dateException.value.isWholeDay) {
        // always return a whole number of days
        // add 1 to compensate for 23:59:59 to count as a whole day
        const nrOfDays = end.diff(start, 'days') + 1;
        if (isNaN(nrOfDays)) return '';
        return nrOfDays + ' ' + translateChoice('generic.days', nrOfDays);
    } else {
        // returns a number of minutes
        const nrOfMinutes = end.diff(start, 'minutes');
        const nrOfHours = end.diff(start, 'hours');
        const H2M = 60 * nrOfHours;
        const minutesLeft = nrOfMinutes - H2M;
        // assemble the resulting string
        const timeArray = [];
        if (nrOfHours > 0) {
            timeArray.push(nrOfHours + ' ' + translateChoice('generic.hours', nrOfHours));
        }
        if (minutesLeft > 0) {
            timeArray.push(minutesLeft + ' ' + translateChoice('generic.minutes', minutesLeft));
        }
        return timeArray.join(' ' + translate('generic.and') + ' ');
    }
});

const daySpanIncomplete = computed(() => {
    return ((dateException.value.isWholeDay) && timespan.value === '');
});
</script>

<style scoped lang="scss">
@import "../../../../sass/tmpl3/_variables";

.static {
    background-color: white;
    color: black;
}

.warn-incomplete {
    background-color: $classred;
    color: white;
}

.extra-margin-left {
    margin-left: 1rem;
}

.reposition-up1 {
    top: -10px
}

.add-width {
    width: 3.5rem;
}

.add-cursor {
    cursor: pointer;
}
</style>
