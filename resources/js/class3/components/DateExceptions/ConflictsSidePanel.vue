<template>
    <side-panel :is-open="isOpen" @close="$emit('close')">
        <template #title>
            {{ dateException?.reason }} ({{ dateExceptionPeriod }})
            {{ ucFirst(translateChoice('generic.conflicts', 2)) }}
        </template>

        <div v-if="conflicts" class="conflicts-list">
            <div v-for="conflict in conflicts" :key="conflict.id" class="card mb-2">
                <div class="card-body">
                    <h6 class="card-title">
                        {{ conflict.course_name }},
                        <a :href="`/students/${conflict.student_id}/edit`">{{ conflict.student_name }}</a><br>
                        {{ displayDateTime(conflict.event_start) }}
                        <span v-if="conflict.flag_sticky" class="badge badge-success p-2">
                            <i class="fa fa-thumbtack"></i>
                        </span>
                    </h6>
                    <div class="card-text">
                        <div>
                            <a :href="`/planning/lesson?studentId=${conflict.student_id}&courseId=${conflict.course_id}`" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fa fa-external-link-alt mr-1"></i>
                                {{ ucFirst(translate('generic.opentimetable')) }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else class="text-center text-muted">
            {{ ucFirst(translate('generic.noconflicts')) }}
        </div>
    </side-panel>
</template>

<script setup>
import { computed } from 'vue';
import SidePanel from '../Layout/SidePanel.vue';
import useLang from '../../composables/useLang';
import useDateTime from "../../composables/useDateTime";

const { ucFirst, translate, translateChoice } = useLang();
const { displayDate, displayDateTime } = useDateTime();

const props = defineProps({
    isOpen: Boolean,
    conflicts: Array,
    dateException: Object
});

defineEmits(['close']);

const dateExceptionPeriod = computed(() => {
    return `${displayDate(props.dateException?.datetime_start)} - ${displayDate(props.dateException?.datetime_end)}`;
});
</script>

<style scoped>
.conflicts-list {
    max-height: calc(100vh - 100px);
    overflow-y: auto;
}
</style> 
