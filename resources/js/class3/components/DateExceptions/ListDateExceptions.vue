<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-edit"></i>
            {{ ucFirst(translateChoice('generic.dateexceptions', 2)) }}
            {{  isSidePanelOpen }}
        </template>
        <template v-slot:subtitle>
            <div class="input-group">
                <select class="form-control mr-2" v-model="chosenYearId">
                    <option
                            v-for="schoolyear in allSchoolYears"
                            :key="schoolyear.id"
                            :value="schoolyear.id"
                    >
                        {{ schoolyear.label }}
                    </option>
                </select>
                <select class="form-control mr-2" v-model="tutorId">
                    <option :value="0">{{ ucFirst(translate('generic.showall')) }}</option>
                    <option :value="-1">{{ ucFirst(translate('generic.wholeschool')) }}</option>
                    <option v-for="tutor in allTutors" :key="tutor.id" :value="tutor.id">{{ tutor.name }}</option>
                </select>
                <button
                    class="btn btn-success btn-sm"
                    @click="dateExceptionIdToEdit = 0"
                    data-toggle="modal"
                    data-target="#editdateexception"
                    data-testid="new-date-exception-button"
                >
                    {{ ucFirst(translate('generic.new')) }}
                </button>
            </div>
        </template>
        <table class="table">
            <thead>
            <tr>
                <th v-if="isAdmin">{{ ucFirst(translate('generic.functions')) }}</th>
                <th>
                    {{ ucFirst(translate('generic.color')) }}
                    <span v-tooltip="translate('generic.explaincalendarcolor')">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
                <th>{{ ucFirst(translate('generic.reason')) }}</th>
                <th>{{ ucFirst(translate('generic.concerns')) }}</th>
                <th>{{ ucFirst(translate('generic.from')) }}</th>
                <th>{{ ucFirst(translate('generic.untilincluding')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.roomlocation')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.detailurl')) }}</th>
                <th class="text-center">
                    {{ ucFirst(translateChoice('generic.alerts', 2)) }}
                    <span v-tooltip="translate('generic.explainignoreforalerts')">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
                <th class="text-center">
                    {{ ucFirst(translateChoice('generic.conflicts', 2)) }}
                    <span v-tooltip="translate('generic.explainConflictsDEEvents')">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="dateException in filteredDateExceptions" :key="dateException.id">
                <td v-if="isAdmin">
                    <button
                        class="btn btn-success btn-sm"
                        @click="dateExceptionIdToEdit = dateException.id"
                        data-toggle="modal"
                        data-target="#editdateexception"
                    >
                        <i class="fa fa-edit"></i>
                    </button>
                    <button
                        class="btn btn-danger btn-sm"
                        @click="dateExceptionToDelete = dateException.id"
                        data-toggle="modal"
                        data-target="#del_areyousure"
                    >
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td>
                    <span
                        v-if="!dateException.plan_blocking"
                        class="badge p-2"
                        :style="{ backgroundColor: dateException.calendar_color }"
                    >Cal</span>
                </td>
                <td>{{ dateException.reason }}</td>
                <td>
                    <template v-if="dateException.tutors.length !== 0">
                        <span v-for="tutor in dateException.tutors" class="mr-1" :key="tutor.id">
                            <span v-if="tutor.pivot.confirmed" v-tooltip="translate('generic.confirmed')">
                                <i class="fa fa-check-circle text-success"></i>
                            </span>
                            <span v-else v-tooltip="translate('generic.notconfirmed')">
                                <i class="fa fa-question-circle text-secondary"></i>
                            </span>
                            {{ tutor.name }}
                        </span>
                    </template>
                    <span v-else>{{ ucFirst(translate('generic.wholeschool')) }}</span>
                </td>
                <td>
                    {{
                        dateException.isWholeDay
                            ? displayDate(dateException.datetime_start)
                            : displayDateTime(dateException.datetime_start)
                    }}
                </td>
                <td>
                    {{
                        dateException.isWholeDay
                            ? displayDate(dateException.datetime_end)
                            : displayDateTime(dateException.datetime_end)
                    }}
                </td>
                <td class="text-center">
                    {{ dateException.location ? dateException.location.name : "-" }}
                </td>
                <td class="text-center">
                    <span v-if="dateException.detail_url">
                        <a :href="dateException.detail_url" target="_blank">
                            <i class="fa fa-external-link-alt"></i>
                        </a>
                    </span>
                    <span v-else>-</span>
                </td>
                <td v-if="dateException.exclude_from_alerts" class="text-center">
                    <strong class="text-danger">{{ translate('generic.no') }}</strong>
                </td>
                <td v-else class="text-center">
                    {{ translate('generic.yes') }}
                </td>
                <td class="text-center">
                    <span
                        class="badge p-2"
                        style="cursor: pointer; color:white; position: relative; z-index: 1041"
                        :style="{ backgroundColor: showConflictsForDateException(dateException.id).code }"
                        v-html="showConflictsForDateException(dateException.id).result"
                        @click="(e) => handleConflictClick(dateException.id, e)"
                    />
                </td>
            </tr>
            </tbody>
        </table>
        <!-- confirm delete course -->
        <are-you-sure
            :button-text="ucFirst(translate('generic.deletedateexception'))"
            @confirmclicked="deleteDateException"
            modal-id="del_areyousure"
        ></are-you-sure>
        <edit-date-exception
            modal-id="editdateexception"
            :date-exception-id="dateExceptionIdToEdit"
            :schoolyear-id=chosenYearId
            @triggerUpdateDEList="updateData"
        ></edit-date-exception>
        <conflicts-side-panel
            :is-open="isSidePanelOpen"
            :conflicts="selectedConflicts"
            :dateException="selectedDateException"
            @close="isSidePanelOpen = false"
        ></conflicts-side-panel>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import Panel from '../Layout/Panel.vue';
import AreYouSure from '../Layout/AreYouSure.vue';
import EditDateException from './EditDateException.vue';
import useBaseData from '../../composables/useBaseData';
import useNoty from '../../composables/useNoty';
import useLang from '../../composables/useLang';
import useDateTime from '../../composables/useDateTime';
import $ from 'jquery';
import axios from 'axios';
import ConflictsSidePanel from './ConflictsSidePanel.vue';

const { allDateExceptions, allTutors, allSchoolYears, initBaseData } = useBaseData();
const { failNoty, successNoty } = useNoty();
const { displayDate, displayDateTime } = useDateTime();
const { ucFirst, translate, translateChoice } = useLang();

const busy = ref(false);
const chosenYearId = ref(0);
const dateExceptionToDelete = ref(0);
const dateExceptionIdToEdit = ref(0);
const tutorId = ref(0);
const lessonConflicts = ref([]);
const selectedConflicts = ref([]);
const selectedDateException = ref(null);
const isSidePanelOpen = ref(false);

defineProps({
    isAdmin: Boolean
});

onMounted(async () => {
    updateData();
    // check if we should open a date exception for edit
    // => url contains valid date exception id, if link is clicked from calendar
    const params = (new URL(document.location)).searchParams;
    const deid = params.get('deid');
    if (deid != null) {
        // open date exception for edit
        dateExceptionIdToEdit.value = parseInt(deid);
        $('#editdateexception').modal();
    }
});

const deleteDateException = () => {
    axios.delete(`/api/dateexceptions/${dateExceptionToDelete.value}`)
        .then(response => {
            updateData();
            successNoty(
                ucFirst(translate('generic.deletesuccessful')),
                ucFirst(translate('generic.success'))
            );
        })
        .catch(err => {
            failNoty(
                err,
                ucFirst(translate('generic.error'))
            );
        });
};

/**
 * set the first received school year 'active' initially
 */
watch(allSchoolYears, (value, oldValue) => {
    chosenYearId.value = value.length > 0
        ? chosenYearId.value = value[0].id
        : 0;
});

/**
 * filter school year into dateExceptionsByYear by comparing with the currently chosen year id
 * filter that list on tutors, where 0 means all tutors (including entire school)
 * and -1 means only "entire school" exceptions.
 * if tutor id other than 0 or -1: show the date exceptions of the selected tutor.
 * @returns {*[]}
 */
const filteredDateExceptions = computed(() => {
    console.log("all dateExceptions", allDateExceptions.value);
    console.log("chosen year id", chosenYearId.value);
    const dateExceptionsByYear = allDateExceptions.value.filter(de => de.schoolyear_id === chosenYearId.value);
    console.log("Filtered by year", dateExceptionsByYear);
    if (tutorId.value === 0) return dateExceptionsByYear;
    if (tutorId.value === -1) return dateExceptionsByYear.filter(de => de.tutors.length === 0);
    
    console.log("Filtered", dateExceptionsByYear.filter(de => de.tutors.some(tutor => tutor.id === tutorId.value)));

    return dateExceptionsByYear.filter(de => de.tutors.some(tutor => tutor.id === tutorId.value));
});

watch(filteredDateExceptions, (value, oldValue) => {
    getLessonConflicts();
});

/**
 * get the lesson conflicts for all date exceptions from the server
 */
const getLessonConflicts = async () => {
    lessonConflicts.value = [];
    for (const de of filteredDateExceptions.value) {
        try {
            const response = await axios.get(`/api/dateexceptions/${de.id}/lessonconflicts`);
            lessonConflicts.value.push(response.data);
        } catch (error) {
            failNoty(
                error,
                ucFirst(translate('generic.error'))
            );
        }
    }
};

/**
 * show the number of conflicts for a date exception
 * if there are any sticky events, count them separately so we know if all conflicts have been resolved
 * @param dateExceptionId
 * @returns {{result: string, code: string}|{result: string, code: (string)}}
 */
const showConflictsForDateException = (dateExceptionId) => {
    // CLASS colors
    const colorTable = {
        green: '#4C9B5E',
        red: '#C75454'
    };
    const conflicts = lessonConflicts.value.find(lc => {
        return parseInt(lc.dateExceptionId) === parseInt(dateExceptionId)
    });
    if (!conflicts) return {code: colorTable.green, result: "0/0"};
    // if conflicts.conflicts is an object, turn it into an array
    // turns out this only happens when we have 1 conflict
    if (!Array.isArray(conflicts.conflicts)) {
        conflicts.conflicts = [conflicts.conflicts];
    }
    const numberOfEventsThatAreSticky = conflicts.conflicts.filter(e => e.flag_sticky).length;
    return {
        code: conflicts.conflicts.length > numberOfEventsThatAreSticky ? colorTable.red : colorTable.green,
        result: `${conflicts.conflicts.length}&nbsp;/&nbsp;${numberOfEventsThatAreSticky}`
    };
};

const handleConflictClick = (dateExceptionId, event) => {
    event.stopPropagation();
    const conflictData = lessonConflicts.value.find(lc => 
        parseInt(lc.dateExceptionId) === parseInt(dateExceptionId)
    );
    // somehow it works now. leave this code in case it pops up again
    // if (conflictData?.conflicts.length === 1) {
    //     // weird, but this looks different from other conflict arrays
    //     selectedConflicts.value = [conflictData?.conflicts[0]['1']];
    //     selectedDateException.value = conflictData?.dateException;
    //     isSidePanelOpen.value = true;
    //     return true;
    // }

    selectedConflicts.value = conflictData?.conflicts;
    selectedDateException.value = conflictData?.dateException;
    isSidePanelOpen.value = true;
};

const updateData = () => initBaseData({
    locations: true,
    tutors: true,
    dateExceptions: true,
    schoolYears: true
}, true);
</script>
