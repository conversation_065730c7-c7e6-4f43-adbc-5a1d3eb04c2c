<template>
    <div>
        <button class="btn btn-sm btn-primary mb-2"
                @click.prevent="noop"
                data-toggle="modal"
                :data-target="'#_' + registrationId">
            {{ buttonText }}
        </button>
        <app-modal :popup-title="popupTitle" :closetext="closeBtnText" size="extralarge"
                   :modalId="'_' + registrationId">

            <div class="row">
                <div class="col-3"><label>{{ ucFirst(translate('generic.courseregistrationfor')) }}:</label></div>
                <div class="col-9">
                    <span id="srCoursename">{{ courseName }}</span>
                </div>
            </div>
            <div class="row">
                <div class="col-3"><label>{{ ucFirst(translate('generic.startdate')) }}:</label></div>
                <div class="col-9"><span id="srStartdate">{{ startDate }}</span></div>
            </div>
            <div class="row">
                <div class="col-3"><label>{{ ucFirst(translate('generic.student')) }}:</label></div>
                <div class="col-9"><span>{{ studentName }}</span></div>
            </div>

            <hr/>
            <div class="row">
                <div class="col-3">
                    <label for="targetemail">
                        {{ ucFirst(translate('generic.sendrequesttoemail')) }}:
                    </label>
                </div>
                <div class="col-9">
                    <input name="targetemail" id="targetemail" class="form-control" v-model="targetEmail">
                </div>
            </div>
            <hr/>
            <div class="row">
                <div class="col-3">
                    {{ ucFirst(translate('generic.templates')) }}<br>
                    <em class="text-success">1: {{ ucFirst(translate('generic.pleasechoosea')) }}
                        {{ translate('generic.template') }}</em>
                </div>
                <div class="col-9">
                    <list-templates
                        for-target="b"
                        @templateChosen="copyTemplate"
                    ></list-templates>
                </div>
            </div>
            <hr v-if="showRawTemplate"/>
            <!-- default text -->
            <div v-if="showRawTemplate" class="row">
                <div class="col-9 offset-3">
                    <div ref="defaultText" class="panel panel-danger raw-template-text" id="defaultText"/>
                </div>
            </div>
            <hr/>
            <div class="row mb-2">
                <div class="col-9 offset-3 text-center">
                    <em class="text-success">{{ ucFirst(translate('generic.pleasemakecorrections')) }}</em>
                </div>
            </div>
            <div class="row">
                <div class="col-3">
                    <em class="text-success mr-1">2:</em>
                    <button :disabled="replacementText.length > 0 || !showRawTemplate"
                            class="btn btn-primary btn-sm"
                            @click.prevent="copyDefaultText">
                        {{ translate("generic.copy") }} {{ translate("generic.templatetext") }}
                    </button>
                    <div class="mt-3">
                        <em class="text-success">
                            {{ translate('generic.orjusttypetekst') }}
                            <i class="fa fa-arrow-right"></i>
                        </em>
                    </div>
                </div>
                <div class="col-9">
<!--                    <ckeditor-->
<!--                        :editor="editor"-->
<!--                        :config="editorConfig"-->
<!--                        v-model="replacementText"-->
<!--                    ></ckeditor>-->
                </div>
            </div>

            <input type="hidden" id="regid" :value="registrationId">
            <template v-slot:okbutton>
                <button
                    type="button"
                    class="btn btn-primary"
                    @click.prevent="sendSignRequest"
                    data-dismiss="modal"
                    :disabled="replacementText.length < 25"
                >
                    {{ translate('generic.sendsignrequest') }}
                </button>
            </template>
        </app-modal>
    </div>
</template>

<script>
import Modal from '../Layout/Modal3';
import ListTemplates from '../Email/ListTemplates';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import CKEditor from '@ckeditor/ckeditor5-vue';
import useConfigItems from '../../composables/useConfigItems';// fixme has vulnerability (uses quill)
import axios from 'axios';
import useLang from "../../composables/useLang";
import useUtils from "../../composables/useUtils";

const { translate, translateChoice, ucFirst } = useLang();
const { uniqueId } = useUtils();

// ckeditor: CKEditor.component

export default {
    name: 'SendRequest',
    components: { 'app-modal': Modal, ListTemplates },
    mounted () {
        this.targetEmail = this.email;
        this.getTemplateVariables();
    },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data: () => {
        return {
            targetEmail: '',
            replacementText: '',
            showRawTemplate: false,
            templateVariables: [],
            MAX_IMG_SIZE: 500, // constant for images in email
            DEFAULT_IMG_SIZE: 100, // constant for images in email
            //editor: ClassicEditor,
            editorConfig: useConfigItems().editorConfigSimple
        };
    },
    props: {
        email: String,
        salutation: String,
        startDate: {
            type: String,
            default: (date) => {
                return '2017-02-03';
            }
        },
        courseName: String,
        studentName: String,
        studentId: Number,
        studentFirstName: { type: String, required: true },
        popupTitle: String,
        closeBtnText: String,
        openButtonText: { type: String, default: '' },
        registrationId: { type: Number, required: true },
        schoolname: { type: String, required: true }
    },
    methods: {
        getTemplateVariables () {
            axios.get(`/api/templateVariables/${this.registrationId}`)
                .then(response => {
                    this.templateVariables = response.data;
                })
                .catch(error => {
                    this.failNoty(
                        translate('generic.couldnotgettemplatevariables') + ': ' + error,
                        translate('generic.error')
                    );
                });
        },
        sendSignRequest () {
            const data = {
                regid: this.registrationId,
                targetemail: this.targetEmail,
                replacementtext: this.finalText
            };

            axios.post('/api/registration/requestsignature', data)
                .then((response) => {
                    if (response.data.success != null) {
                        // ask parent to update this registrations status
                        this.$emit('updateRegistration', { registrationId: this.registrationId });
                        this.successNoty(translate('generic.emailsend'), translate('generic.success'));
                    } else {
                        // notify user that mail send has failed
                        this.failNoty(translate('generic.sendingemailfailed'), translate('generic.error'));
                    }
                })
                .catch((error) => {
                    this.failNoty();
                    this.failNoty(translate('generic.sendingemailfailed') + ': ' + error, translate('generic.error'));
                });
        },

        /**
         * get template content and paste in "default text" field
         * ready to be copied into rich text editor
         * @param e
         */
        copyTemplate (e) {
            if (e.selectedTemplateId > 0) {
                axios(`/api/mailTemplateContent/${e.selectedTemplateId}`)
                    .then(response => {
                        this.showRawTemplate = response.data.content.length > 0;
                        this.$nextTick(() => {
                            this.$refs.defaultText.innerHTML = response.data.content;
                        });
                    })
                    .catch(error => {
                        this.failNoty(
                            ucFirst(translate('generic.retrievingtemplatefailed')) + ': ' + error,
                            translate('generic.error')
                        );
                    });
            } else {
                this.$refs.defaultText.innerHTML = '';
                this.showRawTemplate = false;
            }
        },
        /**
         * copy the template text, replace the variables with their data counterpart
         * and paste it in the editor
         */
        copyDefaultText () {
            this.replacementText = this.$refs.defaultText.innerHTML;
            this.replaceVariablesInTemplateText();
            this.$nextTick(() => {
                this.showRawTemplate = false;
            });
        },
        /**
         * find variables in template text and replace them by the actual data
         */
        replaceVariablesInTemplateText () {
            // find variables in template text
            const variables = [];
            const regex = /%[a-z0-9|'=]+%/gm;
            let m;
            while ((m = regex.exec(this.replacementText)) !== null) {
                m.forEach((match, groupIndex) => {
                    variables.push(match);
                });
            }
            const distinctVariables = [...new Set(variables)];
            // the variables include the delimiter %....%
            let newText = this.replacementText;
            distinctVariables.forEach(variable => {
                if (this.templateVariables.variables.includes(variable)) {
                    // separate exceptions from streamline
                    // if a variable has parameters (schoollogo|width=150) the switch will fail,
                    // so we use the first part of the variable to switch upon, it should be unique and constant
                    const parts = variable.split('|');
                    const switcher = parts.length > 1 ? parts[0] + '%' : parts[0];
                    let imgWidth = this.DEFAULT_IMG_SIZE; let widthParamIndex; let imageLink;
                    switch (switcher) {
                    case '%classyactivationlink%':
                        // not implemented yet
                        newText = newText.replaceAll(
                            variable,
                            ucFirst(translate('generic.notavailableyet')) + `: ${variable}`
                        );
                        break;
                    case '%schoollogo%':
                        // valid parameter: width (not mandatory) - ignore other
                        // not found will return -1
                        widthParamIndex = parts.findIndex((elm) => elm.substr(0, 5) === 'width');
                        if (widthParamIndex !== -1) {
                            const paramWidth = parts[widthParamIndex].substr(0, -1);
                            const parameterParts = paramWidth.split('=');
                            if (
                                parameterParts.length > 1 &&
                                    Number.isInteger(parameterParts[1].trim()) &&
                                    parseInt(parameterParts[1].trim()) <= this.MAX_IMG_SIZE
                            ) {
                                imgWidth = parameterParts[1].trim();
                            }
                        }
                        imageLink = "<a href='#'><img src='" +
                                this.templateVariables.data[variable] +
                                "' width='" + imgWidth + "' alt='school logo'/></a>";
                        newText = newText.replaceAll(variable, imageLink);
                        break;
                    default:
                        newText = newText.replaceAll(variable, this.templateVariables.data[variable]);
                        break;
                    }
                } else {
                    // notify the user if we couldn't translate a variable
                    this.failNoty(
                        ucFirst(
                            translate('generic.couldntreplacevariableintemplate',
                                { theVariable: variable })
                        ),
                        translate('generic.error')
                    );
                }
            });
            this.replacementText = newText;
        }
    },
    computed: {
        modalId () {
            return '_' + uniqueId();
        },
        buttonText () {
            return this.openButtonText === ''
                ? ucFirst(translate('generic.requestsignatureemail'))
                : this.openButtonText;
        },
        finalText () {
            return this.replacementText === '' ? this.$refs.defaultText.innerHTML : this.replacementText;
        }
    }
};

</script>

<style scoped>
#defaultText {
    border: solid 1px black;
    color: #6A6E8F;
}

.raw-template-text {
    padding: .5em;
    min-height: 3rem;
}
</style>
