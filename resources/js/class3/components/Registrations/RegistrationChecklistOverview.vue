<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-edit"></i>
            {{ ucFirst(translate('generic.overviewregistrationschecklists')) }}
        </template>
        <template v-slot:subtitle>
            <button class="btn btn-sm btn-success" @click="switchSortField">
                <i class="fa fa-sort"></i>
                {{ sortByLabel }}
            </button>
        </template>
        <table class="table table-striped table-sm">
            <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.name')) }}</th>
                <th>{{ ucFirst(translate('generic.checklist')) }}</th>
                <th>{{ ucFirst(translate('generic.status')) }}</th>
            </tr>
            </thead>
            <tbody v-for="registration in regsNotCompleted" :key="registration.checklistid">
                <tr>
                    <td>
                        <a :href="`/students/${registration.student_id}/edit`">
                            {{ registration.name }} {{ registration.city }}
                            ({{ registration.course }})
                        </a>
                    </td>
                    <td>{{ registration.checklistname }}</td>
                    <td>
                        <a class="btn btn-danger no_underline no-margin-bottom"
                           :href="`/registrationedit/${registration.id}`">
                            <i class="glyphicon glyphicon-check"></i>
                            {{ ucFirst(translate('generic.checklistincomplete')) }}
                        </a>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <span class="text-sm"><strong>TODO:</strong>&nbsp;{{ registration.incompleteItems.join(', ') }}</span>
                    </td>
                </tr>
            </tbody>
            <tbody>
            <tr v-for="registration in regsCompleted" :key="registration.checklistid">
                <td>
                    <a :href="`/students/${registration.student_id}/edit`">
                        {{ registration.name }} {{ registration.city }}
                    </a>
                </td>
                <td>{{ registration.checklistname }}</td>
                <td>
                    <div class="alert alert-success alert-small">
                        <i class="glyphicon glyphicon-check"></i>
                        {{ ucFirst(translate('generic.checklistcompleted')) }}
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </panel>

</template>

<script>
import Panel from '../Layout/Panel';
import axios from 'axios';
import useLang from '../../composables/useLang';
const { ucFirst, translate } = useLang();

export default {
    name: 'RegistrationChecklistOverview',
    components: { Panel },
    setup () {
        return {
            ucFirst,
            translate
        };
    },
    data () {
        return {
            busy: false,
            registrations: [],
            filterWord: '',
            sortField: 'name'
        };
    },
    mounted () {
        this.getData();
    },
    methods: {
        getData () {
            this.busy = true;
            axios.get('/api/registrationswithchecklists')
                .then(response => {
                    this.registrations = response.data;
                })
                .catch(error => console.error(error))
                .finally(this.busy = false);
        },

        /**
         * sort the array of object with one of these compare functions
         * see computed regsNotCompleted and regsCompleted
         * @param a
         * @param b
         */
        sortRegsName (a, b) {
            return a.name < b.name
                ? -1
                : a.name > b.name
                    ? 1
                    : 0;
        },
        sortRegsList (a, b) {
            return a.checklistname < b.checklistname
                ? -1
                : a.checklistname > b.checklistname
                    ? 1
                    : 0;
        },
        /**
         * switch sort by which field
         */
        switchSortField () {
            this.sortField = this.sortField === 'name' ? 'list' : 'name';
        }
    },
    computed: {
        sortByLabel () {
            return this.sortField === 'name'
                ? ucFirst(translate('generic.sortbylist'))
                : ucFirst(translate('generic.sortbyname'));
        },
        regsNotCompleted () {
            const filtered = this.registrations.filter(reg => !reg.complete);
            return this.sortField === 'name'
                ? filtered.sort(this.sortRegsName)
                : filtered.sort(this.sortRegsList);
        },
        regsCompleted () {
            const filtered = this.registrations.filter(reg => reg.complete);
            return this.sortField === 'name'
                ? filtered.sort(this.sortRegsName)
                : filtered.sort(this.sortRegsList);
        }
    }
};
</script>

<style scoped>
.text-sm {
    font-size: smaller;
}
</style>
