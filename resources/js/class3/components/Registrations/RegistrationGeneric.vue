<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-edit"></i>
            {{ ucFirst(translate('generic.registrationdata')) }}
        </template>
        <template v-if="registration">
            <table class="table">
                <tbody>
                <tr>
                    <th>
                        {{ ucFirst(translate('generic.name')) }}
                    </th>
                    <td colspan="3">
                        <a :href="'/students/' + registration.student.id + '/edit'">
                            {{ registration.student.name }} ({{ registration.student.age }})
                        </a>
                    </td>
                </tr>

                <tr>
                    <th>{{ ucFirst(translate('generic.course')) }}</th>
                    <td colspan="3">
                        <a :href="'/courses/' + registration.course.id + '/edit'">
                            <span v-if="registration.course.is_trial_course">{{ ucFirst(translate('generic.triallesson')) }}:</span>
                            {{ registration.course.name }} - {{ registration.course.recurrenceoption.description }}
                        </a>
                    </td>
                </tr>
                <tr>
                    <th :class="{'display-muted': overridePrice}">
                        {{ ucFirst(translate('generic.priceExTax')) }}
                    </th>
                    <td :class="{'display-muted': overridePrice}">
                        {{ priceExTax }} {{ translate('generic.per') }} {{ translate('priceinterval.' + registration.course.price_is_per) }}
                    </td>
                    <td colspan="2">
                        <div class="row">
                            <div class="col-3 pt-1">
                                <material-switch
                                    switch-id="override-price"
                                    :label-on="''"
                                    v-model="overridePrice"
                                />
                            </div>
                            <div class="col-9">{{translate('generic.overrideprice')}}</div>
                        </div>
                    </td>
                </tr>
                <tr v-if="overridePrice">
                    <th>{{ ucFirst(translate('generic.priceExTax')) }}</th>
                    <td>
                        <input
                            class="form-control"
                            type="text"
                            v-model="registration.incidental_price_ex_tax"
                        >
                    </td>
                    <td>
                        {{ translate('generic.per') }} {{ translate('priceinterval.' + registration.course.price_is_per) }}
                    </td>
                    <td>
                        <button
                            class="btn btn-success"
                            :disabled="incidentalPriceInvalid"
                            @click.prevent="saveIncidentalPrice"
                        >{{ translate('generic.save') }}</button>
                    </td>
                </tr>
                <tr>
                    <th :class="{'display-muted': overrideTaxRate}">
                        {{ ucFirst(translate('generic.taxrate')) }}
                    </th>
                    <td v-if="registration.student.isAdult" :class="{'display-muted': overrideTaxRate}">
                        {{ registration.domain.course_tax_rate }}% ({{ translate('generic.adult') }})
                    </td>
                    <td v-else :class="{'display-muted': overrideTaxRate}">
                        0% ({{ translate('generic.not') }} {{ translate('generic.adult') }})
                    </td>
                    <td colspan="2">
                        <div class="row">
                            <div class="col-3 pt-1">
                                <material-switch
                                    switch-id="override-tax-rate"
                                    :label-on="''"
                                    v-model="overrideTaxRate"
                                />
                            </div>
                            <div class="col-9">{{translate('generic.overridetaxrate')}}</div>
                        </div>
                    </td>
                </tr>
                <tr v-if="overrideTaxRate">
                    <th>
                        {{ ucFirst(translate('generic.taxrate')) }}
                    </th>
                    <td>
                        <input
                            class="form-control"
                            type="number" step="0.1"
                            v-model="registration.incidental_tax_rate"
                        >
                    </td>
                    <td>&nbsp;</td>
                    <td>
                        <button
                            class="btn btn-success"
                            :disabled="incidentalPriceInvalid"
                            @click.prevent="saveIncidentalTaxRate"
                        >{{ translate('generic.save') }}</button>
                    </td>

                </tr>
                <tr>
                    <th>{{ ucFirst(translate('generic.registrationdate')) }}</th>
                    <td colspan="3">{{ startDate }}</td>
                </tr>
                <tr  v-if="!endDateUnsetOrFuture">
                    <th>{{ ucFirst(translate('generic.unregisterdate')) }}</th>
                    <td colspan="3">{{ endDate }}</td>
                </tr>
                <tr>
                    <th>{{ ucFirst(translate('generic.signstatus')) }}</th>
                    <td colspan="3">

                        <div v-if="registration.sign_request_send !== 1 && registration.signed !== 1"
                             class="row"
                        >
                            <div class="col-6">
                                {{ ucFirst(translate('generic.notsignednorequestsent')) }}
                            </div>
                            <div class="col-6">
                                <send-request
                                    v-if="registration.signed !== 1"
                                    :email="registration.finEmail"
                                    :salutation="registration.finSalutation"
                                    :start-date="startDate"
                                    :course-name="registration.course.name"
                                    :student-name="registration.student.name"
                                    :student-first-name="registration.student.firstname"
                                    :close-btn-text="ucFirst(translate('generic.close'))"
                                    :popup-title="ucFirst(translate('generic.sendsignrequest'))"
                                    :registration-id="registration.id"
                                    :schoolname="registration.domain.name"
                                    @updateRegistration="getRegistration"
                                >
                                </send-request>
                            </div>
                        </div>

                        <div v-else-if="registration.sign_request_send === 1 && registration.signed === null"
                             class="row"
                        >
                            <div class="col-4">
                                {{ ucFirst(translate('generic.notsignedbutrequestsent')) }}
                            </div>
                            <div class="col-1 pt-1">
                                <preview-sign-request
                                    :registration="registration"
                                    :student="registration.student"
                                />
                            </div>
                            <div class="col-7">
                                <send-request
                                    v-if="registration.signed !== 1"
                                    :email="registration.finEmail"
                                    :salutation="registration.finSalutation"
                                    :start-date="startDate"
                                    :course-name="registration.course.name"
                                    :student-name="registration.student.name"
                                    :student-first-name="registration.student.firstname"
                                    :close-btn-text="ucFirst(translate('generic.close'))"
                                    :popup-title="ucFirst(translate('generic.sendsignrequest'))"
                                    :open-button-text="ucFirst(translate('generic.resendsignrequest'))"
                                    :registration-id="registration.id"
                                    :schoolname="registration.domain.name"
                                >
                                </send-request>
                                <label class="signRequestLabelAfter alert alert-success"
                                       :data-regid="registration.id">
                                    {{ translate('generic.signrequestsend') }}
                                </label>
                            </div>
                        </div>

                        <div v-else class="row">
                            <div class="col-1 pt-1">
                                <preview-sign-request
                                    :registration="registration"
                                    :student="registration.student"
                                />
                            </div>
                            <div class="col-11">
                                {{ ucFirst(translate('generic.signed')) }}
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>

        </template>
    </panel>
</template>

<script>
import Panel from '../Layout/Panel';
import SendRequest from './SendRequest';
import PreviewSignRequest from './PreviewSignRequest';
import MaterialSwitch from '../Layout/MaterialSwitch';
import useLang from '../../composables/useLang';
import axios from 'axios';
import moment from 'moment';

const { translate, ucFirst } = useLang();

export default {
    name: 'RegistrationGeneric',
    components: { MaterialSwitch, Panel, PreviewSignRequest, SendRequest },
    mounted () {
        this.getRegistration();
    },
    setup () {
        return {
            translate,
            ucFirst
        };
    },
    data () {
        return {
            registration: null,
            busy: false,
            overridePrice: false,
            overrideTaxRate: false
        };
    },
    props: {
        registrationId: {
            type: Number,
            required: true
        }
    },
    methods: {
        getRegistration () {
            this.busy = true;
            axios.get(`/api/registration/${this.registrationId}`)
                .then(response => {
                    this.registration = response.data.data;
                    this.overridePrice = this.registration.incidental_price_ex_tax != null &&
                        this.registration.incidental_price_ex_tax !== '';
                    this.overrideTaxRate = this.registration.incidental_tax_rate != null &&
                        this.registration.incidental_tax_rate !== '';
                })
                .catch(error => {
                    this.failNoty(
                        `${ucFirst(translate('generic.failedtogetdataforregistration'))} ${this.registrationId}: ${error}`,
                        'error'
                    );
                })
                .finally(this.busy = false);
        },
        saveIncidentalPrice () {
            const data = {
                regId: this.registrationId,
                price: this.registration.incidental_price_ex_tax
            };
            axios.put('/api/price_override', data)
                .then(response => {
                    this.successNoty(ucFirst(translate('generic.pricesavedsuccessfull')));
                    if (this.registration.incidental_price_ex_tax == null || this.registration.incidental_price_ex_tax === '') {
                        this.overridePrice = false;
                    }
                })
                .catch(error => {
                    this.failNoty(ucFirst(translate('generic.couldnotsavenewprice')) + ': ' + error);
                });
        },
        saveIncidentalTaxRate () {
            const data = {
                regId: this.registrationId,
                taxrate: this.registration.incidental_tax_rate
            };
            axios.put('/api/tax_rate_override', data)
                .then(response => {
                    this.successNoty(ucFirst(translate('generic.taxratesavedsuccessfull')));
                    if (this.registration.incidental_tax_rate == null || this.registration.incidental_tax_rate === '') {
                        this.overrideTaxRate = false;
                    }
                })
                .catch(error => {
                    this.failNoty(ucFirst(translate('generic.couldnotsavenewtaxrate')) + ': ' + error);
                });
        }
    },
    computed: {
        incidentalPriceInvalid () {
            const regex = /^\d{1,6}((,|.)\d{1,2})?$/gm;
            return this.registration.incidental_price_ex_tax != null &&
                this.registration.incidental_price_ex_tax !== '' &&
                !this.registration.incidental_price_ex_tax.match(regex);
        },
        priceExTax () {
            // assigned but ber used? ms 20240308
            // const multiplicationFactor =
            //     this.registration.student.isAdult &&
            //     (this.registration.incidental_tax_rate != null && this.registration.incidental_tax_rate !== '')
            //         ? (this.registration.incidental_tax_rate / 100) + 1
            //         : 1;
            const price = this.registration.student.isAdult
                ? this.registration.course.price_ex_tax
                : this.registration.course.price_ex_tax_sub_adult;
            const formatter = new Intl.NumberFormat('nl-NL', {
                style: 'currency',
                currency: 'EUR'
            });
            return formatter.format(price);
        },
        startDate () {
            const dateFormat = (this.translate('generic.language') === 'nl') ? 'DD-MM-YYYY' : 'YYYY-MM-DD';
            return moment(this.registration.start_date, 'YYYY-MM-DD').format(dateFormat);
        },
        endDate () {
            const dateFormat = (this.translate('generic.language') === 'nl') ? 'DD-MM-YYYY' : 'YYYY-MM-DD';
            return moment(this.registration.end_date, 'YYYY-MM-DD').format(dateFormat);
        },
        /**
         * check the end date. If it is unset or set in the future, the registration is still 'active'.
         * (provided that the start date is set in the past which is an assumption)
         * @returns {boolean}
         */
        endDateUnsetOrFuture () {
            if (this.registration.end_date !== null && this.registration.end_date !== '') {
                // check if it's maybe a future date
                const now = moment();
                const diff = now.diff(moment(this.registration.end_date, 'YYYY-MM-DD'), 'minute');
                // when < 0 it's in the future
                return (diff < 0);
            } else {
                // end date unset
                return true;
            }
        }
    },
    watch: {
        /**
         * todo: if "override price" is set to false, the current student signature must be declared invalid
         * todo: currently this is way too easy
         * todo: there should at least be a popup to confirm. But avoid too much bureaucracy
         * todo: also keep in mind that the response of saveIncidentalPrice may also trigger this watcher
         * todo: for about the same reason
         */
        overridePrice () {
            // update database if we previously had an override on price and is now switched off
            if (
                (!this.overridePrice) &&
                (this.registration.incidental_price_ex_tax != null) &&
                (this.registration.incidental_price_ex_tax !== '')
            ) {
                axios.put('/api/reset_incidental_price', { regId: this.registrationId })
                    .then(response => {
                        this.successNoty(ucFirst(translate('generic.priceresetsuccessfull')));
                    })
                    .catch(error => {
                        this.failNoty(ucFirst(translate('generic.couldnotresetprice')) + ': ' + error);
                    });
            }
        },
        overrideTaxRate () {
            console.log('watch!');
            // update database if we previously had an override on tax rate and is now switched off
            if (
                (!this.overrideTaxRate) &&
                (this.registration.incidental_tax_rate != null) &&
                (this.registration.incidental_tax_rate !== '')
            ) {
                axios.put('/api/reset_incidental_tax_rate', { regId: this.registrationId })
                    .then(response => {
                        this.successNoty(ucFirst(translate('generic.taxrateresetsuccessfull')));
                    })
                    .catch(error => {
                        this.failNoty(ucFirst(translate('generic.couldnotresettaxrate')) + ': ' + error);
                    });
            }
        }
    }
};
</script>

<style scoped>
.signRequestLabelAfter {
    display: none;
}

label {
    font-weight: bolder;
}
.display-muted {
    color: lightgrey;
}
</style>
