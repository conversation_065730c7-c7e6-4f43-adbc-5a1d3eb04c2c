<template>
    <div class="row mb-3">
        <div class="col-1">
            <span
                v-if="registration.tutoringInStudentGroup && registration.studentgroupname !== ''"
                tabindex="0"
                data-toggle="popover"
                data-trigger="focus"
                :data-content="getStudentgroupPopoverContent()"
                :title="registration.studentgroupname"
                v-tooltip="
                    ucFirst(translate('generic.groupcourseextend')) +
                    (registration.studentgroupname === ''
                    ? ''
                    : '. ' + ucFirst(translateChoice('generic.studentgroups', 1)) + ': ' + registration.studentgroupname)
                "
                class="mr-05"
            >
                <i class="fas fa-users"></i>
            </span>
            <span v-else
                  class="mr-2"
                  v-tooltip="ucFirst(translate('generic.individualcourseextend'))">
                <i class="fas fa-user"></i>
            </span>
            <span v-if="!isActive">
                <button class="btn btn-sm btn-outline-secondary"
                        type="button"
                        data-toggle="modal"
                        :data-target="'#delRegistrationPopup' + registration.pivot.id"
                        v-tooltip="ucFirst(translate('generic.delete'))">
                    <i class="fas fa-trash"></i>
                </button>
            </span>

            <span v-if="isActive">
                <a :class="'btn btn-sm btn-outline-secondary ' + classForChecklist"
                        v-tooltip="labelForChecklist"
                        v-if="userisadmin"
                        :href="'/registrationedit/' + registration.pivot.id">
                    <i class="fas fa-edit"></i>
                </a>
                <!-- Tutor should be able to check tutoring schedule, so no v-if -->
                <a :href="'/planning/lesson?studentId=' + registration.pivot.student_id + '&courseId=' + registration.pivot.course_id"
                   class="btn btn-sm btn-outline-secondary"
                   v-tooltip="ucFirst(translate('generic.opentimetable'))"
                >
                    <i class="fas fa-calendar"></i>
                </a>
            </span>
            <span
                v-if="isGroupable && isActive"
                tabindex="0"
                data-toggle="modal"
                :data-target="'#add-to-studentgroup_' + registration.id"
                class="btn btn-sm btn-outline-primary mb-2"
                v-tooltip="ucFirst(translate('generic.addtostudentgroup'))"
            >
                <i class="fas fa-plus"></i>
                <i class="fas fa-users"></i>
            </span>
        </div>

        <!-- # Appointments -->
        <div class="col-2" v-if="isActive">
            <div class="row">
                <div class="col-6">
                    {{ eventsSummary.future }} / {{ eventsSummary['total-occuring']}} ({{ eventsSummary['total-events'] }})
                    <span v-tooltip="translate('generic.explainnrofevents')"><i class="fa fa-info-circle"></i></span>
                </div>
                <div class="col-6">
                    <span
                        v-tooltip="ucFirst(translate('generic.absentwithoutnotification'))"
                        class="badge badge-danger mr-1"
                    >
                        {{ registration?.attendance?.absentNoShow }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.absentwithnotification'))"
                        class="badge badge-warning mr-1"
                    >
                        {{ registration?.attendance?.absentWithNotification }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.absentnotificationtoolate'))"
                        class="badge bg-blue mr-1 text-white"
                    >
                        {{ registration?.attendance?.absentWithNotificationTooLate }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.present'))"
                        class="badge badge-success mr-1"
                    >
                        {{ registration?.attendance?.present }}
                    </span>
                    <span
                        v-tooltip="ucFirst(translate('generic.attendance')) + ' ' + translate('generic.unknown')"
                        class="badge badge-secondary mr-1"
                    >
                        {{ registration?.attendance?.unknown }}
                    </span>
                </div>
            </div>
        </div>
        <div class="col-2" v-else>&nbsp;</div>

        <div class="col-2">
            <VueDatepicker
                :name="'startdate[' + registration.pivot.id + ']'"
                class="adjust-inputfield"
                v-bind="dpOptionsDate"
                @update:model-value="dirty=true"
                v-model="startDate"
                autocomplete="off"
                required />
        </div>

        <!-- End date-->
        <div class="col-2">
            <VueDatepicker
                    :name="'enddate[' + registration.pivot.id + ']'"
                    class="adjust-inputfield"
                    v-bind="dpOptionsDateWithClear"
                    v-model="endDate"
                    @update:model-value="dirty=true;enddateChanged=true"
                    autocomplete="off">
            </VueDatepicker>
        </div>

        <!-- optional: signed status -->
        <div class="col-2" v-if="isActive && !isTrialCourse && !isNonIteratingCourse">
            <div class="mb-3">
                <template v-if="signStatus === 'open'">
                    <!-- send sign request component -->
                    <send-request
                            :email="studentEmail"
                            :salutation="studentSalutation"
                            :startDate="startDate"
                            :courseName="registration.name"
                            :studentName="student.name"
                            :student-id="student.id"
                            :studentFirstName="student.firstname"
                            :popupTitle="ucFirst(translate('generic.sendsignrequest'))"
                            :closeBtnText="ucFirst(translate('generic.close'))"
                            :registrationId="registration.pivot.id"
                            :schoolname="domain.name"
                            @updateRegistration="refreshMyData"
                    ></send-request>
                </template>
                <template v-else>
                    <preview-sign-request
                            :registration="registration"
                            :student="student"
                    />
                    <span v-if="signStatus === 'requested'" class="push-higher">
                        {{ucFirst(translate('generic.signrequestsend'))}}
                    </span>
                    <span v-else class="push-higher">
                        {{ucFirst(translate('generic.signed'))}}
                    </span>
                </template>
            </div>
        </div>
        <div v-else class="col-2">&nbsp;</div>

        <!-- Course name and recurrence -->
        <div class="col-2">
            <a
                v-tooltip="ucFirst(translate('generic.clicktoopencourseeditpage'))"
                :href="`/courses/${registration.id}/edit`"
            >
                {{registration.name}},
                {{ getGroupSizeLabel(registration.group_size_min, registration.group_size_max) }},
                {{registration.recurrenceoption.description}}
            </a>
            <div v-if="dirty" class="alert alert-warning">
                {{ucFirst(translate('generic.pleasesavechanges'))}}
                <div v-if="enddateChanged">
                    {{ ucFirst(translate('generic.futureappointmentswillbedeleted')) }}
                </div>
            </div>
        </div>

        <modal v-if="!isActive"
               :popup-title  = "ucFirst(translate('generic.areyousure'))"
               :closetext    = "ucFirst(translate('generic.close'))"
               :modal-id     = "'delRegistrationPopup' + registration.pivot.id">
            <p>
                {{ucFirst(translate('generic.thiscannotbeundone'))}}
            </p>
            <p>
                {{translate('generic.explainnotthesameasenddate')}}
            </p>
            <template v-slot:okbutton>
              <button type="button"
                    class="btn btn-danger"
                    data-dismiss="modal"
                    @click.prevent="deleteRegistration(registration.pivot.id)">
                {{ ucFirst(translate('generic.deletethisregistration')) }}
              </button>
            </template>
        </modal>
        <modal
            v-if="isGroupable"
            :popup-title = "ucFirst(translate('generic.possiblestudentgroups'))"
            :closetext   = "ucFirst(translate('generic.close'))"
            :modal-id    = "'add-to-studentgroup_' + registration.id"
        >
            <h3>{{ ucFirst(translate('generic.possiblestudentgroups')) }}</h3>
            <ul class="list-group">
                <li
                    v-for="group in potentialGroupsForCourse"
                    :key="group.id"
                    class="list-group-item list-group-item-action point"
                    @click.prevent="addToGroup(group.id)"
                    data-dismiss="modal"
                >
                    {{ group.lastname }}
                </li>
            </ul>
        </modal>
    </div>

</template>

<script setup>
import { computed, onBeforeMount, onMounted, ref, watch } from 'vue';
import Modal from '../Layout/Modal3';
import PreviewSignRequest from './PreviewSignRequest';
import SendRequest from './SendRequest';
import moment from 'moment';
import axios from 'axios';
import $ from 'jquery';
import usePlanning from "../../composables/usePlanning";
import useLang from "../../composables/useLang";
import useNoty from "../../composables/useNoty";
import useDatePicker from "../../composables/useDatePicker";
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

const { getSummaryOfRegEventsForSY } = usePlanning();
const { ucFirst, translate, translateChoice } = useLang();
const { failNoty, successNoty } = useNoty();

// Get date picker options from the composable
const { dpOptions: dpOptionsDate } = useDatePicker(true); // Date-only picker
const { dpOptions: dpOptionsDateWithClear } = useDatePicker(true, null, true, true); // Date-only picker with clear button

const emit = defineEmits(['registrationDeleted', 'refreshMyData']);

const props = defineProps({
    userisadmin: {
        type: Boolean,
        default: false
    },
    registration: {
        type: Object,
        required: true
    },
    student: {
        type: Object,
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    domain: {
        type: Object,
        required: true
    },
    getRegMetaData: {
        type: Boolean,
        default: true
    }
});

// Using dpOptionsDate and dpOptionsDateWithClear from useDatePicker composable

const startDate = moment(props.registration.pivot.start_date, 'YYYY-MM-DD').format('DD-MM-YYYY');
const endDate = moment(props.registration.pivot.end_date, 'YYYY-MM-DD').format('DD-MM-YYYY');
const dirty = ref(false);
const enddateChanged = ref(false);
const potentialGroupsForCourse = ref([]);
const eventsSummary = ref([]);

onMounted(async () => {
    // set popovers
    $('[data-toggle="popover"]').popover({
        html: true
    });
    if (props.getRegMetaData) {
        eventsSummary.value = await getSummaryOfRegEventsForSY(props.registration.pivot.id, props.registration.actualSchoolYear.id);
    }
});

onBeforeMount(() => {
    // don't check (extra request) if it's not going to be in a group anyway
    // - already in a student group or
    // - has a current schedule
    if (!props.registration.tutoringInStudentGroup && props.registration.nrofappointments === 0) {
        getGroupsForCourse(props.registration.course.id);
    }
});

/**
 * get state of checklist, return correct label
 * Options: a registration may have no checklist, an incomplete checklist or only completed checklists
 * @returns {string}
 */
const labelForChecklist = computed(() => {
    if (props.registration.checklists.length === 0) {
        return ucFirst(translate('generic.openregistration')) +
            '. ' + ucFirst(translate('generic.coursehasnochecklist'));
    }
    return props.registration.allchecklistscomplete
        ? ucFirst(translate('generic.openregistration')) +
            '. ' + ucFirst(translate('generic.coursehascompletedchecklist'))
        : ucFirst(translate('generic.openregistration')) +
            '. ' + ucFirst(translate('generic.coursehasuncompletechecklist'));
});

/**
 * get state of checklist, return correct class (color)
 * possible outcome: incomplete, complete or nochecklist
 * @returns {string}
 */
const classForChecklist = computed(() => {
    if (props.registration.checklists.length === 0) return 'nochecklist';
    return props.registration.allchecklistscomplete ? 'complete' : 'incomplete';
});

const isTrialCourse = computed(() => props.registration.is_trial_course === 1);

const isNonIteratingCourse = computed(() => props.registration.recurrenceoption.ends_after_nr_of_occurrences === 1);

/**
 * compress isSigned and isRequested into 1 status field
 * @return string, one of 'signed'|'requested'|'open'
 */
const signStatus = computed(() => {
    return isSigned.value
        ? 'signed'
        : isRequested.value
            ? 'requested'
            : 'open';
});

const isSigned = computed(() => props.registration.pivot.signed === 1);

const isRequested = computed(() => typeof props.registration.pivot.sign_request_send === 'undefined'
    ? false
    : props.registration.pivot.sign_request_send === 1);

/**
 * find email for finance - if not found, the first email
 * @returns {string|*}
 */
const studentEmail = computed(() => {
    const emailContacts = props.student.contacts.filter(contact => contact.contacttype === 'email');
    if (emailContacts.length === 0) return '';
    if (emailContacts.length === 1) {
        return emailContacts[0].value;
    } else {
        const financeContacts = emailContacts.filter(contact => contact.apply_for_finance === 1);
        if (financeContacts.length > 0) {
            return financeContacts[0].value;
        } else {
            return emailContacts[0].value;
        }
    }
});

/**
 * find salutation for finance - if not found, the studentname
 * @returns {string|*}
 */
const studentSalutation = computed(() => {
    let retString = '';
    const emailContacts = props.student.contacts.filter(contact => contact.contacttype === 'email');
    if (emailContacts.length === 0) return props.student.firstname;
    if (emailContacts.length === 1) {
        retString = emailContacts[0].use_salutation;
    } else {
        const financeContacts = emailContacts.filter(contact => contact.apply_for_finance === 1);
        if (financeContacts.length > 0) {
            // if we find more than 1, use the first
            retString = financeContacts[0].use_salutation;
        } else {
            // return the first we did find
            retString = emailContacts[0].use_salutation;
        }
    }
    // if no salutation was filled in on the found entry, use the student's name
    return retString !== '' && retString != null ? retString : props.student.firstname;
});
/**
 * This registration can be taught in a group if:
 * 1 - not already in a student group
 * 2 - not already in a schedule (no current planning events)
 * 3 - course is also being taught in a group
 * 4 - if it's a trial course: does it lead to a course that is being taught in a group?
 * @returns {boolean}
 */
const isGroupable = computed(() => !props.registration.tutoringInStudentGroup &&
    props.registration.nrofappointments === 0 &&
    potentialGroupsForCourse.value.length > 0);


const getGroupSizeLabel = (minSize, maxSize) => {
    if (minSize === 1 && maxSize === 1) {
        return ucFirst(translate('generic.individualcourse'));
    } else if (maxSize === 2) {
        return ucFirst(translate('generic.duocourse'));
    } else {
        return ucFirst(translate('generic.groupcourse'));
    }
};

/**
 * markup for the list of students in the popover (st-group)
 * @returns {string}
 */
const getStudentgroupPopoverContent = () => {
    const studentListArr = props.registration?.studentgroupstudents?.map(std => {
        const flag = std.pivot.as_trial_student > 0
            ? "<i class='fa fa-flag text-info'></i>"
            : '';
        if (std.id === props.student.id) {
            return `<li><strong>${ std.name }</strong> ${ flag }</li>`;
        } else {
            return `<li><a href='/students/${ std.id }/edit'>${ std.name }</a> ${ flag }</li>`;
        }
    });
    const studenList = studentListArr?.join('');
    return '<ul>' + studenList + '</ul>' +
        "<i class='fa fa-flag text-info'></i> = " + ucFirst(translate('generic.triallesson')) +
        "<br><a href='/studentgroups/" + props.registration.studentgroupid + "/edit' " +
        "class='btn btn-sm btn-success'><i class='fas fa-edit mr-2'></i>" +
        ucFirst(translate('generic.openstudentgroup')) + "</a>";
};

const deleteRegistration = (id) => {
    axios.delete('/api/studentcourseregistration/' + id)
        .then(() => {
            // tell parent component to remove the registration as well
            emit('registrationDeleted', { regid: id });
            // if this is the last registration: show delete student button
            if ($('.courseRow').length === 0) {
                $('#studentDeleteBtn').show();
            }
        })
        .catch(err => {
            failNoty(`Error deleting course registration: ${err}`);
        });
};

const refreshMyData = () => {
    emit('refreshMyData');
};

const getGroupsForCourse = (courseId) => {
    axios.get(`/api/getstudentgroupsforcourse/${courseId}`)
        .then(response => {
            potentialGroupsForCourse.value = response.data;
        })
        .catch(err => {
            failNoty(`Error getting student groups: ${err}`);
        });
};

/**
 * add this student to the chosen group
 * @param groupId
 */
const addToGroup = (groupId) => {
    const data = {
        stgid: groupId,
        stid: props.student.id,
        associatedCourse: props.registration.id
    };
    axios.post('/api/addStudentToGroup', data)
        .then(() => {
            successNoty('Student added to student group');
            refreshMyData();
        })
        .catch(err => {
            failNoty(`Error adding student to student group: ${err}`);
        });
};

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});

</script>

<style scoped lang="scss">
@import "../../../../sass/tmpl3/variables";
.incomplete {
    color: $classred;
}
.complete {
    color: $classgreen;
    display: inline-block;
}
.nochecklist {
    color: $classbgblue;
}
.separator {
    border-left: sold 1px $classdark;
}

/** Positioning fields in row */
.adjust-inputfield {
    position: relative;
    top: -8px;
    /* keep the date better readable on tablet */
    font-size: 0.9rem;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
}
.mr-05 {
    margin-right: 2.5px;
}
button, a.btn {
    position: relative;
    top: -3px;
}
.middletext {
    position: relative;
    top: -6px;
}
.push-higher {
    position: relative;
    top: -0.3rem;
}
/** END Positioning fields in row */

</style>
