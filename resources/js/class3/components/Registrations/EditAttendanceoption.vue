<template>
    <modal :popup-title="popupTitle"
           :closetext="ucFirst(translate('generic.cancel'))"
           modal-id="editAttandanceOption"
           size="large"
    >
        <div class="mb-3 row">
            <label for="label" class="col-sm-2 col-form-label form-label">{{ ucFirst(translate('generic.label')) }}</label>
            <div class="col-sm-10">
                <input class="form-control" id="label" type="text" v-model="attendanceoptionToEdit.label">
            </div>
        </div>
        <div class="mb-3 row">
            <label for="actiontutor" class="col-sm-2 col-form-label form-label">{{ ucFirst(translate('generic.actiontutor')) }}</label>
            <div class="col-sm-10">
                <input class="form-control" id="actiontutor" type="text" v-model="attendanceoptionToEdit.action_tutor">
            </div>
        </div>
        <template #okbutton>
            <button
                type="button"
                class="btn btn-primary"
                data-bs-dismiss="modal"
                :disabled="!isSavable"
                @click.prevent="saveAttendanceoption"
            >
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import Modal from '../Layout/bs5/Modal4.vue';
import useLang from '../../composables/useLang.js';
import useAttendanceoptions from '../../composables/useAttendanceoptions.js';
import { computed } from 'vue';
const { attendanceoptionToEdit, saveAttendanceoption } = useAttendanceoptions();
const { ucFirst, translate, translateChoice } = useLang();
const popupTitle = computed(() => {
    const postfix = translateChoice('generic.attendanceoptions', 1);
    return attendanceoptionToEdit.value?.id > 0
        ? `${ucFirst(translate('generic.edit'))} ${postfix}`
        : `${ucFirst(translate('generic.newattendanceoption'))}`;
});
const isSavable = computed(() => {
    return attendanceoptionToEdit.value.label.length > 0;
});
</script>

<style scoped>

</style>
