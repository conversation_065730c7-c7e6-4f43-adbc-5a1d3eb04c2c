<template>
    <panel :busy="loadingBaseData">
        <template #title>
            <strong>{{ ucFirst(translate("generic.planningdetails")) }}</strong>
        </template>
        <!-- PANEL BODY -->
        <div v-if="!student?.id && !course?.id">
            {{ ucFirst(translate("generic.firstchoosestudentorgroup")) }}
        </div>
        <div v-else-if="!course?.id">
            {{ ucFirst(translate("generic.nowchooseacourse")) }}
        </div>
        <div v-else>
            <template v-if="busyPlanning">
                <div class="alert alert-info">
                    {{ ucFirst(translate("generic.checkingplaningstatus")) }}
                    <div class="ms-3 dot-stretching"></div>
                </div>
            </template>
            <template v-else>
                <span v-if="eventsSummary['total-events'] > 0">
                    <em v-html="
                          translate('generic.nroffutureappointmentsoftotal', {
                            future: eventsSummary.future,
                            totalEvents: eventsSummary['total-events'],
                            blocked: eventsSummary.blocked,
                            totalOccuring: eventsSummary['total-occuring']
                          })
                        "
                    />
                </span>
                <span v-else>
                    <em>
                        {{
                            ucFirst(translate("generic.thissubscriptionhasnoactiveplanning"))
                        }}
                    </em>
                </span>
            </template>
            <ul class="mt-1">
                <li>
                    <strong
                    >{{
                            ucFirst(translateChoice("generic.recurrenceoptions", 1))
                        }}:</strong
                    >
                    {{ course?.recurrenceoption.description }}
                </li>
                <li>
                    <strong
                    >{{ ucFirst(translateChoice("generic.schoolyears", 1)) }}:</strong
                    >
                    {{ currentOrFutureSchoolYear?.label }}
                </li>
                <li v-if="typeOfSource === 'studentgroup'">
                    <strong>{{ ucFirst(translateChoice("generic.groups", 1)) }}:</strong>
                    {{ groupSize }}
                </li>
                <li v-if="typeOfSource === 'studentgroup'">
                    <strong>{{ ucFirst(translate("generic.nrofstudentsingroup")) }}:</strong>
                    {{ studentGroup?.students.length }}
                    <a
                        v-if="studentGroup?.students.length > 0"
                        tabindex="0"
                        class="btn btn-sm btn-outline-secondary ms-1"
                        role="dialog"
                        data-bs-toggle="popover"
                        @click.stop="openPopover"
                        :data-bs-content="listStudents(studentGroup?.students)"
                        :title="ucFirst(translateChoice('generic.students', 2))"
                    >
                        {{ translate("generic.who") }}&nbsp;<i
                        class="fa fa-question-circle"
                    ></i>
                    </a>
                </li>
                <li v-if="typeOfSource === 'student'">
                    <strong>{{ ucFirst(translate("generic.signstatus")) }}</strong
                    >: {{ signStatus }}
                </li>
            </ul>
        </div>
    </panel>
</template>

<script setup>
import { computed, onUpdated } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import useLang from '../../composables/useLang';
import usePlanning from '../../composables/usePlanning';
import usePlanningCreateForm from '../../composables/usePlanningCreateForm';
import useBaseData from '../../composables/useBaseData';
const { ucFirst, translate, translateChoice } = useLang();
const { busyPlanning, course, eventsSummary, listStudents, student, studentGroup } = usePlanning();
const { loadingBaseData, currentOrFutureSchoolYear } = useBaseData();
const { repeats } = usePlanningCreateForm();

/**
 * noop, this is just here to prevent propagation to the popovers underlying TR
 * @param ev
 */
const openPopover = (ev) => {
};

/**
 * The groupSize is the number of students that can be registered for this course.
 * fields: group_size_min and group_size_max
 * if both are 1: it's an individual course
 * if both are 2: it's a course for 2 students
 * if group_size_min is 2 and group_size_max > 2: it's a course for groups
 */
const groupSize = computed(() => {
    let retString = translate('generic.unknown');
    if (course.value.group_size_min === 1 && course.value.group_size_max === 1) {
        retString = translate('generic.individualcourse');
    }
    if (course.value.group_size_min === 2 && course.value.group_size_max === 2) {
        retString = translate('generic.duolesson');
    }
    if (course.value.group_size_min === 2 && course.value.group_size_max > 2) {
        retString =
            translate('generic.groupcourse') +
            ' (max ' +
            course.value.group_size_max +
            ' ' +
            translateChoice('generic.students', 2) +
            ')';
    }
    return retString;
});

onUpdated(() => {
    // Initialize Bootstrap 5 popovers
    const popoverElements = document.querySelectorAll('[data-bs-toggle="popover"]');
    popoverElements.forEach(element => {
        new bootstrap.Popover(element, {
            html: true,
            trigger: 'focus'
        });
    });

    // set repeats based on the course's recurrence option
    // if the table has a null value, this also means 0: repeat until the end of the school year
    if (course.value?.recurrenceoption?.ends_after_nr_of_occurrences !== null) {
        // repeats.value = course.value.recurrenceoption.ends_after_nr_of_occurrences;
        repeats.value = 0;
    } else {
        repeats.value = 0;
    }
});

const signStatus = computed(() => {
    if (course.value?.pivot.signed === 1) return translate('generic.signed');
    if (course.value?.pivot.sign_request_send === 1) { return translate('generic.signrequestsend'); }
    return translate('generic.signrequestnotsend');
});

const typeOfSource = computed(() => {
    if (student.value?.id) return 'student';
    if (studentGroup.value?.id) return 'studentgroup';
    return 'unknown';
});

</script>

<style lang="scss">
@import "../../../../sass/variables.scss";

.dot-stretching {
    position: relative;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: $class-red;
    color: $class-red;
    transform: scale(1.25, 1.25);
    animation: dot-stretching 2s infinite ease-in;

    &::before,
    &::after {
        content: "";
        display: inline-block;
        position: absolute;
        top: 0;
    }

    &::before {
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color: $class-red;
        color: $class-red;
        animation: dot-stretching-before 2s infinite ease-in;
    }

    &::after {
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color: $class-red;
        color: $class-red;
        animation: dot-stretching-after 2s infinite ease-in;
    }
}

@keyframes dot-stretching {
    0% {
        transform: scale(1.25, 1.25);
    }
    50%,
    60% {
        transform: scale(0.8, 0.8);
    }
    100% {
        transform: scale(1.25, 1.25);
    }
}

@keyframes dot-stretching-before {
    0% {
        transform: translate(0) scale(0.7, 0.7);
    }
    50%,
    60% {
        transform: translate(-20px) scale(1, 1);
    }
    100% {
        transform: translate(0) scale(0.7, 0.7);
    }
}

@keyframes dot-stretching-after {
    0% {
        transform: translate(0) scale(0.7, 0.7);
    }
    50%,
    60% {
        transform: translate(20px) scale(1, 1);
    }
    100% {
        transform: translate(0) scale(0.7, 0.7);
    }
}

.popover {
    width: fit-content !important;
    max-width: 100rem !important;
}
</style>
