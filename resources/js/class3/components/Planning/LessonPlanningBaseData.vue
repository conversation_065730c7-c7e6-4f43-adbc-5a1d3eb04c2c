<template>
  <panel :busy="busy || loadingBaseData">
    <template v-slot:title>
      <strong>{{ ucFirst(translate("generic.planningbase")) }}</strong>
    </template>

    {{ ucFirst(translate("generic.tostartorvieplanning")) }}:<br />
    <quick-jump-student
      :exclude-id="parseInt(studentId)"
      :popup-title="
        ucFirst(translate('generic.search')) +
        ' ' +
        translateChoice('generic.students', 1)
      "
      display-fields="name"
      api-get-string="/api/students?onlystudents=true"
      @record-chosen="jumpToStudentFromQuickJump"
      :button-label="translate('generic.chooseastudent')"
    />

    {{ translate("generic.or") }}

    <quick-jump-student
      :exclude-id="parseInt(studentGroupId)"
      :popup-title="
        ucFirst(translate('generic.search')) +
        ' ' +
        translateChoice('generic.studentgroups', 1)
      "
      display-fields="name"
      api-get-string="/api/studentgroups"
      @record-chosen="jumpToStudentGroupFromQuickJump"
      :button-label="translate('generic.choosestudentgroup')"
    />

    <div v-if="student?.id" class="mt-2">
      <strong class="mr-2">{{ ucFirst(translateChoice("generic.students", 1)) }}:</strong>
        <a :href="'/students/' + student?.id + '/edit'">{{ student.name }}</a>
      <div v-if="courses.length === 0" class="alert alert-warning">
        {{ ucFirst(translate("generic.noactivecoursesnoworfuture")) }}
      </div>
      <div v-else>
        <template v-if="courses.length > 1">
          <label for="chooseCourse">{{
            ucFirst(translate("generic.chooseacourse"))
          }}</label>
          <select
            class="form-control"
            id="chooseCourse"
            v-model="courseId"
            @change="setCourse(true)"
          >
            <option
              v-for="course in courses"
              :key="course.id"
              :value="course.id"
            >
              <template
                v-if="
                  student?.id &&
                  student.groupInfo.coursesInStudentGroup[course.id] != null
                "
              >
                {{ course.name }} ({{ translate("generic.from") }}
                {{ displayDate(course.pivot.start_date) }}) =>
                {{ translate("generic.tutoredin") }}
                {{ student.groupInfo.coursesInStudentGroup[course.id].stgName }}
              </template>
              <template v-else>
                  {{ course.name }} ({{ translate("generic.from") }}
                {{ displayDate(course.pivot.start_date) }})
              </template>
            </option>
          </select>
        </template>
        <template v-else>
          <strong class="mr-2">{{ ucFirst(translateChoice("generic.courses", 1)) }}:</strong>
          <a :href="'/courses/' + courses[0]?.id + '/edit'">{{ courses[0].name }}</a> ({{ translate("generic.from") }}
          {{ displayDate(courses[0].pivot.start_date) }})
        </template>
      </div>
    </div>
    <div v-if="studentGroup?.id" class="mt-2">
      <strong class="mr-2">{{ ucFirst(translateChoice("generic.studentgroups", 1)) }}:</strong>
      <a :href="`/studentgroups/${studentGroupId}/edit`">{{ studentGroup.name }}</a>
      <div v-if="courses.length === 0" class="alert alert-warning">
        {{ ucFirst(translate("generic.noactivecoursesnoworfuture")) }}
      </div>
      <div v-else>
        <!-- a studentgroup has only one course -->
        <strong class="mr-2">{{ ucFirst(translateChoice("generic.courses", 1)) }}:</strong>
        <a :href="'/courses/' + courses[0]?.id + '/edit'">{{ courses[0]?.name }}</a>
      </div>
    </div>
  </panel>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import QuickJumpStudent from '../Layout/QuickJump.vue';
import useToast from '../../composables/useToast.js';
import Panel from '../Layout/bs5/Panel4.vue';
import usePlanning from '../../composables/usePlanning.js';
import useDateTime from '../../composables/useDateTime.js';
import useBaseData from '../../composables/useBaseData.js';
import useLang from '../../composables/useLang.js';
import useApi from '../../composables/useApi.js';

const { ucFirst, translate, translateChoice } = useLang();
const { failToast } = useToast();
const { course, events, student, studentGroup } = usePlanning(); // the student and course that we are going to plan for
const { loadingBaseData } = useBaseData();
const { displayDate } = useDateTime();
const { apiGet } = useApi();
const busy = ref(false); // is the page busy with loading data?
const studentId = ref(0); // the student that we are going to plan for
const studentGroupId = ref(0); // the student group that we are going to plan for
const courses = ref([]); // all active courses of a student
const courseId = ref(0); // the course that we are going to plan for

onMounted(async () => {
    // if we have a studentId in the url, then jump to that student
    const urlParams = new URLSearchParams(window.location.search);
    const chosenStudentId = urlParams.get('studentId');
    const chosenStudentGroupId = urlParams.get('studentgroupId');
    // prefer a student above a student group if they would both be present
    if (chosenStudentId) await jumpToStudent(chosenStudentId);
    else if (chosenStudentGroupId) await jumpToStudentGroup(chosenStudentGroupId);
    // now, if we have a courseId present in the interface and then select it.
    // however, this is only relevant for students, not for student groups because they can
    // only have one course so it will be selected automatically
    const chosenCourseId = urlParams.get('courseId');
    if (chosenCourseId && studentId.value > 0) {
        courseId.value = parseInt(chosenCourseId);
        setCourse();
    }
});
/**
 * Jump to a specific student.
 *
 * @param {string} chosenStudentId - The ID of the chosen student. Defaults to "0".
 * @param {boolean} forceForward - Whether the user wants to navigate forward or not. Defaults to false.
 * @returns {Promise<void>}
 */
const jumpToStudent = async (chosenStudentId = '0', forceForward = false) => {
    const id = parseInt(chosenStudentId);
    if (id > 0) {
        if (forceForward) {
            window.location.href = '/planning/lesson?studentId=' + id;
        }
        // first rest a previous choice
        resetData();
        studentId.value = id;
        // get the student's data
        try {
            busy.value = true;
            const response = await apiGet('/api/students/' + studentId.value);
            student.value = response.data;
            // filter out the current or future active courses of this student
            courses.value = student.value.courses.filter((course) => {
                const endDate = course.pivot.end_date;
                const now = new Date();
                const hasStarted = new Date(course.pivot.start_date) <= now;
                const endsInFuture = endDate == null || new Date(endDate) >= now;
                return hasStarted && endsInFuture;
            });
            // if there is only one course, then select it directly
            if (courses.value.length === 1) {
                // but! if this course is being tutored in a student group, then we will forward the interface to the student group
                // we can use the student.groupInfo.coursesInStudentGroup to find out if this is the case and use jumpToStudentGroup, i think...
                if (student.value.groupInfo.coursesInStudentGroup[courses.value[0]?.id] != null) {
                    await jumpToStudentGroup(
                        student.value.groupInfo.coursesInStudentGroup[courses.value[0].id]
                            .stgId,
                        true
                    );
                } else {
                    courseId.value = courses.value[0]?.id;
                    setCourse();
                }
            }
            busy.value = false;
        } catch (error) {
            failToast(error);
            busy.value = false;
        }
    }
};

const jumpToStudentFromQuickJump = async (chosenStudentId) => {
    window.location.href = '/planning/lesson?studentId=' + chosenStudentId;
};

/**
 * Jump to a specific student group.
 * @async
 * @param {string} chosenStudentGroupId - The ID of the student group to jump to.
 * @param {boolean} [forceForward=false] - Whether to navigate forward or not.
 */
const jumpToStudentGroup = async (
    chosenStudentGroupId = '0',
    forceForward = false
) => {
    const id = parseInt(chosenStudentGroupId);
    if (id > 0) {
        if (forceForward) {
            window.location.href = '/planning/lesson?studentgroupId=' + id;
        }
        // first rest a previous choice
        resetData();
        studentGroupId.value = id;
        try {
            busy.value = true;
            const response = await apiGet(
                '/api/studentgroups/' + studentGroupId.value
            );
            studentGroup.value = response.data.data;
            // A student group can only have 1 course, so we can select it directly
            courses.value = [studentGroup.value.course];
            course.value = courses.value[0];
            busy.value = false;
        } catch (error) {
            failToast(error);
            busy.value = false;
        }
    }
};

const jumpToStudentGroupFromQuickJump = async (chosenStudentGroupId) => {
    window.location.href =
    '/planning/lesson?studentgroupId=' + chosenStudentGroupId;
};
/**
 * Sets the course based on the provided parameters.
 *
 * @param {boolean} [forward=false] - Indicates whether to forward the interface or not. Default is `false`.
 * @returns {void}
 */
const setCourse = (forward = false) => {
    const chosenCourse = courses.value.find(
        (course) => course.id === courseId.value
    );
    if (!forward) {
        course.value = chosenCourse;
        return;
    }
    if (
        student.value?.id && student.value?.groupInfo.coursesInStudentGroup[chosenCourse?.id]
    ) {
        // we are planning for a student (not a student group) and the course is being tutored in a student group:
        // we will forward the interface to the student group
        window.location.href =
      '/planning/lesson?studentgroupId=' +
      student.value.groupInfo.coursesInStudentGroup[chosenCourse?.id].stgId;
    } else if (studentGroupId.value > 0) {
        // we already have a studentgroupId in the url, so we will not change the url and simply set the course.value
        // this is the case when we are planning for a student group, it only has 1 course
        course.value = chosenCourse;
    } else {
        // the course is not being tutored in a student group
        // we do not have a studentId in the url, so we will forward the interface to the student plus the studentID
        window.location.href =
      '/planning/lesson?studentId=' +
      student.value?.id +
      '&courseId=' +
      chosenCourse?.id;
    }
};

const resetData = () => {
    student.value = null;
    studentGroup.value = null;
    courses.value = [];
    course.value = null;
    studentId.value = 0;
    studentGroupId.value = 0;
    courseId.value = 0;
    events.value = [];
};
</script>

<style scoped></style>
