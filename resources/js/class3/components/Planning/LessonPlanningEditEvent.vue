<template>
    <modal
        :popup-title="ucFirst(translate('generic.editevent')) + ' ' + eventToEdit.course.name + ' ' + displayDateTime(eventToEdit.datetime)"
        :size="'large'"
        :modal-id="'edit-event-' + eventToEdit.id"
        :closetext="ucFirst(translate('generic.cancel'))"
        @closeBtnClicked="doRevert"
    >
        <div v-if="eventToEdit" class="modal-content">
            <div class="row">
                <div class="col-md-2"><label>{{ ucFirst(translate('generic.datetime')) }}</label></div>
                <div class="col-md-10">{{ displayDateTime(eventToEdit.datetime) }}</div>
            </div>
            <div class="row">
                <div class="col-md-2"><label>{{ ucFirst(translateChoice('generic.courses', 1)) }}</label></div>
                <div class="col-md-10">
                    <span class="mr-1">{{ eventToEdit.course.name }}:</span>
                    <span v-if="isAStudentGroup">
                        {{
                            studentGroup.students.length
                        }} {{ translateChoice('generic.students', parseInt(studentGroup.students.length)) }}
                        <button
                            v-if="studentGroup.students.length > 0"
                            class="btn btn-primary btn-sm ml-2"
                            @click="isOpen = !isOpen"
                            type="button"
                            aria-controls="show list of students in course"
                        >
                            <i class="fa" :class="studentsSectionIsOpenClass"></i>
                        </button>
                        <div
                            v-if="studentGroup.students.length > 0"
                            :class="['collapse', {show: isOpen}]"
                            id="studentsList"
                        >
                            <span v-for="student in studentGroup.students" :key="student.id" class="mr-2">
                                <a :href="'/students/' + student.id + '/edit'" class="badge badge-primary">
                                    {{ student.name }}
                                    <i class="fa fa-id-card"></i>
                                </a>
                            </span>
                        </div>
                    </span>
                    <span>
                        <a :href="'/students/' + eventToEdit.student.id + '/edit'">
                            {{ eventToEdit.student.name }}&nbsp;<i class="fa fa-id-card"></i>
                        </a>
                    </span>
                </div>
            </div>
            <hr>
            <div class="row">
                <!-- Tutor-->
                <div class="col-md-1"><label class="movedown10px">{{ ucFirst(translateChoice('generic.tutors', 1)) }}</label></div>
                <div class="col-md-4">
                    <select class="form-control" v-model="newTutorId">
                        <option v-for="tutor in allTutors" :key="tutor.id" :value="tutor.id">
                            {{ tutor.name }}
                        </option>
                    </select>
                </div>
                <!-- Location -->
                <div class="col-md-2"><label class="movedown10px">{{ ucFirst(translateChoice('generic.locations', 1)) }}</label></div>
                <div class="input-group col-md-5">
                    <span class="input-group-text" v-html="chosenLocationIcon" />
                    <select class="form-control" v-model="newLocationId">
                        <option v-for="location in allLocations" :key="location.id" :value="location.id">
                            {{ location.name }}
                        </option>
                    </select>
                </div>
            </div>
            <!-- Start date and time -->
            <div class="row">
                <div class="col-md-2">
                    <label for="startdate" class="movedown10px">
                        {{ ucFirst(translate('generic.startdate')) }}
                    </label>
                </div>
                <div class="col-md-4">
                    <VueDatepicker
                        v-model="newStartDate"
                        v-bind="dpOptionsDate"
                        :placeholder="translate('generic.date')"
                    />
                </div>
                <div class="col-md-2">
                    <label for="starttime" class="movedown10px">
                        {{ ucFirst(translate('generic.starttime')) }}
                    </label>
                </div>
                <div class="col-md-4">
                    <VueDatepicker
                        v-model="newStartTime"
                        format="HH:mm"
                        :locale="dpOptions.locale"
                        :placeholder="translate('generic.time')"
                        time-picker
                        model-type="format"
                    />
                </div>
            </div>

            <div class="row">
                <div class="col-md-2">
                    <label>
                        {{ ucFirst(translate('generic.enddate')) }}
                    </label>
                </div>
                <div class="col-md-4">
                    <input class="form-control input-date-time-field" readonly :value="newEndDate" />
                </div>
                <div class="col-md-2">
                    <label>
                        {{ ucFirst(translate('generic.endtime')) }}
                    </label>
                </div>
                <div class="col-md-4">
                    <input class="form-control input-date-time-field" :readonly="freezeTimespan"
                           v-model="newEndTime" />
                </div>
            </div>
            <div class="row">
                <div
                    class='col-md-6 offset-md-6'
                    v-if="freezeTimespan"
                    v-html="translate('generic.explainonlysingleevent')" />
            </div>
        </div>
    </modal>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import Modal from '../Layout/Modal3.vue';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import useLang from '../../composables/useLang';
import useDateTime from '../../composables/useDateTime';
import useDatePicker from '../../composables/useDatePicker';
import usePlanning from '../../composables/usePlanning';
import useBaseData from '../../composables/useBaseData';

const props = defineProps({
    eventToEdit: {
        type: Object,
        default: () => {}
    }
});

const { ucFirst, translate, translateChoice } = useLang();
const { displayDateTime } = useDateTime();
const { studentGroup } = usePlanning();
const { allTutors, allLocations, initBaseData } = useBaseData();
const { dpOptions } = useDatePicker(false); // never "wholeday", this is an event
const { dpOptions: dpOptionsDate } = useDatePicker(true); // date-only picker

const isOpen = ref(false);
const newTutorId = ref(0);
const newLocationId = ref(0);
const newStartDate = ref('');
const newStartTime = ref('');
const newEndDate = ref('');
const newEndTime = ref('');
const updateOccurrence = ref('thisEvent');

onMounted(async () => {
    newTutorId.value = props.eventToEdit?.tutor_id;
    newLocationId.value = props.eventToEdit?.location_id;
    await initBaseData({
        locations: true,
        tutors: true
    });
});

const doRevert = () => {
    console.log('doRevert');
};

const freezeTimespan = computed(() => {
    return updateOccurrence.value !== 'thisEvent';
});

/* ----------------- Student / Student group -------------- */
const isAStudentGroup = computed(() => {
    return props.eventToEdit.student.firstname === '-' && props.eventToEdit.student.date_of_birth === '1800-01-01';
});
// fixme: this is not working, new class is not being rendered
const studentsSectionIsOpenClass = computed(() => isOpen.value ? 'fa-arrow-circle-up' : 'fa-arrow-circle-down');

/* ----------------------- Location ----------------------- */

/**
 * pickup the icon of the chosen location
 * @type {ComputedRef<string>}
 */
const chosenLocationIcon = computed(() => {
    return allLocations.value.find(loc => loc.id === newLocationId.value)?.iconSmall;
});

</script>

<style scoped lang="scss">
@import "../../../../sass/variables.scss";
.modal-content {
    color: black;
    padding: 1rem;
    label {
        font-weight: bold;
        color: $class-blue;
    }
    hr {
        margin: 1rem 0;
        border-top: 1px solid $class-blue;
    }
    .movedown10px {
        margin-top: 10px;
    }
}
</style>
