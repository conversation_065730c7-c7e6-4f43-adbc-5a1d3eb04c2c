<template>
    <Panel>
        <template #title>
            {{ ucFirst(translate("generic.preferredschedule")) }}
            <small>
                {{ translate("generic.youravailability") }}
            </small>
        </template>
        <section class="sticky-section">
            <div v-if="totalNumberSelected === 0" class="alert alert-success" >{{ translate("generic.pleasemarkyourpreferedtimesegments") }}</div>
            <div v-else-if="totalNumberSelected < 4" class="alert alert-warning">
                {{totalNumberSelected}} {{translate("generic.totaltimetoolow")}}
            </div>
            <div v-else class="alert alert-success">
                <strong>{{translate("generic.notice")}}:</strong> {{translate("generic.allreadyyoumayclosethiswindow")  }}
            </div>
            <span v-if="!saving && saveresult.length > 0" class="alert alert-success ml-2">
                <strong>
                    <i class="fa fa-check"></i>
                    {{ saveresult }}
                </strong>
            </span>
            <img class="ml-2" v-if="saving" src="/images/class-loader-small.gif">
        </section>
        <section class="preferences-section">
            <div class="days-grid">
                <div class="day" v-for="(dayName, index) in dayNames" :key="index">
                    <div v-if="dayName !== 'saturday' && dayName !== 'sunday'" class="dayname">
                        {{ translate('localisation.' + dayName) }}
                    </div>
                    <div v-else class="dayname weekend">
                        {{ translate('localisation.' + dayName) }}
                    </div>
                    <div class="hourSequence">
                        <div @mouseover="markMe" @mousedown="startDragSelect" @mouseup="endDragSelect" 
                            :data-entry="`${dayName}_${Object.keys(hourName)[0]}`"
                            class="hour" v-for="(hourName, index) in dayMarkers[dayName]" 
                            :key="dayName+index">{{Object.keys(hourName)[0]}}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </Panel>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import Panel from '../Layout/Panel.vue';
import useLang from '../../composables/useLang';
import useSchedulePreferences from '../../composables/useSchedulePreferences';

const { accessToken, isAdmin, student, studentPrefs } = useSchedulePreferences();
const { ucFirst, translate } = useLang();

const saving = ref(false);
const saveresult = ref('');
const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
const dayMarkers = ref({
    'monday': [], 'tuesday': [], 'wednesday': [], 'thursday': [], 'friday': [], 'saturday': []
});
const dragging = ref(false);
const totalNumberSelected = ref(0);

const MINTIME = 9;
const MAXTIME = 23;

const startDragSelect = (ev) => {
    const LEFTCLICK = 1;
    // only if it is a left-click!
    if (ev.which === LEFTCLICK) {
        // only if we are not already dragging (failsafe, should never be the case)
        if (!dragging.value) {
            dragging.value = true;
            markMe(ev);
        }
    }
};

const endDragSelect = async () => {
    if (dragging.value) {
        dragging.value = false;
        saving.value = true;
        // find all currently selected timeslots
        try {
            await saveAvailability();
            saveresult.value = translate("generic.saved");
        } catch (error) {
            saveresult.value = translate("generic.error");``
        } finally {
            saving.value = false;            
            setTimeout(() => {
                saveresult.value = "";
            }, 5000); 
        }
    }
};

const markMe = (ev, orgClass = "hour") => {
    if (dragging.value) {
        // toggle highlight
        const currentClass = ev.target.getAttribute('class');
        if (currentClass.indexOf('markMe') === -1) {
            // set "on"
            ev.target.setAttribute("class", `${orgClass} markMe`);
        } else {
            ev.target.setAttribute("class", orgClass);
        }
        calculateTotalNumberSelected();
    }
};

onMounted(() => {
    fillHourNames();
});

watch(studentPrefs, () => {
    fillTimeSheetFromApiData();
});

const fillHourNames = () => {
    for (let i = MINTIME; i < MAXTIME; i++) {
        let name1 = i + ":00";
        let name2 = i + ":30";
        // prepend 0
        if (i < 10) {
            name1 = "0" + name1;
            name2 = "0" + name2
        }
        // enter timeslot into dataset
        for (let dayName in dayMarkers.value) {
            if (dayMarkers.value.hasOwnProperty(dayName)) {
                dayMarkers.value[dayName].push({[name1]: false});
                dayMarkers.value[dayName].push({[name2]: false});
            }
        }
    }
};

/**
 * Total number of minutes that have been selected 
 * = number of segments X 30 minutes
 */
const calculateTotalNumberSelected = () => {
    totalNumberSelected.value = (document.querySelectorAll('.markMe').length * 30) / 60;
};

const saveAvailability = async () => {
    let data = {
        student: student.value.id, // in case of admin
        token: accessToken.value, // in case of student (w.o. login)
        availability: {
            'monday': [], 'tuesday': [], 'wednesday': [],
            'thursday': [], 'friday': [], 'saturday': [], 'sunday': []
        }
    };
    // get all id's as data
    $(".markMe").map((key, val) => {
        const parts = val.getAttribute("data-entry").split('_');
        data.availability[parts[0]].push(parts[1]);
    });
    // send to backend
    let url = (isAdmin.value) ?
        `/api/adminupdateschedulepreferences` :
        `/api/updateschedulepreferences`;
    return axios.post(url, data);
};

/** 
 * remove all 'markme' classes.
 */
const resetTimeSheet = () => {
    const markMeElements = document.querySelectorAll('.markMe');
    markMeElements.forEach(element => {
        element.classList.remove('markMe');
    });
}

/** 
 * fill the sheet with the data retrieved from the backend
 */
const fillTimeSheetFromApiData = () => {
    // clear the sheet first
    resetTimeSheet();
    // fill the sheet with the data
    dayNames.forEach(dayName => {
        studentPrefs.value[dayName].forEach(item => {
            if (item) {
                // find the elemt that needs to be marked. e.g. data-entry="thursday_10:30" dayname="thursday" hour="10:30"
                const element = document.querySelector(`[data-entry="${dayName}_${item}"]`);
                if (element) {
                    element.classList.add('markMe');
                } else {
                    // should never happen
                    console.log("element not found: ", `${dayName}_${item}!`);
                }
            }
        });
    });
}
</script>   

<style scoped lang="scss">
.sticky-section {
    position: sticky;
    top: 50px;
    background: white;
    z-index: 100;
    padding: 1rem 0;
    margin: -1rem 0;  /* Compensate for the padding to maintain alignment */
}

.preferences-section {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #F7F9FC;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.days-grid {
    display: grid;
    gap: 0.75rem;
    max-width: 1200px;
    margin: 0 auto;

    @media (min-width: 768px) {
        grid-template-columns: repeat(6, calc(16.666% - 0.625rem));
    }

    @media (max-width: 767.98px) {
        grid-template-columns: repeat(3, calc(33.333% - 0.5rem));
    }

    @media (max-width: 400px) {
        grid-template-columns: repeat(2, calc(50% - 0.375rem));
    }
}

.day {
    width: 100%;
    border: solid black 2px;
    display: inline-block;
    text-align: center;
    .dayname {
        background-color: #26BB9C;
        border-bottom: solid black 2px;
        color:white;
    }
    .weekend {
        background-color: #ededed;
        color:black;
    }
    .hour {
        /* not selectable */
        /* because we do this programmatically */
        -moz-user-select: none;
        -khtml-user-select: none;
        -webkit-user-select: none;
        -o-user-select: none;
        user-select: none;
        border-bottom: solid #2A3F54 1px;
        &:last-child {
            border-bottom: none;
        }
    }
    .markMe {
        background-color: #3E5367;
        color: white;
    }
}
</style>
