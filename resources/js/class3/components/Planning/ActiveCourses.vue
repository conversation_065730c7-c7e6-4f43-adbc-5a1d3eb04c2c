<template>
    <Panel>
        <template #title>
            {{ ucFirst(translateChoice("generic.regs", activeRegistrations.length)) }} {{ student.name }}
        </template>
        <template #subtitle>
            <div v-if="isAdmin">
                <a class="btn btn-primary" href="/calendar">
                    <font-awesome-icon :icon="['fa', 'calendar']"/>
                    {{ ucFirst(translate("generic.openfullcalendar")) }}
                </a>
                <a class="btn btn-primary" :href="`/students/${student.id}/edit`">
                    <font-awesome-icon :icon="['fa', 'file']"/>
                    {{ ucFirst(translate('generic.studentfile')) }} {{ translate('generic.of') }} {{ student.name }}
                </a>
            </div>
        </template>
        <template v-if="activeRegistrations.length > 0">
            <div v-for="(registration, index) in activeRegistrations" :key="registration.id" class="mb-3">
                <strong>{{ registration.course.name }}</strong>
                <small>{{ registration.course.recurrenceoption.description }}</small>
                <a v-if="isAdmin"
                   class="btn btn-success btn-sm ml-2"
                   :href="getLink(registration)"
                >
                    <font-awesome-icon :icon="['fa', 'calendar']"/>
                    {{ ucFirst(translate('generic.thiscourseplanning')) }}
                    <span v-if="registration.course.studentgroup">
                        ({{ translate('generic.ingroup') }} {{ registration.course.studentgroup.name }})
                    </span>
                    <span v-else>
                        ({{ translate('generic.individual') }})
                    </span>
                </a>
                <div v-if="Array.isArray(registration.course.planning) && registration.course.planning.length > 0">
                    {{ ucFirst(translate('generic.currentlessontime')) }}
                    {{ translate(`localisation.${ registration.course.planning[0].dayname.toLowerCase() }`) }},
                    {{ registration.course.planning[0].time }}
                    <input class="ml-2" type="checkbox" v-model="regKeepSchedule[index].keepSchedule"/>
                    <strong class="text-danger">{{ translate("generic.iprefertokeepcurrenttime") }}</strong> <sup
                    class="text-danger">*)</sup>
                </div>
                <div v-else>
                    ({{ ucFirst(translate('generic.newcoursetobeplanned')) }})
                </div>
            </div>
            <div class="d-flex" v-if="hasActivePlanning">
                <div>
                    <sup class="text-danger mr-2">*)</sup>
                </div>
                <div>
                    <small>
                        <em v-html="translate('generic.explainkeepscheduletimerestriction')"></em>
                    </small>
                </div>
            </div>
        </template>
        <div v-else>
            {{ ucFirst(translate('generic.youhavenocourses')) }}
        </div>
    </Panel>
</template>

<script setup>
import { computed, ref, watchEffect, watch } from 'vue';
import useSchedulePreferences from '../../composables/useSchedulePreferences';
import useLang from '../../composables/useLang';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import Panel from '../Layout/Panel.vue';

const firstTime = ref(true);
const { activeRegistrations, isAdmin, student } = useSchedulePreferences();
const { translate, translateChoice, ucFirst } = useLang();
const getLink = (registration) => {
    if (registration.course?.studentgroup) {
        return `/planning/lesson?studentgroupId=${ registration.course.studentgroup.id }`;
    }
    return `/planning/lesson?studentId=${ registration.student_id }&courseId=${ registration.course_id }`;
};

const regKeepSchedule = ref({});

// initialize regKeepSchedule from activeRegistrations
watchEffect(() => {
    const newSchedule = [];
    activeRegistrations.value?.forEach(registration => {
        newSchedule.push({ regid: registration.id, keepSchedule: registration.please_keep_scheduled_time ?? false });
    });
    regKeepSchedule.value = newSchedule;
});

watch(regKeepSchedule, async (newVal) => {
    if (firstTime.value) {
        firstTime.value = false;
        return;
    }
    let apiUrl = isAdmin.value ? `/api/adminkeepscheduletime/${ student.value.id }` : `/api/keepscheduletime/${ student.value.accesstoken }`;
    try {
        await axios.put(apiUrl, {
            registrations: newVal
        });
    } catch (error) {
        console.error(error);
    }
}, { deep: true }); // Voeg deep: true toe om nested veranderingen te detecteren

const hasActivePlanning = computed(() => {
    return activeRegistrations.value.length > 0 && activeRegistrations.value.some(reg => reg.course.planning.length > 0);
});
</script>

<style scoped>

</style>
