<template>
    <div v-if="!loadingBaseData">
        <div v-if="(!student?.id && !studentGroup?.id) || !course?.id">
            {{ ucFirst(translate("generic.firstchooseplandetails")) }}
        </div>
        <div v-else>
            <div class="row">
                <div class="col-4">
                    <div class="mb-3">
                        <label for="date" class="form-label">{{ ucFirst(translate("generic.startdate")) }}</label>
                        <VueDatepicker
                            v-model="date"
                            v-bind="dpOptionsDate"
                            @update:model-value="onDataChanged"
                        />
                    </div>
                </div>
                <div class="col-4">
                    <div class="mb-3">
                        <label for="time" class="form-label">{{ ucFirst(translate("generic.time")) }}</label>
                        <input
                            type="time"
                            step="5"
                            class="form-control"
                            v-model="time"
                            @input="onDataChanged"
                        />
                    </div>
                </div>
                <div class="col-4">
                    <div class="mb-3">
                        <label for="repeats" class="form-label">
                            {{ ucFirst(translate("generic.repeats")) }}
                            <span v-tooltip="translate('generic.howmanyeventsdoyouwanttoplan')">
                              <i class="fas fa-question-circle"></i>
                            </span>
                        </label>
                        <input
                            type="number"
                            class="form-control"
                            v-model="repeats"
                            @input="onDataChanged"
                        />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-4">
                    <div class="mb-3">
                        <label for="location" class="form-label">{{
                                ucFirst(translateChoice("generic.locations", 1))
                            }}</label>
                        <select
                            class="form-select"
                            v-model="locationId"
                            @input="onDataChanged"
                        >
                            <!-- option value needs to be int, hence the : in front of value -->
                            <option :value="0">
                                {{ translate("generic.none") }}
                            </option>
                            <option
                                v-for="location in locationsFirstChoice"
                                :key="location.id"
                                :value="location.id"
                            >
                                {{ location.name }}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-4">
                    <div class="mb-3">
                        <label for="location" class="form-label">
                            Alt. {{ translateChoice("generic.locations", 1) }}
                            <span v-tooltip="translate('generic.ifoccupiedmoveto')">
                                <i class="fas fa-question-circle"></i>
                            </span>
                        </label>
                        <select
                            class="form-select"
                            v-model="locationIdAlt"
                            @input="onDataChanged"
                        >
                            <option :value="0">
                                {{ translate("generic.none") }}
                            </option>
                            <option
                                v-for="location in locationsSecondChoice"
                                :key="location.id"
                                :value="location.id"
                            >
                                {{ location.name }}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="col-4">
                    <div class="mb-3">
                        <label for="tutor" class="form-label">
                            {{ ucFirst(translateChoice("generic.tutors", 1)) }}
                        </label>
                        <select class="form-select" v-model="tutorId" @input="onDataChanged">
                            <option :value="0">
                                {{ translate("generic.none") }}
                            </option>
                            <option
                                v-for="tutor in allTutors"
                                :key="tutor.id"
                                :value="tutor.id"
                            >
                                {{ tutor.name }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
            <hr>
            <button
                type="submit"
                class="btn btn-primary"
                :disabled="!canStartAnalysis"
                data-bs-toggle="modal"
                data-bs-target="#LessonPlanningPreparePopup"
                @click="startAnalysis = true"
            >
                <i class="fas fa-calendar-plus me-2"></i>
                {{ ucFirst(translate("generic.analyse")) }}
            </button>
        </div>
        <lesson-planning-prepare-popup
            :date="date"
            :time="time"
            :location-id="locationId"
            :tutor-id="tutorId"
            :location-id-alt="locationIdAlt"
            :repeats="repeats"
            :start-analysis="startAnalysis"
        />
    </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import LessonPlanningPreparePopup from './LessonPlanningPreparePopup.vue';
import useLang from '../../composables/useLang';
import useBaseData from '../../composables/useBaseData';
import usePlanning from '../../composables/usePlanning';
import usePlanningCreateForm from '../../composables/usePlanningCreateForm';
import useDatePicker from '../../composables/useDatePicker';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

const { allTutors, loadingBaseData } = useBaseData();
const { translate, translateChoice, ucFirst } = useLang();
const { student, studentGroup, course } = usePlanning();
const {
    date,
    locationId,
    locationIdAlt,
    locationsFirstChoice,
    locationsSecondChoice,
    repeats,
    time,
    tutorId
} = usePlanningCreateForm();

// Get date picker options from the composable
const { dpOptions: dpOptionsDate } = useDatePicker(true);

const startAnalysis = ref(false);

const canStartAnalysis = computed(() => {
    return (
        date.value &&
        time.value &&
        locationId.value &&
        locationId.value !== 0 &&
        tutorId.value &&
        tutorId.value !== 0
    );
});

const onDataChanged = () => {
    startAnalysis.value = false;
};

</script>

<style scoped></style>
