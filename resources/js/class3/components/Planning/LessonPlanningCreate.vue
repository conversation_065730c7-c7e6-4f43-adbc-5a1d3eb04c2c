<template>
  <panel :busy="loadingBaseData">
    <template v-slot:title>
      <strong>
          {{ ucFirst(translate("generic.create")) }}
          {{ translateChoice("generic.events", 2) }}
      </strong>
    </template>
    <lesson-planning-create-form @action="doAction" />
  </panel>
</template>

<script setup>
import Panel from '../Layout/bs5/Panel4.vue';
import useLang from '../../composables/useLang';
import useBaseData from '../../composables/useBaseData';

import LessonPlanningCreateForm from './LessonPlanningCreateForm.vue';

const { ucFirst, translate, translateChoice } = useLang();

const { loadingBaseData } = useBaseData();

const actions = {
    analyse: (data) => {
        console.log('doAnalyse');
    },
    confirm: (data) => {
        console.log('doConfirmAnalysis');
    }
};
const doAction = (actionData) => {
    if (actions[actionData.stage] && typeof actions[actionData.stage] === 'function') {
        actions[actionData.stage](actionData);
    } else {
        console.log('No such function ' + actionData.stage);
    }
};
</script>

<style scoped>
.container {
  padding: 10px;
}
</style>
