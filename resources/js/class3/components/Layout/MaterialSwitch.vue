<template>
    <div class="mb-3">
        <template v-if="labelOn !== ''">
            <label :for="switchId" class="form-label">
                {{ currentLabel }}
            </label>
            <span
                v-if="tooltip !== ''"
                class="badge rounded-pill bg-primary ms-1 me-2"
                v-tooltip="tooltip"
            >
                <i class="fas fa-question"></i>
            </span>
        </template>
        <div
            :class="['material-switch', {'p-2': labelOn !== ''}]"
            v-tooltip="labelOn === '' ? tooltip : ''"
        >
            <slot name="before"/>
            <input
                :id="switchId"
                :name="switchId"
                type="checkbox"
                @change="sendChangeEvent"
                v-model="inputVal"
            />
            <label :for="switchId" :class="'label-' + color"></label>
            <slot name="after"/>
        </div>
    </div>
</template>

<script setup>
/**
 * Remarks about usage:
 * Slots: don't use label, it will mess up the switch
 * If you want to use the switch, but not the label, pass labelOn attribute as an empty string.
 * However, tooltip only works if labelOn <> ''
 * Dependency: v-tooltip
 */
import { computed } from 'vue';

const props = defineProps({
    labelOn: {
        type: String,
        required: true
    },
    labelOff: {
        type: String,
        default: ''
    },
    /*
    enum for type of value:
    string: 'on'|'off'
    boolean: true|false
    integer: 0|1
    */
    valueType: {
        type: String,
        default: 'boolean',
        validator: value => ['dummy', 'string', 'boolean', 'integer'].indexOf(value) > 0
    },
    color: {
        type: String,
        default: 'success'
    },
    switchId: {
        type: String,
        required: true
    },
    tooltip: {
        type: String,
        default: ''
    },
    // For Vue 3 v-model compatibility
    modelValue: {
        type: [String, Boolean, Number],
        default: false
    }
});

const emit = defineEmits(['update:modelValue', 'changed', 'change']);

/**
 * Update the label
 * @returns {String}
 */
const currentLabel = computed(() => {
    return (props.modelValue === 'on' || props.modelValue === '1' || props.modelValue === 1 || props.modelValue === true)
        ? props.labelOn
        : props.labelOff === '' ? props.labelOn : props.labelOff;
});

const inputVal = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        if (props.valueType === 'string') {
            emit('update:modelValue', val ? 'on' : '');
        } else if (props.valueType === 'boolean') {
            emit('update:modelValue', val);
        } else if (props.valueType === 'integer') {
            emit('update:modelValue', val ? 1 : 0);
        }
    }
});

const sendChangeEvent = () => {
    emit('changed', { data: inputVal.value });
    emit('change', inputVal.value);
};
</script>

<style lang="scss" scoped>
@import '../../../../sass/tmpl3/variables';
.material-switch {
    > input[type="checkbox"] {
        display: none;

        &:checked {
            + label::before {
                background: inherit;
                opacity: 0.5;
            }

            + label::after {
                background: inherit;
                left: 20px;
            }
        }
    }

    > label {
        cursor: pointer;
        height: 0;
        position: relative;
        width: 40px;

        &::before {
            background: rgb(0, 0, 0);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            content: '';
            height: 16px;
            margin-top: -8px;
            position: absolute;
            opacity: 0.3;
            transition: all 0.4s ease-in-out;
            width: 40px;
        }

        &::after {
            background: rgb(255, 255, 255);
            border-radius: 16px;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
            content: '';
            height: 24px;
            left: -4px;
            margin-top: -8px;
            position: absolute;
            top: -4px;
            transition: all 0.3s ease-in-out;
            width: 24px;
        }
    }

    .label-success {
        background-color: $classgreen;
    }
    .label-danger {
        background-color: $classred;
    }
}

</style>
