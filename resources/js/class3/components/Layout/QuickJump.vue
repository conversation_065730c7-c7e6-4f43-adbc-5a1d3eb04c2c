<template>
    <span>
        <button
            @click.prevent="noop"
            class="btn btn-success btn-sm"
            data-toggle="modal"
            :data-target="'#' + modalId"
        >{{buttonLabel}}</button>
        <modal
            :size="size"
            :modal-id="modalId"
            :popup-title="popupTitle"
            :closetext="ucFirst(translate('generic.close'))"
        >
            <div class="form-group">
                <div class="input-group searchbox mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="search-addon"><i class="fas fa-search"></i></span>
                    </div>
                    <input type="text" class="form-control" :placeholder="translate('generic.searchfromlist')"
                           aria-label="Searchbox" aria-describedby="search-addon"
                           v-model="searchKey">
                </div>
            </div>
            <div v-if="busy">
                <spinner-svg />
            </div>
            <div v-else class="overflow-popup">
                <div v-for="(record, index) in filteredRecords" :key="index">
                    <a :href="noop" @click.prevent="recordChosen(record.id)">
                        {{ showFieldsOfRecord(record) }}
                    </a>
                </div>
            </div>
        </modal>
    </span>
</template>

<script>
import Modal from '../Layout/Modal3';
import SpinnerSvg from '../Layout/SpinnerSvg';
import axios from 'axios';
import $ from 'jquery';
import useLang from "../../composables/useLang";
import useUtils from "../../composables/useUtils";

const { translate, translateChoice, ucFirst } = useLang();
const { uniqueId } = useUtils();

export default {
    name: 'QuickJump',
    components: { Modal, SpinnerSvg },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            records: [],
            searchKey: '',
            busy: false
        };
    },
    props: {
        size: {
            type: String,
            default: 'large'
        },
        excludeIds: {
            type: Array,
            default: () => []
        },
        popupTitle: {
            type: String,
            required: true
        },
        // comma separated list of fields
        displayFields: String,
        // api request to be called to retrieve te records
        apiGetString: {
            type: String,
            default: ''
        },
        useData: {
            type: Array,
            default: null
        },
        // label to show on toggle button
        buttonLabel: {
            type: String,
            default: 'Quick-Jump'
        }
    },
    computed: {
        filteredRecords () {
            return this.records.filter((record) => {
                return !this.excludeIds.includes(record.id) &&
                    record.name.toLowerCase().includes(this.searchKey.toLowerCase());
            });
        },
        /**
         * in case this component is used more than once on a page
         * the modal's ID must be unique
         */
        modalId () {
            return 'QJ_' + uniqueId();
        }
    },
    mounted () {
        this.getRecords();
    },
    methods: {
        getRecords () {
            if (this.useData === null) {
                this.busy = true;
                axios.get(this.apiGetString)
                    .then(response => {
                        this.records = response.data.data;
                    })
                    .catch(err => {
                        this.failNoty(`Error fetching records: ${err}`);
                    })
                    .finally(() => {
                        this.busy = false;
                    });
            } else {
                this.records = this.useData;
            }
        },
        /**
         * Merge fields to display into 1 string
         * @param record
         * @returns {string}
         */
        showFieldsOfRecord (record) {
            let retString = '';
            this.displayFields.split(',').forEach(fieldName => {
                let insertString = record[fieldName.trim()];
                // some tweaks for student groups
                insertString = insertString === null ? '' : insertString;
                if (insertString.charAt(0) === '-') insertString = insertString.substr(2);
                retString += insertString + ' - ';
            });
            return retString.slice(0, -3);
        },
        recordChosen (recordId) {
            $('#' + this.modalId).modal('hide');
            // because of async nature, the backdrop of the modal stays.
            // force it to be removed
            $('body').removeClass('modal-open');
            $('.modal-backdrop').remove();
            this.$emit('record-chosen', recordId);
        }
    }
};
</script>

<style scoped>
    .overflow-popup {
        max-height:  500px;
        overflow-y: auto;
    }
    a {
        text-decoration: underline;
    }
</style>
