<template>
    <div class="card">
        <!-- the toggle button-->
        <div :class="[{active: active}, 'card-header', 'pointer', 'accordion']" @click="toggleMe">
            <slot name="title"/>
        </div>
        <div class="card-body panel" ref="panel">
            <slot />
        </div>
    </div>
</template>

<script>
export default {
    name: 'Accordion',
    data () {
        return {
            active: false
        };
    },
    methods: {
        toggleMe () {
            this.active = !this.active;
            const panel = this.$refs.panel;
            if (panel.style.maxHeight) {
                panel.style.maxHeight = null;
            } else {
                panel.style.maxHeight = panel.scrollHeight + 'px';
            }
        }
    }
};
</script>

<style scoped>
.accordion {
    background-color: #eee;
    color: #444;
    cursor: pointer;
    padding: 18px;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 15px;
    transition: 0.4s;
}

.active, .accordion:hover {
    background-color: #ccc;
}

/** closed */
.accordion:after {
    content: "\2304"; /* chevron down */
    color: #777;
    font-weight: bold;
    float: right;
    margin-left: 5px;
}

/** open */
.active:after {
    content: "\2303"; /* chevron up */
}

.panel {
    padding: 0 18px;
    background-color: white;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
}
</style>
