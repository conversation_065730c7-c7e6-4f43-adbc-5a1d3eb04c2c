<template>
    <div :class="'card shadow my-2 ' + extraClass" :data-testid="panelTestId">
        <!-- Card Header -->
        <div
            class="card-header py-3 d-flex flex-row align-items-center justify-content-between"
            aria-expanded="true"
        >
            <h6 class="m-0 font-weight-bold text-primary">
                <slot name="title">Please add a title</slot>
            </h6>
            <span class="float-right">
                <slot name="subtitle" class="mr-1"></slot>
                <span
                    v-if="collapsible"
                    class="btn btn-primary btn-sm ml-2 px-3 collapse-button"
                    @click.prevent="triggerCollapse"
                >
                    <!-- can't use fontawesome reactive for some reason -->
                    <span v-if="isCollapsed" class="iconfont"> &#8711; </span>
                    <span v-else class="iconfont"> &#8710; </span>
                </span>
            </span>
        </div>
        <!-- Card Body - extra wrapper div to prevent stutter when collapsing (which is due to margin in card-body) -->
        <div :id="uid" :class="['collapse', { show: !isCollapsed }]">
            <div class="card-body">
                <spinner-svg v-if="busy" size="50px"/>
                <slot v-else>Please add some body content</slot>
            </div>
        </div>
    </div>
</template>

<script>
import SpinnerSvg from './SpinnerSvg';
import useUtils from "../../composables/useUtils";

const { uniqueId } = useUtils();

export default {
    name: 'Panel',
    components: { SpinnerSvg },
    data() {
        return {
            uid: uniqueId(), // needed when making collapsible
            isCollapsed: false // trigger classes for collapse and rotate icon
        };
    },
    props: {
        busy: {
            type: Boolean,
            default: false
        },
        collapsible: {
            type: Boolean,
            default: false
        },
        collapse: {
            type: Boolean,
            default: false
        },
        extraClass: {
            type: String,
            default: ''
        },
        panelTestId: {
            type: String,
            default: 'panel'
        }
    },
    methods: {
        triggerCollapse() {
            this.isCollapsed = !this.isCollapsed;
        }
    },
    mounted() {
        // start collapsed if collapse is true
        if (this.collapse) {
            this.isCollapsed = this.collapse;
        }
    }
};
</script>

<style scoped lang="scss">
@import "../../../../sass/tmpl3/variables";
/* to be able to use bootstrap breakpoints (mixin) */
@import "node_modules/bootstrap/scss/functions";
@import "node_modules/bootstrap/scss/variables";
@import "node_modules/bootstrap/scss/mixins/_breakpoints";

.card-header .form-check {
    margin-bottom: 0;
}

.point {
    cursor: pointer;
}

.rotate-if-collapsed {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

/*
    Number of Cards by Row based on Viewport
    1 Column: min-width: 50.1%;
    2 columns: min-width: 33.4%;
    3 columns: min-width: 25.1%;
    4 Columns: min-width: 20.1%;
    */
@include media-breakpoint-up(md) {
    .card-deck .card {
        min-width: 50.1%; /* 1 Columns */
    }
}

@include media-breakpoint-up(lg) {
    .card-deck .card {
        min-width: 33.4%; /* 2 Columns */
    }
}

.collapse-button {
    &:hover {
        background-color: $classblue;
        color: white !important;
    }
    /**
    * arrow in collapse button
    * can't use fontawesome reactively
    */
    &.iconfont {
        width: 3rem;
        font-size: 1rem;
        color: $classblue;

        &:hover {
            color: white;
        }
    }
}
</style>
