<template>
    <div class="input-group">
        <input type="text" class="form-control" v-model="minuteLabel" v-on:input="updateValue($event.target.value)"/>
        <div class="input-group-append">
            <button class="btn btn-sm btn-outline-primary" @click.prevent="doStep('left')">
                <i class="fas fa-caret-left"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary" @click.prevent="doStep('right')">
                <i class="fas fa-caret-right"></i>
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MinuteEntry',
    data () {
        return {
            minuteLabel: '',
            minuteIntValue: 0
        };
    },
    props: {
        initValue: {
            type: Number,
            default: 0
        },
        prependText: {
            type: String,
            default: ''
        },
        min: {
            type: Number,
            default: 0
        },
        max: {
            type: Number,
            default: 55
        },
        step: {
            type: Number,
            default: 5,
            validate (value) {
                return value < this.max;
            }
        }
    },
    mounted () {
        this.minuteLabel = this.prependText + (this.initValue < 10 ? '0' + this.initValue : this.initValue);
        this.minuteIntValue = this.initValue;
        this.validStartTime(this.minuteLabel);
    },
    methods: {
        /**
         * substract/add step from/to current minute value
         * taking boundaries into account
         * @param direction String 'left'|'right'
         */
        doStep (direction) {
            if (direction === 'left') {
                this.minuteIntValue = this.minuteIntValue - this.step;
                if (this.minuteIntValue <= 0) {
                    this.minuteIntValue = 0;
                }
            } else if (direction === 'right') {
                this.minuteIntValue = this.minuteIntValue + this.step;
                if (this.minuteIntValue >= this.max) {
                    this.minuteIntValue = this.max;
                }
            }
            // int2string
            this.minuteLabel = this.minuteIntValue < 10
                ? this.prependText + '0' + this.minuteIntValue
                : this.prependText + this.minuteIntValue;
            // emit change
            this.updateValue(this.minuteLabel);
        },
        /**
         * send updates to parent, so they can use v-model on this component
         * @param value
         */
        updateValue (value) {
            this.$emit('input', value);
        },
        validStartTime (value) {
            this.$emit('validStartTime', { initStartTime: value });
        }

    }
};
</script>

<style scoped>

</style>
