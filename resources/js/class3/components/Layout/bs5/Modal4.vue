<template>
    <div
        class="modal fade"
        :id="modalId"
        tabindex="-1"
        :aria-labelledby="`ModalLabel${modalId}`"
        aria-hidden="true"
        :data-bs-backdrop="hasBackdrop"
    >
        <div :class="[modalSize, 'modal-dialog modal-dialog-centered']" role="document">
            <div class="modal-content">
                <div :class="[{'moveable': isDraggable}, 'modal-header']">
                    <h5 class="modal-title" :id="`ModalLabel${modalId}`">{{ popupTitle }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <slot>
                        no popup content passed...
                    </slot>
                </div>
                <div class="modal-footer">
                    <slot name="popupmessage"></slot>
                    <button
                        type="button"
                        @click="closeBtnClicked"
                        class="btn btn-outline-secondary"
                        data-bs-dismiss="modal"
                        data-id="modalclosebutton">
                        {{ closetext }}
                    </button>
                    <slot name="okbutton"></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, computed } from 'vue';

const props = defineProps({
    closetext: {
        type: String,
        default: ''
    },
    popupTitle: {
        type: String,
        default: ''
    },
    modalId: {
        type: String,
        default: ''
    },
    size: {
        type: String,
        default: 'default'
    },
    isDraggable: {
        type: Boolean,
        default: false
    },
    boundingBox: {
        type: Object,
        default: () => {
            return {
                hor: 500, vert: 150
            };
        }
    }
});

const emit = defineEmits(['closeBtnClicked', 'closebtnclick']);

const closeBtnClicked = () => {
    // event to parent, in two variants (backwards compatible)
    emit('closeBtnClicked');
    emit('closebtnclick');
};

const modalSize = computed(() => {
    if (props.size.toLowerCase() === 'small') {
        return 'modal-sm';
    } else if (props.size.toLowerCase() === 'default') {
        return '';
    } else if (props.size.toLowerCase() === 'large') {
        return 'modal-lg';
    } else if (props.size.toLowerCase() === 'extralarge') {
        return 'modal-xl';
    } else {
        return '';
    }
});

const hasBackdrop = computed(() => {
    // please note: returns a string, not a boolean!
    return props.isDraggable ? 'static' : 'true';
});

onMounted(() => {
    if (props.isDraggable) {
        // Bootstrap 5 doesn't include jQuery, so we need to use a different approach for draggable
        // If you need draggable functionality, you'll need to add a separate library or implement it with vanilla JS
        const modalElement = document.getElementById(props.modalId);
        
        if (modalElement) {
            const modalDialog = modalElement.querySelector('.modal-dialog');
            const modalHeader = modalElement.querySelector('.modal-header');
            
            if (modalDialog && modalHeader) {
                let isDragging = false;
                let offsetX, offsetY;
                
                modalHeader.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    const rect = modalDialog.getBoundingClientRect();
                    offsetX = e.clientX - rect.left;
                    offsetY = e.clientY - rect.top;
                    modalDialog.style.margin = '0';
                    modalDialog.style.position = 'absolute';
                });
                
                document.addEventListener('mousemove', (e) => {
                    if (!isDragging) return;
                    
                    let newX = e.clientX - offsetX;
                    let newY = e.clientY - offsetY;
                    
                    // Boundary constraints
                    if (newX > 0) {
                        newX = Math.min(props.boundingBox.hor, newX);
                    } else {
                        newX = Math.max(-(props.boundingBox.hor), newX);
                    }
                    
                    newY = Math.max(-(props.boundingBox.vert), newY);
                    
                    modalDialog.style.left = `${newX}px`;
                    modalDialog.style.top = `${newY}px`;
                });
                
                document.addEventListener('mouseup', () => {
                    isDragging = false;
                });
            }
        }
    }
});
</script>

<style scoped>
.modal-header {
    background-color: var(--bs-primary);
    color: white;
}

.moveable {
    cursor: move;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}
</style>
