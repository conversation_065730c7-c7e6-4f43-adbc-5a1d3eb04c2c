<template>
    <div class="row floating-section-bottom float-right" v-if="!readOnly">
        <div class="d-flex flex-row-reverse">
            <div class="extra-spacing floating-content">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
defineProps({
    position: {
        type: String,
        default: 'bottom'
    },
    readOnly: {
        type: Boolean,
        default: false
    }
});
</script>

<style scoped>
    .floating-section-bottom {
        position: sticky;
        bottom: 1rem;
        left: 2rem;
    }
    .float-right {
        right: 1rem;
    }
    .floating-content {
        background-color: rgba(108, 117, 125, 0.3); /* Semitransparent version of badge-secondary */
        backdrop-filter: blur(3px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        border-radius: 0.2rem;
        padding: 0.5rem 1rem;
    }
</style>
