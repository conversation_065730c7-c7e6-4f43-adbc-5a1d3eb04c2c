<template>
    <modal
        :popup-title = "ucFirst(translate('generic.areyousure'))"
        :closetext   = "ucFirst(translate('generic.cancel'))"
        :modal-id     = "modalId"
    >
        <p v-if="confirmText !== ''" v-html="confirmText"></p>
        <p v-else>
            {{ucFirst(translate('generic.thiscannotbeundone'))}}
        </p>
        <slot></slot>
        <template v-slot:okbutton>
            <button
                type="button"
                class="btn btn-danger"
                data-dismiss="modal"
                @click.prevent="confirmClicked"
                data-testid="confirm-delete-button"
            >
                {{ buttonText === '' ? ucFirst(translate('generic.delete')) : buttonText }}
            </button>
        </template>
    </modal>
</template>

<script>
import Modal from './Modal3';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

export default {
    name: 'AreYouSure.vue',
    components: { Modal },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    props: {
        buttonText: {
            type: String,
            default: ''
        },
        modalId: {
            type: String,
            required: true
        },
        confirmText: {
            type: String,
            default: ''
        }
    },
    methods: {
        confirmClicked () {
            this.$emit('confirmclicked');
        }
    }
};
</script>

<style scoped>

</style>
