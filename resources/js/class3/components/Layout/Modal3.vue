<template>
    <div
        class="modal fade setAutoHeight"
        :id="modalId"
        tabindex="-1"
        role="dialog"
        :aria-labelledby="`ModalLabel${modalId}`"
        aria-hidden="true"
        :data-backdrop="hasBackdrop"
    >
        <div :class="[modalSize, 'modal-dialog modal-dialog-centered modal-border']" role="document">
            <div class="modal-content">
                <div :class="[{'moveable': isDraggable}, 'modal-header']">
                    <h5 class="modal-title" :id="`ModalLabel${modalId}`">{{ popupTitle }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <slot>
                        no popup content passed...
                    </slot>
                </div>
                <div class="modal-footer">
                    <slot name="popupmessage"></slot>
                    <button
                        type="button"
                        @click="closeBtnClicked"
                        class="btn btn-outline-secondary"
                        data-id="modalclosebutton"
                        data-dismiss="modal">
                        {{ closetext }}
                    </button>
                    <slot name="okbutton"></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import $ from 'jquery';

export default {
    name: 'Modal3',
    mounted () {
        if (this.isDraggable) {
            /*
            const modalElement = document.querySelector(`#${this.modalId}`);
            modalElement.style.overflowY = "hidden";
            */
            $(`#${this.modalId}`).draggable({
                handle: '.modal-header',
                drag: (event, ui) => {
                    // restrict drag inside bounding box
                    if (ui.position.left > 0) {
                        ui.position.left = Math.min(this.boundingBox.hor, ui.position.left);
                    } else {
                        ui.position.left = Math.max(-(this.boundingBox.hor), ui.position.left);
                    }
                    // for now, I won't restrict the drag towards bottom of screen
                    ui.position.top = Math.max(-(this.boundingBox.vert), ui.position.top);
                }
            });
        }
    },

    props: {
        closetext: {
            type: String,
            default: ''
        },
        popupTitle: {
            type: String,
            default: ''
        },
        modalId: {
            type: String,
            default: ''
        },
        okButtonCheckCallback: () => {}, // keep for backward compatibility
        size: {
            type: String,
            default: 'default'
        },
        isDraggable: {
            type: Boolean,
            default: false
        },
        boundingBox: {
            type: Object,
            default: () => {
                // todo test this if is a sensible default
                return {
                    hor: 500, vert: 150
                };
            }
        }
    },
    methods: {
        closeBtnClicked () {
            // event to parent, in two variants (backwards compatible)
            this.$emit('closeBtnClicked');
            this.$emit('closebtnclick');
        }
    },
    computed: {
        modalSize () {
            if (this.size.toLowerCase() === 'small') {
                return 'modal-sm';
            } else if (this.size.toLowerCase() === 'default') {
                return '';
            } else if (this.size.toLowerCase() === 'large') {
                return 'modal-lg';
            } else if (this.size.toLowerCase() === 'extralarge') {
                return 'modal-xl';
            } else {
                return '';
            }
        },
        hasBackdrop () {
            // please note: returns a string, not a boolean!
            return this.isDraggable ? 'false' : 'true';
        }
    }
};
</script>

<style scoped lang="scss">
@import '../../../../sass/tmpl3/variables';

.modal-header {
    background-color: $classbgblue;
    color: white;

    & .close {
        color: white;
        font-size: 2rem;
    }
}

.moveable {
    cursor: move;
}

/**
 * fix missing footer:
 * don't set height to 100% but make it dynamic
 */
.setAutoHeight {
    height: 100vh;
}

.modal-body {
    height: 80vh;
    overflow-y: hidden;
}

.modal-border {
    border-radius: 5px;
}
</style>
