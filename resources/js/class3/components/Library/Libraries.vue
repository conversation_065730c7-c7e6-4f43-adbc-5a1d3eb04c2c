<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translateChoice('generic.libraries', 2)) }}</h3>
        </template>
        <template #subtitle>
            <button
                class="btn btn-primary"
                @click.prevent="emptyLibrary"
            >
                {{ ucFirst(translate('generic.newlibrary')) }}
            </button>
        </template>
        <div class="row">
            <div class="col-12 col-xl-7 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <ListLibraries />
                    </div>
                </div>
            </div>
            <div class="col-12 col-xl-5 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <EditLibraries />
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import Panel from "../Layout/bs5/Panel4.vue";
import ListLibraries from "./ListLibraries.vue";
import EditLibraries from "./EditLibraries.vue";
import useLang from "../../composables/useLang";
import useLibraries from "../../composables/useLibraries";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, emptyLibrary, getLibraries } = useLibraries();

onMounted(() => {
    getLibraries();
});
</script>

<style scoped>

</style>
