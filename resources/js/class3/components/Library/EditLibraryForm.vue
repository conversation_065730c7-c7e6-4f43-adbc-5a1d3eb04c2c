<template>
    <div>
        <div class="mb-3">
            <label for="name" class="form-label">{{ ucFirst(translate("generic.name")) }}</label>
            <input type="text" id="name" v-model="libraryToEdit.label" class="form-control" />
        </div>
        <hr>
        <!-- submit button -->
        <button class="btn btn-primary" @click="saveLibrary">{{ ucFirst(translate("generic.save")) }}</button>
    </div>
</template>

<script setup>
import useLibraries from "../../composables/useLibraries";
import useLang from "../../composables/useLang";

const { libraryToEdit, saveLibrary } = useLibraries();
const { translate, ucFirst } = useLang();
</script>

<style scoped>

</style>
