<template>
<div>
    <strong>{{ ucFirst(translate("generic.sharing")) }}</strong>
    <div class="d-flex justify-content-between">
        <p>{{ ucFirst(translate("generic.sharewithwholeschool")) }}</p>
        <material-switch
            label-on=""
            switch-id="shareWholeSchool"
            v-model="shareWholeSchool"
            value-type="boolean"
        />
    </div>
    <template v-if="!shareWholeSchool && libraryToManageShares.id">
        <hr>
        <p>{{ ucFirst(translate("generic.share_with")) }}</p>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.courses", 2)) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareCoursesSelected"
                    :options="coursesOptionsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select courses"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedCoursesDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="course in selectedCoursesDisplay" :key="course.id" class="badge bg-primary">
                            {{ course.label }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translate("generic.coursegroups")) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareCourseGroupsSelected"
                    :options="courseGroupsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select course groups"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedCourseGroupsDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="courseGroup in selectedCourseGroupsDisplay" :key="courseGroup.id" class="badge bg-success">
                            {{ courseGroup.label }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.students", 2)) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareStudentsSelected"
                    :options="studentsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select students"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedStudentsDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="student in selectedStudentsDisplay" :key="student.id" class="badge bg-info">
                            {{ student.label }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.studentgroups")) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareStudentGroupsSelected"
                    :options="studentGroupsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select student groups"
                    valueProp="id"
                    label="label"
                />
                <div v-if="selectedStudentGroupsDisplay.length > 0" class="mt-2">
                    <small class="text-muted">{{ ucFirst(translate("generic.sharedwith")) }}:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <span v-for="studentGroup in selectedStudentGroupsDisplay" :key="studentGroup.id" class="badge bg-warning text-dark">
                            {{ studentGroup.label }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </template>
    <hr>
    <div class="d-flex justify-content-end">
        <!-- Save button -->
        <button class="btn btn-primary" @click="saveSharesLibrary">
            {{ ucFirst(translate("generic.save")) }}
        </button>
    </div>
</div>
</template>

<script setup>
import { computed, onMounted, watch } from "vue";
import useLang from "../../composables/useLang";
import MaterialSwitch from "../Layout/MaterialSwitch.vue";
import useBaseData from "../../composables/useBaseData";
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';
import useLibraries from "../../composables/useLibraries";

const { allCourses, allCourseGroups, allStudentGroups, allStudents, initBaseData } = useBaseData();
const { ucFirst, translate, translateChoice } = useLang();
const {
    saveSharesLibrary,
    libraryToManageShares,
    shareWholeSchool,
    shareCoursesSelected,
    shareCourseGroupsSelected,
    shareStudentGroupsSelected,
    shareStudentsSelected
} = useLibraries();

/**
 * strip the api data to only include the id and name
 */
const coursesOptionsForSelect = computed(() => {
    return allCourses.value.map(course => ({
        id: course.id,
        label: `${course.name} ${course.recurrenceoption}`
    }));
});
const courseGroupsForSelect = computed(() => {
    return allCourseGroups.value.map(courseGroup => ({
        id: courseGroup.id,
        label: courseGroup.name
    }));
});
const studentsForSelect = computed(() => {
    return allStudents.value.map(student => ({
        id: student.id,
        label: student.name
    }));
});
const studentGroupsForSelect = computed(() => {
    return allStudentGroups.value.map(studentGroup => ({
        id: studentGroup.id,
        label: studentGroup.name
    }));
});

// Computed properties to display selected items
const selectedCoursesDisplay = computed(() => {
    return coursesOptionsForSelect.value.filter(course =>
        shareCoursesSelected.value.includes(course.id)
    );
});

const selectedCourseGroupsDisplay = computed(() => {
    return courseGroupsForSelect.value.filter(courseGroup =>
        shareCourseGroupsSelected.value.includes(courseGroup.id)
    );
});

const selectedStudentsDisplay = computed(() => {
    return studentsForSelect.value.filter(student =>
        shareStudentsSelected.value.includes(student.id)
    );
});

const selectedStudentGroupsDisplay = computed(() => {
    return studentGroupsForSelect.value.filter(studentGroup =>
        shareStudentGroupsSelected.value.includes(studentGroup.id)
    );
});

onMounted(() => {
    initBaseData({
        courses: true,
        courseGroups: true,
        studentGroups: true,
        students: true
    }).then(() => {
        initLibraryShares();
    });
});
const initLibraryShares = () => {
    shareWholeSchool.value = !!libraryToManageShares.value.share_with_whole_school; // convert initial value to boolean
    shareCoursesSelected.value = libraryToManageShares.value.courses
        ? libraryToManageShares.value.courses.map(course => course.id)
        : [];
    shareCourseGroupsSelected.value = libraryToManageShares.value.coursegroups
        ? libraryToManageShares.value.coursegroups.map(courseGroup => courseGroup.id)
        : [];
    shareStudentsSelected.value = libraryToManageShares.value.students
        ? libraryToManageShares.value.students.map(student => student.id)
        : [];
    shareStudentGroupsSelected.value = libraryToManageShares.value.studentgroups
        ? libraryToManageShares.value.studentgroups.map(studentGroup => studentGroup.id)
        : [];
};

watch(libraryToManageShares, () => {
    initLibraryShares();
});
</script>

<style scoped>

</style>
