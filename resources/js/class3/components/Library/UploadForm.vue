<template>
    <div>
        <div class="input-group mb-3" v-if="initiateUpload">
            <input type="file" class="form-control" id="inputGroupFile02" v-on:change="setFileToUpload">
            <button
                v-if="fileToUpload != null"
                class="btn btn-success float-end"
                @click.prevent="uploadFile(uploadSuccess)"
            >
                {{ translate('generic.upload') }}
                <font-awesome-icon icon="upload"/>
            </button>
        </div>
        <button
            v-if="!initiateUpload"
            class="btn btn-sm btn-outline-primary"
            @click.prevent="initiateUpload=true"
        >
            {{ initiateUploadButtonText }}
            <font-awesome-icon icon="plus"/>
        </button>
    </div>
</template>

<script setup>
import { ref } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import useUploadForm from "../../composables/useUploadForm";
import useLang from "../../composables/useLang";

const emits = defineEmits(['uploadSuccess']);

const { translate } = useLang();

defineProps({
    initiateUploadButtonText: {
        type: String,
        required: true
    }
});
const {
    fileToUpload,
    setFileToUpload,
    uploadFile
} = useUploadForm();
const initiateUpload = ref(false);

const uploadSuccess = (docId) => {
    console.log('uploadSuccess', docId);
    emits('uploadSuccess', docId);
}
</script>
