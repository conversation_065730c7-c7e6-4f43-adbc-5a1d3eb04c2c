<template>
    <panel :busy="busy" v-show="filteredData.length > 0 || busy">
        <template v-slot:title>
            <i :class="['fa', icon]"></i>
            {{ paneltitle }}
        </template>
        <template v-if="filteredData.length > 0">
            <table class="table">
                <tr v-for="item in filteredData" :key="item.entity_id">
                    <td v-if="item.showas === 'alert'" class="text-danger">
                        <span v-tooltip="translate('generic.highpriority')">
                            <i class="fas fa-exclamation-triangle"></i>
                        </span>
                    </td>
                    <td v-else>
                        &nbsp;
                    </td>

                    <td>{{ item.message }}</td>
                    <td><a :href="`/students/${item.student.id}/edit`">{{ item.student.name }}</a></td>
                    <td><a :href="item.link">{{ ucFirst(translate('generic.solutionlink')) }}</a></td>
                </tr>
            </table>
        </template>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import useLang from '../../composables/useLang';
import Panel from '../Layout/Panel';

const props = defineProps({
    token: {
        type: String,
        default: ''
    },
    paneltitle: {
        type: String,
        required: true
    },
    subject: {
        type: String,
        required: true
    },
    icon: {
        type: String,
        default: 'fa-bell'
    }
}
);

const data = ref([]);
const busy = ref(false);
const { translate, ucFirst } = useLang();

onMounted(async () => {
    busy.value = true;
    const response = await fetch(`api/singlealertstatus?subject=${props.subject}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'X-CSRF-Token': props.token
        }
    });
    data.value = await response.json();
    busy.value = false;
});

/**
 * remove status and error elements
 * @type {ComputedRef<*>}
 */
const filteredData = computed(() => {
    return data.value?.status
        ? data.value.status
        : [];
});
</script>

<style scoped>

</style>
