<template>
    <div>
        <panel>
            <template v-slot:title>
                {{ ucFirst(translate('generic.unreadmessages')) }}<br/>
            </template>
            <template v-slot:subtitle>
                <!-- nog even niet
                <button
                    class="btn btn-success"
                    @click="createNewMessage"
                >
                    {{ucFirst(translate('generic.newmessage'))}}
                </button>-->
            </template>
            <table class="table" v-if="unreadMessages.length > 0">
                <thead>
                <tr>
                    <!-- nog even niet
                    <th class="text-center">{{ ucFirst(translate('generic.delete')) }}</th>
                    -->
                    <th class="text-center">{{ ucFirst(translate('generic.open')) }}</th>
                    <th>{{ ucFirst(translate('generic.createdat'))}}</th>
                    <th>{{ ucFirst(translate('generic.subject'))}}</th>
                    <th>{{ ucFirst(translate('generic.recipient'))}}</th>
                    <th>{{ ucFirst(translateChoice('generic.messages', 1))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(message, index) in unreadMessages" :key="index">
                    <td class="text-center">
                        <button class="btn-success btn-sm" @click.prevent="openDetail(message)">
                            <i class="fa fa-eye"></i>
                        </button>
                    </td>
                    <td>{{ displayDateTime(message.created_at) }}</td>
                    <td>{{ message.subject }}</td>
                    <td>{{ message.to_label }}</td>
                    <td class="ellipsis">{{ message.body }}</td>
                </tr>
                </tbody>
            </table>
            <div v-else>
                {{ ucFirst(translate('generic.nomessagesfound'))}}
            </div>
        </panel>
        <panel>
            <template v-slot:title>
                {{ ucFirst(translate('generic.readmessages')) }}<br/>
            </template>
            <table class="table" v-if="readMessages.length > 0">
                <thead>
                <tr>
                    <th class="text-center">{{ ucFirst(translate('generic.open')) }}</th>
                    <th>{{ ucFirst(translate('generic.createdat'))}}</th>
                    <th>{{ ucFirst(translate('generic.readat'))}}</th>
                    <th>{{ ucFirst(translate('generic.subject'))}}</th>
                    <th>{{ ucFirst(translate('generic.recipient'))}}</th>
                    <th>{{ ucFirst(translateChoice('generic.messages', 1))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(message, index) in readMessages" :key="index">
                    <td class="text-center">
                        <button class="btn-success btn-sm" @click.prevent="openDetail(message)">
                            <i class="fa fa-eye"></i>
                        </button>
                    </td>
                    <td>{{ displayDateTime(message.created_at) }}</td>
                    <td>{{ displayDateTime(message.read_at) }}</td>
                    <td>{{ message.subject }}</td>
                    <td>{{ message.to_label }}</td>
                    <td class="ellipsis">{{ message.body }}</td>
                </tr>
                </tbody>
            </table>
            <div v-else>
                {{ ucFirst(translate('generic.nomessagesfound'))}}
            </div>
        </panel>
        <!-- nog even niet
        <are-you-sure
            :button-text    = "ucFirst(translate('generic.deletemessage'))"
            modal-id        = "confirm-delete-message"
            @confirmclicked = "deleteTutor(messageToDelete)"
        ></are-you-sure>
        -->
        <modal
            modal-id="detailview"
            :closetext="ucFirst(translate('generic.close'))"
            :popup-title="detailMessage != null ? detailMessage.subject : ''"
            size="large"
        >
            <div class="row">
                <div class="col">
                    <div>{{ ucFirst(translate('generic.fromemail')) }}: {{ detailMessage.from_label }}</div>
                    <div>{{ ucFirst(translate('generic.to')) }}: {{ detailMessage.to_label }}</div>
                </div>
                <div class="col">
                    <div>
                        {{ ucFirst(translate('generic.createdat')) }}: {{ displayDateTime(detailMessage.created_at) }}
                    </div>
                    <div v-if="detailMessage.read_at">
                        {{ ucFirst(translate('generic.readat')) }}: {{ displayDateTime(detailMessage.read_at) }}
                    </div>
                </div>
            </div>
            <hr>
            <div>
                {{ detailMessage.body }}
            </div>
        </modal>
    </div>
</template>

<script>
import { computed, onMounted, ref } from 'vue';
import Panel from '../Layout/Panel';
import Modal from '../Layout/Modal3';
import useLang from '../../composables/useLang';
import useDateTime from '../../composables/useDateTime';
import axios from 'axios';
import $ from 'jquery';

export default {
    name: 'ListMessages',
    components: { Modal, Panel },
    setup () {
        const { ucFirst, translate, translateChoice } = useLang();
        const { displayDateTime } = useDateTime();
        const messages = ref([]);
        const detailMessage = ref({
            from_label: '',
            to_label: '',
            created_at: '',
            subject: '',
            body: ''
        });

        const getAllMessages = async () => {
            const resp = await axios.get('/api/messages');
            messages.value = resp.data;
        };

        onMounted(async () => {
            await getAllMessages();
        });

        const unreadMessages = computed(() => {
            return messages.value.filter(row => row?.read_at == null || row?.read_at === '');
        });
        const readMessages = computed(() => {
            return messages.value.filter(row => row?.read_at != null && row?.read_at.length > 0);
        });

        const openDetail = (message) => {
            detailMessage.value = message;
            $('#detailview').modal();
        };

        return {
            detailMessage,
            displayDateTime,
            openDetail,
            readMessages,
            translate,
            translateChoice,
            ucFirst,
            unreadMessages
        };
    }
};
</script>

<style scoped>
.ellipsis {
    display: block;
    width: 25rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
