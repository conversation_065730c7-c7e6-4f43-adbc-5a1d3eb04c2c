<template>
    <panel :busy="loading" panel-test-id="student-card-logbook">
        <template v-slot:title>
            <i class="fas fa-comments"></i>
            {{ucFirst(translate('generic.logbook'))}}
        </template>
        <template v-slot:subtitle>
            <button type="button" v-tooltip="ucFirst(translate('generic.newlogentry'))"
                    class="btn btn-sm btn-success" @click.prevent="setLogentry(null)"
                    data-toggle="modal" data-target="#logbookEntryModal">
                {{ucFirst(translate('generic.new'))}}
            </button>
        </template>

        <!-- only show the table if we have logentries -->
        <table class="table table-striped" v-if="logentries.length > 0">
            <colgroup>
                <col style="width: 15%">
                <col style="width: 30%">
                <col style="width: 30%">
                <col style="width: 25%">
            </colgroup>
            <thead>
                <tr>
                    <th>{{ucFirst(translate('generic.functions'))}}</th>
                    <th>{{ucFirst(translate('generic.datetimecreated'))}}</th>
                    <th>{{ucFirst(translate('generic.datetimeupdated'))}}</th>
                    <th>{{ucFirst(translate('generic.logbookentry'))}}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="logentry in logentries" :key="logentry.id">
                    <td>
                        <div class="form-control-static">
                            <span v-tooltip="ucFirst(translate('generic.delete'))">
                                <span data-toggle="modal"
                                      data-target="#delLogEntry"
                                      class="btn btn-sm btn-outline-danger"
                                      @click.prevent="setLogentryToDelete(logentry)">
                                    <i class="fas fa-trash"></i>
                                </span>
                            </span>
                            <span v-tooltip="ucFirst(translate('generic.openlogentry'))">
                                <span data-toggle="modal"
                                      data-target="#logbookEntryModal"
                                      class="btn btn-sm btn-outline-primary"
                                      @click.prevent="setLogentry(logentry)">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </span>
                        </div>
                    </td>
                    <td>{{displayDateTime(logentry.created_at)}}</td>
                    <td>{{displayDateTime(logentry.updated_at)}}</td>
                    <td><div class="breakoff-text">{{ stripHtml(logentry.entry) }}</div></td>
                </tr>
            </tbody>
        </table>

        <!-- No entries found -->
        <div v-if="logentries.length===0" class="text-warning">{{ucFirst(translate('generic.nologentriesfound'))}}</div>

        <!-- POPUP DELETE -->
        <modal :popup-title = "ucFirst(translate('generic.areyousure'))"
               :closetext   = "ucFirst(translate('generic.cancel'))"
               modal-id     = "delLogEntry">
            <p>
                {{ucFirst(translate('generic.thiscannotbeundone'))}}
            </p>
            <p>
                <strong>{{ucFirst(translate('generic.wewillbedeleting'))}}:</strong><br/>
                <span style="display: inline-block" class="breakoff-text text-danger">
                    {{ stripHtml(logentryToDelete.entry) }}
                </span>
            </p>
            <template v-slot:okbutton>
                <button
                    type="button"
                    class="btn btn-danger"
                    data-dismiss="modal"
                    @click.prevent="deleteLogentry()"
                >
                    {{ ucFirst(translate('generic.deletethislogentry')) }}
                </button>
            </template>
        </modal>

        <!-- POPUP new / edit -->
        <modal :popup-title="ucFirst(translate('generic.logbookentry'))"
               :closetext="ucFirst(translate('generic.close'))"
               modal-id="logbookEntryModal"
               size="large">

            <div class="row">
                <label v-if="logentry.created_at !== ''" class="col-5">
                    {{ucFirst(translate('generic.datetimecreated'))}}:
                </label>
                <span class="col-7">{{ displayDateTime(logentry.created_at) }}</span>
            </div>
            <div class="row">
                <label v-if="logentry.updated_at !== ''" class="col-5">
                    {{ucFirst(translate('generic.datetimeupdated'))}}:
                </label>
                <span class="col-7">{{ displayDateTime(logentry.updated_at) }}</span>
            </div>

<!--            <ckeditor :editor="editor" v-model="logentry.entry"></ckeditor>-->

            <template v-slot:okbutton>
                <button
                    type="button"
                    class="btn btn-primary"
                    data-dismiss="modal"
                    @click.prevent="saveLogEntry()"
                >
                    {{ ucFirst(translate('generic.savelogentry')) }}
                </button>
            </template>
        </modal>

    </panel>
</template>

<script>
import Panel from '../Layout/Panel';
import Modal from '../Layout/Modal3';
// import CKEditor from '@ckeditor/ckeditor5-vue';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import axios from 'axios';
import useLang from "../../composables/useLang";
import useDateTime from "../../composables/useDateTime";
import useUtils from "../../composables/useUtils";

const { translate, translateChoice, ucFirst } = useLang();
const { displayDateTime } = useDateTime();
const { stripHtml } = useUtils();

export default {
    name: 'StudentLog',
    setup() {
        return {
            displayDateTime,
            stripHtml,
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            //editor: ClassicEditor,
            loading: false,
            logentry: {}, // current logentry (when editing)
            logentries: [], // all logentries
            currentEntryIndex: 0, // index of logentry in logentries
            logentryToDelete: { entry: '' } // used in confirm popup
        };
    },
    props: {
        studentid: {
            type: Number,
            required: true
        }
    },
    // ckeditor: CKEditor.component
    components: { Modal, Panel },
    mounted () {
        this.getLogentries();
    },
    methods: {
        getLogentries () {
            this.loading = true;
            axios.get(`/api/studentlogentries/${this.studentid}`)
                .then(resp => {
                    this.logentries = resp.data.data;
                })
                .catch(err => {
                    this.failNoty(`Error retrieving student logentries: ${err}`);
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        /**
             * Setup popup fields for edit or new logentry
             * @param logentry
             */
        setLogentry (logentry) {
            // a new entry will have null as logentry (new, not edit)
            // this will also clear a value if an edit action was clicked before "new'
            if (!logentry) {
                this.logentry = {};
            } else {
                this.logentry = logentry;
            }
        },

        /**
             * save and update logentry in the backend
             */
        saveLogEntry () {
            const logid = this.logentry.id || 0;
            const axiosURL = `/api/studentlogentry/${logid}`;
            const data = { entry: this.logentry.entry, student_id: this.studentid };

            axios.post(axiosURL, data)
                .then((response) => {
                    this.getLogentries();
                    this.successNoty(ucFirst(translate('generic.savesuccess')));
                })
                .catch((error) => {
                    this.failNoty(ucFirst(translate('generic.savingfailed')) + ` - ${error}`);
                });
        },

        /**
             * this entry will be shown in the confirm delete popup,
             * so the user knows which entry will be deleted
             * @param logentryToDelete
             */
        setLogentryToDelete (logentryToDelete) {
            this.logentryToDelete = logentryToDelete;
        },

        /**
             * Delete 1 logentry
             * Confirm will be in the popup that precedes this action
             */
        deleteLogentry () {
            axios.delete('/api/studentlogentry/' + this.logentryToDelete.id)
                .then((response) => {
                    this.getLogentries();
                    this.successNoty(ucFirst(translate('generic.deletesuccessful')));
                })
                .catch((error) => {
                    this.failNoty(ucFirst(translate('generic.deletefailed')) + ` - ${error}`);
                });
        }
    }
};
</script>

<style>
.breakoff-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}
</style>
