<template>
<div>
    <panel :collapsible="true" :collapse="defaultCollapsed" :busy="isBusy">
        <template #title>
            {{ description }}
        </template>
        <table class="table" v-if="students.length > 0">
            <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.studentname')) }}</th>
                <th>{{ ucFirst(translate('generic.city')) }}</th>
                <th>{{ ucFirst(translate('generic.email')) }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(student, index) in students" :key="index">
                <td>{{ student.name }}</td>
                <td>{{ student?.city || '-' }}</td>
                <td><a :href="student.email">{{ student.email }}</a></td>
            </tr>
            </tbody>
        </table>
        <div v-else>
            {{ ucFirst(translate('generic.nostudentsfound'))}}
        </div>
    </panel>
</div>
</template>

<script setup>
import { computed, onMounted, ref } from "vue";
import useLang from "../../composables/useLang";
import useNoty from "../../composables/useNoty";
const { translate, translateChoice, ucFirst } = useLang();
const { failNoty } = useNoty();
const students = ref([]);
const isBusy = ref(false);

const props = defineProps({
    studentsFilter: {
        type: String,
        required: true,
    },
    defaultCollapsed: {
        type: Boolean,
        default: false,
    },
});

const description = computed(() => {
    let returnString = "";
    switch (props.studentsFilter) {
        case 'atLeastOneCourse':
            returnString = ucFirst(translate('generic.explainatleastonecourse'));
            break;
        case 'studentsNoCourses':
            returnString = ucFirst(translate('generic.explainstudentsnocourses'));
            break;
        default:
            returnString = "";
            break;
    }
    return `${returnString} (${students.value?.length || 0})`;
});

const lookupUrl = computed(() => {
    switch (props.studentsFilter) {
        case 'atLeastOneCourse':
            return '/api/students/studentsactive';
        case 'studentsNoCourses':
            return '/api/students/studentsnocourses';
        default:
            return '/api/students';
    }
});

onMounted(async () => {
    await getStudents();
});

const getStudents = async () => {
    try {
        isBusy.value = true;
        const resp = await axios.get(lookupUrl.value);
        students.value = resp.data.data;
        isBusy.value = false;
    } catch (error) {
        failNoty(error);
    }
};
</script>

<style scoped>

</style>
