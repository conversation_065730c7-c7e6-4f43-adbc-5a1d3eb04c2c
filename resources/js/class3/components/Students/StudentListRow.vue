<template>
    <div class="row">
        <div class="col-1">
            <div class="form-check form-switch mt-2">
                <input 
                    class="form-check-input" 
                    type="checkbox"
                    :id="'studentListParticipation' + studentlist.id"
                    v-model="myParticipation"
                    v-tooltip="translate('generic.participation')"
                >
            </div>
        </div>
        <div class="col-1">
            <a
                class="btn btn-outline-success btn-sm"
                v-tooltip="ucFirst(translate('generic.edit'))"
                :href="'/studentlists/' + studentlist.id + '/edit'"
            >
                <i class="fas fa-edit"></i>
            </a>
        </div>

        <div class="col-1">
            <span
                class="dot"
                :style="'background-color: #' + studentlist.hexcolor "
            />
        </div>
        <div class="col-6">
            {{studentlist.name}}
            <span v-tooltip="translate('generic.totalnrofstudentsonthislist')">
                ({{ studentlist.students.length }})
            </span>
        </div>
        <div class="col-3 text-truncate">
            <span v-tooltip="studentlist.remarks">{{studentlist.remarks}}</span>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import useLang from "../../composables/useLang";

const props = defineProps({
    studentlist: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['changed']);

const { translate, translateChoice, ucFirst } = useLang();

const myParticipation = computed({
    get() {
        return props.studentlist.participating;
    },
    set(value) {
        emit('changed', { 
            participating: value ? 'on' : '', 
            listid: props.studentlist.id 
        });
    }
});
</script>

<style scoped>
.dot {
    height: 25px;
    width: 25px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
}

/* Bootstrap 5 switch customization */
.form-check-input {
    cursor: pointer;
}

.form-switch .form-check-input {
    width: 2.5em;
    height: 1.25em;
}
</style>
