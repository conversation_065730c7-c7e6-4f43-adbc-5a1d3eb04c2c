<template>
    <panel :busy="busy" panel-test-id="student-card-contact">
        <template v-slot:title>
            <i class="fas fa-address-card"></i>&nbsp;{{ucFirst(translate('generic.contactdata'))}}
        </template>
        <template v-slot:subtitle>
            <button type="button" v-tooltip="ucFirst(translate('generic.newcontactitem'))"
                    class="btn btn-sm btn-success" @click.prevent="addContactItemRow">
                {{ucFirst(translate('generic.new'))}}
            </button>
        </template>
        <table class="table ">
            <colgroup>
                <col style="width:100px"/>
                <col style="width:150px"/>
                <col style="width:250px"/>
                <col style="width:250px"/>
                <col />
            </colgroup>
            <thead>
            <tr>
                <td colspan="4" :class="{'alert alert-warning': dirty}">
                    <span v-if="dirty">
                        {{ucFirst(translate('generic.pleasesavechanges'))}}
                    </span>&nbsp;
                </td>
                <td colspan="3" class="bl br">
                    {{ucFirst(translate('generic.usefor'))}}
                    <span class="badge badge-pill badge-primary ml-2"
                          v-tooltip="ucFirst(translate('generic.explainusefor'))"
                    >
                        <i class="fas fa-question"></i>
                    </span>
                </td>
                <td colspan="2">&nbsp;</td>
            </tr>
            <tr>
                <th>{{ucFirst(translate('generic.functions'))}}</th>
                <th>{{ucFirst(translate('generic.type'))}}</th>
                <th>{{ucFirst(translate('generic.label'))}} {{translate('generic.explainstudentcontactlabel')}}</th>
                <th>{{ucFirst(translate('generic.value'))}}</th>
                <th class="bl">
                    {{ucFirst(translate('generic.useforfinance'))}}
                </th>
                <th>{{ucFirst(translate('generic.useforplanning'))}}</th>
                <th class="br">{{ucFirst(translate('generic.useforpromotions'))}}</th>
                <th>{{ucFirst(translate('generic.usesalutation'))}}</th>
                <th>{{ucFirst(translateChoice('generic.links',1))}}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(contact,key) in contacts" :key="contact.id">
                <td>
                    <div class="form-control-static">
                            <span v-tooltip="ucFirst(translate('generic.delete'))"
                                  class="btn btn-sm btn-danger"
                                  @click.prevent="deleteRow(contact.id)"
                            >
                                <span class="fas fa-trash"></span>
                            </span>
                    </div>
                </td>
                <td>
                    <select class="form-control"
                            v-model="contact.contacttype"
                            @change="dirty=true"
                            :name="'contacttype_' + contact.id"
                    >
                        <option value="telephone">telefoon</option>
                        <option value="email" selected="selected">e-mail</option>
                    </select>
                </td>
                <td>
                    <input type="text"
                           class="form-control"
                           v-model="contact.label"
                           @keydown="dirty=true"
                           :name="'contactlabel_' + contact.id"
                    />
                </td>
                <td>
                    <div class="input-group mb-3" v-if="contact.contacttype === 'telephone'">
                        <input :id="'contactValue_' + key"
                               class="form-control"
                               v-model="contact.value"
                               @keydown="dirty=true"
                               :name="'contactvalue_' + contact.id"
                        />
                        <div class="input-group-append">
                            <button
                                class="btn btn-outline-secondary"
                                type="button"
                                @click.prevent="formatIfPhonenumber(key)"
                                v-tooltip="ucFirst(translate('generic.formatphonenumber'))"
                            >
                                <i class="fas fa-phone"></i>
                            </button>
                        </div>
                    </div>
                    <input v-else
                           :id="'contactValue_' + key"
                           class="form-control"
                           v-model="contact.value"
                           @keydown="dirty=true"
                           :name="'contactvalue_' + contact.id"
                    />
                </td>
                <td v-if="!contact.id.toString().includes('new')">
                    <div class="checkbox" v-if="!contact.id.toString().includes('new')">
                        <input type="checkbox" class="regular-checkbox" value="1"
                               @change="saveApplyForValue('finance', key)"
                               v-model="contact.apply_for_finance"/>
                    </div>
                </td>
                <td v-if="!contact.id.toString().includes('new')">
                    <div class="checkbox" v-if="!contact.id.toString().includes('new')">
                        <input type="checkbox" class="regular-checkbox" value="1"
                               @change="saveApplyForValue('planning', key)"
                               v-model="contact.apply_for_planning"/>
                    </div>
                </td>
                <td v-if="!contact.id.toString().includes('new')">
                    <div class="checkbox" v-if="!contact.id.toString().includes('new')">
                        <input type="checkbox" class="regular-checkbox" value="1"
                               @change="saveApplyForValue('promotions', key)"
                               v-model="contact.apply_for_promotions"/>
                    </div>
                </td>
                <td colspan="3" v-else><em>{{ucFirst(translate('generic.pleasesavefirst'))}}</em></td>
                <td>
                    <input type="text"
                           class="form-control"
                           v-model="contact.use_salutation"
                           @keydown="dirty=true"
                           :name="'contactsalutation_' + contact.id"
                    />
                </td>
                <td>
                    <div class="form-control-static">
                        <a v-if="contact.contacttype === 'telephone'" :href="'tel:'+contact.value">{{ contact.value }}</a>
                        <a v-else :href="'mailto:' + contact.value" target="_blank">{{ contact.value }}</a>
                    </div>
                </td>
            </tr>
            <tr v-if="contacts.length === 0">
                <td colspan="3">{{ucFirst(translate('generic.nocontactsfound'))}}</td>
            </tr>
            </tbody>
        </table>
    </panel>
</template>

<script>
import Panel from '../Layout/Panel';
import axios from 'axios';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

export default {
    name: 'StudentContact',
    components: { Panel },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            dirty: false,
            busy: false,
            contacts: []
        };
    },
    mounted () {
        this.getStudentContacts();
    },

    props: {
        studentId: {
            type: String,
            required: true
        }
    },
    watch: {
        dirty () {
            // propagate the dirty flag to 'window' (global) to be able
            // to use it in the window.onbeforeunload handler
            window.dirty = this.dirty;
        }
    },
    methods: {
        getStudentContacts () {
            this.busy = true;
            axios.get(`/api/studentcontacts/${this.studentId}`)
                .then(resp => {
                    this.contacts = resp.data.data;
                })
                .catch(err => {
                    this.failNoty(`Error retrieving student email: ${err}`);
                })
                .finally(() => {
                    this.busy = false;
                });
        },
        addContactItemRow () {
            this.contacts.push({
                apply_for_finance: 0,
                apply_for_planning: 0,
                apply_for_promotions: 0,
                contacttype: 'email',
                label: '',
                // make unique ID so user can insert multiple new fields
                id: 'new_' + this.contacts.length,
                student_id: this.student_id,
                use_salutation: '',
                value: ''
            });
        },

        /**
         * Delete a contact row
         * @param id
         */
        deleteRow (id) {
            axios.delete(`/api/studentcontact/${id}`)
                .then(resp => {
                    // now remove from local data
                    const index = this.contacts.findIndex(row => row.id === id);
                    if (index > -1) {
                        this.contacts.splice(index, 1);
                    }
                    // notify user
                    this.successNoty(ucFirst(translate('generic.studentcontactdeleted')));
                })
                .catch(err => {
                    this.failNoty(ucFirst(translate('generic.studentcontactdeletefailed')) + `: ${err}`);
                });
        },

        /**
         * save a changed 'apply-for' value to the database
         * @param applyFor
         * @param contactArrayKey
         */
        saveApplyForValue (applyFor, contactArrayKey) {
            const data = {
                field: `apply_for_${applyFor}`,
                // convert to int
                newValue: this.contacts[contactArrayKey][`apply_for_${applyFor}`] ? '1' : '0'
            };
            // todo: translate
            axios.put(`/api/updateContactApplyFor/${this.contacts[contactArrayKey].id}`, data)
                .then(resp => {
                    this.successNoty(ucFirst(translate('generic.useforfieldupdated')));
                })
                .catch(err => {
                    this.failNoty(`Could not update field: ${err}`);
                });
        },

        /**
         * if we may assume the entry is a phone number, format it to digits with spaces: dd_dd_dd_dd_dd
         * @param index
         */
        formatIfPhonenumber (index) {
            if ((this.contacts[index].contacttype != null) && (this.contacts[index].contacttype === 'telephone')) {
                // seems to be meant as a telephone number
                // if only contains digits and not already spaces - if user knows better, don't overrule
                let inputVal = this.contacts[index].value;
                const regexOnlyDigits = /\d*/g;
                const regexEveryTwoDigits = /(\d\d)(?=\d)/g;
                const subst = '$1 ';
                inputVal = inputVal.replaceAll(' ', '');
                console.log(inputVal);
                if (regexOnlyDigits.exec(inputVal)) {
                    console.log('reformatting alleged telephone number');
                    this.contacts[index].value = inputVal.replace(regexEveryTwoDigits, subst);
                } else {
                    console.log("cant reformat it's not a sequence of purely digits");
                }
            }
        }
    }
};
</script>

<style scoped>
.bl {
    border-left: 1px solid #DEE2E6;
}
.br {
    border-right: 1px solid #DEE2E6;
}
</style>
