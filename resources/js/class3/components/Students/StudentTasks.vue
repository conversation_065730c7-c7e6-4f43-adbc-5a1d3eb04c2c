<template>
    <panel :busy="busy" panel-test-id="student-card-student-tasks">
        <template v-slot:title>
            <i class="fas fa-list"></i>
            {{ucFirst(translate('generic.studenttasks'))}}
        </template>
        <template v-slot:subtitle>
            <a href="/tasks" class="btn btn-success btn-sm">
                {{ucFirst(translate('generic.jumpto'))}}: {{translateChoice('generic.tasks', 2)}}
            </a>
        </template>
        <div class="row">
            <div class="col-2">
                {{ ucFirst(translate('generic.assignedto')) }}
            </div>
            <div class="col-2">
                {{ ucFirst(translate('generic.startdate')) }}
            </div>
            <div class="col-2">
                {{ ucFirst(translate('generic.duedate')) }}
            </div>
            <div class="col-3">
                {{ ucFirst(translate('generic.tasktype')) }}
            </div>
            <div class="col-2">
                {{ ucFirst(translate('generic.status')) }}
            </div>
            <div class="col-1">&nbsp;</div>
        </div>
        <hr>
        <div v-for="(task, key) in tasks" :key="key">
            <div class="row">
                <div class="col-2">
                    {{ task.assigned_user_id > 0 ? shortenName(task.assignedTo.name) : '-' }}
                </div>
                <div class="col-2">
                    {{ displayDate(task.date_opened) }}
                </div>
                <div class="col-2">
                    {{ displayDate(task.date_due) }}
                </div>
                <div class="col-3">
                    <a :href="'/tasks/' + task.id + '/edit'">
                        {{ translate('generic.' + task.tasktype.description) }}
                    </a>
                    <span v-tooltip="task.remarks">
                        <i class="fas fa-info-circle"></i>
                    </span>
                </div>
                <div class="col-2">
                    <i :class="taskIcons[getTaskStatus(task)]"></i>
                    {{ translate('generic.' + getTaskStatus(task)) }}
                </div>
                <div class="col-1">
                    <button
                        v-if="getTaskStatus(task) !== 'closed'"
                        class="btn btn-outline-danger btn-sm"
                        @click="closeTask(task.id)"
                    >
                        {{ translate('generic.close') }}
                    </button>
                </div>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import useNoty from '../../composables/useNoty';
import useDateTime from '../../composables/useDateTime';
import useLang from '../../composables/useLang';
import axios from 'axios';

const busy = ref(false);
const tasks = ref([]);
const { failNoty } = useNoty();
const { displayDate } = useDateTime();
const { ucFirst, translate, translateChoice } = useLang();
const taskIcons = {
    notopenyet: 'fas fa-hourglass-start',
    open: 'fas fa-clipboard-list',
    overdue: 'fas fa-exclamation-triangle',
    closed: 'fas fa-check-circle',
    other: 'fas fa-question'
};

const props = defineProps({
    studentId: {
        type: String,
        required: true
    }
});

onMounted(async () => {
    await getStudentTasks();
});

const getStudentTasks = async () => {
    busy.value = true;
    try {
        const { data } = await axios.get(`/api/gettaskforstudent/${props.studentId}`);
        tasks.value = data.data;
    } catch (err) {
        failNoty(`Error retrieving studenttasks for student ${props.studentId}: ${err}`);
    } finally {
        busy.value = false;
    }
};

/**
 * Returns the status of a task.
 *
 * @param {Object} task - The task object.
 * @return {string} - The status of the task. Returns one of: "not open yet", "open", "closed", "overdue", "unknown".
 */
const getTaskStatus = (task) => {
    if (task == null) {
        return 'unknown';
    }
    // first check if the end date is filled in
    if (task.date_closed) {
        return 'closed';
    }
    // then check if we have a due date, and if it is in the past
    if (task.date_due && new Date(task.date_due) < new Date()) {
        return 'overdue';
    }
    // check if the task is not open yet
    if (new Date(task.date_opened) > new Date()) {
        return 'notopenyet';
    }
    // check if the task has started
    if (new Date(task.date_opened) < new Date()) {
        return 'open';
    }
    return 'unknown';
};

const closeTask = async (taskId) => {
    busy.value = true;
    try {
        await axios.put(`/api/task/${taskId}/close`);
        await getStudentTasks();
    } catch (err) {
        failNoty(`Error closing task ${taskId}: ${err}`);
    } finally {
        busy.value = false;
    }
};

const shortenName = (fullname) => {
    if (fullname == null) {
        return '-';
    }
    const parts = fullname.split(' ');
    return parts[0] + parts[parts.length - 1].charAt(0) + '.';
};

</script>

<style scoped>

</style>
