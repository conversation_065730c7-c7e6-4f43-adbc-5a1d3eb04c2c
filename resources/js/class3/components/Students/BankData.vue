<template>
    <panel :busy="busy" panel-test-id="student-card-bank">
        <template v-slot:title>
            <i class="fas fa-money-bill"></i>
            {{ucFirst(translate('generic.bankdata'))}}
        </template>
        <template v-slot:subtitle></template>

        <div class="row">
            <div class="col-xs-12 col-md-3">
                <div class="form-group">
                    <label for="bankaccount_number" class="control-label">
                        {{ucFirst(translate('generic.bankaccountnumber'))}}
                    </label>
                    <input name="bankaccount_number" type="text"
                           id="bankaccount_number" class="form-control bank_account"
                           v-model="bankaccount_number"
                           @keydown="dirty=true"
                    >
                </div>
            </div>
            <div class="col-xs-12 col-md-3">
                <div class="form-group">
                    <label for="bankaccount_name" class="control-label">
                        {{ucFirst(translate('generic.bankaccountname'))}}
                    </label>
                    <input name="bankaccount_name" id="bankaccount_name"
                           type="text"
                           v-model="bankaccount_name" class="form-control"
                           @keydown="dirty=true"
                    >
                </div>
            </div>
            <div class="col-xs-12 col-md-3">
                <div id="init_permission_auto_banktransfer" class="hide"></div>
                <material-switch
                    color="success"
                    :label-on="ucFirst(translate('generic.permissionautobanktransfer'))"
                    :label-off="ucFirst(translate('generic.nopermissionautobanktransfer'))"
                    switch-id="permission_auto_banktransfer"
                    v-model="permission_auto_banktransfer"
                    value-type="string"
                    @changed="dirty=true"
                ></material-switch>
            </div>
            <div class="col-xs-12 col-md-1" v-if="permission_auto_banktransfer === 'on'">
                <label class="control-label">Refresh</label><br>
                <button
                    v-tooltip="ucFirst(translate('generic.createnewmandatenumber'))"
                    class="btn btn-sm btn-success mt-1"
                    @click.prevent="noop"
                    data-toggle="modal"
                    data-target="#NewMandateNumberPopup"
                >
                   <i class="fas fa-sync"></i>
                </button>
            </div>
            <div class="col-xs-12 col-md-2">
                <div v-if="permission_auto_banktransfer === 'on'">
                    <label for="mandate_number" class="control-label">{{ucFirst(translate('generic.mandatenumber'))}}</label>
                    <input name="mandate_number" readonly type="text" v-model="mandate_number" id="mandate_number" class="form-control">
                </div>
            </div>
        </div>
        <div class="d-flex flex-row" v-if="dirty">
            <div class="alert alert-warning">
                {{ucFirst(translate('generic.pleasesavechanges'))}}
            </div>
        </div>
        <are-you-sure
                button-text="Ok"
                modal-id="NewMandateNumberPopup"
                @confirmclicked="changeMandateNumber"
        ></are-you-sure>
    </panel>
</template>

<script>
import Panel from '../Layout/Panel.vue';
import AreYouSure from '../Layout/AreYouSure';
import MaterialSwitch from '../Layout/MaterialSwitch';
import axios from 'axios';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

export default {
    name: 'BankData',
    components: { AreYouSure, MaterialSwitch, Panel },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            dirty: false,
            busy: false,
            bankaccount_name: '',
            bankaccount_number: '',
            permission_auto_banktransfer: '',
            mandate_number: ''
        };
    },
    props: {
        studentid: {
            type: String,
            required: true
        }
    },
    mounted () {
        this.getBankAccountInfo();
    },
    methods: {
        getBankAccountInfo () {
            this.busy = true;
            axios.get(`/api/studentbankdata/${this.studentid}`)
                .then(response => {
                    this.bankaccount_name = response.data.data.bankaccount_name;
                    this.bankaccount_number = response.data.data.bankaccount_number;
                    this.permission_auto_banktransfer = response.data.data.permission_auto_banktransfer === 'Ja' ? 'on' : '';
                    this.mandate_number = response.data.data.mandate_number;
                })
                .catch(err => {
                    console.log(`Error getting students bank info: ${err}`);
                })
                .finally(() => {
                    this.busy = false;
                });
        },

        changeMandateNumber () {
            this.busy = true;
            axios.put(`/api/changemandatenumber/${this.studentid}`)
                .then(response => {
                    this.mandate_number = response.data.mandate_number;
                    this.successNoty(ucFirst(translate('generic.savemandatemumbersuccess')), ucFirst(translate('generic.success')));
                })
                .catch(err => {
                    this.failNoty(ucFirst(translate('generic.errorupdatingmandatenumber')) + `: ${err}`, ucFirst(translate('generic.failed')));
                })
                .finally(() => {
                    this.busy = false;
                });
        }
    },
    watch: {
        dirty () {
            // propagate the dirty flag to 'window' (global) to be able
            // to use it in the window.onbeforeunload handler
            window.dirty = this.dirty;
        }
    }
};
</script>

<style scoped>
.bank_account {
    text-transform: uppercase;
}
</style>
