<template>
    <div>
        <button class="btn btn-success"
                data-target="#choosecopysource"
                data-toggle="modal"
                @click.prevent="() => {}"
        >
            {{ucFirst(translate('generic.copydatafromotherstudent'))}}
        </button>

        <modal :popup-title = "ucFirst(translate('generic.searchstudent'))"
               :closetext   = "ucFirst(translate('generic.close'))"
               modal-id     = "choosecopysource"
               size         = "large"
        >
            <template v-if="theStudent == null">
                <div class="row">
                    <div class="col-md-12">
                        <label>{{ucFirst(translate('generic.search'))}}:
                            <input class="form-control" id="search" v-model="searchKey">
                        </label>
                    </div>
                </div>
                <div class="row" v-if="searchResult.length > 0">
                    <div class="col-md-12">
                        <label>
                            {{ucFirst(translateChoice('generic.students', searchResult.length))}}
                            ({{ucFirst(translate('generic.currentcourses'))}})
                        </label>
                        <ul class="list-group">
                            <li v-for="(student, index) in searchResult" :key="index" class="list-group-item">
                                <button
                                    class="btn btn-primary btn-sm"
                                    :data-student-id="student.id"
                                    @click.prevent="chooseStudent"
                                >
                                    {{ucFirst(translateChoice('generic.choosestudents', 1))}}
                                </button>
                                {{student.name}} ({{student.currentcoursessarray.length}})
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="row" v-else>
                    <div class="col-md-12">
                        <p>
                            <i class="fa fa-question-circle mr-2"></i>
                            {{ucFirst(translate('generic.searchstudentbypartofname'))}}
                        </p>
                    </div>
                </div>
            </template>
            <!-- Show rest of input after source student has been choosen -->
            <template v-else>
                <div class="row">
                    <div class="col-md-5">
                        <button
                            class="btn btn-primary btn-sm"
                            @click.prevent="resetSearch"
                        >
                            <i class="fa fa-arrow-left"></i>
                            {{ucFirst(translate('generic.back'))}}
                        </button>
                    </div>
                </div>
                <div class="form">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label>Voornaam</label>
                                <input
                                    class="form-control"
                                    name="firstname"
                                    v-model="newFirstName">
                            </div> <!-- end form-group -->
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Tussenvoegsel</label>
                                <input
                                    class="form-control"
                                    name="preposition"
                                    v-model="newPreposition">
                            </div> <!-- end form-group -->
                        </div>
                        <div class="col-md-5">
                            <div class="form-group">
                                <label>Achternaam (*)</label>
                                <input
                                    ref="lastNameField"
                                    id="newLastName"
                                    class="form-control"
                                    name="newLastName"
                                    v-model="newLastName">
                            </div> <!-- end form-group -->
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label for="newStudentDateOfBirth" class="control-label">Geboortedatum (*)</label>
                                <VueDatepicker
                                    v-model="newDateOfBirth"
                                    v-bind="dpOptionsDate"
                                    name="newStudentDateOfBirth"
                                    id="newStudentDateOfBirth"
                                    autocomplete="off"
                                    required
                                ></VueDatepicker>
                            </div> <!-- end form-group -->
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <material-switch
                                switch-id="copyaddressdata"
                                v-model="copyToggleAddress"
                                value-type="string"
                                :label-on="ucFirst((translate('generic.copyaddressdata')))"
                            />
                        </div>
                        <div class="col-md-3">
                            <material-switch
                                switch-id="copycontactdata"
                                v-model="copyToggleContact"
                                value-type="string"
                                :label-on="ucFirst((translate('generic.copycontactdata')))"
                            />
                        </div>
                        <div class="col-md-3">
                            <material-switch
                                switch-id="copybankdata"
                                v-model="copyToggleBank"
                                value-type="string"
                                :label-on="ucFirst((translate('generic.copybankdata')))"
                            />
                        </div>
                        <div class="col-md-3">
                            <material-switch
                                switch-id="copystudentlistsdata"
                                v-model="copyToggleStudentlists"
                                value-type="string"
                                :label-on="ucFirst((translate('generic.copystudentlists')))"
                            />
                        </div>
                    </div>
                </div>
            </template>
            <template #okbutton v-if="theStudent != null">
                <button
                    class="btn btn-danger btn-ok"
                    id="createfromotherstudentsavable"
                    data-dismiss="modal"
                    @click="createfromotherstudent"
                    :disabled="!savable"
                >
                    {{ucFirst(translate('generic.createstudentwiththesevalues'))}}
                </button>
            </template>
        </modal>
    </div>
</template>

<script>
import Modal from '../Layout/Modal3';
import MaterialSwitch from '../Layout/MaterialSwitch';
import axios from 'axios';
import useLang from "../../composables/useLang";
import useDatePicker from "../../composables/useDatePicker";
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

const { translate, translateChoice, ucFirst } = useLang();
const { dpOptions: dpOptionsDate } = useDatePicker(true);

export default {
    name: 'NewStudentByCopy',
    components: { MaterialSwitch, Modal, VueDatepicker },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst,
            dpOptionsDate
        };
    },
    data () {
        return {
            // Using dpOptionsDate from useDatePicker composable
            students: [],
            searchKey: '',
            theStudent: null,
            newDateOfBirth: '',
            newLastName: '',
            newFirstName: '',
            newPreposition: '',
            copyToggleAddress: 'on',
            copyToggleBank: 'on',
            copyToggleStudentlists: 'on',
            copyToggleContact: 'on'
        };
    },
    mounted () {
        console.log('newStudentByCopy component mounted');
        this.fetchStudents();
    },
    methods: {
        /**
         * Fetch from backend, returns individuals and groups.
         * computed filter gets rid of the groups
         */
        fetchStudents () {
            axios.get('/api/students')
                .then((response) => {
                    this.students = response.data.data;
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        /**
         * Student chosen, go to part 2
         * @param e
         */
        chooseStudent (e) {
            this.theStudent =
                this.students.find(student => student.id === parseInt(e.target.getAttribute('data-student-id')));
            // after the date field is on the screen due to the student now been chosen...
            this.$nextTick(() => {
                // initialize the students lastname and preposition, so v-model works
                this.newLastName = this.theStudent.lastname;
                this.newPreposition = this.theStudent.preposition;
                /* mag weg?
                $("[class *= 'datepicker']").datepicker({
                    onSelect: (d,i) => {
                        this.newDateOfBirth = this.$refs.newDateOfBirth.value;
                    }
                });
                */
            });
        },
        resetSearch () {
            this.theStudent = null;
        },
        /**
         * Save/create/copy new student in the backend
         * fields, mandatory:
         *      studentId, newLastName, newDateOfBirth, copyToggleAddress, copyToggleContact, copyToggleBank
         * fields optional:
         *      newFirstName, newPreposition
         */
        createfromotherstudent () {
            const data = {
                studentId: this.theStudent.id,
                newLastName: this.newLastName,
                newFirstName: this.newFirstName,
                newPreposition: this.newPreposition,
                newDateOfBirth: this.newDateOfBirth,
                copyToggleAddress: this.copyToggleAddress === 'on',
                copyToggleContact: this.copyToggleContact === 'on',
                copyToggleBank: this.copyToggleBank === 'on',
                copyToggleStudentlists: this.copyToggleStudentlists === 'on'
            };
            axios.post('/api/students/newfromotherstudent', data)
                .then((response) => {
                    this.successNoty(ucFirst(translate('generic.openingstudentpage')), translate('generic.savesuccess'));
                    setTimeout(() => {
                        window.location.href = `/students/${response.data.studentId}/edit`;
                    }, 5000);
                })
                .catch(err => {
                    this.failNoty(ucFirst(translate('generic.errorsavingdata')) +
                    `: ${err}`);
                    console.log(err);
                });
        }
    },
    computed: {
        /**
         * are mandatory fields filled
         * @returns {boolean}
         */
        savable () {
            return this.newDateOfBirth.length === 10 && this.newLastName.length > 1;
        },
        /**
         * filters the students array using the search key
         * also filters out student groups
         * @returns {*[]}
         */
        searchResult () {
            if (this.searchKey.length < 3) {
                return [];
            }
            return this.students
                .filter(student => {
                    return student.name.toLowerCase().includes(this.searchKey.toLowerCase()) &&
                        student.type === 'individual';
                })
                .slice(0, 4);
        }
    }
};
</script>

<style scoped>

</style>
