<template>
    <panel panel-test-id="student-card-generic">
        <template #title>
            <i class="fas fa-user"></i>&nbsp;{{ucFirst(translate('generic.genericdata'))}}
        </template>

        <template v-if="studentId > 0" #subtitle>
            <span
                class="mx-2"
                v-tooltip="ucFirst(translate('generic.youneedthisidtodelete'))"
            >
                ID: {{studentId}}
                <i class="fa fa-question-circle"></i>
            </span>
            <quick-jump-student
                    :exclude-id="parseInt(studentId)"
                    :popup-title="ucFirst(translate('generic.search')) + ' ' + translateChoice('generic.students',1)"
                    display-fields="name"
                    api-get-string="/api/students?onlystudents=true"
                    @record-chosen="jumpToStudent"
            />
        </template>
        <template v-else v-slot:subtitle>
            <new-student-by-copy></new-student-by-copy>
        </template>

        <div class="d-flex flex-row">
            <div class="flex-sm-fill mr-2 form-group">
                <label for="firstname">{{ucFirst(translate('generic.firstname'))}}</label>
                <input id="firstname"
                       v-model="dataFirstname"
                       @input="dirty=true"
                       type="text"
                       name="firstname"
                       class="form-control">
            </div>
            <div class="flex-sm-fill mr-2 form-group">
                <label for="preposition">{{ucFirst(translate('generic.preposition'))}}</label>
                <input id="preposition"
                       v-model="dataPreposition"
                       @input="dirty=true"
                       type="text"
                       name="preposition"
                       class="form-control">
            </div>
            <div class="flex-sm-fill mr-2 form-group">
                <label for="lastname">{{ucFirst(translate('generic.lastname'))}} (*)</label>
                    <input id="lastname"
                       v-model="dataLastname"
                       @input="dirty=true"
                       type="text"
                       name="lastname"
                       required
                       class="form-control">
            </div>
        </div>

        <div class="d-flex flex-row">
            <div class="flex-sm-fill mr-2 form-group">
                <label for="name">{{ucFirst(translate('generic.name'))}}</label>
                <input id="name"
                       :value="fullname"
                       type="text"
                       name="name"
                       readonly="readonly"
                       class="form-control">
            </div>
            <div class="flex-sm-fill mr-2 form-group">
                <label for="address">{{ucFirst(translate('generic.address'))}}</label>
                <input id="address"
                       v-model="dataAddress"
                       @change="dirty=true"
                       type="text"
                       name="address"
                       class="form-control">
            </div>
            <div class="flex-sm-fill mr-2 form-group">
                <label for="zipcode">{{ucFirst(translate('generic.zipcode'))}}</label>
                <input id="zipcode"
                       v-model="dataZipcode"
                       @change="dirty=true"
                       type="text"
                       name="zipcode"
                       class="form-control">
            </div>
            <div class="flex-sm-fill mr-2 form-group">
                <label for="city">{{ucFirst(translate('generic.city'))}}</label>
                <input id="city"
                       v-model="dataCity"
                       @change="dirty=true"
                       type="text"
                       name="city"
                       class="form-control">
            </div>
        </div>
        <div class="d-flex flex-row">
            <div class="flex-sm-fill mr-2 form-group">
                <label for="date_of_birth">{{ucFirst(translate('generic.birthdate'))}}  (*)</label>
                <VueDatepicker
                    v-model="dobNL"
                    @update:model-value="dirty=true"
                    name="date_of_birth"
                    id="date_of_birth"
                    autocomplete="off"
                    required
                    v-bind="dpOptionsDate"
                ></VueDatepicker>
            </div>
            <div class="flex-sm-fill mr-2 form-group">
                <label for="age">{{ucFirst(translate('generic.age'))}}</label>
                <input id="age"
                       :value="displayAge(dobNL)"
                       type="text"
                       name="age"
                       readonly="readonly"
                       class="form-control">
            </div>

            <!-- accesstoken field has no name, should not be in the post (will be ignored anyway) -->
            <div v-if="!isNewAction" class="flex-sm-fill mr-2 form-group">
                <label for="accesstoken">{{ucFirst(translate('generic.accesstoken'))}}</label>
                <input id="accesstoken"
                       :value="accessToken"
                       type="text"
                       readonly="readonly"
                       class="form-control"
                >
            </div>

            <div v-if="!isNewAction" class="flex-sm-fill mr-2 form-group">
                <label>{{ucFirst(translate('generic.permissionsocialshare'))}}</label>
                <input
                    class="form-control"
                    :value="agreeSocialShare === 1
                        ? ucFirst(translate('generic.yes'))
                        : ucFirst(translate('generic.no'))"
                    readonly="readonly"
                >
            </div>

            <div v-if="!isNewAction" class="flex-sm-fill mr-2 form-group">
                <div class="row">
                    <div class="col-6">
                        <material-switch
                            switch-id="has_access"
                            :label-on="ucFirst(translate('generic.hasaccess'))"
                            :label-off="ucFirst(translate('generic.hasnoaccess'))"
                            color="success"
                            v-model="StudentHasAccess"
                            value-type="string"
                            @changed="dirty=true"
                            :tooltip="ucFirst(translate('generic.explainhasaccess'))"
                        />
                    </div>
                    <!-- temp hidden: this page is not available yet in CL3 -->
                    <div class="col-6 hide">
                        <label>{{ucFirst(translate('generic.preferredschedule'))}}</label>
                        <div class="form-group">
                            <a class="btn btn-primary form-control" :href="'/students/preferredschedule/' + studentId">
                                {{ucFirst(translate('generic.openpage'))}}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-row" v-if="dirty">
            <div class="alert alert-warning">
                {{ucFirst(translate('generic.pleasesavechanges'))}}
            </div>
        </div>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import Panel from '../Layout/Panel';
import QuickJumpStudent from '../Layout/QuickJump';
import MaterialSwitch from '../Layout/MaterialSwitch';
import NewStudentByCopy from './NewStudentByCopy';
import useLang from '../../composables/useLang';
import useNoty from '../../composables/useNoty';
import useDateTime from '../../composables/useDateTime';
import useDatePicker from '../../composables/useDatePicker';
import moment from 'moment';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

const { displayAge } = useDateTime();

const props = defineProps({
    studentId: {
        type: Number,
        default: 0
    },
    firstname: {
        type: String,
        default: ''
    },
    preposition: {
        type: String,
        default: ''
    },
    lastname: {
        type: String,
        default: ''
    },
    address: {
        type: String,
        default: ''
    },
    zipcode: {
        type: String,
        default: ''
    },
    city: {
        type: String,
        default: ''
    },
    dateOfBirth: {
        type: String,
        default: ''
    },
    accessToken: {
        type: String,
        default: ''
    },
    hasAccess: {
        type: String,
        default: ''
    },
    agreeSocialShare: {
        type: Number,
        default: 0
    }
});

const { ucFirst, translate, translateChoice } = useLang();
const { successNoty, failNoty } = useNoty();

// Get date picker options (language is handled internally by the composable)
const { dpOptions: dpOptionsDate } = useDatePicker(true);

const dirty = ref(false);
const dobNL = ref(props.dateOfBirth === ''
    ? ''
    : moment(props.dateOfBirth, 'YYYY-MM-DD').format('DD-MM-YYYY'));
const StudentHasAccess = ref(props.hasAccess === '0' ? '' : 'on');
const dataFirstname = ref(props.firstname);
const dataLastname = ref(props.lastname);
const dataPreposition = ref(props.preposition);
const dataAddress = ref(props.address);
const dataZipcode = ref(props.zipcode);
const dataCity = ref(props.city);

onMounted(() => {
    StudentHasAccess.value = props.hasAccess === '0' ? '' : 'on';
    dataFirstname.value = props.firstname;
    dataLastname.value = props.lastname;
    dataPreposition.value = props.preposition;
    dataAddress.value = props.address;
    dataZipcode.value = props.zipcode;
    dataCity.value = props.city;
});

/**
 * basically: concatenate firstname, preposition and lastname
 * this will be saved in the backend so we need to do this only once
 * @type {ComputedRef<string>}
 */
const fullname = computed(() => {
    const concatenated = isStudentGroup.value
        ? dataLastname.value
        : dataFirstname.value + ' ' +
              dataPreposition.value + ' ' +
              dataLastname.value;
        // if no preposition, there will be two consecutive spaces
        // if all fields empty remove the only space left (trim)
    return (concatenated.replace('  ', ' ')).trim();
});

/**
 * determine if the student is actually a studentgroup
 * @type {ComputedRef<boolean>}
 */
const isStudentGroup = computed(() => {
    return props.firstname === '-' && props.dateOfBirth === '1800-01-01';
});

/**
 * see if the component is used for a 'new' action instead of an update
 * @type {ComputedRef<boolean>}
 */
const isNewAction = computed(() => {
    return props.studentId === 0;
});

const jumpToStudent = (studentId) => {
    window.location.href = `/students/${studentId}/edit`;
};

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});
</script>

<style scoped lang="scss">

</style>
