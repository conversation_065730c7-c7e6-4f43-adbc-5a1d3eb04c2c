<template>
    <div>
        <panel
            :busy="busy"
            :collapsible="true"
        >
            <template v-slot:title>
                <i class="text-success fas fa-users"></i>
                {{ucFirst(translate('generic.current'))}} {{translateChoice('generic.students', 2)}}
            </template>
            <template v-slot:subtitle>
                <div class="cls-grid">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <div class="input-group-text" id="btnGroupAddon"><i class="fa fa-search"></i></div>
                        </div>
                        <input id="searchbox_students"
                               :placeholder="translate('generic.searchfilter')"
                               v-model="studentSearchFilter"
                               name="searchbox_students"
                               type="text"
                               class="form-control input"
                               autocomplete="off"
                        >
                    </div>
                    <a href="/exportcontactlist" class="btn btn-sm btn-primary mx-2">
                        <i class="fa fa-file-export"></i>
                        {{ucFirst(translate('generic.exporttoexcel'))}}
                    </a>
                    <a href="/students/create" class="btn btn-sm btn-success">
                        <i class="fa fa-plus"></i>
                        {{ucFirst(translate('generic.newstudent'))}}
                    </a>
                </div>
            </template>
            <table class="table">
                <thead>
                <tr>
                    <th class="text-center"><i class="fa fa-edit"></i></th>
                    <th>{{ucFirst(translate('generic.name'))}}</th>
                    <th>{{ucFirst(translate('generic.email'))}}</th>
                    <th>{{ucFirst(translate('generic.telephone'))}}</th>
                    <th>
                        {{ucFirst(translate('generic.current'))}} {{translateChoice('generic.courses', 2)}}<br/>
                        <small><i class="fa fa-lock"></i> = {{translate('generic.keepcurrentschedule')}}</small>
                    </th>
                    <th>
                        {{ucFirst(translate('generic.preferencesince'))}}
                    </th>
                    <th>
                        {{ucFirst(translate('generic.classyaccess'))}}<br/>
                        <small><em>{{translate('generic.clicktorevealpin')}}</em></small>
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="student in activeStudents" :key="student.id">
                    <td class="text-center">
                        <a :href="'/students/'+student.id + '/edit'"
                           class="btn btn-sm btn-success">
                            <i class="fa fa-edit"></i>
                        </a>
                    </td>
                    <td>{{student.name}}</td>
                    <td v-html="student.contacts
                                    .filter(c => c.contacttype==='email')
                                    .map(e => `<a href='mailto:${e.value}'>${e.value}</a>`)
                                    .join('<br>')"
                    >
                    </td>
                    <td v-html="student.contacts
                                    .filter(c => c.contacttype==='telephone')
                                    .map(e => `<a href='mailto:${e.value}'>${e.value}</a>`)
                                    .join('<br>')"
                    >
                    </td>
                    <td v-html="showCourseDetails(student.currentcoursesdetails)"></td>
                    <td>
                        <span v-if="student.lastprefdate==='-'">{{translate('generic.unknown')}}</span>
                        <span v-else><a :href="'/students/preferredschedule/' + student.id">{{student.lastprefdate}}</a></span>
                    </td>
                    <td>
                        <input
                            type="password"
                            :value="student.apipin"
                            class="form-control"
                            @click.prevent="$event.target.type='text'"
                            @blur="$event.target.type='password'"
                            @change="savePinForStudent($event, student.id)"
                            autocomplete="off"
                        >
                        <br/>
                        <span class="text-success" :id="'msg_' + student.id"></span>
                    </td>
                </tr>
                </tbody>
            </table>
        </panel>
        <panel
            :busy="busy"
            :collapsible="true"
        >
            <template v-slot:title>
                <i class="fas fa-users"></i>
                {{ucFirst(translateChoice('generic.formerstudents', 2))}}
            </template>
            <table class="table">
                <thead>
                <tr>
                    <th class="text-center"><i class="fa fa-edit"></i></th>
                    <th>{{ucFirst(translate('generic.name'))}}</th>
                    <th>{{ucFirst(translate('generic.email'))}}</th>
                    <th>{{ucFirst(translate('generic.telephone'))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="student in inactiveStudents" :key="student.id">
                    <td class="text-center">
                        <a :href="'/students/'+student.id + '/edit'"
                           class="btn btn-sm btn-success">
                            <i class="fa fa-edit"></i>
                        </a>
                    </td>
                    <td>{{student.name}}</td>
                    <td v-html="student.contacts
                                    .filter(c => c.contacttype==='email')
                                    .map(e => `<a href='mailto:${e.value}'>${e.value}</a>`)
                                    .join('<br>')"
                    >
                    </td>
                    <td v-html="student.contacts
                                    .filter(c => c.contacttype==='telephone')
                                    .map(e => `<a href='mailto:${e.value}'>${e.value}</a>`)
                                    .join('<br>')"
                    >
                    </td>
                </tr>
                </tbody>
            </table>
        </panel>
    </div>
</template>

<script>
import axios from 'axios';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

export default {
    name: 'ListStudents.vue',
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            busy: false,
            students: [],
            studentSearchFilter: ''
        };
    },
    mounted () {
        this.getStudents();
    },
    methods: {
        getStudents () {
            this.busy = true;
            axios.get('/api/students?onlystudents=true')
                .then(resp => {
                    this.students = resp.data.data;
                })
                .catch(err => {
                    this.failNoty(
                        ucFirst(translate('generic.errorloadingstudents')) + ' ' + err,
                        ucFirst(translate('generic.error'))
                    );
                })
                .finally(() => {
                    this.busy = false;
                });
        },
        /**
             * Show current courses,
             * and indicate 'locked' if users has 'please_keep_scheduled_time' for this course
             * @param detailsArray
             * @returns {*}
             */
        showCourseDetails (detailsArray) {
            return detailsArray.map(c => {
                return (c.please_keep_scheduled_time !== null)
                    ? `${c.name} &nbsp;<i class='fas fa-lock'></i>`
                    : c.name;
            }).join('<br>');
        },
        /**
             * show access code for ClassY
             * @param event
             */
        showcode (event) {
            event.target.type = 'text';
        },
        /**
             * hide access code for ClassY
             * @param event
             */
        hidecode (event) {
            event.target.type = 'password';
        },
        async savePinForStudent (event, studentid, classyaccount) {
            const apipin = event.target.value;
            if (apipin.match(/^\d{6}$/gm)) {
                await axios.put('/api/studentsavepin', { studentid, apipin })
                    .then(resp => {
                        this.successNoty(
                            ucFirst(translate('generic.pinsavedcorrectly')),
                            ucFirst(translate('generic.success'))
                        );
                    })
                    .catch(err => {
                        this.failNoty(ucFirst(translate('generic.errorsavingpin')) + ': ' + err,
                            ucFirst(translate('generic.notsaving')));
                    });
            } else {
                this.failNoty(ucFirst(translate('generic.pinnotvalid')) + ': ' +
                        translate('generic.pinrestrictionsare'),
                ucFirst(translate('generic.notsaving'))
                );
            }
        }
    },
    computed: {
        activeStudents () {
            return this.students.filter(
                student =>
                    (
                        (student.currentcoursessarray.length !== 0) &&
                            (student.name.toLowerCase().includes(this.studentSearchFilter.toLowerCase()))
                    )
            );
        },
        inactiveStudents () {
            return this.students.filter(
                student =>
                    (
                        (student.currentcoursessarray.length === 0) &&
                            (student.name.toLowerCase().includes(this.studentSearchFilter.toLowerCase()))
                    )
            );
        }
    }
};
</script>

<style scoped>
.cls-grid {
    grid-template-columns: auto 250px 200px;
    display: inline-grid;
}

</style>
