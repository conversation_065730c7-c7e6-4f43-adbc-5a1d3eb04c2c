<template>
    <panel :busy="busy" panel-test-id="student-card-course-data">
        <template v-slot:title>
            <i class="fas fa-school"></i>
            {{ucFirst(translate('generic.coursedata'))}}
        </template>
        <template v-slot:subtitle>
            <choose-course-for-registration
                :button-label="ucFirst(translate('generic.add'))"
                :popup-title="ucFirst(translate('generic.newcourseregistration'))"
                display-fields="name, groupSize, recurrenceoption"
                api-get-string="/api/courses"
                @record-chosen="doChooseCourseForRegistration"
                :exclude-ids="activeCourseIds"
            ></choose-course-for-registration>
        </template>
        <div v-if="courses.length > 0">
            <!---->
            <div v-if="activeCourses.length > 0">
                <p class="h5">
                    {{ucFirst(translate('generic.current'))}} {{translateChoice('generic.courses', activeCourses.length)}}
                </p>
                <div v-for="course in activeCourses" :key="course.id">
                    <student-course-row
                        :registration="course"
                        :userisadmin="userisadmin"
                        :student="student"
                        :domain="domain"
                        @refreshMyData="getCourseData"
                    />
                </div>
            </div>
            <!-- -->
            <hr v-if="inactiveCourses.length > 0 && activeCourses.length > 0" />
            <!-- -->
            <div v-if="inactiveCourses.length > 0">
                <p class="h5" v-html="ucFirst(translateChoice('generic.endedcourses', inactiveCourses.length))"></p>
                <div v-for="course in inactiveCourses" :key="course.id">
                    <student-course-row
                        :registration="course"
                        :is-active="false"
                        :userisadmin="userisadmin"
                        :student="student"
                        :domain="domain"
                        :getRegMetaData="false"
                        @registrationDeleted="deleteRegistrationRow"
                    />
                </div>
            </div>
        </div>
        <div v-else>
            <h5>{{ucFirst(translate('generic.nocoursesfound'))}}</h5>
        </div>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import useNoty from "../../composables/useNoty";
import useLang from "../../composables/useLang";
import Panel from '../Layout/Panel.vue';
import StudentCourseRow from '../Registrations/StudentCourseRow';
import ChooseCourseForRegistration from '../Layout/QuickJump';
import axios from 'axios';
import moment from 'moment';

const { failNoty, successNoty } = useNoty();
const { ucFirst, translate, translateChoice } = useLang();

const busy = ref(false);
const courses = ref([]);

const props = defineProps({
    userisadmin: {
        type: Boolean,
        default: false
    },
    student: {
        type: Object,
        required: true
    },
    domain: {
        type: Object,
        required: true
    }
});

onMounted(() => {
    getCourseData();
});

const getCourseData = () => {
    busy.value = true;
    axios.get(`/api/studentcoursedata/${props.student.id}`)
        .then(resp => {
            courses.value = resp.data.data;
        })
        .catch(err => {
            failNoty(
                ucFirst(translate('generic.errorfetchingcoursedata')) + `: ${err}`,
                ucFirst(translate('generic.error'))
            );
        })
        .finally(() => {
            busy.value = false;
        });
};

/**
 * couples the chosen course to the student as a new registration
 */
const doChooseCourseForRegistration = (courseId) => {
    axios.put('/api/students/addCourseRegistration', { courseId, studentId: props.student.id })
        .then(response => {
            // re-fetch registered courses list
            getCourseData();
            successNoty(
                ucFirst(translate('generic.registrationsaved')),
                ucFirst(translate('generic.success'))
            );
        })
        .catch(error => {
            failNoty(
                ucFirst(translate('generic.errorsavingregistration')) + `: ${error}`,
                ucFirst(translate('generic.error'))
            );
        });
};

/**
 * Handle event: delete registration (NOT setting end date, but actual delete row!)
 * @param ev
 */
const deleteRegistrationRow = ({ regid }) => {
    // remove this course from the local data. please note type-safety
    courses.value = courses.value.filter(course => parseInt(course.pivot.id) !== parseInt(regid));
    // show success to user
    successNoty(ucFirst(translate('generic.registrationdeleted')));
};

const activeCourses = computed(() => {
    const now = moment();
    return courses.value.filter(course => {
        const endDate = moment(course.pivot.end_date);
        return !endDate.isBefore(now);
    });
});

const inactiveCourses = computed(() => {
    const now = moment();
    return courses.value.filter(course => {
        const endDate = moment(course.pivot.end_date);
        return endDate.isBefore(now);
    });
});

/**
 * get id's of courses to be filtered out when coupling a new course
 * @returns {int[]}
 */
const activeCourseIds = computed(() => {
    return activeCourses.value.map(course => course.id);
});

</script>

<style scoped>

</style>
