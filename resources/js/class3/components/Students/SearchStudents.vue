<template>
    <panel :busy="busy" extraClass="min-with-cards-2" panel-test-id="class-search-students">
        <template v-slot:title>
            <i class="fas fa-search"></i>
            {{ucFirst(translate('generic.directsearch'))}}
            {{translateChoice('generic.students',2)}} / {{translateChoice('generic.studentgroups',2)}}
        </template>
        <template v-slot:subtitle>
            <div class="form-group form-check">
                <input type="checkbox" class="form-check-input" id="onlyactive" v-model="onlyactive">
                <label class="form-check-label" for="onlyactive">{{ucFirst(translate('generic.onlyactive'))}}</label>
            </div>
        </template>
        <div class="form-group">
            <div class="input-group searchbox mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="search-addon">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
                <input type="text" class="form-control" :placeholder="translate('generic.studentname')"
                       aria-label="Searchbox" aria-describedby="search-addon"
                       v-model="studentSearchkey">
            </div>
        </div>
        <div class="table-fix-head">
            <table class="table table-striped table-sm table-responsive-md">
                <thead class="thead-light">
                <tr>
                    <th>{{ucFirst(translate('generic.name'))}}</th>
                    <th>
                        {{ucFirst(translate('generic.age'))}}
                        <div class="text-info text-truncate">(# {{translateChoice('generic.students', 2)}})</div>
                    </th>
                    <th>{{ucFirst(translateChoice('generic.courses', 2))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="student in filteredStudents" :key="student.id">
                    <!-- als het type is: studentgroup -->
                    <td>
                        <a v-if="student.learnertype === 'studentgroup'"
                           :href="'/studentgroups/' + student.id + '/edit'">
                            {{student.name}}
                        </a>
                        <a v-else :href="'/students/' + student.id + '/edit'">
                            {{student.name}}
                        </a>
                    </td>

                    <!-- als het type is: studentgroup -->
                    <!-- aantal leerlingen in de groep -->
                    <td v-if="student.learnertype === 'studentgroup'">
                        <span class="text-info">({{student.participants.length}})</span>
                    </td>
                    <td v-else>
                        {{student.age}}
                    </td>
                    <td style="white-space: nowrap; text-overflow:ellipsis; overflow: hidden;max-width: 450px">
                        {{student.mycourses}}
                    </td>
                </tr>
                <tr v-if="filteredStudents.length === 0">
                    <td colspan="3">{{ucFirst(translate('generic.nostudentsfound'))}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </panel>
</template>

<script>
import Panel from '../Layout/Panel';
import axios from 'axios';
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

export default {
    name: 'SearchStudents',
    components: { Panel },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            busy: false,
            studentSearchkey: '',
            onlyactive: true,
            students: []
        };
    },
    mounted () {
        this.getStudents();
    },
    methods: {
        getStudents () {
            this.busy = true;
            axios.get('/api/students')
                .then((response) => {
                    this.students = response.data.data;
                })
                .catch((err) => {
                    this.failNoty(
                        ucFirst(translate('generic.errorloadingstudents')) + ' ' + err,
                        ucFirst(translate('generic.error'))
                    );
                })
                .finally(() => {
                    this.busy = false;
                });
        }
    },
    computed: {
        filteredStudents () {
            if (this.onlyactive) {
                return this.students.filter(
                    student =>
                        student.name.toLowerCase().includes(this.studentSearchkey.toLowerCase()) &&
                            student.mycoursesarray.length > 0
                );
            } else {
                return this.students.filter(
                    student => student.name.toLowerCase().includes(this.studentSearchkey.toLowerCase())
                );
            }
        }
    }
};
</script>

<style scoped lang="scss">
@import '../../../../sass/tmpl3/variables';
.table-fix-head {
    overflow-y: auto;
    height: 20rem;
    & thead th {
        position: sticky;
        top: -3px;
    }
}
</style>
