<template>
<Panel>
    <template #title>
        {{ ucFirst(translate('generic.pleasecheckyouraddressinfo')) }}
    </template>
    <div>
        {{  ucFirst(translate("generic.thisinformationisinavailableouradministration")) }}
        {{ ucFirst(translate("generic.pleasenotifyusifnotcomplete")) }}
        <a :href="`mailto:${domainData.strAddress.email}?subject=${translate('generic.updatemydatainclass')}`">
            {{ domainData.strAddress.email }}
        </a>
    </div>
    <hr>

    <!-- base data -->
    <div class="row mb-4">
        <div class="col-sm-3 col-12 mb-1 mb-sm-0">
            <label>{{ ucFirst(translate("generic.name")) }}</label>
        </div>
        <div class="col-sm-9 col-12">
            <div class="text-sm-right">{{ student.name }}</div>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-sm-3 col-12 mb-1 mb-sm-0">
            <label>{{ ucFirst(translate("generic.address")) }}</label>
        </div>
        <div class="col-sm-9 col-12">
            <div class="text-sm-right">{{ student.address }}</div>
            <div class="text-sm-right">{{ student.zipcode }} {{ student.city }}</div>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-sm-3 col-12 mb-1 mb-sm-0">
            <label>{{ ucFirst(translate("generic.birthdate")) }}</label>
        </div>
        <div class="col-sm-9 col-12">
            <div class="text-sm-right">{{ displayDate(student.date_of_birth) }}</div>
        </div>
    </div>

    <!-- contact data -->
    <table class="table table-condensed table-responsive-sm mt-2 contact-table">
        <thead>
            <tr>
                <th colspan="3">{{ ucFirst(translate("generic.contactdata")) }}</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="contact in student.contacts" :key="contact.id">
                <td>{{ translate(`generic.${contact.contacttype}`) }}</td>
                <td>{{ contact.value }}</td>
                <td>{{ contact.label }}</td>
            </tr>
        </tbody>
    </table>
</Panel>
</template>

<script setup>
import { onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import useLang from "../../composables/useLang";
import useDomain from "../../composables/useDomain";
import useDateTime from "../../composables/useDateTime";

const { translate, ucFirst } = useLang();
const { domainData, getDomainDataByAccessToken } = useDomain();
const { displayDate } = useDateTime();

defineProps({
    student: {
        type: Object,
        required: true
    }
});

onMounted(() => {
    getDomainDataByAccessToken();
});

</script>

<style scoped>
label {
    font-weight: bold;
}

.contact-table {
    margin-left: -0.75rem;
    width: calc(100% + 1.5rem);
}

@media (max-width: 575.98px) {
    .contact-table {
        margin-left: -10px;
        width: 100%;
    }
}
</style>
