<template>

    <!-- Revoke all access modal -->
    <modal
        :popup-title="ucFirst(translate('generic.areyousure')) + '?'"
        :modal-id="modalId"
        :closetext="ucFirst(translate('generic.cancel'))"
    >
        <strong class="mt-2">{{ ucFirst(translate('generic.revokeaccess')) }}</strong>
        <div class="mt-3">
            <div class="form-check">
                <input class="form-check-input" type="radio" id="revoke-all" v-model="revokeFilter" value="all">
                <label class="form-check-label" for="revoke-all">
                    {{ ucFirst(translate('generic.forallstudents')) }}
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="radio" id="revoke-filter" v-model="revokeFilter" value="filter">
                <label class="form-check-label" for="revoke-filter">
                    {{ ucFirst(translate('generic.explainleavesopennotrespondedyet', { days: domainThreshold })) }}*
                </label>
            </div>
        </div>
        <div class="mt-3">
            <em>
                *) {{
                    ucFirst(translate("generic.explainleavesopennotrespondedyetextend", {
                        days: domainThreshold,
                        days2: domainThreshold
                    }))
                }}
            </em>
        </div>
        <template #okbutton>
            <button
                type="button"
                class="btn btn-danger"
                data-bs-dismiss="modal"
                data-testid="confirm-revoke-all-button"
                :disabled="revokeFilter === ''"
                @click.prevent="revokeAccessToAll"
            >
                {{ ucFirst(translate('generic.revokeaccess')) }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import useStudentAccessCodes from "../../composables/useStudentAccessCodes.js";
import useLang from "../../composables/useLang.js";
import Modal from "../Layout/bs5/Modal4.vue";

defineProps({
    modalId: {
        type: String,
        required: true
    }
});
const { ucFirst, translate } = useLang();
const { domainThreshold, revokeAccessToAll, revokeFilter } = useStudentAccessCodes();

</script>

<style scoped>

</style>
