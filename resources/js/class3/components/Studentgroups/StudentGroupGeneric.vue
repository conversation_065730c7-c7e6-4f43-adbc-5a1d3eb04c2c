<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-users"></i>
            {{ ucFirst(translate('generic.genericdata')) }}
        </template>
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <label for="studentGroupName">
                        {{ ucFirst(translate('generic.studentgroupname')) }}
                    </label>
                    <input
                        class="form-control"
                        id='studentGroupName'
                        v-model="studentGroup.name"
                    >
                </div>
            </div>
        </div>
        <button
            class="btn btn-primary"
            @click="saveStudentGroup"
        >
            {{ ucFirst(translate('generic.save')) }}
        </button>
    </panel>
</template>

<script>
import { onMounted, ref } from 'vue';
import Panel from '../Layout/Panel';
import useLang from '../../composables/useLang';
import useEditStudentGroup from '../../composables/useEditStudentGroup';

export default {
    name: 'StudentGroupGeneric',
    components: { Panel },
    props: {
        id: {
            type: Number,
            default: 0
        }
    },
    setup (props) {
        const { ucFirst, translate } = useLang();
        const busy = ref(false);
        const {
            getStudentGroup,
            saveStudentGroup,
            studentGroup
        } = useEditStudentGroup();

        /**
         * initialize.
         * the props.id needs to be present before getStudentGroup is executed
         */
        onMounted(async () => {
            busy.value = true;
            studentGroup.value.id = props.id;
            await getStudentGroup();
            busy.value = false;
        });

        return {
            busy,
            saveStudentGroup,
            studentGroup,
            translate,
            ucFirst
        };
    }
};
</script>

<style scoped>

</style>
