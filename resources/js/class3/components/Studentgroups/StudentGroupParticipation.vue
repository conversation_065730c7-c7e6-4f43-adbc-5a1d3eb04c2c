<template>
    <span
        class="alert alert-sm"
        :class="{
            'alert-warning': !participation.isParticipating,
            'alert-success': participation.isParticipating
        }"
        v-tooltip="participation.isParticipating ? translate('generic.participating') : translate('generic.notparticipating')"
    >
        <span v-if="participation?.end">{{ participation.start }} t/m {{ participation.end }}</span>
        <span v-else>{{ translate('generic.from') }} {{ participation.start }}</span>
        <template v-if="participation.courseIsNotTargetCourse" >
            <span
                v-tooltip="translate('generic.explaincoursenottargetcourse')">
                <i class="fa fa-exclamation-triangle text-warning"></i>
            </span>
        </template>
    </span>
</template>

<script setup>
import { ref, watch } from "vue";
import useEditStudentGroup from "../../composables/useEditStudentGroup.js";
import useLang from "../../composables/useLang.js";

const {
    getStudentParticipation,
} = useEditStudentGroup();
const { translate } = useLang();

const props = defineProps({
    student: Object,
    targetCourse: Object
});

const participation = ref({
    start: '',
    end: '',
    isParticipating: false,
    courseIsNotTargetCourse: false
});

watch(() => props.student, async () => {
    console.log('watch student');
    participation.value = await getStudentParticipation(props.student);
}, { immediate: true });

</script>

