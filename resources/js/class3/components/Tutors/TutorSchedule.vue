<template>
    <panel v-if="tutor">
        <template v-slot:title>
            {{ ucFirst(translate('generic.availability')) }}
        </template>
        <schedule-board
            v-if="tutor.id > 0"
            :tutor-id="tutor.id"
            maxnrofhours="4"
            :addtimeslicebtntitle="ucFirst(translate('generic.addtimeslice'))"
        ></schedule-board>
        <div v-else>
            {{ ucFirst(translate('generic.pleasesavefirst'))}}
        </div>
    </panel>
</template>

<script>
import useLang from '../../composables/useLang';
import useEditTutor from '../../composables/useEditTutor';
import Panel from '../Layout/Panel';
import ScheduleBoard from './ScheduleBoard';

export default {
    name: 'TutorSchedule',
    components: { Panel, ScheduleBoard },
    setup () {
        const { tutor } = useEditTutor();
        const { ucFirst, translate } = useLang();
        return {
            translate,
            tutor,
            ucFirst
        };
    }
};
</script>

<style scoped>

</style>
