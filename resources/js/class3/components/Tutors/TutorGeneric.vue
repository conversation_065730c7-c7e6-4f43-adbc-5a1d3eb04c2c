<template>
    <div>
        <panel v-if="tutor">
            <template v-slot:title>
                {{ ucFirst(translate('generic.tutordata')) }}
            </template>
            <div class="row">
                <div class="col-xs-12 col-md-6">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.name'))} (${translate('generic.mandatory')})` }}</label>
                        <input type="text" class="form-control" v-model="tutor.name" required @input="dirty=true">
                    </div>
                </div>
                <div class="col-xs-12 col-md-3">
                    <div class="form-group">
                        <label>{{ ucFirst(translate('generic.telephone')) }}</label>
                        <input type="text" class="form-control" v-model="tutor.telephone" @input="dirty=true">
                    </div>
                </div>
                <div class="col-xs-12 col-md-3">
                    <div class="form-group">
                        <label>{{ ucFirst(translate('generic.telephoneextra')) }}</label>
                        <input type="text" class="form-control" v-model="tutor.telephone_extra" @input="dirty=true">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-md-6">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.email'))} (${translate('generic.mandatory')})` }}</label>
                        <input type="text" class="form-control" v-model="tutor.email" required @input="dirty=true">
                    </div>
                </div>
                <div class="col-xs-12 col-md-3">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.hexcolor'))} (${translate('generic.mandatory')})` }}</label>
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <input class="input-group-text form-control add-width add-cursor"
                                       type="color" v-model="tutor.hexcolor">
                            </div>
                            <input type="text" class="form-control" :value="tutor.hexcolor" readonly @input="dirty=true">
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-md-3">
                    <div class="form-group">
                        <label>{{ ucFirst(translate('generic.startdate')) }}</label>
                        <VueDatepicker
                            v-model="tutor.start_date"
                            v-bind="dpOptionsDate"
                            :placeholder="translate('generic.clicktoedit')"
                            @update:model-value="dirty=true"
                        />
                    </div>
                </div>
            </div>
            <!-- address fields: address_street_1, address_street_2, address_zipcode, address_city, address_country -->
            <div class="row">
                <div class="col-xs-12 col-md-6">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.streetline1'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_street_1" @input="dirty=true">
                    </div>
                </div>
                <div class="col-xs-12 col-md-6">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.streetline2'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_street_2" @input="dirty=true">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-md-4">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.zipcode'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_zipcode" @input="dirty=true">
                    </div>
                </div>
                <div class="col-xs-12 col-md-4">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.city'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_city" @input="dirty=true">
                    </div>
                </div>
                <div class="col-xs-12 col-md-4">
                    <div class="form-group">
                        <label>{{ `${ucFirst(translate('generic.country'))}` }}</label>
                        <input type="text" class="form-control" v-model="tutor.address_country" @input="dirty=true">
                    </div>
                </div>
            </div>

            <div class="row" v-if="!(tutorToEditIsLoggedinUser || tutor.id === -1)">
                <div class="col-xs-12 col-md-4">
                    <div class="form-group">
                        <material-switch
                            :switch-id="'tutorBlock_' + tutor.id"
                            v-model="tutor.is_blocked"
                            value-type="integer"
                            :label-on="translate('generic.isblockedforloginclasse')"
                            :label-off="translate('generic.isnotblockedforloginclasse')"
                            color="danger"
                            @click="dirty=true"
                        ></material-switch>
                    </div>
                </div>
                <div class="col-xs-12 col-md-4">
                    <div class="form-group">
                        <label>{{ ucFirst(translate('generic.lastactivity')) }}</label>
                        <input type="text" class="form-control" :value="displayDateTime(tutor.last_active_at)" readonly>
                    </div>
                </div>
                <!-- only show end date field if this tutor has no future events -->
                <div class="col-xs-12 col-md-4">
                    <div class="form-group">
                        <label>{{ ucFirst(translate('generic.enddate')) }}</label>
                        <input
                            v-if="tutor.has_future_events"
                            :value="ucFirst(translate('generic.hasfutureevents'))"
                            class="form-control"
                            readonly
                        >
                        <input
                            v-else
                            type="text"
                            class="form-control"
                            v-model="tutor.end_date"
                            @input="dirty=true"
                        >
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-md-3">
                    <div class="form-group">
                        <button
                            :disabled="!canBeSaved"
                            @click="saveTutor"
                            class="btn btn-secondary btn-block"
                        >
                            <i class="fa fa-save"></i>
                            {{ ucFirst(translate('generic.save'))}}
                        </button>
                    </div>
                </div>
                <div
                    v-if="!tutor.end_date && tutor.id > 0"
                    class="col-xs-12 offset-md-6 col-md-3">
                    <div class="form-group">
                        <button
                            data-toggle="modal"
                            data-target="#confirm-pw-reset"
                            class="btn btn-secondary btn-block"
                        >
                            <i class="fa fa-key"></i>
                            {{ pwResetText }}
                        </button>
                    </div>
                </div>
            </div>
        </panel>
        <are-you-sure
            modal-id="confirm-pw-reset"
            :button-text="pwResetText"
            :confirm-text="ucFirst(translate('generic.explainpwreset'))"
            @confirmclicked="resetPassword"
        />
    </div>
</template>

<script>
import { computed, ref, watch } from 'vue';
import AreYouSure from '../Layout/AreYouSure';
import Panel from '../Layout/Panel';
import MaterialSwitch from '../Layout/MaterialSwitch';
import useLang from '../../composables/useLang';
import useEditTutor from '../../composables/useEditTutor';
import useDatePicker from '../../composables/useDatePicker';
import useNoty from '../../composables/useNoty';
import useDateTime from "../../composables/useDateTime";
import useUtils from "../../composables/useUtils";
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import axios from 'axios';

const { scrollToSection } = useUtils();

export default {
    name: 'TutorGeneric',
    components: { AreYouSure, MaterialSwitch, Panel, VueDatepicker },
    updated () {
        scrollToSection('layoutSidenav_content', 500);
    },
    props: {
        currentUser: {
            type: Number,
            required: true
        }
    },
    setup (props) {
        const { saveTutor, tutor } = useEditTutor();
        const { ucFirst, translate } = useLang();
        const { dpOptions } = useDatePicker(false);
        const { dpOptions: dpOptionsDate } = useDatePicker(true);
        const { successNoty, failNoty } = useNoty();
        const { displayDateTime } = useDateTime()

        const dirty = ref(false);

        const tutorToEditIsLoggedinUser = computed(() => {
            return tutor.value?.id === props.currentUser;
        });

        const canBeSaved = computed(() => {
            return tutor.value?.name.length > 3 &&
                tutor.value?.email.length > 6 &&
                tutor.value?.email.includes('@') &&
                tutor.value?.email.includes('.') &&
                tutor.value?.hexcolor.length === 7;
        });

        watch(dirty, () => {
            // propagate the dirty flag to 'window' (global) to be able
            // to use it in the window.onbeforeunload handler
            window.dirty = dirty.value;
        });

        const pwResetText = computed(() => {
            return tutor.value?.has_password
                ? ucFirst(translate('generic.resetpassword'))
                : ucFirst(translate('generic.createpassword'));
        });

        const resetPassword = () => {
            axios.post('/api/resetpassword', { tutorid: tutor.value?.id })
                .then(response => {
                    successNoty('gelukt!', ucFirst(translate('generic.success')));
                })
                .catch(err => {
                    failNoty('Fout: ' + err, ucFirst(translate('generic.fail')));
                });
        };

        return {
            canBeSaved,
            dirty,
            displayDateTime,
            dpOptions,
            dpOptionsDate,
            pwResetText,
            resetPassword,
            saveTutor,
            translate,
            tutor,
            tutorToEditIsLoggedinUser,
            ucFirst
        };
    }
};
</script>

<style scoped>
.add-width {
    width: 3.5rem;
}
.add-cursor {
    cursor: pointer;
}
</style>
