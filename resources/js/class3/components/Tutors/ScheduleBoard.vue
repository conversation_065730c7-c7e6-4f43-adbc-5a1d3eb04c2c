<template>
    <div>
        <div class="row">
            <!-- canvas -->
            <div class="col-md-12">
                <canvas id="scheduleboard">{{ translate('generic.canvasnotsupported') }}</canvas>
            </div>
        </div>
        <div class="row my-1">
            <div class="col-md-12">
                <button
                    class="btn btn-primary"
                    type="button"
                    @click="openDetails"
                >
                    <i class="fa fa-edit"></i>
                    {{ ucFirst(translate('generic.edit')) }}
                </button>
            </div>
        </div>
        <div class="collapse" id="detailsections">
            <div class="row">
                <!-- controls -->
                <div class="col-md-8">
                    <div class="row" v-for="(timeslice, index) in timeSlices" :key="index">
                        <div class="col-md-12">
                            <button @click.prevent="fillpopupfields(timeslice, 'edit')"
                                    data-toggle="modal" data-target="#entertimeslicepopup"
                                    class="btn btn-primary btn-sm"
                            >
                                <i class="fa fa-edit"></i>
                            </button>
                            <button @click.prevent="fillpopupfields(timeslice, 'delete')"
                                    data-toggle="modal" data-target="#delete-timeslice-confirm"
                                    class="btn btn-danger btn-sm"
                            >
                                <i class="fa fa-trash"></i>
                            </button>
                            {{ timeslice.fromTime.substring(0,5)}} - {{timeslice.endTime.substring(0,5) }}
                                ({{ transDaynames[timeslice.dayNumber-1] }})
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary btn-sm float-right"
                            @click.prevent="clearpopupfields"
                            data-toggle="modal"
                            data-target="#entertimeslicepopup"
                    >
                        <i class="fa fa-plus"></i> {{ addtimeslicebtntitle }}
                    </button>
                </div>
            </div>
        </div>

        <!-- popup enter timeslice specs -->
        <modal
            :popup-title="ucFirst(translate('generic.entertimeslice'))"
            :closetext="ucFirst(translate('generic.close'))"
            modal-id="entertimeslicepopup"
        >
            <div class="row">
                <div class="col-md-4">
                    <label for="daynumber">{{ucFirst(translate('generic.enterday'))}}: </label>
                    <select id="daynumber" v-model="chosenDay" class="form-control">
                        <option v-for="(dayName,index) in transDaynames" :value="(index+1)" :key="index">{{dayName}}</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="fromtime">{{ucFirst(translate('generic.enterfromtime'))}}: </label>
                    <input id="fromtime" class="form-control" required
                           v-model="chosenFromTime" :placeholder="translate('generic.hhmm')"
                    >
                </div>
                <div class="col-md-4">
                    <label for="endtime">{{ucFirst(translate('generic.entertotime'))}}: </label>
                    <input id="endtime" class="form-control" required
                           v-model="chosenEndTime" :placeholder="translate('generic.hhmm')"
                    >
                </div>
            </div>

            <div class="alert alert-danger alert-small mt-1" v-if="timeError !== ''">{{timeError}}</div>

            <template v-slot:okbutton>
                <button  type="button" class="btn btn-success"
                        data-dismiss="modal" @click.prevent="saveTimeslice" :disabled="timeError!==''"
                >
                    {{ ucFirst(translate('generic.save')) }}
                </button>
            </template>
        </modal>

        <!-- popup delete timeslice -->
        <modal
            :popup-title="ucFirst(translate('generic.areyousure'))"
            :closetext="ucFirst(translate('generic.cancel'))"
            modal-id="delete-timeslice-confirm">
            <div>
                <strong>{{ucFirst(translate('generic.youareabouttodeletetimeslice'))}}:</strong>
                <div class="alert alert-success">
                    {{transDaynames[deleteableTimeslice.dayNumber-1]}}:
                        {{deleteableTimeslice.fromTime.substring(0,5)}} - {{deleteableTimeslice.endTime.substring(0,5)}}
                </div>
            </div>
            <p>
                {{ucFirst(translate('generic.thiscannotbeundone'))}}
            </p>
            <template v-slot:okbutton>
                <button  type="button" class="btn btn-danger"
                    data-dismiss="modal" @click.prevent="deleteTimeslice">{{translate('generic.delete')}}
                </button>
            </template>
        </modal>
    </div>
</template>

<script>
/*
A timeslice is defined by
- a day number (1 .. number of daynames)
- a starttime
- an endtime
*/
import Modal3 from '../Layout/Modal3';
import axios from 'axios';
import _ from 'lodash';
import $ from 'jquery';
import useLang from '../../composables/useLang';
import moment from 'moment';

const { translate, ucFirst } = useLang();

export default {
    name: 'ScheduleBoard',
    setup () {
        return {
            translate,
            ucFirst
        };
    },
    data: () => {
        return {
            canvas: null,
            ctx: null,
            startTime: 8,
            endTime: 23,
            canvasWidth: 800,
            canvasHeight: 325,
            lineHeight: 40, // for lines of text
            rowNamesWidth: 80, // left margin containing the rownames
            textpaddinghor: 2,
            textpaddingvert: 20,
            timeSlices: [],
            dayrowYcoor: [],
            timecolXcoor: [],
            transDaynames: [], // translated day names
            chosenDay: 0,
            chosenFromTime: '',
            chosenEndTime: '',
            editableTimeslice: null,
            deleteableTimeslice: { dayNumber: 0, fromTime: '', endTime: '' },
            blue: { rgb: '55,152,207', hex: '#3798cf' },
            red: { rgb: '189,83,48', hex: '#bd5330' },
            green: { rgb: '49,184,154', hex: '#31b89a' },
            timeError: '' // show user feedback if time entry is not complete or invalid
        };
    },
    props: {
        addtimeslicebtntitle: {
            type: String,
            required: true
        },
        maxnrofhours: {
            validator: (val) => {
                // only positive whole numbers, being at least 1
                const regex = /^[1-9]([0-9]+)?$/gm;
                return regex.exec(val);
            },
            required: true
        },
        tutorId: {
            type: Number,
            required: true
        }
    },
    mounted () {
        console.log('ScheduleBoard mounted.');
        const dayNames =
                `${ucFirst(translate('localisation.monday'))},
                ${ucFirst(translate('localisation.tuesday'))},
                ${ucFirst(translate('localisation.wednesday'))},
                ${ucFirst(translate('localisation.thursday'))},
                ${ucFirst(translate('localisation.friday'))},
                ${ucFirst(translate('localisation.saturday'))},
                ${ucFirst(translate('localisation.sunday'))},`;
        this.transDaynames = dayNames.split(',');
        // remove CRLF and other whitespace
        this.transDaynames.forEach((item, index, arr) => {
            arr[index] = item.trim();
        });
        this.canvas = document.getElementById('scheduleboard');
        this.canvas.width = this.canvasWidth;
        this.canvas.height = this.canvasHeight;
        this.canvas.style.width = this.canvasWidth;
        this.canvas.style.height = this.canvasHeight;
        this.ctx = this.canvas.getContext('2d');
        this.getCurrentTimeSlicesFromDB();
    },
    components: {
        modal: Modal3
    },
    methods: {
        /**
             * (re-)builds the complete board
             * this function is called initially plus everytime the number of timeslices or the content of the timeslices change
             */
        render () {
            if (this.endTime <= this.startTime) {
                console.log('endtime must be bigger than starttime');
                return;
            }
            // clear canvas for re-render
            this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
            this.dayrowYcoor = [];

            // get dimensions to calculate with
            const nrOfFullHours = this.endTime - this.startTime;
            const nrOfPixelsOf1Hour = Math.floor((this.canvasWidth - this.rowNamesWidth) / nrOfFullHours);
            const nrOfPixelsOf1Minute = Math.abs(nrOfPixelsOf1Hour / 60);

            console.log(`1 hour is ${nrOfPixelsOf1Hour} pixels. 1 minute is ${nrOfPixelsOf1Minute} pixels`);
            // building the raster of hours (vertical) ....
            for (let i = 0; i < nrOfFullHours; i++) {
                // vertical lines
                this.ctx.strokeStyle = `rgba(${this.red.rgb},0.9)`;
                this.ctx.beginPath();
                this.ctx.moveTo(i * nrOfPixelsOf1Hour + this.rowNamesWidth, 0);
                this.ctx.lineTo(i * nrOfPixelsOf1Hour + this.rowNamesWidth, this.canvasHeight);
                this.ctx.stroke();
                this.ctx.closePath();
                // time texts
                this.ctx.fillStyle = 'black';
                this.ctx.font = '12px Arial';
                const time = (i + this.startTime) + ':00';
                // save the hour x-coord for use in rendering timeslices
                this.timecolXcoor[i + this.startTime] = i * nrOfPixelsOf1Hour + this.rowNamesWidth;
                this.ctx.fillText(time, this.timecolXcoor[i + this.startTime] + this.textpaddinghor, this.textpaddingvert);
            }
            // ...and days (horizontal)
            for (let i = 1; i <= this.transDaynames.length; i++) {
                // seperator line (horizontal
                this.ctx.beginPath();
                this.ctx.moveTo(0, (i) * (this.lineHeight));
                this.ctx.lineTo(this.canvasWidth, (i) * (this.lineHeight));
                this.ctx.stroke();
                this.ctx.closePath();
                // rowtext
                this.ctx.fillStyle = 'black';
                this.ctx.font = '12px Arial';
                // save the height of the day-row for later use in timeslices
                this.dayrowYcoor[i] = ((i + 1) * this.lineHeight) - (Math.floor(this.lineHeight / 2));
                this.ctx.fillText(this.transDaynames[i - 1], (this.textpaddingvert / 2), this.dayrowYcoor[i]);
            }
            // headers
            // horizontal bar for column titles
            this.ctx.fillStyle = `rgba(${this.red.rgb},0.3)`;
            this.ctx.fillRect(0, 0, this.canvasWidth, this.lineHeight);
            // horizontal bar for column titles
            this.ctx.fillStyle = `rgba(${this.blue.rgb},0.3)`;
            this.ctx.fillRect(0, 0, 80, this.canvasHeight);

            // Last seperator line (horizontal), one more is the previous i plus 1 (but i is out of scope)
            const i = this.transDaynames.length;
            this.ctx.beginPath();
            this.ctx.moveTo(0, (i + 1) * (this.lineHeight));
            this.ctx.lineTo(this.canvasWidth, (i + 1) * (this.lineHeight));
            this.ctx.stroke();
            this.ctx.closePath();

            // render timeslices
            for (const timeSlice of this.timeSlices) {
                const timeCoor = this.getTimeCoor(timeSlice.fromTime, nrOfPixelsOf1Minute);
                const timeWidth = this.getTimeCoor(timeSlice.endTime, nrOfPixelsOf1Minute) - timeCoor;
                this.ctx.fillStyle = this.blue.hex;
                // render timeslice bar
                this.ctx.fillRect(timeCoor, (this.dayrowYcoor[timeSlice.dayNumber] - (this.lineHeight / 4)), timeWidth, this.lineHeight / 2);
            }
        },

        /**
             * Get current state from DB
             */
        getCurrentTimeSlicesFromDB () {
            axios.get(`/api/getAvailabilityOfTutor/${this.tutorId}`)
                .then((resp) => {
                    // clear current content
                    this.timeSlices = [];
                    console.log(`received ${resp.data.length} timeslice(s)`);
                    // [{id: 1, tutor_id: 3, day_number: 2, from_time: "10:00:00", to_time: "12:30:00",…}]
                    resp.data.forEach((av) => {
                        this.timeSlices.push({ tsId: av.id, dayNumber: av.day_number, fromTime: av.from_time, endTime: av.to_time });
                    });
                    this.render();
                })
                .catch((error) => {
                    console.log('Error retrieving timeslices for tutor: ' + error);
                });
        },

        /**
             * ask user input for timeslice specs (daynr, start / end time)
             * and add to timeslices
             */
        saveTimeslice () {
            // default: insert
            let url = '/api/addTimeslice'; let method = 'post';
            let data = {
                chosenFromTime: this.chosenFromTime,
                chosenEndTime: this.chosenEndTime,
                dayNumber: this.chosenDay,
                tutorId: this.tutorId
            };
                // update or insert?
            if (this.editableTimeslice && this.editableTimeslice.tsId) {
                method = 'put';
                url = '/api/updateTimeslice';
                data = {
                    chosenFromTime: this.chosenFromTime,
                    chosenEndTime: this.chosenEndTime,
                    dayNumber: this.chosenDay,
                    tutorId: this.tutorId,
                    timesliceId: this.editableTimeslice.tsId
                };
            }
            axios({
                method,
                url,
                data
            })
                .then((response) => {
                    this.getCurrentTimeSlicesFromDB();
                })
                .catch((error) => {
                    console.log('Error saving timeslice: ' + error);
                })
                .then(() => {
                    // reset
                    this.editableTimeslice = null;
                });
        },
        /**
             * remove a timeslice. confirmation has already been asked
             */
        deleteTimeslice () {
            if (this.deleteableTimeslice && this.deleteableTimeslice.tsId) {
                const url = '/api/deleteTimeslice'; const method = 'delete';
                const data = {
                    tutorId: this.tutorId,
                    timesliceId: this.deleteableTimeslice.tsId
                };
                axios({
                    method,
                    url,
                    data
                })
                    .then((result) => {
                        this.getCurrentTimeSlicesFromDB();
                    })
                    .catch((error) => {
                        console.log('Error deleting timeslice: ' + error);
                    })
                    .then(() => {
                        this.deleteableTimeslice = { dayNumber: 0, fromTime: '', endTime: '' };
                    });
            }
        },

        /**
             * reset fields of pupup
             */
        clearpopupfields () {
            this.chosenDay = 0;
            this.chosenFromTime = '';
            this.chosenEndTime = '';
        },
        fillpopupfields (timeslice, mode) {
            if (mode === 'edit') {
                this.editableTimeslice = timeslice;
                this.chosenDay = timeslice.dayNumber;
                this.chosenFromTime = timeslice.fromTime.substr(0, 5);
                this.chosenEndTime = timeslice.endTime.substr(0, 5);
            } else {
                this.deleteableTimeslice = timeslice;
                this.chosenDay = 0;
                this.chosenFromTime = '';
                this.chosenEndTime = '';
            }
        },
        /**
             * calculate the y-pixel to use for a certain time
             * @param time
             * @param nrOfPixelsOf1Minute
             */
        getTimeCoor (time, nrOfPixelsOf1Minute) {
            const parts = time.split(':');
            const minutes = parts[1];
            const hours = parts[0];
            // 09 should be 9 (as index for the array
            const hourIndex = (hours.substr(0, 1) === '0') ? hours.substr(1) : hours;
            if (this.timecolXcoor[hourIndex] === undefined) {
                const max = (this.timecolXcoor.length - 1);
                // fixme: too small or too big? now works only for too big
                console.log(`cant render ${hours}:${minutes}. too big? max= ${max} inserting biggest possible value.`);
                return this.canvasWidth;
            }
            return this.timecolXcoor[hourIndex] + (minutes * nrOfPixelsOf1Minute);
        },

        /**
             *
             * @param canvas
             * @param evt
             * @returns {{x: number, y: number}}
             */
        getMousePos (canvas, evt) {
            const rect = canvas.getBoundingClientRect();
            return {
                x: evt.clientX - rect.left,
                y: evt.clientY - rect.top
            };
        },

        /**
             *
             */
        checkTimeEntries () {
            let errorFrom = ''; let errorEnd = ''; let errorGen = '';

            // checks from time
            if (this.chosenFromTime === '') {
                errorFrom = translate('generic.timeisempty', { field: _.capitalize(translate('generic.from')) });
            } else {
                // check number
                if (!/^\d\d?:?\d?\d?$/gm.exec(this.chosenFromTime)) {
                    errorFrom = translate('generic.chosentimeinvalidchar', { field: _.capitalize(translate('generic.from')) });
                } else {
                    // we seem to have a valid time, check if is is before startTime
                    const chosenFromHour = parseInt(this.chosenFromTime.split(':')[0]);
                    if (chosenFromHour < this.startTime) {
                        errorFrom = translate('generic.timeistooearly', { field: _.capitalize(translate('generic.from')) });
                    }
                }
            }
            if ((errorFrom === '')) {
                if (!/^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/gm.exec(this.chosenFromTime)) {
                    errorFrom = translate('generic.timeisnotvalid', { field: _.capitalize(translate('generic.from')) });
                }
            }
            // checks end time
            if (this.chosenEndTime === '') {
                errorFrom = translate('generic.timeisempty', { field: _.capitalize(translate('generic.until')) });
            } else {
                // check number
                if (!/^\d\d?:?\d?\d?$/gm.exec(this.chosenEndTime)) {
                    errorEnd = translate('generic.chosentimeinvalidchar', { field: _.capitalize(translate('generic.until')) });
                }
            }

            if ((errorEnd === '')) {
                if (!/^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/gm.exec(this.chosenEndTime)) {
                    errorEnd = translate('generic.timeisnotvalid', { field: _.capitalize(translate('generic.until')) });
                }
            }

            // if no errors so far: check if from < to
            if ((errorFrom === '') && (errorEnd === '')) {
                const fromtime = moment(this.chosenFromTime, 'HH:mm:ss'); // hour may only have 1 digit
                const endtime = moment(this.chosenEndTime, 'HH:mm:ss'); // hour may only have 1 digit
                if (fromtime > endtime) {
                    errorGen = _.capitalize(translate('generic.fromtimegreaterthanendtime'));
                }
                if (this.chosenDay === 0) {
                    errorGen = _.capitalize(translate('generic.pleaseselectaday'));
                }
            }

            if ((errorFrom !== '') && (errorEnd !== '')) {
                this.timeError = errorFrom + ', ' + errorEnd;
            } else if ((errorFrom === '') && (errorEnd !== '')) {
                this.timeError = errorEnd;
            } else {
                this.timeError = errorFrom;
            }
            // errorgen can only be filled if errorFrom and errorEnd are empty
            if (errorGen !== '') {
                this.timeError = errorGen;
            }
        },
        /**
             * somehow the attributes version (data-toggle) does not work
             * possible a problem with Vue / virtual DOM
             * JS seems to do the job though
             */
        openDetails () {
            // toggle details view
            $('#detailsections').collapse('toggle');
        }
    },
    watch: {
        chosenFromTime () {
            this.checkTimeEntries();
        },

        chosenEndTime () {
            this.checkTimeEntries();
        },

        chosenDay () {
            this.checkTimeEntries();
        },
        tutorId () {
            this.getCurrentTimeSlicesFromDB();
        }
    }
};
</script>

<style scoped>
    #scheduleboard {
        border: solid #aaa 1px;
    }
    .mt-1 {
        margin-top: 1em;
    }
    .alert-small {
        padding-top: 2px;
        padding-bottom: 2px;
    }
</style>
