<template>
    <div>
        <panel>
            <template v-slot:title>
                {{ ucFirst(translate('generic.activetutors')) }}<br/>
                <small>{{ ucFirst(translate('generic.explaindeleterestrictiontutor')) }}</small>
            </template>
            <template v-slot:subtitle>
                <button
                    class="btn btn-success"
                    @click="createNewTutor"
                >
                    {{ ucFirst(translate('generic.newtutor')) }}
                </button>
            </template>
            <table class="table">
                <thead>
                <tr>
                    <th class="text-center">{{ ucFirst(translate('generic.delete')) }}</th>
                    <th class="text-center">{{ ucFirst(translate('generic.edit')) }}</th>
                    <th class="text-center">{{ ucFirst(translate('generic.classeaccess')) }}</th>
                    <th class="text-center">{{ ucFirst(translate('generic.color')) }}</th>
                    <th>{{ ucFirst(translate('generic.name')) }}</th>
                    <th>{{ ucFirst(translate('generic.startdate')) }}</th>
                    <th>{{ ucFirst(translate('generic.email')) }}</th>
                    <th>{{ ucFirst(translate('generic.telephone')) }}</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="tutor in activeTutors" :key="tutor.id">
                    <td class="text-center">
                        <button
                            v-if="!tutor.inUse"
                            class="btn btn-danger"
                            data-toggle="modal"
                            data-target="#confirm-delete-tutor"
                            @click="tutorToDelete=tutor.id"
                        >
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                    <td class="text-center">
                        <button
                            class="btn btn-success"
                            @click="idToEdit = tutor.id"
                        >
                            <i class="fa fa-edit"></i>
                        </button>
                    </td>
                    <td class="text-center">
                        <span
                            v-if="(!tutor.is_blocked) && tutor.has_password"
                            class="text-success"
                        >
                            <h3><i class="fa fa-unlock"></i></h3>
                        </span>
                        <span
                            v-else
                            class="text-danger"
                        >
                            <h3><i class="fa fa-lock"></i></h3>
                        </span>
                    </td>
                    <td class="text-center">
                        <span
                            class="badge"
                            :style="'width:2rem;background-color:' + tutor.hexcolor"
                        >&nbsp;</span>
                    </td>
                    <td>{{ tutor.name }}</td>
                    <td>{{ displayDate(tutor.start_date) }}</td>
                    <td><a :href="'mailto:' + tutor.email">{{ tutor.email }}</a></td>
                    <td>{{ tutor.telephone }}</td>
                    <td>{{ tutor.telephone_extra }}</td>
                </tr>
                </tbody>
            </table>
        </panel>
        <panel>
            <template v-slot:title>
                {{ ucFirst(translate('generic.inactivetutors')) }}<br/>
                <small>{{ ucFirst(translate('generic.explaindeleterestrictiontutor')) }}</small>
            </template>
            <table class="table">
                <thead>
                <tr>
                    <th class="text-center">{{ ucFirst(translate('generic.delete')) }}</th>
                    <th class="text-center">{{ ucFirst(translate('generic.edit')) }}</th>
                    <th>{{ ucFirst(translate('generic.name')) }}</th>
                    <th>{{ ucFirst(translate('generic.startdate')) }}</th>
                    <th>{{ ucFirst(translate('generic.enddate')) }}</th>
                    <th>{{ ucFirst(translate('generic.email')) }}</th>
                    <th>{{ ucFirst(translate('generic.telephone')) }}</th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="tutor in inactiveTutors" :key="tutor.id">
                    <td class="text-center">
                        <button
                            v-if="!tutor.inUse"
                            class="btn btn-danger"
                            data-toggle="modal"
                            data-target="#confirm-delete-tutor"
                        >
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                    <td class="text-center">
                        <button
                            class="btn btn-success"
                            @click="idToEdit = tutor.id"
                        >
                            <i class="fa fa-edit"></i>
                        </button>
                    </td>
                    <td>{{ tutor.name }}</td>
                    <td>{{ displayDate(tutor.start_date) }}</td>
                    <td>{{ displayDate(tutor.end_date) }}</td>
                    <td><a :href="'mailto:' + tutor.email">{{ tutor.email }}</a></td>
                    <td>{{ tutor.telephone }}</td>
                    <td>{{ tutor.telephone_extra }}</td>
                </tr>
                </tbody>
            </table>
        </panel>
        <are-you-sure
            :button-text="ucFirst(translate('generic.deletetutor'))"
            modal-id="confirm-delete-tutor"
            @confirmclicked="deleteTutor(tutorToDelete)"
        ></are-you-sure>
    </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import Panel from '../Layout/Panel';
import AreYouSure from '../Layout/AreYouSure';
import axios from 'axios';
import useLang from '../../composables/useLang';
import useEditTutor from '../../composables/useEditTutor';
import useDateTime from "../../composables/useDateTime";

const { ucFirst, translate } = useLang();
const { idToEdit, deleteTutor } = useEditTutor();
const { displayDate } = useDateTime();
const tutorToDelete = ref(0);
const tutors = ref([]);

/**
 * update the list of tutors if we change anything in the edit form
 */
window.emitter.on('updatetutors', () => {
    getAllTutors();
});

const getAllTutors = async () => {
    const resp = await axios.get('/api/tutors');
    tutors.value = resp.data.data;
};

onMounted(async () => {
    await getAllTutors();
    // check if we want to create a new tutor directly
    const params = window.location.search;
    if (params.includes('action=new')) {
        createNewTutor();
    }
});

const activeTutors = computed(() => {
    return tutors.value.filter(
        row => row.end_date == null ||
            row.end_date === '' ||
            (new Date(row.end_date) > new Date())
    );
});

const inactiveTutors = computed(() => {
    return tutors.value.filter(row => {
        return row.end_date != null && (new Date(row.end_date) <= new Date());
    });
});

const createNewTutor = () => {
    idToEdit.value = -1;
};

</script>

<style scoped>

</style>
