<template>
    <div v-if="registrationsForThisYear">
        <strong>{{ ucFirst(translate('reporting.registrationsthisyear')) }}</strong>
        <div class="row">
            <div class="col-md-12">
                <label class="mr">{{ ucFirst(translate('generic.schoolyear')) }}</label>
                {{ registrationsForThisYear.schoolyearlabel }}
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.firstregistration')) }}</th>
                <th>{{ ucFirst(translate('generic.studentname')) }}</th>
                <th>{{ ucFirst(translate('generic.coursename')) }}</th>
                <th>{{ ucFirst(translate('generic.nrofappointments'))}} {{translate('generic.scheduled')}}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(registration, index) in filteredRegistrations" :key="index">
                <td>{{ registration.start_date }}</td>
                <td>{{ getStudentName(registration.studentname) }}</td>
                <td>
                    {{ registration.coursename }}
                    {{ registration.course.recurrenceoption.description }}
                </td>
                <td>{{ registration.nrOfEvents }}</td>
            </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup>
import { computed } from "vue";
import useLang from "../../composables/useLang";
import useRegistration from "../../composables/useRegistrations";
const { ucFirst, translate, translateChoice } = useLang();
const { registrationsForThisYear } = useRegistration();
const getStudentName = (studentname) => {
    // if it starts with "-" it's a course group,
    if (studentname.startsWith("-")) {
        return `${studentname.substring(1).trim()} (${translateChoice("generic.groups", 1)})`;
    } else {
        return studentname;
    }
}
const filteredRegistrations = computed(() => {
    return registrationsForThisYear.value.registrations.filter(registration => !registration.studentname.startsWith("-"));
});
</script>
