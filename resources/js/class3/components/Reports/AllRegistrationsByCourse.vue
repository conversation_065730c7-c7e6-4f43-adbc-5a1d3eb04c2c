<template>
    <div v-if="registrationsForCourses">
        <strong>{{ ucFirst(translate('reporting.all_registrations_by_course')) }}</strong>
        <div class="row">
            <div class="col-md-3">
                <label>{{ ucFirst(translate('generic.totalnrofstudents')) }}</label>
            </div>
            <div class="col-md-9">
                {{ registrationsForCourses["total-students"] }}
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <label>{{ ucFirst(translate('generic.totalnrofcourses')) }}</label>
            </div>
            <div class="col-md-9">
                {{ registrationsForCourses["total-courses"] }}
            </div>
        </div>
        <hr>
        <table class="table">
            <thead>
                <tr>
                    <th>{{ ucFirst(translate('generic.course')) }}</th>
                    <th>{{ ucFirst(translate('generic.nrofregistrations')) }}</th>
                    <th>{{ ucFirst(translate('generic.unregistered')) }}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(course, index) in registrationsForCourses.courses" :key="index">
                    <td>
                        <strong>{{ course.name }}</strong>
                        {{ course.recurrenceoption.description }}
                    </td>
                    <td>{{ course.nstudents }}</td>
                    <td>{{ course.unregistered }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup>
import useLang from "../../composables/useLang";
import useRegistration from "../../composables/useRegistrations";
const { registrationsForCourses } = useRegistration();

const { ucFirst, translate } = useLang();

</script>

<style scoped>

</style>
