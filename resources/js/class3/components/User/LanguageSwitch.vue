<template>
    <div>
        <strong>{{ ucFirst(translate('generic.preferredlanguage')) }}</strong>
        <div class="row" v-if="profile">
            <div class="col-12 col-xl-12">
                <div class="d-flex flex-column">
                    <label class="mb-2" v-tooltip="translate('generic.dutch')">
                        <input type="radio" v-model="profile.preferred_language" value="nl" class="me-2" />
                        <img src="/images/nl.svg" class="lang-img" />
                    </label>
                    <label v-tooltip="translate('generic.english')">
                        <input type="radio" v-model="profile.preferred_language" value="en" class="me-2" />
                        <img src="/images/gb.svg" class="lang-img" />
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { watch } from 'vue';
import useLang from '../../composables/useLang.js';
import useProfile from '../../composables/useProfile.js';

const { ucFirst, translate } = useLang();
const { profile } = useProfile();

// Set language after profile is loaded
watch(profile, (newProfile) => {
    if (newProfile) {
        newProfile.preferred_language = newProfile.preferred_language || newProfile.domain?.language;
    }
}, { immediate: true });
</script>

<style scoped>
.lang-img {
    width: 1.5625rem;
    height: 1.25rem;
}
</style>
