<template>
    <div>
        <!-- This is the popup content for changing password -->
        <div class="mb-3">
            <label for="pw" class="form-label">{{ucFirst(translate('generic.currentpassword'))}}</label>
            <input type="password" class="form-control" v-model="currentPw"/>
        </div>
        <!--  pw, repeat pw -->
        <div class="mb-3">
            <label for="pw" class="form-label">{{ucFirst(translate('generic.newpassword'))}}</label>
            <input type="password" class="form-control" v-model="newPw"/>
        </div>
        <div class="mb-3">
            <label for="pw" class="form-label">{{ucFirst(translate('generic.repeatnewpassword'))}}</label>
            <input type="password" class="form-control" v-model="repeatNewPw"/>
        </div>
        <div v-if="passwordErrors.length > 0">
            <small class="text-danger">
                {{ucFirst(translate('generic.yournewpasswordneedsto'))}}:
                <ul>
                    <li v-for="error in passwordErrors">{{error}}</li>
                </ul>
            </small>
        </div>
    </div>
</template>

<script setup>
import useLang from '../../composables/useLang.js';
import usePassword from '../../composables/usePassword.js';
const { ucFirst, translate } = useLang();
const { currentPw, newPw, repeatNewPw, passwordErrors} = usePassword();

</script>
