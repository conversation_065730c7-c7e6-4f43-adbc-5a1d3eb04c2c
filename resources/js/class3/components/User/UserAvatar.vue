<template>
<img :src="userImage" class="round-avatar" alt="user image">
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
    avatar: {
        type: String,
        default: ""
    },
});

const userImage = computed(() => {
    return props.avatar === '' ? "/images/no-profile.png" : props.avatar;
});
</script>

<style scoped>
.round-avatar {
    border-radius: 50%;
    width: 40px;
    height: 40px;
}
</style>
