<template>
    <div>
        <strong>{{ ucFirst(translate('generic.deletestudent')) }}</strong>
        <div class="row">
            <div class="col-12 col-xl-12">
                <div class="mb-3">
                    <label class="form-label">
                        {{ ucFirst(translate('generic.uniquestudentidentifier')) }}
                        <span v-tooltip="translate('generic.studenteditformtopright')">
                            <i class="fa fa-question-circle"></i>
                        </span>
                    </label>
                    <input class="form-control" v-model="studentId"/>
                </div>
            </div>
        </div>
        <div class="row" v-if="validStudentId">
            <div class="col-12 col-xl-12">
                <button
                    class="btn btn-danger"
                    @click="getStudentData()"
                    data-bs-toggle="modal"
                    data-bs-target="#delStudent"
                >
                    {{ ucFirst(translate('generic.deletethisstudent')) }}
                </button>
            </div>
        </div>
        <Modal
            modal-id="delStudent"
            :popup-title="ucFirst(translate('generic.deletestudent'))"
            :closetext="ucFirst(translate('generic.close'))"
            @closebtnclick="resetStudentId"
        >
            <p>{{ translate('generic.studentsfirstnameis') }}: <strong>{{ studentDataById.firstname }}</strong></p>
            <p>{{ translate('generic.pleaseenterlastnameascheck') }}:</p>
            <input class="form-control" v-model="delStudentLastname"/>
            <template v-slot:okbutton>
                <button class="btn btn-danger" @click="deleteStudent()" data-bs-dismiss="modal">
                    {{ ucFirst(translate('generic.deletethisstudent')) }}
                </button>
            </template>
        </Modal>
    </div>
</template>

<script setup>
import useLang from '../../composables/useLang.js';
import useStudent from '../../composables/useStudent.js';
import Modal from '../Layout/bs5/Modal4.vue';

const { ucFirst, translate } = useLang();
const {
    deleteStudent,
    delStudentLastname,
    getStudentData,
    resetStudentId,
    studentId,
    studentDataById,
    validStudentId
} = useStudent();

</script>
