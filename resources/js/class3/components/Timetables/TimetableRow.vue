<template>
    <div class="row mb-2">
        <div class="col-md-2">
            <a :href="studentLink">
                {{ studentName }}
            </a>
        </div>
        <div class="col-md-2">{{ timetable.course_name }}</div>
        <div class="col-md-3">{{ timetable.recurrence }}</div>
        <div class="col-md-5" v-if="timetable.event_summary">
            <span class="badge badge-info">
                {{ ucFirst(translate('generic.billable')) }}:
                {{ numberOfBillableEvents }}
            </span>
            <span class="badge badge-primary mr-1">
                {{ ucFirst(translateChoice('generic.totals', 1)) }}:
                {{ timetable.event_summary['total-events'] }}
            </span>
            <span class="badge badge-success mr-1">
                {{ ucFirst(translateChoice('generic.futureappointments', timetable.event_summary.future)) }}:
                {{ timetable.event_summary.future }}
            </span>
            <span class="badge badge-secondary mr-1">
                {{ ucFirst(translate('generic.past')) }}:
                {{ timetable.event_summary.past }}
            </span>
            <span v-if="timetable.event_summary.blocked > 0" class="badge badge-warning">
                {{ ucFirst(translate('generic.blocked')) }}:
                {{ timetable.event_summary.blocked }}
            </span>
        </div>
        <div class="col-md-5" v-else>
            <!-- should never happen -->
            <span class="badge badge-primary mr-1">
                {{ ucFirst(translateChoice('generic.totals', 1)) }}:
                {{ timetable.event_count }} }}
            </span>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import useLang from "../../composables/useLang";

const { ucFirst, translate, translateChoice } = useLang();

const props = defineProps({
    timetable: {
        type: Object,
        required: true
    },
    isGroup: {
        type: Boolean,
        default: false
    }
});

// Compute the student name based on whether it's a group or individual
const studentName = computed(() => {
    return props.isGroup
        ? props.timetable.student_last_name
        : props.timetable.student_name;
});

// Compute the link based on whether it's a group or individual
const studentLink = computed(() => {
    return props.isGroup
        ? `/timetableedit/${props.timetable.registration_id}`
        : `/students/${props.timetable.student_id}/edit`;
});

const numberOfBillableEvents = computed(() => {
    return props.timetable.event_summary['total-events'] - props.timetable.event_summary.blocked;
});
</script>
