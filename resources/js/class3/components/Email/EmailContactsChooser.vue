<template>
    <panel :collapsible="true">
        <template #title>
            <i class="fas fa-address-book"></i>
            {{ ucFirst(translate("generic.targetedmailcontacts")) }}
        </template>
        <div class="row">
            <div class="col-3">
                <strong>{{ ucFirst(translate("generic.mailto")) }}:</strong>
                <!-- checkboxes: students, staff -->
                <div class="form-check mt-2">
                    <input
                        class="form-check-input"
                        type="checkbox"
                        id="students"
                        name="students"
                        value="students"
                        v-model="cbStudents"
                    />
                    <label class="form-check-label" for="students">
                        {{ ucFirst(translateChoice("generic.students", 2)) }}
                    </label>
                </div>
                <div class="form-check">
                    <input
                        class="form-check-input"
                        type="checkbox"
                        id="staff"
                        name="staff"
                        value="staff"
                        v-model="cbStaff"
                    />
                    <label class="form-check-label" for="staff">
                        {{ ucFirst(translateChoice("generic.staff", 2)) }}
                    </label>
                </div>
                <hr/>
                <label for="extra_email" class="mt-2">
                    <strong
                    >{{
                            ucFirst(translate("generic.optionalcommaseparatedemailaddresses"))
                        }}:</strong
                    > </label
                ><br/>
                <input
                    class="form-control"
                    type="text"
                    id="extra_email"
                    name="extra_email"
                    v-model="extraEmailAddressesRawString"
                />
            </div>

            <!-- Student filter section -->
            <div class="col-2" v-if="cbStudents">
                <strong
                >{{
                        ucFirst(translate("generic.preferstudentemailthataremarkedfor"))
                    }}:</strong
                >
                <span v-tooltip="ucFirst(translate('generic.explainifchosentypenotfound'))">
          <i class="fas fa-info-circle"></i>
        </span>
                <!-- radiobuttons: financial, planning, promotions -->
                <div class="form-check mt-2">
                    <input
                        class="form-check-input"
                        type="radio"
                        id="financial"
                        name="mail_marked"
                        value="financial"
                        v-model="emailType"
                    />
                    <label class="form-check-label" for="financial">
                        {{ translate("generic.useforfinance") }}
                    </label>
                </div>
                <div class="form-check">
                    <input
                        class="form-check-input"
                        type="radio"
                        id="planning"
                        name="mail_marked"
                        value="planning"
                        v-model="emailType"
                    />
                    <label class="form-check-label" for="planning">
                        {{ translate("generic.useforplanning") }}
                    </label>
                </div>
                <div class="form-check">
                    <input
                        class="form-check-input"
                        type="radio"
                        id="promotions"
                        name="mail_marked"
                        value="promotions"
                        v-model="emailType"
                    />
                    <label class="form-check-label" for="promotions">
                        {{ translate("generic.useforpromotions") }}
                    </label>
                </div>
            </div>

            <!-- extra filtering students -->
            <div class="col-4" v-if="cbStudents">
                <div class="row">
                    <div class="col-3">
                        <label for="sbGroupFilter">
                            <strong>
                                {{ ucFirst(translate("generic.groupfilter")) }}:
                            </strong>
                        </label>
                    </div>
                    <div class="col-9">
                        <select
                            class="form-select"
                            id="sbGroupFilter"
                            v-model="sbGroupFilter"
                        >
                            <option value="none">{{ translate("generic.none") }}</option>
                            <option value="courseGroup">
                                {{ translate("generic.coursegroups") }}
                            </option>
                            <option value="studentGroup">
                                {{ translateChoice("generic.studentgroups", 2) }}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-3">
                        <small>
                            <em v-if="sbCourseGroup.length > 0 || sbStudentGroup.length > 0">
                                {{ translate("generic.keepctrlpressformultiselect") }}
                            </em>
                        </small>
                    </div>
                    <div class="col-9">
                        <template v-if="sbGroupFilter === 'courseGroup'">
                            <select
                                multiple
                                v-if="sbGroupFilter === 'courseGroup'"
                                v-model="sbCourseGroup"
                                class="form-select"
                            >
                                <option
                                    v-for="group in filteredCourseGroups"
                                    :key="group.id"
                                    :value="group.id"
                                >
                                    {{ group.name }}
                                </option>
                            </select>
                            <br/>
                            <!-- list selected coursegroups in sbCoursegroup as span elements -->
                            <!-- somewhat complex because the list sbCourseGroup has only id, not the name -->
                            <template v-if="sbCourseGroup.length > 0">
                                <span
                                    v-for="courseGroup in filteredFilteredCourseGroups"
                                    :key="courseGroup.id"
                                    class="badge rounded-pill bg-primary"
                                    @click="unselectCourseGroup(courseGroup.id)"
                                >
                                    {{ courseGroup.name }}
                                    <i class="fas fa-times"></i>
                                </span>
                            </template>
                        </template>
                        <template v-if="sbGroupFilter === 'studentGroup'">
                            <select multiple v-model="sbStudentGroup" class="form-select">
                                <option
                                    v-for="group in allStudentGroups"
                                    :key="group.id"
                                    :value="group.id"
                                >
                                    {{ group.name }}
                                </option>
                            </select>
                            <br/>
                            <!-- list selected studentgroups in sbStudentgroup as span elements -->
                            <!-- somewhat complex because the list sbStudentGroup has only id, not the name -->
                            <template v-if="sbStudentGroup.length > 0">
                                <span
                                    v-for="studentGroup in filteredStudentGroups"
                                    :key="studentGroup.id"
                                    class="badge rounded-pill bg-primary"
                                    @click="unselectStudentGroup(studentGroup.id)"
                                >
                                    {{ studentGroup.name }}
                                    <i class="fas fa-times"></i>
                                </span>
                            </template>
                        </template>
                    </div>
                </div>
            </div>

            <!-- totals section -->
            <div class="col-3">
                <strong
                >{{ ucFirst(translate("generic.counttargetemailaddresses")) }}:</strong
                ><br/>
                <span
                    v-if="cbStudents"
                    class="badge rounded-pill bg-primary"
                    data-bs-toggle="popover"
                    :data-bs-content="studentEmailAddressesAsString"
                    title="Student email addresses"
                >
          {{ ucFirst(translateChoice("generic.students", 2)) }}:
          {{ nrOfStudentEmails }}
        </span>
                <span
                    v-if="cbStaff"
                    class="badge rounded-pill bg-primary"
                    data-bs-toggle="popover"
                    :data-bs-content="staffEmailAddressesAsString"
                    title="Staff email addresses"
                >
          {{ ucFirst(translateChoice("generic.staff", 2)) }}:
          {{ nrOfStaffEmails }} </span
                ><br/>
                <span
                    v-if="extraEmailAddresses.length > 0"
                    class="badge rounded-pill bg-primary"
                    data-bs-toggle="popover"
                    :data-bs-content="extraEmailAddresses.join(', ')"
                    title="Extra email addresses"
                >
          {{ translate("generic.extraemailaddresses") }}:
          {{ extraEmailAddresses.length }}
        </span>
                <br/>
                <small v-if="cbStudents || cbStaff" class="muted">
                    <div v-if="cbStudents">
                        {{ ucFirst(translate("generic.explainactivestudents")) }}
                    </div>
                    <div v-if="cbStaff">
                        {{ ucFirst(translate("generic.explainactivestaff")) }}
                    </div>
                </small>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { onMounted, computed } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import useEmailContacts from '../../composables/useEmailContacts.js';
import useBaseData from '../../composables/useBaseData.js';
import useLang from "../../composables/useLang.js";

const { allStudentGroups, filteredCourseGroups, initBaseData } = useBaseData();
const { translate, translateChoice, ucFirst } = useLang();

const {
    cbStudents,
    cbStaff,
    emailType,
    extraEmailAddressesRawString,
    extraEmailAddresses,
    nrOfStaffEmails,
    nrOfStudentEmails,
    sbCourseGroup,
    sbGroupFilter,
    sbStudentGroup,
    staffEmailAddressesAsString,
    studentEmailAddressesAsString,
    unselectCourseGroup,
    unselectStudentGroup
} = useEmailContacts();

const filteredStudentGroups = computed(() =>
    allStudentGroups.value.filter(group => sbStudentGroup.value.includes(group?.id))
);

const filteredFilteredCourseGroups = computed(() =>
    filteredCourseGroups.value.filter(group => sbCourseGroup.value.includes(group?.id))
);

onMounted(async () => {
    await initBaseData({
        studentGroups: true,
        courseGroups: true
    });
});
</script>

<style scoped></style>
