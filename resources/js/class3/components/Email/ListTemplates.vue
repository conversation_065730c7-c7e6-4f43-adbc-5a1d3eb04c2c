<template>
    <div class="mb-1">
        <div v-if="templates.length > 0">
            <select v-model="selectedTemplateId" class="form-select mb-2">
                <option value="0">{{ ucFirst(translate('generic.pleasechooseanoption')) }}</option>
                <option v-for="mTemplate in templates" :key="mTemplate.id" :value="mTemplate.id">
                    {{mTemplate.created_at.substring(0,10)}} - {{ mTemplate.label }}
                </option>
            </select>
        </div>
        <div v-else>
            {{ ucFirst(translate('generic.notemplatesfound')) }}.
        </div>
        <a href="/mailtemplates">
            ({{ ucFirst(translate('generic.goto')) }} {{ translate('generic.mailtemplates') }})
        </a>
    </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import useMailTemplates from "../../composables/useMailTemplates.js";
import useLang from "../../composables/useLang.js";

const { translate, ucFirst } = useLang();
const { getTemplates, templates, useFor } = useMailTemplates();

onMounted(() => {
    useFor.value = props.forTarget;
    getTemplates();
});

const emit = defineEmits(['templateChosen']);
const props = defineProps({
    forTarget: {
        type: String,
        required: true
    }
});

const selectedTemplateId = ref(0);

watch(selectedTemplateId, (newValue, oldValue) => {
    if (newValue !== oldValue) {
        const selectedTemplate = templates.value.find(template => template.id === selectedTemplateId.value);
        if (selectedTemplate) {
            emit('templateChosen', {
                selectedTemplateId: selectedTemplateId.value,
                selectedTemplate: selectedTemplate
            });
        }
    }
});
</script>

<style scoped>

</style>
