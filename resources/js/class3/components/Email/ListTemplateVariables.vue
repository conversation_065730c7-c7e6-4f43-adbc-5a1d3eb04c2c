<template>
    <ul class="list-group list-group-flush">
        <li v-for="(variable, index) in templateVariables"
            :key="index"
            class="list-group-item py-1 d-flex justify-content-between align-items-center"
        >
            <span :id="'variable_' + index" class="small">{{variable}}</span>
            <button
                v-tooltip="ucFirst(translate('generic.copy'))"
                class="btn btn-primary btn-sm clipboardTarget"
                :data-clipboard-target="'#variable_' + index"
            >
                <i class="fa fa-clipboard" aria-hidden="true"></i>
            </button>
        </li>
    </ul>
</template>

<script setup>
import { onMounted } from 'vue';
import useLang from "../../composables/useLang";
import useMailTemplateVariables from "../../composables/useMailTemplateVariables";

const { translate, ucFirst } = useLang();
const { templateVariables, getTemplateVariables } = useMailTemplateVariables();

onMounted(() => {
    getTemplateVariables();
});
</script>

<style scoped>

</style>
