<template>
    <panel>
        <template #title>
            <i class="fas fa-envelope"></i>
            {{ ucFirst(translate("generic.mailbody")) }}
            <br/>
            <small>
                {{ ucFirst(translate("generic.mailto")) }}:
                <span
                    v-if="cbStudents"
                    class="badge rounded-pill bg-primary"
                    data-bs-toggle="popover"
                    :data-bs-content="studentEmailAddressesAsString"
                    title="Student email addresses"
                >
                  {{ ucFirst(translateChoice("generic.students", 2)) }}:
                  {{ nrOfStudentEmails }}
                </span>
                <span
                    v-if="cbStaff"
                    class="badge rounded-pill bg-primary"
                    data-bs-toggle="popover"
                    :data-bs-content="staffEmailAddressesAsString"
                    title="Staff email addresses"
                >
                  {{ ucFirst(translateChoice("generic.staff", 2)) }}:
                  {{ nrOfStaffEmails }}
                </span>
                <span
                    v-if="extraEmailAddresses.length > 0"
                    class="badge rounded-pill bg-primary"
                    data-bs-toggle="popover"
                    :data-bs-content="extraEmailAddresses.join(', ')"
                    title="Extra email addresses"
                >
                  {{ translate("generic.extraemailaddresses") }}:
                  {{ extraEmailAddresses.length }}
                </span>
                <div
                    v-if="cbStudents || cbStaff"
                    v-tooltip="translate('generic.warnsendmailtoselffirst')"
                    class="badge bg-danger rounded-pill"
                >
                    <i class="fa fa-exclamation-circle"/>
                </div>
            </small>
        </template>
        <div class="row">
            <div class="col-8">
                <input
                    type="text"
                    class="form-control"
                    v-model="mailSubject"
                    :placeholder="ucFirst(translate('generic.subject'))"
                />
                <hr/>
                <ckeditor
                    v-if="ClassicEditor && configMailTemplate"
                    :editor="ClassicEditor"
                    v-model="mailBody"
                    :config="configMailTemplate"
                />
            </div>
            <div class="col-4">
                <button
                    class="btn btn-primary"
                    :disabled="!sendable"
                    data-bs-toggle="modal"
                    data-bs-target="#areYouSureSendModal"
                >
                    <i class="fas fa-arrow-right"></i>
                    {{ ucFirst(translate("generic.sendemail")) }}
                </button>
                <hr/>
                <button class="btn btn-primary" @click="saveConceptToLocalStorage">
                    <i class="fas fa-save"></i>
                    {{ ucFirst(translate("generic.saveconcept")) }}
                </button>
                <span class="ms-2" v-tooltip="ucFirst(translate('generic.explainonlyoneconcept'))">
                  <i class="fas fa-question-circle"></i>
                </span>

                <div class="mt-4">
                    <button
                        v-if="hasMailConceptInLocalStorage"
                        class="btn btn-outline-success btn-sm"
                        @click="getConceptFromLocalStorage"
                    >
                        {{ ucFirst(translate("generic.restoresavedconcept")) }}
                    </button>
                    <button
                        v-if="hasMailConceptInLocalStorage"
                        class="btn btn-outline-danger btn-sm"
                        data-bs-toggle="modal"
                        data-bs-target="#areYouSureClearLSModal"
                    >
                        {{ ucFirst(translate("generic.deletesavedconcept")) }}
                    </button>
                </div>
                <hr/>
                <label>
                    {{ ucFirst(translate("generic.uploadimagesyouwillbeusing")) }}
                    <span
                        v-tooltip="
                          ucFirst(
                            translate('generic.pleaseusesmallimages', {
                              maxfilesize: convertBytesToHumanReadable(maxImageSize),
                              maxtotalfilesize:
                                convertBytesToHumanReadable(maxTotalImagesSize),
                            })
                          )
                        "
                    >
                        <i class="fas fa-question-circle"/>
                    </span>
                </label>
                <br/>
                <input type="file" @change="previewFiles" multiple/>
                <!-- info after uploading, info will be here (has preference) -->
                <div v-if="uploadedImagesResponse">
                    <ul class="thumbnail-list">
                        <li v-for="(image, index) in uploadedImagesResponse.images" :key="index">
                            <img :src="uploadedImagesResponse.baseUrlToImages + '/' + image.name_in_url"
                                 class="thumbnail"
                                 v-tooltip="image.original_name + ' (' + convertBytesToHumanReadable(image.size) + ')'"
                            />
                            <button
                                v-tooltip="translate('generic.explainimageusageinemail')"
                                @click="insertImageUrl(uploadedImagesResponse.baseUrlToImages + '/' + image.name_in_url)"
                            >
                                <i class="fa fa-copy"></i>
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- info during uploading -->
                <div v-else-if="imagesToUpload.length > 0">
                    <strong v-html="uploadMessage"/>
                    <ul>
                        <li v-for="(image, index) in imagesToUpload" :key="index">
                            {{ image.name }} ({{ image.size }} bytes)
                        </li>
                    </ul>
                </div>
                <hr/>
                <em>{{ ucFirst(translate("generic.explaindefaultsignaturewillbeadded")) }}</em>
            </div>
        </div>

        <are-you-sure
            modal-id="areYouSureSendModal"
            :button-text="ucFirst(translate('generic.yessend'))"
            :confirm-text="ucFirst(translate('generic.areyousuresendemail'))"
            @confirmclicked="sendEmail"
        />
        <are-you-sure
            modal-id="areYouSureClearLSModal"
            @confirmclicked="removeConceptFromLocalStorage"
        />
    </panel>
</template>

<script setup>
import { onMounted, onUpdated } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';

import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ClassicEditor } from 'ckeditor5';
import 'ckeditor5/ckeditor5.css';

import AreYouSure from '../Layout/bs5/AreYouSure4.vue';
import useLang from "../../composables/useLang.js";
import useConversion from '../../composables/useConversion.js';
import useEmailContacts from '../../composables/useEmailContacts.js';
import useConfigItems from '../../composables/useConfigItems.js';

const { configMailTemplate, isLayoutReady } = useConfigItems();

const { convertBytesToHumanReadable } = useConversion();
const { translate, translateChoice, ucFirst } = useLang();

const {
    cbStaff,
    cbStudents,
    extraEmailAddresses,
    getConceptFromLocalStorage,
    hasMailConceptInLocalStorage,
    insertImageUrl,
    imagesToUpload,
    mailBody,
    mailSubject,
    maxImageSize,
    maxTotalImagesSize,
    nrOfStaffEmails,
    nrOfStudentEmails,
    previewFiles,
    removeConceptFromLocalStorage,
    saveConceptToLocalStorage,
    sendable,
    sendEmail,
    staffEmailAddressesAsString,
    studentEmailAddressesAsString,
    uploadedImagesResponse,
    uploadMessage
} = useEmailContacts();

onUpdated(() => {
    // Initialize Bootstrap 5 popovers
    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl, {
        html: true,
        trigger: 'hover',
        placement: 'bottom'
    }));
});

onMounted(() => {
    isLayoutReady.value = true;
});
</script>

<style scoped>
/* Max Width of the popover depending on the container! */
.popover {
    max-width: 70%;
}

.thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border: solid 1px #ccc;
}

ul.thumbnail-list {
    margin-top: 1rem;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    padding: 0;
}
</style>
