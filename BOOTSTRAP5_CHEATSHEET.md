# Bootstrap 5 Migration Cheat Sheet

This document provides a quick reference for migrating from Bootstrap 4 to Bootstrap 5 in the Class application.

## Spacing Utility Classes

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `ml-*` | `ms-*` | Margin left → Margin start |
| `mr-*` | `me-*` | Margin right → Margin end |
| `pl-*` | `ps-*` | Padding left → Padding start |
| `pr-*` | `pe-*` | Padding right → Padding end |

## Data Attributes

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `data-toggle` | `data-bs-toggle` | For toggles like dropdowns, modals, etc. |
| `data-target` | `data-bs-target` | For targeting elements |
| `data-dismiss` | `data-bs-dismiss` | For dismissing elements like alerts, modals |
| `data-backdrop` | `data-bs-backdrop` | For modal backdrop behavior |
| `data-keyboard` | `data-bs-keyboard` | For keyboard interaction with modals |
| `data-content` | `data-bs-content` | For popover content |
| `data-trigger` | `data-bs-trigger` | For popover trigger behavior |
| `data-html` | `data-bs-html` | For HTML content in popovers/tooltips |

## Form Classes

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `form-group` | `mb-3` | Form group wrapper |
| `form-control` (for selects) | `form-select` | Select inputs |
| `custom-control` | `form-check` | Custom form controls |
| `custom-checkbox` | `form-check` | Custom checkboxes |
| `custom-radio` | `form-check` | Custom radio buttons |
| `custom-select` | `form-select` | Custom select dropdowns |
| `custom-file` | `form-control` | File inputs |
| `custom-range` | `form-range` | Range inputs |
| `custom-control-input` | `form-check-input` | Custom control inputs |
| `custom-control-label` | `form-check-label` | Custom control labels |
| `custom-switch` | `form-switch` | Toggle switches |
| No equivalent | `form-label` | New class for form labels |

## Close Button

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `<button class="close" data-dismiss="modal">×</button>` | `<button class="btn-close" data-bs-dismiss="modal"></button>` | Close button |

## Modal Component

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `data-backdrop="static"` | `data-bs-backdrop="static"` | Prevent closing when clicking outside |
| `data-keyboard="false"` | `data-bs-keyboard="false"` | Disable keyboard closing |
| `$('#myModal').modal('show')` | `new bootstrap.Modal(document.getElementById('myModal')).show()` | Show modal |
| `$('#myModal').modal('hide')` | `bootstrap.Modal.getInstance(document.getElementById('myModal')).hide()` | Hide modal |
| `$('#myModal').modal('toggle')` | `bootstrap.Modal.getInstance(document.getElementById('myModal')).toggle()` | Toggle modal |
| `$('#myModal').modal('dispose')` | `bootstrap.Modal.getInstance(document.getElementById('myModal')).dispose()` | Dispose modal |

## Popover Component

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `data-content="Text"` | `data-bs-content="Text"` | Popover content |
| `data-placement="top"` | `data-bs-placement="top"` | Popover placement |
| `data-trigger="click"` | `data-bs-trigger="click"` | Popover trigger |
| `data-html="true"` | `data-bs-html="true"` | Allow HTML in popover |
| `$('[data-toggle="popover"]').popover()` | `const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))`<br>`popoverTriggerList.map(el => new bootstrap.Popover(el))` | Initialize popovers |

## JavaScript Initialization

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `$('.tooltip').tooltip()` | `new bootstrap.Tooltip(document.querySelector('.tooltip'))` | Tooltip initialization |
| `$('#myModal').modal('show')` | `new bootstrap.Modal(document.getElementById('myModal')).show()` | Modal initialization |
| `$('.dropdown').dropdown()` | `new bootstrap.Dropdown(document.querySelector('.dropdown'))` | Dropdown initialization |
| `$('.collapse').collapse()` | `new bootstrap.Collapse(document.querySelector('.collapse'))` | Collapse initialization |
| `$('.toast').toast('show')` | `new bootstrap.Toast(document.querySelector('.toast')).show()` | Toast initialization |
| `$('[data-toggle="popover"]').popover()` | `var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))`<br>`popoverTriggerList.map(function(popoverTriggerEl) { return new bootstrap.Popover(popoverTriggerEl) })` | Popover initialization |

## Text Alignment

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `text-left` | `text-start` | Text alignment |
| `text-right` | `text-end` | Text alignment |
| `text-monospace` | `font-monospace` | Monospace text |

## Float and Position

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `float-left` | `float-start` | Float alignment |
| `float-right` | `float-end` | Float alignment |
| `left-*` | `start-*` | Left position |
| `right-*` | `end-*` | Right position |

## Font Weight and Style

| Bootstrap 4 | Bootstrap 5 | Description |
|-------------|-------------|-------------|
| `font-weight-bold` | `fw-bold` | Bold text |
| `font-weight-bolder` | `fw-bolder` | Bolder text |
| `font-weight-normal` | `fw-normal` | Normal text |
| `font-weight-light` | `fw-light` | Light text |
| `font-weight-lighter` | `fw-lighter` | Lighter text |
| `font-italic` | `fst-italic` | Italic text |

## Removed Components

| Bootstrap 4 | Bootstrap 5 Alternative | Description |
|-------------|-------------------------|-------------|
| `jumbotron` | Use custom classes or cards | Jumbotron component |
| `card-deck` | Use grid system instead | Card deck layout |
| `card-columns` | Use grid system instead | Card columns layout |
| `input-group-append` | `input-group-text` | Input group append |
| `input-group-prepend` | `input-group-text` | Input group prepend |
| `form-row` | `row g-3` | Form row with gutters |
| `form-inline` | `d-flex` | Inline form |
| `btn-block` | `w-100` | Block-level button |
| `media` | `d-flex` | Media object |
| `nav-justified` | `nav-fill` | Justified navigation |

## New Features in Bootstrap 5

- **Utilities API**: Create your own utility classes
- **RTL Support**: Built-in right-to-left support
- **Improved Grid System**: New breakpoint (xxl) and gutter classes
- **Improved Forms**: Floating labels, validation styles
- **New Components**: Offcanvas, Accordion
- **CSS Custom Properties**: More CSS variables for easier theming
- **Enhanced Icons**: Bootstrap Icons library

## Common Migration Tasks

1. **Update Spacing Classes**:
   - Search for `ml-` and replace with `ms-`
   - Search for `mr-` and replace with `me-`
   - Search for `pl-` and replace with `ps-`
   - Search for `pr-` and replace with `pe-`

2. **Update Data Attributes**:
   - Search for `data-toggle` and replace with `data-bs-toggle`
   - Search for `data-target` and replace with `data-bs-target`
   - Search for `data-dismiss` and replace with `data-bs-dismiss`
   - Search for `data-backdrop` and replace with `data-bs-backdrop`
   - Search for `data-content` and replace with `data-bs-content`

3. **Update Form Classes**:
   - Search for `form-group` and replace with `mb-3`
   - Search for `<select class="form-control"` and replace with `<select class="form-select"`
   - Search for `custom-` classes and replace with their Bootstrap 5 equivalents
   - Add `form-label` to form labels

4. **Update JavaScript**:
   - Replace jQuery-based component initialization with vanilla JavaScript
   - Use the Bootstrap 5 JavaScript API for components

5. **Update Close Buttons**:
   - Replace `<button class="close">×</button>` with `<button class="btn-close"></button>`

6. **Update Text Alignment**:
   - Replace `text-left` with `text-start`
   - Replace `text-right` with `text-end`

7. **Update Float Classes**:
   - Replace `float-left` with `float-start`
   - Replace `float-right` with `float-end`
