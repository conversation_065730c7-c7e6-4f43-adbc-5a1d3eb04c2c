#!/usr/bin/env node

/**
 * This script generates a file with imports for all Vue components.
 * It's meant to be run before the webpack build.
 */

// This script uses CommonJS since it's run by Node.js directly
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const writeFile = promisify(fs.writeFile);

const componentsDir = path.resolve(__dirname, '../resources/js/class3/components');
const pagesDir = path.resolve(__dirname, '../resources/js/class3/pages');
const outputFile = path.resolve(__dirname, '../resources/js/class3/generated/component-imports.js');

// Ensure the output directory exists
const outputDir = path.dirname(outputFile);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

/**
 * Recursively find all .vue files in a directory
 */
async function findVueFiles(dir, fileList = []) {
  const files = await readdir(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const fileStat = await stat(filePath);

    if (fileStat.isDirectory()) {
      // Recursively search subdirectories
      await findVueFiles(filePath, fileList);
    } else if (file.endsWith('.vue')) {
      // Add .vue files to the list
      fileList.push(filePath);
    }
  }

  return fileList;
}

/**
 * Generate the import statements and component registration
 */
async function generateImports() {
  try {
    // Find all Vue files in both components and pages directories
    const componentFiles = await findVueFiles(componentsDir);
    const pageFiles = await findVueFiles(pagesDir);
    const vueFiles = [...componentFiles, ...pageFiles];

    // Sort the files for consistent output
    vueFiles.sort();

    // Generate the import statements
    let importStatements = '';
    let componentRegistrations = '';

    // Create a map to track component name occurrences
    const componentNameCount = {};

    vueFiles.forEach((filePath) => {
      // Get the relative path from the output file to the component
      const relativePath = path.relative(outputDir, filePath).replace(/\\/g, '/');

      // Get the component name (filename without extension)
      const componentName = path.basename(filePath, '.vue');

      // Track component name occurrences to handle duplicates
      componentNameCount[componentName] = (componentNameCount[componentName] || 0) + 1;

      // Generate a unique variable name based on the file path
      // Create a unique ID based on the file path to avoid naming conflicts
      const pathParts = filePath.split(path.sep);
      const componentDir = pathParts[pathParts.length - 2]; // Get the parent directory name
      const varName = `${componentDir}_${componentName}`.replace(/[^a-zA-Z0-9_]/g, '_');

      // Add the import statement
      importStatements += `import ${varName} from '${relativePath}';\n`;

      // Add the component registration
      componentRegistrations += `  ${componentName}: ${varName},\n`;
    });

    // Check for duplicate component names
    const duplicateComponents = Object.entries(componentNameCount)
      .filter(([name, count]) => count > 1)
      .map(([name]) => name);

    if (duplicateComponents.length > 0) {
      console.warn('\nWARNING: Found duplicate component names:');
      duplicateComponents.forEach(name => {
        console.warn(`  - ${name}`);
      });
      console.warn('\nThe last component with each name will override previous ones.\n');
    }

    // Create the final output
    const output = `/**
 * This file is auto-generated by scripts/generate-component-imports.cjs
 * Do not edit this file directly.
 */

// Import all Vue components
${importStatements}

// Export the components object
export const components = {
${componentRegistrations}};
`;

    // Write the output file
    await writeFile(outputFile, output);

    console.log(`Generated component imports at ${outputFile}`);
    console.log(`Found ${pageFiles.length} page components in pages directory`);
    console.log(`Found ${componentFiles.length} sub components in components directory`);
    console.log(`Found ${vueFiles.length} total Vue components`);
  } catch (error) {
    console.error('Error generating component imports:', error);
    process.exit(1);
  }
}

// Run the script
generateImports();
