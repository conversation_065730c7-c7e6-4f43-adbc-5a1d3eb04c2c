# Vue 2 to Vue 3 Migration

This document tracks the migration process from Vue 2 to Vue 3 for the Class project.

## Current Vue Version
- Vue 2.7.16

## Dependencies to Update/Replace

### Vue Core Packages
- [ ] vue (^2.7.16 → ^3.3.0)
- [ ] vue-loader (^15.10.1 → ^17.0.0)
- [ ] vue-template-compiler (remove - not needed in Vue 3)
- [ ] @vue/compiler-sfc (add - needed for Vue 3)

### Vue-Related Packages
- [ ] vuex (^3.6.2 → ^4.1.0)
- [ ] vue-test-utils (^1.0.0-beta.11 → @vue/test-utils ^2.0.0)

### Packages with Vue 2 Dependencies
- [ ] @ckeditor/ckeditor5-vue (^1.0.1 → ^4.0.0)
- [ ] @fortawesome/vue-fontawesome (^2.0.10 → ^3.0.0)
- [ ] @fullcalendar/vue (^5.11.5 → ^6.0.0)
- [ ] element-ui (^2.15.14 → element-plus)
- [ ] v-tooltip (^2.1.2 → floating-vue)
- [ ] vue-bootstrap-datetimepicker (^5.0.1 → evaluate alternatives)
- [ ] vue-cal (^3.10.5 → check compatibility)
- [ ] vue-ctk-date-time-picker (^2.5.0 → evaluate alternatives)
- [ ] vue-drag-drop (^1.1.2 → evaluate alternatives)
- [ ] vue-multiselect-component (^1.2.0 → evaluate alternatives)
- [ ] vue-window-size (^0.6.2 → ^1.0.0)
- [ ] vue2-editor (^2.10.3 → evaluate alternatives)
- [ ] vuejs-auto-complete (^0.9.0 → evaluate alternatives)
- [ ] vuejs-paginate (^2.1.0 → evaluate alternatives)

## Migration Steps

1. Update webpack.mix.js to support Vue 3
2. Update package.json with Vue 3 dependencies
3. Update component syntax to Vue 3 standards
4. Replace incompatible packages with Vue 3 alternatives
5. Update tests to use Vue 3 testing library

## Progress

### Completed
- Initial assessment of dependencies
- Updated webpack.mix.js to support Vue 3
- Updated core Vue dependencies in package.json:
  - vue: ^2.7.16 → ^3.3.0
  - vue-loader: ^15.10.1 → ^17.0.0
  - Removed vue-template-compiler
  - Added @vue/compiler-sfc
  - Updated vue-test-utils to @vue/test-utils
  - Updated vuex to v4
- Replaced incompatible packages with Vue 3 alternatives:
  - @ckeditor/ckeditor5-vue: ^1.0.1 → ^4.0.0
  - @fortawesome/vue-fontawesome: ^2.0.10 → ^3.0.3
  - @fullcalendar/vue → @fullcalendar/vue3 (and updated all FC packages)
  - element-ui → element-plus
  - v-tooltip → floating-vue
  - Replaced various Vue 2 components with Vue 3 alternatives
- Replaced all usage of the old trans(), ucfirst() and trans_choice() functions with the new useLang() composable

### In Progress
- Testing the application with Vue 3
- Updating components to use Vue 3 compatible packages

### Completed
- Resolved dependency conflicts
- Updated main app.js to use Vue 3 syntax:
  - Replaced Vue with { createApp }
  - Replaced Vuex with { createStore }
  - Updated component registration
  - Replaced Vue.prototype with app.config.globalProperties
  - Updated plugin usage
  - Replaced v-tooltip with floating-vue (using app.use(FloatingVue))
  - Replaced vue-bootstrap-datetimepicker with @vuepic/vue-datepicker
- Updated components:
  - EditDateException.vue: Replaced vue-ctk-date-time-picker with @vuepic/vue-datepicker
  - EditEventC3.vue: Replaced vue-ctk-date-time-picker with @vuepic/vue-datepicker
  - TutorGeneric.vue: Replaced vue-ctk-date-time-picker with @vuepic/vue-datepicker
  - EditSchoolYear.vue: Replaced vue-ctk-date-time-picker with @vuepic/vue-datepicker
  - LessonPlanningEditEvent.vue: Replaced vue-ctk-date-time-picker with @vuepic/vue-datepicker
  - TaskGeneric.vue: Replaced vue-ctk-date-time-picker with @vuepic/vue-datepicker
  - LessonPlanningCreateForm.vue: Replaced date-picker with VueDatepicker
  - NewStudentByCopy.vue: Replaced date-picker with VueDatepicker
  - StudentCourseRow.vue: Replaced date-picker with VueDatepicker
  - ShareLibraryForm.vue: Replaced vue-multiselect-component with @vueform/multiselect
  - SendRequest.vue: Replaced vue2-editor with @ckeditor/ckeditor5-vue and used centralized editorConfigSimple from useConfigItems
  - ShowCalendar.vue: Updated vue-cal to version 4.8.1 for Vue 3 compatibility
  - StudentGeneric.vue: Replaced date-picker with VueDatepicker and used centralized dpOptionsDate from useDatePicker
- Updated composables:
  - useDatePicker.js: Updated to work with @vuepic/vue-datepicker instead of vue-ctk-date-time-picker
  - Updated date formats to use Unicode standard tokens (dd-MM-yyyy instead of DD-MM-YYYY)
  - Added language parameter to useDatePicker to force a specific language
  - Added showClear parameter to useDatePicker to show a clear button
  - Updated Dutch format to use dd-MM-yyyy for better readability
  - Improved useDatePicker to handle language detection internally, simplifying component usage
  - Fixed time picker visibility by using enableTimePicker prop instead of only-date

## Notes
- Some packages may need to be replaced with alternatives that support Vue 3
- Component code will need to be updated to use the Composition API consistently
- Tests will need to be updated to use the new Vue Test Utils API

## Potential Issues to Watch For

### Breaking Changes in Vue 3
- Global Vue instance is removed - use createApp instead
- Multiple root nodes are now allowed in templates
- v-model has different implementation
- $listeners is removed (merged into $attrs)
- Filters are removed (use computed properties or methods instead)
- Functional components have a different syntax

### Package Replacements
- element-ui → element-plus
- v-tooltip → floating-vue
- vue2-editor → @tiptap/vue-3 and @vueup/vue-quill
- vue-bootstrap-datetimepicker → vue-datepicker-next and @vuepic/vue-datepicker
- vue-cal → (needs custom replacement or wrapper)
- vue-ctk-date-time-picker → @vuepic/vue-datepicker
- vue-drag-drop → vue-draggable-next
- vue-multiselect-component → @vueform/multiselect
- vuejs-paginate → @hennge/vue3-pagination
- @fullcalendar/vue → @fullcalendar/vue3

### Testing Changes
- mount and shallowMount have different syntax
- findComponent replaces find for component instances
- trigger is async and returns a Promise
