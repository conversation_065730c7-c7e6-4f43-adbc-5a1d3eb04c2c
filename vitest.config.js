import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/Vue/setup-vitest.js'],
    include: ['tests/Vue/**/*.test.js'],
    coverage: {
      provider: 'v8',
      reporter: ['html', 'text-summary'],
      reportsDirectory: './coverage',
      include: ['resources/js/class3/composables/**/*.js']
    },
    alias: {
      '@': path.resolve(__dirname, './resources/js/class3')
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './resources/js/class3')
    },
    modules: ['node_modules', 'resources/js/class3']
  }
});
