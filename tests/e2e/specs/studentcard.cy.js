describe("Student card", () => {

    const testAccount = {
        secret: 'UX7E3PXBUKZE4VIB',
        email: '<EMAIL>',
        password: 'cladmin'
    };

    beforeEach(() => {
        cy.session([testAccount.email, testAccount.password], () => {
            cy.login(testAccount);
        });
        cy.visit("/students/5/edit");
    });

    it("should show the student card and handle API calls", () => {
        // Intercept API calls
        cy.intercept('GET', '**/api/**').as('apiCalls');
        
        // Check authentication state
        cy.window().then((win) => {
            cy.log('Auth check:', !!win.localStorage.getItem('token'));
            cy.log('Session check:', win.document.cookie);
        });

        // Should show the student card sections
        cy.get("[data-testid='student-card-generic']").should("be.visible");
        cy.get("[data-testid='student-card-contact']").should("be.visible");
        cy.get("[data-testid='student-card-bank']").should("be.visible");
        cy.get("[data-testid='student-card-logbook']").should("be.visible");
        cy.get("[data-testid='student-card-course-data']").should("be.visible");
        cy.get("[data-testid='student-card-student-lists']").should("be.visible");
        cy.get("[data-testid='student-card-student-tasks']").should("be.visible");

        // Wait for and check API responses
        cy.wait('@apiCalls').then((interception) => {
            cy.log('API Response:', interception.response);
            if (interception.response.statusCode === 500) {
                cy.log('API Error:', interception.response.body);
            }
        });
    });

    it("should show the student's birthdate and age", () => {
        cy.get('#date_of_birth').invoke('val').should('match', /^\d{2}-\d{2}-\d{4}$/);
        cy.get('#age').invoke('val').then(parseInt).should('be.gt', 1);
    });

    it("should show the student's contacts telephone and email", () => {
        cy.get('#contactValue_0').should('be.visible');
        cy.get('#contactValue_1').should('be.visible');
        cy.get('#contactValue_0').should(($input) => {
            expect($input.val()).to.not.be.empty;
        });
        cy.get('#contactValue_1').should(($input) => {
            expect($input.val()).to.not.be.empty;
        });
    });

    it("should quick-jump to another student", () => {
        cy.get('[exclude-id="5"] > .btn-success').click();
        // click the first student in the list
        // i.e: click the first a tag in the div with the class .overflow-popup
        cy.get('.overflow-popup > div > a').first().click();
        // url should now no longer be /students/5/edit
        cy.url().should('not.include', '/students/5/edit');
        // but it should contain /students/ and /edit
        cy.url().should('include', '/students/');
        cy.url().should('include', '/edit');
    });
});
