import { authenticator } from 'otplib';

/**
 * Test the login process
 * for subsequent tests, we will use the custom command cy.login ( see cypress/support/commands.js )
 */
it('should login, fail TOTP, then succeed with correct TOTP', () => {
    const testAccount = {
        secret: 'UX7E3PXBUKZE4VIB',
        email: '<EMAIL>',
        password: 'cladmin'
    }

    // Visit the login page
    cy.visit('/login');

    // Fill in email and password
    cy.get('input[name="email"]').type(testAccount.email);
    cy.get('input[name="password"]').type(testAccount.password);

    // Click login button
    cy.get('button[type="submit"]').click();

    // Assert that TOTP field is shown
    cy.get('input[name="one_time_password"]').should('be.visible');

    // /////////////////////////////
    // Wrong TOTP code
    // /////////////////////////////
    cy.get('input[name="one_time_password"]').type('123456');

    // Click Authenticate button
    cy.get('button[data-testid="authenticate-btn"]').click();

    // Assert that an error is shown
    // div: div.alert.alert-danger
    cy.get("div[class$='alert-danger']").should('be.visible');

    // /////////////////////////////
    // Generate correct TOTP
    // /////////////////////////////
    const correctTotp = authenticator.generate(testAccount.secret);

    // Clear TOTP field and enter correct code
    cy.get('input[name="one_time_password"]').clear();
    cy.get('input[name="one_time_password"]').type(correctTotp);
    // Force input event
    cy.get('input[name="one_time_password"]').then($input => {
        $input[0].dispatchEvent(new Event('input', { bubbles: true }));
    });
    // Set up network interceptor
    cy.intercept('POST', '/2fa/2faVerify').as('verifyTotp');

    // Click Authenticate button again
    cy.get('button[data-testid="authenticate-btn"]').click();

    // Wait for the intercepted request to complete
    cy.wait('@verifyTotp').then((interception) => {
        expect(interception.response.statusCode).to.equal(302);
    });

    // now re-navigate to the home page, it should now skip the 2FA page
    cy.visit('/home');
    // Wait for a moment before asserting the URL
    cy.wait(2000);
    // Assert that we are navigated to the home page
    cy.url().should('include', '/home');
});
