import { authenticator } from 'otplib';

// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

Cypress.Commands.add('login', (account) => {
    cy.visit('/login');
    cy.get('input[name="email"]').type(account.email);
    cy.get('input[name="password"]').type(account.password);
    
    // Intercept alle requests om te zien wat er gebeurt
    cy.intercept('**').as('allRequests');
    
    cy.get('button[type="submit"]').click();
    
    // Wacht op de 2FA input met meer debug info
    cy.get('input[name="one_time_password"]', { timeout: 20000 })
        .should('be.visible')
        .then($input => {
            cy.log('2FA input field found');
        });

    // Generate en invullen TOTP
    const totp = authenticator.generate(account.secret);
    cy.log(`Generated TOTP: ${totp}`);

    cy.get('input[name="one_time_password"]')
        .type(totp)
        .then($input => {
            $input[0].dispatchEvent(new Event('input', { bubbles: true }));
            cy.log('TOTP entered and event dispatched');
        });

    // Intercept specifiek de 2FA verificatie
    cy.intercept('POST', '/2fa/2faVerify').as('verifyTotp');
    
    cy.get('button[data-testid="authenticate-btn"]')
        .should('be.visible')
        .click();

    // Wacht op de 2FA verificatie en log de response
    cy.wait('@verifyTotp').then((interception) => {
        cy.log(`2FA verification status: ${interception.response.statusCode}`);
        cy.log(`2FA response headers:`, interception.response.headers);
    });
});
