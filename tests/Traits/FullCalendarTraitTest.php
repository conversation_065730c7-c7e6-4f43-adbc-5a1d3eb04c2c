<?php

namespace Tests\Traits;

use App\Models\Attendancenote;
use App\Models\Attendanceoption;
use App\Models\Course;
use App\Models\Domain;
use App\Models\Event;
use App\Models\Location;
use App\Models\RecurrenceOption;
use App\Models\Registration;
use App\Models\Schoolyear;
use App\Models\Student;
use App\Models\Studentgroup;
use App\Models\Timetable;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class FullCalendarTraitTest extends TestCase
{
    use RefreshDatabase;
    use WithoutMiddleware;

    private $user;

    protected function setUp(): void
    {
        parent::setUp();

        // SETUP RELATED TABLES
        // create a domain
        $this->domain = Domain::factory()->create(['id' => 1]);
        // I need a user to be logged in
        $this->user = User::factory()->create(["id" => 1, "domain_id" => $this->domain->id]);
        // give it the role admin + tutor (2)
        $this->user->roles()->attach(1);
        $this->user->roles()->attach(2);
        // student
        $this->student1 = Student::factory()->create(["id" => 1, "domain_id" => $this->domain->id]);
        $this->student2 = Student::factory()->create(["id" => 2, "domain_id" => $this->domain->id]);
        $this->student3 = Student::factory()->create(["id" => 3, "domain_id" => $this->domain->id]);
        // Studentgroup
        $this->studentgroup = Studentgroup::factory()->create([
            "id" => 4,
            "domain_id" => $this->domain->id,
            "lastname" => "testgroup",
            "name" => "- testgroup"
        ]);
        // set both students as participants in the studentgroup
        $this->studentgroup->students()->attach($this->student1->id, [
            "start_date" => Carbon::now(),
            "end_date" => Carbon::now()->addYear()
        ]);
        $this->studentgroup->students()->attach($this->student2->id, [
            "start_date" => Carbon::now(),
            "end_date" => Carbon::now()->addYear()
        ]);
        // Student3 has an end_date in the past
        $this->studentgroup->students()->attach($this->student3->id, [
            "start_date" => Carbon::now()->subYears(2),
            "end_date" => Carbon::now()->subYear()
        ]);

        // Course
        $this->course = Course::factory()->create(["id" => 1, "domain_id" => $this->domain->id]);
        // Registration (course_student) for student1: has an active registration
        $this->registration = Registration::factory()->create([
            "student_id" => $this->student1->id, 
            "course_id" => $this->course->id,
            "start_date" => Carbon::now()->subDay(),
            "end_date" => Carbon::now()->addYear()
        ]);
        // Registration (course_student) for student2: has an inactive registration
        $this->registration2 = Registration::factory()->create([
            "student_id" => $this->student2->id, 
            "course_id" => $this->course->id,
            "start_date" => Carbon::now()->subYear(),
            "end_date" => Carbon::now()->subDay()
        ]);
        // Registration (course_student) for student3, no longer participant in the student group, but has active registration (probably individual student)
        $this->registration3 = Registration::factory()->create([
            "student_id" => $this->student3->id, 
            "course_id" => $this->course->id,
            "start_date" => Carbon::now()->subYear(),
            "end_date" => Carbon::now()->addYear()
        ]);
        // Registration (course_student) for student-group
        $this->registration4 = Registration::factory()->create(["student_id" => $this->studentgroup->id, "course_id" => $this->course->id]);

        // Schoolyear
        $currentYear = Carbon::now()->year;
        $this->schoolyear = Schoolyear::factory()->create([
            "id" => 1,
            "domain_id" => $this->domain->id,
            "start_year" => $currentYear,
            "end_year" => $currentYear + 1,
            "start_date" => Carbon::create($currentYear, 9, 1),
            "end_date" => Carbon::create($currentYear + 1, 8, 31)
        ]);
        // Timetable
        $this->timetable = Timetable::factory()->create([
            "id" => 1,
            "course_student_id" => $this->registration->id,
            "schoolyear_id" => $this->schoolyear->id
        ]);
        // Timetable for student-group
        $this->timetable2 = Timetable::factory()->create([
            "id" => 2,
            "course_student_id" => $this->registration4->id,
            "schoolyear_id" => $this->schoolyear->id
        ]);
        $this->location = Location::factory()->create(["id" => 1, "domain_id" => $this->domain->id]);
        $this->recurrenceoptions = RecurrenceOption::factory()->create(["id" => 1]);
        $this->attendanceoption = Attendanceoption::factory()->create(["id" => 1, "domain_id" => $this->domain->id]);
        $this->attendancenote = Attendancenote::factory()->create(["id" => 1, "attendanceoption_id" => $this->attendanceoption->id, "student_id" => $this->student1->id, "event_id" => 1]);
        // login the user
        Auth::login($this->user);
    }

    /**
     * Tests the `getCalendarEvents` method in the `FullCalendarTrait` class.
     */
    public function testGetCalendarEvents(): void
    {
        // PREPARATION
        $now = Carbon::now();
        $eventData = [
            'id' => 1,
            'location_id' => $this->location->id,
            'tutor_id' => $this->user->id,
            'timetable_id' => $this->timetable->id,
            'caluniqueid' => md5((string)$now->timestamp),
            'datetime' => $now->format("Y-m-d H:i:s"),
            'sequence' => '1',
            'timespan' => '15 minutes',
            'remarks' => 'test remark'
        ];
        Event::factory()->create($eventData);
        $startDate = $now->copy()->subDay(1); // 1 day earlier yields only the day in endDate
        $endDate = $now->copy()->addDay(1); // 1 day later yields only the day in startDate

        // EXECUTION
        $results = Event::getCalendarEvents($startDate->format("Y-m-d"), $endDate->format("Y-m-d"));

        // ASSERTIONS
        // check the number of events currently in the database
        $this->assertEquals(1, Event::count());
        $this->assertIsArray($results);
        // count array size, should have 1 result
        $this->assertCount(1, $results);
        $result = reset($results);
        $this->assertIsArray($result); // a fail would be a Boolean

        // Title maybe something like: '<div style='position: absolute;float: left;background-color:#9e56ca;width: 5px;height: 100%;'></div>Emmitt Kautzer<span data-student-id='1' class='btn btn-outline-primary btn-sm ml-1' v-tooltip='\'studentcard\''>   <i data-student-id='1' class='fa fa-id-card'></i></span>
        $this->assertStringContainsString('<div style=\'position: absolute;float: left;', $result['title']);
        $this->assertStringContainsString('<i data-student-id=\'1\' class=\'fa fa-id-card\'></i>', $result['title']);
        $this->assertStringContainsString('<span data-student-id=\'1\' class=\'btn btn-outline-primary btn-sm ml-1\'', $result['title']);
        $this->assertEquals($eventData['datetime'], $result['start']);
    }

    /**
     * If the event targets a student group, the code checks the participants of the student group
     * and removes students that do not have an active registration for the target course in the student group.
     * @return void
     */
    public function testCalendarResponseStudentgroepParticipants(): void
    {
        // PREPARATION
        $now = Carbon::now();
        $eventData = [
            'id' => 2,
            'location_id' => $this->location->id,
            'tutor_id' => $this->user->id,
            'timetable_id' => $this->timetable2->id,
            'caluniqueid' => md5((string)$now->timestamp),
            'datetime' => $now->format("Y-m-d H:i:s"),
            'sequence' => '1',
            'timespan' => '15 minutes',
            'remarks' => 'test remark'
        ];
        Event::factory()->create($eventData);
        $startDate = $now->copy()->subDay(1); // 1 day earlier yields only the day in endDate
        $endDate = $now->copy()->addDay(1); // 1 day later yields only the day in startDate

        // EXECUTION
        $results = Event::getCalendarEvents($startDate->format("Y-m-d"), $endDate->format("Y-m-d"));

        // ASSERTIONS
        // student 1 should be in the response as a participant,
        // but student 2 should not be in the response as a participant because they have no active registration
        $this->assertIsArray($results);
        $this->assertCount(1, $results);

        $result = $results[0];
        $this->assertIsArray($result); // a fail would be a Boolean
        // it should have isAStudentGroup set to true
        $this->assertTrue($result['isAStudentGroup']);
        // it should have an array: students
        $this->assertIsArray($result['students']);
        // the array should only have 1 student (the second one has been removed from the response)
        $this->assertCount(1, $result['students']);
        // and that student should be the active one: Student1
        $this->assertEquals($this->student1->id, $result['students'][0]->id);
    }
}