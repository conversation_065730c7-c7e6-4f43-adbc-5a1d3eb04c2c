<?php

namespace Tests\Unit;

use Tests\TestCase;

class NameChunkTest extends TestCase {

	// lastname
	public function testNormalLastname() {
		$retVal = getLastnameFromAssembledField("van den Bogaard");
		$this->assertTrue($retVal === "Bogaard");
	}
	public function testOnlyLastname() {
		$retVal = getLastnameFromAssembledField("Bogaard");
		$this->assertTrue($retVal === "Bogaard");
	}
	public function testOnlyLastnameSpecialChar() {
		$retVal = getLastnameFromAssembledField("Schröer");
		$this->assertTrue($retVal === "Schröer");
	}
    public function testLastNamequotedPreposition() {
        $retVal = getLastnameFromAssembledField("in 't Veld");
        $this->assertTrue($retVal === "Veld");
    }

	// preposition
	public function testNormalPreposition() {
		$retVal = getPrepositionFromAssembledField("van den Bogaard");
		$this->assertTrue($retVal === "van den");
	}
	public function testNormalPrepositionSingle() {
		$retVal = getPrepositionFromAssembledField("van Meteren");
		$this->assertTrue($retVal === "van");
	}

	public function testPrepositionOnlyLastname() {
		$retVal = getPrepositionFromAssembledField("Janssen");
		$this->assertTrue($retVal === "");
	}
	public function testPrepositionSpecialChar() {
		$retVal = getPrepositionFromAssembledField("in 't Veld");
		$this->assertTrue($retVal === "in 't");
	}

}
