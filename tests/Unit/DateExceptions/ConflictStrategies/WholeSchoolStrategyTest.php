<?php

namespace Tests\Unit\DateExceptions\ConflictStrategies;

use Tests\TestCase;
use App\Models\DateException;
use App\Services\DateExceptions\ConflictStrategies\WholeSchoolStrategy;
use App\Services\DateExceptions\ConflictStrategies\ConflictStrategyInterface;
use Mockery;

class WholeSchoolStrategyTest extends TestCase {
    private WholeSchoolStrategy $strategy;

    public function setUp(): void
    {
        parent::setUp();
        $this->strategy = new WholeSchoolStrategy();
    }

    public function test_whole_school_exception_returns_true_without_calling_next()
    {
        $dateException = new DateException();
        $dateException->tutor_id = 0;
        $dateException->location_id = 0;

        $event = (object)[
            'tutor_id' => 1,
            'location_id' => 1
        ];

        $nextStrategy = Mockery::mock(ConflictStrategyInterface::class);
        $nextStrategy->shouldNotReceive('hasConflict');
        
        $this->strategy->setNext($nextStrategy);
        $this->assertTrue($this->strategy->hasConflict($dateException, $event));
    }

    public function test_specific_location_calls_next()
    {
        $dateException = new DateException();
        $dateException->tutor_id = 0;
        $dateException->location_id = 1;

        $event = (object)[
            'tutor_id' => 1,
            'location_id' => 1
        ];

        $nextStrategy = Mockery::mock(ConflictStrategyInterface::class);
        $nextStrategy->shouldReceive('hasConflict')
            ->once()
            ->with($dateException, $event)
            ->andReturn(true);
        
        $this->strategy->setNext($nextStrategy);
        $this->assertTrue($this->strategy->hasConflict($dateException, $event));
    }

    public function test_specific_tutor_calls_next()
    {
        $dateException = new DateException();
        $dateException->tutor_id = 1;
        $dateException->location_id = 0;

        $event = (object)[
            'tutor_id' => 1,
            'location_id' => 1
        ];

        $nextStrategy = Mockery::mock(ConflictStrategyInterface::class);
        $nextStrategy->shouldReceive('hasConflict')
            ->once()
            ->with($dateException, $event)
            ->andReturn(true);
        
        $this->strategy->setNext($nextStrategy);
        $this->assertTrue($this->strategy->hasConflict($dateException, $event));
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
