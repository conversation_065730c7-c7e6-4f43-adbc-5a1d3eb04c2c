<?php

namespace Tests\Unit\DateExceptions\ConflictChecker;

use Tests\TestCase;
use App\Models\DateException;
use App\Services\DateExceptions\ConflictStrategies\WholeSchoolStrategy;
use App\Services\DateExceptions\ConflictStrategies\LocationStrategy;
use App\Services\DateExceptions\ConflictStrategies\TutorStrategy;

class ConflictCheckerTest extends TestCase {
    private WholeSchoolStrategy $wholeSchoolStrategy;
    private LocationStrategy $locationStrategy;
    private TutorStrategy $tutorStrategy;

    public function setUp(): void
    {
        parent::setUp();
        $this->wholeSchoolStrategy = new WholeSchoolStrategy();
        $this->locationStrategy = new LocationStrategy();
        $this->tutorStrategy = new TutorStrategy();
    }

    public function test_different_strategy_orders_give_same_result()
    {
        $dateException = new DateException();
        $dateException->tutor_id = 1;
        $dateException->location_id = 1;

        $event = (object)[
            'tutor_id' => 1,
            'location_id' => 1
        ];

        // Order 1: WholeSchool -> Location -> Tutor
        $this->wholeSchoolStrategy->setNext($this->locationStrategy);
        $this->locationStrategy->setNext($this->tutorStrategy);
        $result1 = $this->wholeSchoolStrategy->hasConflict($dateException, $event);

        // Order 2: Location -> Tutor -> WholeSchool
        $this->locationStrategy->setNext($this->tutorStrategy);
        $this->tutorStrategy->setNext($this->wholeSchoolStrategy);
        $result2 = $this->locationStrategy->hasConflict($dateException, $event);

        $this->assertEquals($result1, $result2);
    }

    public function test_conflict_scenarios()
    {
        $scenarios = [
            [
                'desc' => 'Same location different tutor',
                'exception' => ['tutor_id' => 1, 'location_id' => 1],
                'event' => ['tutor_id' => 2, 'location_id' => 1],
                'expected' => true
            ],
            [
                'desc' => 'Same tutor different location',
                'exception' => ['tutor_id' => 1, 'location_id' => 1],
                'event' => ['tutor_id' => 1, 'location_id' => 2],
                'expected' => true
            ],
            [
                'desc' => 'Whole school exception',
                'exception' => ['tutor_id' => 0, 'location_id' => 0],
                'event' => ['tutor_id' => 1, 'location_id' => 1],
                'expected' => true
            ],[
                'desc' => 'Whole school exception',
                'exception' => ['tutor_id' => null, 'location_id' => null],
                'event' => ['tutor_id' => 1, 'location_id' => 1],
                'expected' => true
            ],
            // add more scenarios here
        ];

        foreach ($scenarios as $scenario) {
            $dateException = new DateException();
            $dateException->tutor_id = $scenario['exception']['tutor_id'];
            $dateException->location_id = $scenario['exception']['location_id'];

            $event = (object)$scenario['event'];

            $this->wholeSchoolStrategy->setNext($this->locationStrategy);
            $this->locationStrategy->setNext($this->tutorStrategy);

            $result = $this->wholeSchoolStrategy->hasConflict($dateException, $event);
            $this->assertEquals($scenario['expected'], $result, "Failed scenario: {$scenario['desc']}");
        }
    }
}
