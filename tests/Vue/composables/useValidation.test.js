import useValidation  from "../../../resources/js/class3/composables/useValidation";

describe('useValidation', () => {
    describe('isValidEmail', () => {
        const {isValidEmail} = useValidation();
        it('should return true when input is "<EMAIL>"', () => {
            expect(isValidEmail("<EMAIL>")).toBe(true);
        });
        it('should return true when input is "<EMAIL>"', () => {
            expect(isValidEmail("<EMAIL>")).toBe(true);
        });
        it('should return false when input is "info@scolavisa"', () => {
            expect(isValidEmail("info@scolavisa")).toBe(false);
        });
        it('should return false when input is "info@scolavisa."', () => {
            expect(isValidEmail("info@scolavisa.")).toBe(false);
        });
        it('should return false when input is "info@scolavisa@eu"', () => {
            expect(isValidEmail("info@scolavisa@eu")).toBe(false);
        });
        it('should return false when input is "scolavisa.eu"', () => {
            expect(isValidEmail("scolavisa.eu")).toBe(false);
        });
    });
});