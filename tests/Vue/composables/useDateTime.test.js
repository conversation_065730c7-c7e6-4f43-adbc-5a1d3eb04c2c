import useDateTime from "@/composables/useDateTime";
import moment from "moment-timezone";


beforeAll(() => {
    moment.tz.setDefault('Europe/Amsterdam');
});

afterAll(() => {
  moment.tz.setDefault();
});

// mock translation file, located at window.trans (injected from backend)
beforeEach(() => {
    global.window = Object.create(window);
    setLang("nl");
});

const setLang = (lang) => {
    const value = {
        "generic":{
            "language": lang
        }
    };
    Object.defineProperty(window, 'trans', {
        value,
        writable: true,
    });
}

const {
    convertToMysqlDateTime,
    displayDateTime,
    displayDate,
    displayAge,
    getDateOfWeekByDayName,
    getEndTime,
    getHighestWeekNumber,
    getMonthName,
    getMonthNameOfMondayOfWeekNr,
    getWeekNumber,
    isFutureDate
} = useDateTime();

describe('useDateTime', () => {
    describe('displayDateTime', () => {
        const testDate = "2021-01-31 12:34:56";
        const testDate2 = "2021-02-28 12:34:56";
        const testDateNl = "31-01-2021 12:34:56";
        const testDate2GB = "28/02/2021, 12:34:56";

        it('should return "-" when input is null', () => {
            expect(displayDateTime(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(displayDateTime(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(displayDateTime("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(displayDateTime("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(displayDateTime("123456789")).toBe("-");
        });
        it(`should return "${testDateNl.substring(0,16)}" when input is "${testDate}" and language is "nl"`, () => {
            expect(displayDateTime(testDate, false).replaceAll(", ", " ")).toBe(testDateNl.substring(0,16));
        });
        it(`should return "${testDateNl}" when input is "${testDate}" and language is "nl" and showSeconds is "true"`, () => {
            expect(displayDateTime(testDate, true).replaceAll(", ", " ")).toBe(testDateNl);
        });
        // check dislay of dayname
        it(`should return "zo" when input is "${testDate}" and language is "nl" and showDayName is "true"`, () => {
            expect(displayDateTime(testDate, false, true).replaceAll(", ", " ")).toBe("zo 31-01-2021 12:34");
        });
        it(`should return "${testDate2GB.substring(0,16)}" when input is "${testDate2}" and language is "en" and showSeconds is default (false)`, () => {
            // first  change language to en in window trans
            setLang("en");
            expect(displayDateTime(testDate2).replaceAll(", ", " ")).toBe(testDate2GB.substring(0,17).replaceAll(", ", " "));
        });
        it(`should return "${testDate2GB.substring(0,16)}" when input is "${testDate2}" and language is "en" and showSeconds is "false"`, () => {
            setLang("en");
            expect(displayDateTime(testDate2, false).replaceAll(", ", " ")).toBe(testDate2GB.substring(0,17).replaceAll(", ", " "));
        });
        it(`should return "${testDate2GB}" when input is "${testDate2}" and language is "en" and showSeconds is "true"`, () => {
            setLang("en");
            expect(displayDateTime(testDate2, true)).toBe(testDate2GB);
        });
        // check display of dayname
        it(`should return "sun" when input is "${testDate}" and language is "nl" and showDayName is "true"`, () => {
            setLang("en");
            expect(displayDateTime(testDate2, false, true).replaceAll(", ", " ")).toBe("Sun " + testDate2GB.substring(0,17).replaceAll(", ", " "));
        });
    });
    describe('displayDate', () => {
        const testDate = "2021-01-01";
        const testDateShortNl = "01-01-2021";
        it('should return "-" when input is null', () => {
            expect(displayDate(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(displayDate(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(displayDate("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(displayDate("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(displayDate("123456789")).toBe("-");
        });
        it('should return "01-01-2021" when input is "2021-01-01 12:34:56" and language is "nl"', () => {
            expect(displayDate(testDate)).toBe(testDateShortNl);
        });
        it('should return "02-01-2021" when input is "2021-02-01 12:34:56" and language is "en"', () => {
            setLang("en");
            expect(displayDate("2021-02-01 12:34:56")).toBe("02-01-2021");
        });
    });
    describe('displayAge', () => {
        // set test date to be now+ 20 years
        const testDate = moment().subtract(20, 'years').format('YYYY-MM-DD');
        const testDateNL = moment().subtract(20, 'years').format('DD-MM-YYYY');

        it('should return "-" when input is null', () => {
            expect(displayAge(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(displayAge(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(displayAge("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(displayAge("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(displayAge("123456789")).toBe("-");
        });
        it(`should return "20" when input is "${testDate}"`, () => {
            expect(displayAge(testDate)).toBe(20);
        });
        it(`should return "20" when input is "${testDateNL}"`, () => {
            expect(displayAge(testDateNL)).toBe(20);
        });
    });

    describe('isFutureDate', () => {
        // set test date to be now+ 20 years
        const testDate = moment().add(20, 'years').format('YYYY-MM-DD');
        const testDateNL = moment().add(20, 'years').format('DD-MM-YYYY');

        it('should return false when input is null', () => {
            expect(isFutureDate(null)).toBe(false);
        });
        it('should return false when input is undefined', () => {
            expect(isFutureDate(undefined)).toBe(false);
        });
        it('should return false when input is ""', () => {
            expect(isFutureDate("")).toBe(false);
        });
        it('should return false when input is "123"', () => {
            expect(isFutureDate("123")).toBe(false);
        });
        it('should return false when input is "123456789"', () => {
            expect(isFutureDate("123456789")).toBe(false);
        });
        it(`should return true when input is "${testDate}"`, () => {
            expect(isFutureDate(testDate)).toBe(true);
        });
        it(`should return true when input is "${testDateNL}"`, () => {
            expect(isFutureDate(testDateNL)).toBe(true);
        });
    });
    describe('getEndTime', () => {
        it('should return "Invalid date" when input is null', () => {
            expect(getEndTime(null, null)).toBe("Invalid date");
        });
        it('should return "Invalid date" when input is undefined', () => {
            expect(getEndTime(undefined, undefined)).toBe("Invalid date");
        });
        it('should return "Invalid date" when input is ""', () => {
            expect(getEndTime("", "")).toBe("Invalid date");
        });
        it('should return the time when duration part is missing', () => {
            expect(getEndTime("12:00")).toBe("12:00");
        });
        it('should return the time when duration part is not a number', () => {
            expect(getEndTime("12:00", "abc")).toBe("12:00");
        });
        it('should return "13:00" when input is "12:00" and duration is 60', () => {
            expect(getEndTime("12:00", 60)).toBe("13:00");
        });
        it('should return "13:00" when input is "12:00:01" and duration is 60', () => {
            expect(getEndTime("12:00:01", 60)).toBe("13:00");
        });
    });

    describe('convertToMysqlDateTime', () => {
        it('should return null when input is null', () => {
            expect(convertToMysqlDateTime(null)).toBe(null);
        });
        it('should return null when input is undefined', () => {
            expect(convertToMysqlDateTime(undefined)).toBe(null);
        });
        it('should return null when input is ""', () => {
            expect(convertToMysqlDateTime("")).toBe(null);
        });
        it('should return null when input is "123"', () => {
            expect(convertToMysqlDateTime("123")).toBe(null);
        });
        it('should return "2020-12-10" when input is "10-12-2020"', () => {
            expect(convertToMysqlDateTime("10-12-2020")).toBe("2020-12-10");
        });
        it('should return "2020-12-10" when input is "tue 10-12-2020"', () => {
            expect(convertToMysqlDateTime("tue 10-12-2020")).toBe("2020-12-10");
        });
        it('should return "2020-12-10" when input is "Sa 10-12-2020"', () => {
            expect(convertToMysqlDateTime("Sa 10-12-2020")).toBe("2020-12-10");
        });
        it('should return mysql date when input is already in mysql format', () => {
            expect(convertToMysqlDateTime("2020-12-10")).toBe("2020-12-10");
        });
    });

    describe('getWeekNumber', () => {
        it('should return "-" when input is null', () => {
            expect(getWeekNumber(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(getWeekNumber(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(getWeekNumber("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(getWeekNumber("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(getWeekNumber("123456789")).toBe("-");
        });
        it('should return "52" when input is "2020-12-25"', () => {
            expect(getWeekNumber("2020-12-25")).toBe(52);
        });
        it('should return "52" when input is "25-12-2020"', () => {
            expect(getWeekNumber("25-12-2020")).toBe(52);
        });
    });

    describe('getMonthName', () => {
        it('should return "-" when input is null', () => {
            expect(getMonthName(null)).toBe("-");
        });
        it('should return "-" when input is undefined', () => {
            expect(getMonthName(undefined)).toBe("-");
        });
        it('should return "-" when input is ""', () => {
            expect(getMonthName("")).toBe("-");
        });
        it('should return "-" when input is "123"', () => {
            expect(getMonthName("123")).toBe("-");
        });
        it('should return "-" when input is "123456789"', () => {
            expect(getMonthName("123456789")).toBe("-");
        });
        it('should return "Dec" when input is "2020-12-25"', () => {
            expect(getMonthName("2020-12-25")).toBe("Dec");
        });
        it('should return "Dec" when input is "25-12-2020"', () => {
            expect(getMonthName("25-12-2020")).toBe("Dec");
        });
        it('should return "December" when input is "2020-12-25" and short is false', () => {
            expect(getMonthName("2020-12-25", false)).toBe("December");
        });
        it('should return "December" when input is "25-12-2020" and short is false', () => {
            expect(getMonthName("25-12-2020", false)).toBe("December");
        });
        it('should ignore a time part in the string', () => {
            expect(getMonthName("25-12-2020 12:34:56")).toBe("Dec");
        });
    });

    describe('getMonthNameOfMondayOfWeekNr', () => {
        it('should return "-" when input is null', () => {
            expect(getMonthNameOfMondayOfWeekNr(null)).toBe("-");
        });
        it("should return 'Jan' when input is 2021 and weekNr is 2", () => {
            expect(getMonthNameOfMondayOfWeekNr(2021, 2)).toBe("Jan");
        });
        it("should return 'Dec' when input is 2021 although weekNr is 1", () => {
            expect(getMonthNameOfMondayOfWeekNr(2021, 1)).toBe("Dec");
        });
        it("should return 'January' when input is 2021 and weekNr is 2 and short is false", () => {
            expect(getMonthNameOfMondayOfWeekNr(2021, 2, false)).toBe("January");
        });
    });

    describe('getDateOfWeekByDayName', () => {
        it('should return sunday for week 5 of 2021', () => {
            expect(getDateOfWeekByDayName(5, 2021, 'Monday')).toBe("2021-02-01");
        });
        // 2021 has 53 weeks
        it('should return sunday for week 53 of 2020', () => {
            expect(getDateOfWeekByDayName(53, 2020, 'Sunday')).toBe("2021-01-03");
        });
        // the first week of 2021 is week 53 of 2020, so if we ask monday in week 1 of 2021, we should get 2021-01-04
        it('should return monday for week 1 of 2021', () => {
            expect(getDateOfWeekByDayName(1, 2021, 'Monday')).toBe("2021-01-04");
        });
        it("should fail gracefully when weeknumber is too high", () => {
            expect(getDateOfWeekByDayName(54, 2020, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when weeknumber is too low", () => {
            expect(getDateOfWeekByDayName(0, 2020, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when dayname is invalid", () => {
            expect(getDateOfWeekByDayName(1, 2021, 'Funday')).toBe("Invalid date");
        });
        it("should fail gracefully when year is invalid", () => {
            expect(getDateOfWeekByDayName(1, 0, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when year is null", () => {
            expect(getDateOfWeekByDayName(1, null, 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when year is not a number", () => {
            expect(getDateOfWeekByDayName(1, 'abc', 'Monday')).toBe("Invalid date");
        });
        it("should fail gracefully when weekNumber is not a number", () => {
            expect(getDateOfWeekByDayName('abc', 2021, 'Monday')).toBe("Invalid date");
        });
    });

    describe('getHighestWeekNumber', () => {
        it('should return 52 for 2021', () => {
            expect(getHighestWeekNumber(2021)).toBe(52);
        });
        // The last days of december 2020 are in week 53
        it('should return 53 for 2020', () => {
            expect(getHighestWeekNumber(2020)).toBe(53);
        });
        // The last days of december 2024 are in week 1
        it('should return 52 for 2024', () => {
            expect(getHighestWeekNumber(2024)).toBe(52);
        });
    });
});
