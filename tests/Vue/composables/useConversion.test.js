import useConversion from "../../../resources/js/class3/composables/useConversion";

const { convertBytesToHumanReadable } = useConversion();

describe('useConversion', () => {
    describe('convertBytesToHumanReadable', () => {
        it('should return "0 Bytes" when input is null', () => {
            expect(convertBytesToHumanReadable(null)).toBe('0 Bytes');
        });
        it('should return "0 Bytes" when input is undefined', () => {
            expect(convertBytesToHumanReadable(undefined)).toBe('0 Bytes');
        });
        it('should return "0 Bytes" when input is ""', () => {
            expect(convertBytesToHumanReadable("")).toBe('0 Bytes');
        });
        it('should return "0 Bytes" when input is "NOTANINT"', () => {
            expect(convertBytesToHumanReadable("NOTANINT")).toBe('0 Bytes');
        });
        it('should return "0 Bytes" when input is "NOTANINT123" (partial int)', () => {
            expect(convertBytesToHumanReadable("NOTANINT123")).toBe('0 Bytes');
        });
        it('should return "123 Bytes" when input is "123"', () => {
            expect(convertBytesToHumanReadable("123")).toBe('123 Bytes');
        });
        it('should return "117.7 MB" when input is "123456789"', () => {
            expect(convertBytesToHumanReadable("123456789")).toBe('117.7 MB');
        });
        it('should return "112.3 TB" when input is "1234567890123456789"', () => {
            expect(convertBytesToHumanReadable("123456789012345")).toBe('112.3 TB');
        });
        it('should return "1.0 KB" when input is "1024"', () => {
            expect(convertBytesToHumanReadable("1024")).toBe('1.0 KB');
        });
        it('should return "1.0 KB" when input is "1024.1"', () => {
            expect(convertBytesToHumanReadable("1024.1")).toBe('1.0 KB');
        });
        it('should return "1.9 Bytes" when input is "1.9"', () => {
            expect(convertBytesToHumanReadable("1.9")).toBe('1.9 Bytes');
        });
    });
});