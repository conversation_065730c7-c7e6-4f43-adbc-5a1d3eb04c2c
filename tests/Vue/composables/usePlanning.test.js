import usePlanning from "../../../resources/js/class3/composables/usePlanning";

const { listStudents } = usePlanning();

describe('usePlanning', () => {
    describe('listStudents', () => {
        it('should return a list of students without columns if number of students is < 12', () => {
            const students = [{id: 0, name: "Scolavisa"}, {id: 1, name: "Scolavisa2"}];
            const expected = `<li class="list-group-item"><a href="/students/0/edit">Scolavisa</a></li><li class="list-group-item"><a href="/students/1/edit">Scolavisa2</a></li>`;
            const expectedRows = `<div class="col-12">`; // one column (effectively no columns)
            let result = listStudents(students);
            expect(result).toContain(expected);
            expect(result).toContain(expectedRows);
        });
        it('should return a list of students with 2 columns if number of students is > 12 and < 24', () => {
            const students = [];
            for (let i = 0; i < 13; i++) {
                students.push({id: i, name: `Scolavisa${i}`});
            }
            const expected = `<li class="list-group-item"><a href="/students/0/edit">Scolavisa0</a></li><li class="list-group-item"><a href="/students/1/edit">Scolavisa1</a></li>`;
            const expectedRows = `<div class="col-6">`; // two columns
            let result = listStudents(students);
            expect(result).toContain(expected);
            expect(result).toContain(expectedRows);
        });
        it('should return a list of students with 3 columns if number of students is > 24', () => {
            const students = [];
            for (let i = 0; i < 25; i++) {
                students.push({id: i, name: `Scolavisa${i}`});
            }
            const expected = `<li class="list-group-item"><a href="/students/0/edit">Scolavisa0</a></li><li class="list-group-item"><a href="/students/1/edit">Scolavisa1</a></li>`;
            const expectedRows = `<div class="col-4">`; // three columns
            let result = listStudents(students);
            expect(result).toContain(expected);
            expect(result).toContain(expectedRows);
        });
    });
});