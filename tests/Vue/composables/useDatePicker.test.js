import useDatePicker from "../../../resources/js/class3/composables/useDatePicker";

// mock variable lang from useLang to return 'nl'
beforeEach(() => {
    global.window = Object.create(window);
    setLang("nl");
});

const setLang = (lang) => {
    const value = {
        "generic":{
            "language": lang
        }
    };
    Object.defineProperty(window, 'trans', {
        value,
        writable: true,
    });
}

const { dpOptions } = useDatePicker(true);
const { dpOptions: dpOptions2 } = useDatePicker(false);


// fixme: get rid of all those setTimeouts
describe('useDatePicker', () => {
    describe('dpOptions, whole day', () => {
        it('should return nl options when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions.value.locale).toBe('nl');
            }, 1000);
        });
        it('should return "date" label when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions.value.label).toBe('datum');
            }, 1000);
        });
        it('should return en options when lang is "en"', () => {
            setLang("en");
            expect(dpOptions.value.locale).toBe('en');
        });
    });
    describe('dpOptions, not whole day', () => {
        it('should return nl options when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions2.value.locale).toBe('nl');
            }, 1000);
        });
        it('should return "date / time" label when lang is "nl"', async () => {
            await setTimeout(() => {
                expect(dpOptions2.value.label).toBe('datum / tijd');
            }, 1000);
        });
        it('should return en options when lang is "en"', () => {
            setLang("en");
            expect(dpOptions2.value.locale).toBe('en');

        });
    });
});