import useLang from "../../../resources/js/class3/composables/useLang";

const { translate, translateChoice, ucFirst, lang } = useLang();

// mock translation file, located at window.trans (injected from backend)
beforeEach(() => {
    global.window = Object.create(window);
    const value = {
        "generic":{
            "language":"nl",
            "a":"een",
            "absentwithnotification":"absent met afmelden",
            "accesscodes":"toegangscode|toegangscodes",
            "hello":"Hallo :name",
            "hellostudents": "Hallo leerling :name|Hallo leerlingen :name",
        }
    };
    Object.defineProperty(window, 'trans', {
        value,
        writable: true,
    });
});

describe('useLang', () => {
    describe('lang', () => {
        it('should return "nl" when input is "nl"', () => {
            expect(lang.value).toBe("nl");
        });
    });
    describe('translate', () => {
        it('should return "een" when input is "generic.a"', () => {
            expect(translate('generic.a')).toBe("een");
        });
        it('should return "absent met afmelden" when input is "generic.absentwithnotification"', () => {
            expect(translate('generic.absentwithnotification')).toBe("absent met afmelden");
        });
        it('should return "generic.b" when input is "generic.b"', () => {
            expect(translate('generic.b')).toBe("generic.b");
        });
        it('should inject variable "name" when input is "generic.hello" and params is {name: "Scolavisa"}', () => {
            expect(translate('generic.hello', {name: "Scolavisa"})).toBe("Hallo Scolavisa");
        });
    });
    describe('translate_choice', () => {
        it('should return "toegangscode" when input is "generic.accesscodes" and count is "1"', () => {
            expect(translateChoice('generic.accesscodes', 1)).toBe("toegangscode");
        });
        it('should return "toegangscodes" when input is "generic.accesscodes" and count is "2"', () => {
            expect(translateChoice('generic.accesscodes', 2)).toBe("toegangscodes");
        });
    });
    describe('ucFirst', () => {
        it('should return "A" when input is "a"', () => {
            expect(ucFirst('a')).toBe("A");
        });
        it('should return Scolavisa when input is scolavisa', () => {
            expect(ucFirst('scolavisa')).toBe("Scolavisa");
        });
        it('should return "?" when input is undefined', () => {
            expect(ucFirst(undefined)).toBe("?");
        });
        it('should inject name when input is "generic.hellostudents" and params is {name: "Scolavisa"} and count is "1"', () => {
            expect(translateChoice('generic.hellostudents', 1, {name: "Scolavisa"})).toBe("Hallo leerling Scolavisa");
        });
        it('should inject name when input is "generic.hellostudents" and params is {name: "Scolavisa"} and count is "2"', () => {
            expect(translateChoice('generic.hellostudents', 2, {name: "Scolavisa"})).toBe("Hallo leerlingen Scolavisa");
        });
    });
});
