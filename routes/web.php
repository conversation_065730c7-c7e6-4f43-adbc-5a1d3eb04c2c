<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
})->middleware(['redirectifauthenticated', 'throttle:web']);

// student does not need to be logged in to see this page. they do need a valid sign_code
Route::get('registration/signregistration/{signtoken}', 'RegistrationsController@signRegistration');
Route::post('registration/savesigningofregistration/{regid}/{signtoken}', 'RegistrationsController@saveSigningOfRegistration');

// student page for entering preferred schedule times. No login, but the student does need an access token.
Route::get('schedulepreference/{accesstoken}', 'SchedulepreferenceController@edit');

// routes needed for authorisation (like login)
Route::middleware(['throttle:login'])->group(function () {
    Auth::routes();
});

Route::group(['prefix'=>'2fa'], function(){
    Route::get('/','LoginsecurityController@show2faForm');
    Route::post('/generateSecret','LoginsecurityController@generate2faSecret')->name('generate2faSecret');
    Route::post('/enable2fa','LoginsecurityController@enable2fa')->name('enable2fa');
    Route::post('/disable2fa','LoginsecurityController@disable2fa')->name('disable2fa');
    // 2fa middleware
    Route::post('/2faVerify', function () {
        return redirect(URL()->previous());
    })->name('2faVerify')->middleware('2fa');
});

// Bootstrap 5 migration test pages
Route::get('/bootstrap5-scoped-test', function () {
    return view('bootstrap5-scoped-test');
})->middleware(['auth', '2fa', 'throttle:web']);

Route::get('/bootstrap5-comparison', function () {
    return view('bootstrap5-comparison');
})->middleware(['auth', '2fa', 'throttle:web']);

Route::get('/bootstrap5-migration-guide', function () {
    return view('bootstrap5-migration-guide');
})->middleware(['auth', '2fa', 'throttle:web']);

Route::get('/bootstrap5-migration-example', function () {
    return view('bootstrap5-migration-example');
})->middleware(['auth', '2fa', 'throttle:web']);

Route::get('/bootstrap5-page-example', function () {
    return view('bootstrap5-page-example');
})->middleware(['auth', '2fa', 'throttle:web']);

// protected interfaces
Route::middleware(['auth', '2fa', 'throttle:web'])->group(function () {
    // SETUP (test) account
    Route::get('/setup/overview', 'SetupController@overview');
    // User Profile
    Route::get('/users/profile', 'UsersController@profile')->name('profileedit');
    Route::post('profileupdate', 'UsersController@updateprofile')->name('updateprofile');
    Route::post('passwchange', 'UsersController@changepassword')->name('changepassword');
    Route::post('resetpw', 'TutorsController@resetpw')->name('resetpw');
    // Home
    Route::get('/home', 'HomeController@index')->name('home');


    Route::get('registrationedit/{registrationid}', 'RegistrationsController@editregistration')->name('editregistration');
    Route::get('email-contacts', 'EmailController@mailContacts');

    // Domain
    Route::post('domains', 'DomainsController@update')->name('updatedomain');
    Route::get('domainsettings', 'DomainsController@domainSettings');

    // Students
    Route::get('students/createfromtrialrequest/{requestid}', 'StudentsController@createFromTrialRequest')->name('createfromtrialrequest');
    Route::get('students/accesscodes', 'StudentsController@accesscodes');
    Route::get('students/preferredschedule/{studentid}', 'StudentsController@preferencesPage');
    Route::resource('students', 'StudentsController');
    Route::get('exportcontactlist', 'StudentsController@studentListContactsExport');
    Route::get('students/maillist', 'StudentsController@maillist');
    Route::get('students/exportmaillist/{sortOrder?}', 'StudentsController@exportForMaillist');
    Route::resource('defaultchecklists', "DefaultChecklistsController");

    // Studentgroups
    Route::resource('studentgroups', "StudentgroupsController");

    // Courses
    Route::resource('courses', 'CoursesController');
    Route::get('archived-courses', "CoursesController@archived");
    Route::resource('locations', 'LocationsController');
    Route::resource('tutors', 'TutorsController');
    Route::get('dateexceptions/create/{prefilledSchoolyearId?}', 'DateExceptionsController@create');
    Route::resource('dateexceptions', 'DateExceptionsController');
    Route::resource('recurrenceoptions', 'RecurrenceOptionsController');
    Route::resource('coursegroups', "CoursegroupController");
    Route::get('removechecklist/{registrationid}/{checklistid}', "RegistrationsController@removechecklist");
    Route::resource('registrations', "RegistrationsController");
    Route::resource('schoolyears', "SchoolyearController");
    Route::resource('trialstudents', 'TrialstudentsController');
    Route::resource('tasks', 'TasksController');
    Route::resource('studentlists', 'StudentlistController');

    // reports
    Route::get('report-registrations', 'ReportsController@registrations');
    Route::get('report-registrations-open-checklists', 'ReportsController@registrationsOpenChecklists');
    Route::get('report-students', 'ReportsController@unregisteredStudents');
    Route::get('report-emaillists', 'ReportsController@emaillists');
    Route::get('report-active-students-grouped', 'ReportsController@activeStudentsGroupedByMonth');
    Route::get('dashboardoverview', 'ReportsController@dashboardoverview');
    Route::get('report-email-log', 'EmaillogentriesController@index');
    Route::get('mailtemplates', 'EmailController@mailtemplates');
    Route::get('report-alarms', 'AlertController@reportAlarms');

    // attendance
    Route::get('attendanceoptions', 'AttendanceController@edit');

    // API's
    Route::get('/getcoursegroups', "CoursegroupController@apiIndex");
    Route::get('/getevents/{onlyfuture?}/{type?}/{addevent?}', 'EventsController@apiIndexByType');
    Route::get('/gettasktypes', "TasktypesController@apiIndex");
    Route::get('/getregistrations', 'ApiController@registrations');
    Route::get('/getchecklist/{checklistid}', 'ChecklistController@get');
    Route::get('/getchecklists/{registrationid}', 'RegistrationsController@getAllChecklists');
    Route::put('/addChecklist/{defaultChecklistid}/{registrationid}', 'RegistrationsController@addChecklist');
    Route::delete('/deleteChecklist/{checklistid}/{registrationid}', 'RegistrationsController@delChecklist');
    Route::put('/setChecklistItem/{checklistid}/{itemid}/{value}', 'ChecklistController@setItem');
    Route::delete('/students/full/{studentid}', 'StudentsController@delFull'); // oud
    Route::get('/getdateexceptions/{schoolyearId}', 'DateExceptionsController@get');
    Route::get('/getlocations', 'LocationsController@get');
    Route::get('/alertstatus', 'AlertController@status');
    Route::get('/gettimetableconflicts', 'AlertController@getConflicts');

    // Timetable, Scheduling, Calendar
    Route::get("events/importics", "EventsController@importics");
    Route::get('timetables/schedulingproposal', "TimetablesController@schedulingproposal");
    Route::get('timetables/showstudentprefs', 'TimetablesController@showstudentprefs');
    Route::get('timetables/report', 'TimetablesController@report');
    Route::get('timetableedit/{registrationid}', 'TimetablesController@timetableedit')->name("edittimetable");
    Route::get('calendar', 'TimetablesController@calendar');
    Route::get('calendar/scheduleforday', 'CalendarController@scheduleforday');
    // this is not needed for dateexception, the resource is OK as it is
    Route::get('calendarevent/{caleventid}', 'EventsController@getEventShort');
    // excel export as a tmp solution for studentprefs
    Route::get('timetables/exportprefs/{sortOrder?}', 'TimetablesController@exportPrefs');
    Route::resource('timetables', "TimetablesController");
    Route::resource('events', "EventsController");
    // Planning (=layers planning)
    Route::prefix('planning')->group(function() {
        Route::get('lesson', 'PlanningController@lessonplanning');
    });

    // ClassY: libraries
    Route::resource('libraries', 'LibrariesController');
    Route::get('libraries/{libid}/contents', 'LibrariesController@contents');
    Route::get('libraries/{libid}/sharing', 'LibrariesController@sharing');
    Route::get('file/{filename}', 'FileResolveController@get');

    // ClassY: Messages
    Route::get("messages/broadcast", "MessageController@broadcast");
    Route::post("messages/sendbroadcast", "MessageController@sendbroadcast");
    Route::get("messages/forparticipants", "MessageController@forparticipants");

    // ClassE: Messages
    Route::get("messages/list", "MessageController@list");

    // catch all
    Route::get('{any}', function () {
        return view('home');
    })->where('any', '.*');
});

