# Class Project Guidelines

## general rules for Vue
- Use Vue Single File Components (SFCs) for all new UI components
- Use Composition API instead of Options API for all Vue components
- Prefer async/await over promise chains
- Follow the existing project structure where components are organized by feature
- When refactoring server-side code to client-side, ensure API endpoints return JSON
- Don't use Pinia or Vuex, we keep state in composables.
- Always use <script setup> instead of the setup() method
- Always order an SFC as 1. template, 2. script, 3. css
- Name composables with the `use` prefix (e.g., `useStudents`, `useTimetableReport`)
- Place composables in the `resources/js/class3/composables` directory
- Composables should handle API calls, state management, and business logic
- Components should get their data from composables, not via props from parent components
- For computed properties, don't use the 'get' prefix (use 'studentName' instead of 'getStudentName')
- Only use the 'get' prefix for methods that make API calls or fetch data
- The return fields in composables are always in alphabetical order
- Variables in composables that will be imported in components are placed outside the function useName() scope so they are in global scope.

Here's an example of a composable:
``` js
import { ref } from 'vue';
import axios from 'axios';
import useNoty from './useNoty.js';
const students = ref([]);

export default function useStudents() {
    import { failNoty } from useNoty;

    const getStudents = async () => {
        try {
            const response = await axios.get('/api/students');
            students.value = response.data;
        } catch (error) {
            failNoty(error);
        }
    };

    const saveStudent = async (student) => {
        try {
            await axios.post('/api/students', student);
        } catch (error) {
            failNoty(error);
        }
    };

    return {
        getStudents,
        saveStudent,
        students
    };
}
```

## embedding in the laravel backend
- We use a root component (aka page) for each page in the app. The root component is responsible for fetching data by calling the get function in a composable.
- The child components will use the data from the composable.
- We minimize the use of properties and methods in the root component. most communication is done using the composable
- in laravel we use the router and controller to start a blade template that contains the root component.
- There is usually no data passed to the blade template (there are exceptions). The vue component will fetch the data from the API which is just another route to the controller.
- The methods in the controller for api calls are prefixed with api, like apiIndex, apiStore, apiUpdate, apiDestroy, apiShow, apiGet

