import DefaultTheme from 'vitepress/theme';
import { inBrowser } from 'vitepress';
import mediumZoom from 'medium-zoom';
import { onMounted, watch, nextTick } from 'vue';
import { useRoute } from 'vitepress';
import './custom.css';

const theme = {
    extends: DefaultTheme,
    setup() {
        const route = useRoute();
        
        const initZoom = () => {
            mediumZoom('.medium-zoom', {
                background: 'var(--vp-c-bg)',
                margin: 5
            }).on('open', (zoom: any) => {
                console.log('Zoomed in', zoom);
                zoom.scaleTarget(3.0);
            });
        };

        onMounted(() => {
            if (inBrowser) {
                initZoom();
            }
        });

        // Herinitialiseer zoom wanneer de route verandert
        watch(
            () => route.path,
            () => nextTick(() => initZoom())
        );
    }
};

export default theme;
