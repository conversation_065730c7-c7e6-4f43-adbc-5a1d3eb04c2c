import { defineConfig } from 'vitepress'

// https://vitepress.dev/reference/site-config
export default defineConfig({
    title: "CLASS technical docs",
    description: "For developers, by developers",
    themeConfig: {
        search: {
            provider: 'local'
        },
        // https://vitepress.dev/reference/default-theme-config
        nav: [
            { text: 'Home', link: '/' },
            { text: 'Components', link: '/components' },
            { text: 'Test data', link: '/testdata' }
        ],

        sidebar: [
            {
                text: 'Components',
                items: [
                    { text: 'Class', link: '/components/Class' },
                    { text: 'ClassY', link: '/components/ClassY' }
                ]
            },
            {
                text: "Test data",
                items: [
                    { text: "Registrations", link: '/testdata/students-and-courses' }
                ]
            }
        ]
    }
})
