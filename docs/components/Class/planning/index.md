# Planning Components

## PAGE: Plancard
The planning page serves two stages of the planning process: **Prepare & Create** and **Edit**. The diagram below also shows the components per process stage.
The neccecary data for the components is fetched from the API using the baseData composable. Base data is always initiated at the root component: LessonPlanning. 
The root component can be thought of as the "page" component. It is responsible for fetching the data and passing it down to the child components.

<img class="medium-zoom" src="./Class_Plancard_Components.drawio.png" alt="Plancard Components Diagram" />

### Prepare & Create
Components used in the **Prepare & Create** stage:
- LessonPlanningBaseData: determine what we are planning for (course, student and schoolyear)
- LessonPlanningDetails: shows details of the planning (do we have a planning already, what's the recurrence, etc)
- LessonPlanningCreate: container for the create components:
  - LessonPlanningCreateForm: form to create a new planning (date, time, location, tutor, recurrence)
  - LessonPlanningPreparePopup: Analyse the planning to be created, warn if there are conflicts

### Edit
Components used in the **Edit** stage:
- LessonPlanningEventSeries: shows the planning events in a series, allows to move, delete the events
- LessonPlaaningTutoringEvent: shows the details of a single event, allows to edit the event

## PAGE: Full Calendar
The full calendar shows the events in a month, week or day view. It uses a third party library called [VueCal](https://antoniandre.github.io/vue-cal/).
In this case ShowCalendar is the root component (or page). 

<img class="medium-zoom" src="./Class_FullCalendar_Components.drawio.png" alt="Full Calendar Components Diagram" />

## Page Schedule Preferences
This one is special, because it has a admin version and a (public) student version. The admin page uses standard credentials. For the student version we use an access token which is available for every student record.
The components are largely the same except for the way they retrieve and store data, based on login or access token.

<img class="medium-zoom" src="./Class_SchedulePrefs_Components.drawio.png" alt="Schedule preferences Components Diagram" />
